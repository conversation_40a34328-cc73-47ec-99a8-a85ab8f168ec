FROM apache/airflow:latest

USER root

# Update package lists and install necessary packages
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        default-jdk \
        git \
        curl \
        libcairo2 \
        libpango-1.0-0 \
        libglib2.0-0 \
        libnss3 \
        libnspr4 \
        libdbus-1-3 \
        libatk1.0-0 \
        libatk-bridge2.0-0 \
        libatspi2.0-0 \
        libx11-6 \
        libxcomposite1 \
        libxdamage1 \
        libxext6 \
        libxfixes3 \
        libxrandr2 \
        libgbm1 \
        libxcb1 \
        libxkbcommon0 \
        libasound2 \
        libcups2 && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Set JAVA_HOME environment variable
ENV JAVA_HOME=/usr/lib/jvm/default-java
ENV PATH="${JAVA_HOME}/bin:${PATH}"

# Verify Java installation
RUN java -version && echo "Java installed successfully"

# Create directories with proper permissions inside the image
# Note: For volumes, host permissions are often required, but setting ownership in image is good.
RUN mkdir -p /opt/airflow/etl \
    /opt/airflow/data \
    /opt/airflow/logs \
    /opt/airflow/logs/dag_processor \
    /opt/airflow/logs/scheduler \
    /opt/airflow/dags \
    /opt/airflow/config \
    /opt/airflow/scripts && \
    chown -R airflow:root /opt/airflow && \
    chmod -R 775 /opt/airflow

# Switch to airflow user
USER airflow

# Copy requirements and install Python packages
COPY requirements.txt /tmp/requirements.txt

# Install Python packages
RUN pip install --no-cache-dir -r /tmp/requirements.txt && \
    pip install playwright && \
    playwright install

# Set working directory and environment variables
WORKDIR /opt/airflow
ENV PYTHONPATH=/opt/airflow:/opt/airflow/dags
ENV AIRFLOW_HOME=/opt/airflow

# Copy project files into the image for production deployment
# Volumes below will overlay these for persistence/external access
COPY --chown=airflow:root dags/ /opt/airflow/dags/
COPY --chown=airflow:root scripts/ /opt/airflow/scripts/
COPY --chown=airflow:root etl/ /opt/airflow/etl/
COPY --chown=airflow:root config/ /opt/airflow/config/
COPY --chown=airflow:root data/ /opt/airflow/data/
COPY --chown=airflow:root logs/ /opt/airflow/logs/


# Final verification
RUN python -c "import sys; print('Python path:', sys.path)" && \
    java -version && \
    echo "Setup completed successfully"