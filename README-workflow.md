# ETL Workflow Orchestration

This document describes how to set up and run the ETL workflow orchestration system.

## Overview

The ETL workflow consists of the following components:

1. **Extraction**: Extract job links and job content
2. **Prediction**: Run batch prediction and embedding
3. **Processing**: Process prediction and embedding results using PySpark

The workflow is orchestrated using Celery, with the following schedule:

- Extraction and prediction flow: Every 6 hours
- Process prediction results: Every 8 hours
- Process embedding results: Every 8.5 hours (8 hours and 30 minutes)

## Setup

### Prerequisites

- Docker and Docker Compose
- Python 3.9+
- Redis (for Celery broker and result backend)
- Google Cloud Platform credentials (for GCS access)

### Environment Variables

Create a `.env` file with the following variables:

```
GCP_CONFIG_PATH=/app/config/gcp_config.json
GCS_BUCKET=your-gcs-bucket
GCS_INPUT_PREFIX=input
GCS_OUTPUT_PREFIX=output
```

### Installation

1. Install the required Python packages:

```bash
pip install -r requirements.txt
```

2. Make the scripts executable:

```bash
chmod +x scripts/start_celery_workers.sh scripts/start_celery_beat.sh scripts/run_workflow.py
```

## Running the Workflow

### Using Docker Compose

The easiest way to run the workflow is using Docker Compose:

```bash
docker-compose -f docker-compose-celery.yml up -d
```

This will start:
- Redis for Celery broker and result backend
- Celery workers for extraction, prediction, and processing tasks
- Celery beat scheduler
- Flower for monitoring Celery tasks

You can access the Flower dashboard at http://localhost:5555 to monitor the tasks.

### Running Manually

Alternatively, you can run the components manually:

1. Start Redis:

```bash
docker run -d -p 6379:6379 redis:latest
```

2. Start Celery workers:

```bash
./scripts/start_celery_workers.sh
```

3. Start Celery beat scheduler:

```bash
./scripts/start_celery_beat.sh
```

4. Trigger a workflow manually:

```bash
python scripts/run_workflow.py extraction-prediction
```

## Available Workflows

You can trigger the following workflows manually:

- `extraction-prediction`: Run extraction and prediction workflow
- `process-prediction`: Run prediction results processing
- `process-embedding`: Run embedding results processing
- `all`: Run all workflows in sequence

Example:

```bash
python scripts/run_workflow.py all
```

## Monitoring

You can monitor the tasks using Flower:

```bash
celery -A etl.celery_config flower --port=5555
```

Then open http://localhost:5555 in your browser.

## Troubleshooting

### Logs

Check the logs for each component:

```bash
# Celery workers
docker-compose -f docker-compose-celery.yml logs extraction-worker
docker-compose -f docker-compose-celery.yml logs prediction-worker
docker-compose -f docker-compose-celery.yml logs processing-worker

# Celery beat
docker-compose -f docker-compose-celery.yml logs beat

# Flower
docker-compose -f docker-compose-celery.yml logs flower
```

### Common Issues

- **Task not running**: Check if the Celery workers are running and connected to Redis
- **Task failing**: Check the task logs for error messages
- **Scheduler not triggering tasks**: Check if Celery beat is running and the schedule is correctly configured
