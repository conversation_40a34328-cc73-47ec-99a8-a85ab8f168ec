# Job Description Processing Pipeline

This project implements a complete ETL pipeline for processing job descriptions:

1. Extract job links from company websites
2. Extract job content from the links
3. Process job descriptions using GenAI for prediction
4. Process prediction results using PySpark
5. Generate embeddings for job descriptions
6. Process embedding results using PySpark

## Architecture

The pipeline is orchestrated using Apache Airflow with the following components:

- **Extraction**: Scrapes job links and content from company websites
- **Prediction**: Submits job descriptions to GenAI for batch prediction
- **Processing**: Processes prediction and embedding results using PySpark

## Schedule

The pipeline runs on the following schedule:

- **JD Extraction and Prediction**: Every 6 hours
- **Prediction Results Processing**: Every 8 hours
- **JSONL Processing**: Every 8 hours
- **Embedding Results Processing**: Every 8.5 hours (at 00:30, 08:30, 17:30)

## Setup

### Prerequisites

- Docker and Docker Compose
- Python 3.9+
- Apache Spark
- Google Cloud Platform account with access to Vertex AI

### Environment Variables

Create a `.env` file with the following variables:

```
GOOGLE_CLOUD_PROJECT=your-project-id
GCS_BUCKET=your-gcs-bucket
GCS_INPUT_PREFIX=input
GCS_OUTPUT_PREFIX=output
GCP_CONFIG_PATH=/opt/airflow/config/gcp_config.json
```

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd backend-career-data-engine
```

2. Build and start the Docker containers:

```bash
docker-compose up -d
```

## Running the Pipeline

### Using Airflow

1. Access the Airflow UI at http://localhost:8080
2. Enable the following DAGs:
   - `jd_continuous_scraping`: Extracts JD links and content, submits for batch prediction
   - `jd_prediction_processing`: Processes prediction results
   - `jd_jsonl_processing`: Processes JSONL files
   - `jd_embedding_processing`: Processes embedding results

### Running Manually

You can also run the components manually:

#### Extract JD Links and Content

```bash
python -m etl.extractor.jd_content_extractor
```

#### Process Prediction Results

```bash
./scripts/run_prediction_processor.sh data/prediction data/output/processed_jds true
```

#### Process JSONL Files

```bash
./scripts/run_jsonl_processor.sh data/prediction data/output/processed_jds true
```

## Project Structure

```
backend-career-data-engine/
├── dags/                  # Airflow DAGs
│   └── jd_pipeline.py     # Main pipeline DAG
├── etl/                   # ETL components
│   ├── ai/                # AI-related code
│   ├── extractor/         # Extraction components
│   ├── transformer/       # Transformation components
│   └── loader/            # Loading components
├── scripts/               # Helper scripts
└── docker-compose.yml     # Docker Compose configuration
```

## Monitoring and Maintenance

### Logs

- Airflow logs: Available in the Airflow UI
- Container logs: `docker-compose logs -f <service-name>`

### Troubleshooting

- **DAG not running**: Check Airflow scheduler logs
- **Task failing**: Check task logs in Airflow UI
- **PySpark job failing**: Check logs in the Airflow UI or run the job manually

## Development

### Adding a New Component

1. Create a new Python module in the appropriate directory
2. Update the Airflow DAG to include the new component
3. Test the component manually
4. Deploy the changes

### Testing

Run unit tests:

```bash
pytest tests/
```

## Production Deployment

For production deployment:

1. Use a production-grade database for Airflow (PostgreSQL)
2. Configure proper authentication for Airflow
3. Set up monitoring and alerting
4. Use a production-grade message broker (Redis or RabbitMQ)
5. Configure proper resource limits for containers

## License

[Your License]
