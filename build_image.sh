# Define service and image names for ETL
service_name="job-scraper-etl"
image_name="job-scraper-etl"

location=us-central1
project=eco-seeker-458712-i8
repository=hire10x-prod
version=release

registry_path=$location-docker.pkg.dev/$project/$repository

echo "Building ETL image..."
docker build -t $image_name:$version -f Dockerfile.prod .

echo "Tagging image..."
docker tag $image_name:$version $registry_path/$service_name:$version

echo "Pushing to registry..."
docker push $registry_path/$service_name:$version

echo "ETL image pushed to: $registry_path/$service_name:$version"