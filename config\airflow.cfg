# [database]
# sql_alchemy_conn = postgresql+psycopg2://airflow:airflow@etl-postgres:5432/airflow

# [core]
# executor = CeleryExecutor
# load_examples = False
# dags_folder = /opt/airflow/dags
# load_default_connections = False
# fernet_key = TuKvQxSWVhSpJ-JA42pqEsbMV8UQ6i6fS8Ibbqw7tvs=


# [celery]
# broker_url = redis://etl-redis:6379/0
# result_backend = db+**********************************************/airflow
# worker_concurrency = 16
# worker_prefetch_multiplier = 1
# task_acks_late = True

# # REMOVE [api] SECTION

# # REMOVE [api_auth] SECTION

# [scheduler]
# min_file_process_interval = 10
# print_stats_interval = 30
# scheduler_heartbeat_sec = 10
# parsing_processes = 2

# [dag_processor]
# refresh_interval = 10

# [logging]
# base_log_folder = /opt/airflow/logs
# logging_level = INFO
# delete_local_logs = True