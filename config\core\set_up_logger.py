import sys
import os
import logging

LOGGER_NAME = "JD_SCRAPER"
LOG_LEVEL = int(os.getenv("LOG_LEVEL",10))

logger = logging.getLogger(LOGGER_NAME)
logger.setLevel(LOG_LEVEL)

# Define log format with filename and line number
formatter = logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
)

# Add console handler
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)