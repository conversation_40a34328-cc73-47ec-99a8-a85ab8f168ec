from functools import lru_cache
import os

from pydantic import ConfigDict
from pydantic_settings import BaseSettings
from dotenv import load_dotenv
load_dotenv()

class Config(BaseSettings):
    """Configuration management class"""
    APP_NAME: str = "JOB SCRAPING SERVICE"
    APP_VERSION: str = "1.0.0"
    SUMMARY: str = "Collect the job description and extract the key information from it"
    ENVIRONMENT: str
    LOG_LEVEL: int
    SERVER_NAME: str

    #### db config ####
    POSTGRES_HOST:str
    POSTGRES_USER:str
    POSTGRES_PASSWORD:str
    POSTGRES_DB:str
    POSTGRES_PORT:int
    POSTGRES_SCHEMA:str

    #################################
    ###### CORS Config ##############
    #################################
    ORIGINS: str
    ALLOWED_HOST: str

    # GCP Settings
    GCP_PROJECT_ID: str 
    GCP_PROJECT_LOCATION: str 
    GCP_CONFIG_PATH: str 
    
    # GCS Settings
    GCS_BUCKET: str 
    GCS_INPUT_PREFIX: str 
    GCS_OUTPUT_PREFIX: str 
    GCS_BASE_PATH: str 
    
    # vertex batch config
    EMBEDDING_BATCH_SIZE: int =1500
    VERTEX_PREDICTION_BATCH_SIZE: int = 1000
    VERTEX_PREDICTION_BATCH_INPUT_DIR: str
    
    # Local Paths file management
    LOCAL_SCRAPED_LINKS_DIR: str
    LOCAL_JD_NEEDS_PREDICTION_DIR : str
    LOCAL_JD_SKIP_PREDICTION_DIR: str
    VERTEX_PREDICTION_RESULTS_DIR: str 
    EMBEDDING_INPUT_BATCH_DIR: str
    EMBEDDING_RESULTS_DIR:str


    
    # Processing Settings
    GCS_OUTPUT_PREDICTION_PREFIX: str
    GCS_OUTPUT_EMBEDDING_PREFIX:str

    @property
    def gs_base_path(self) -> str:
        return f"gs://{self.GCS_BUCKET}"
    
    def gs_input_path(self, processing_type: str) -> str:
        return f"{self.gs_base_path}/{self.GCS_INPUT_PREFIX}/{processing_type}/"
    
    def gs_output_path(self, processing_type: str) -> str:
        return f"{self.gs_base_path}/{self.GCS_OUTPUT_PREFIX}/{processing_type}/"

    model_config = ConfigDict(extra="ignore")
    
@lru_cache
def get_settings() -> Config:
    """Load settings from .env file"""
    if os.getenv("ENVIRONMENT") and os.getenv("SERVER_NAME"):
        return Config()
    return Config(_env_file="./.env", _env_file_encoding="utf-8")

