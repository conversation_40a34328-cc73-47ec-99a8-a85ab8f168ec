
"""
JD Processing Pipeline DAG

This DAG orchestrates the complete JD processing pipeline:
1. A scheduled scraping pipeline that extracts JD links and content (every 6 hours)
2. Batch prediction for extracted JDs
3. Processing prediction results with PySpark (every 8 hours)
4. Batch embedding generation
5. Processing embedding results with PySpark (every 8.5 hours)

The design follows a continuous processing approach where:
- Scraping happens company by company with immediate batch submission
- Results are processed periodically by scheduled PySpark jobs
"""
from datetime import datetime, timedelta
import logging
from airflow.sdk import DAG
from airflow.providers.standard.operators.python import PythonOperator

from etl.ai.prediction_batch_processing import main as run_batch_prediction
from etl.extractor.jd_content_extractor import main as extract_job_content
from etl.extractor.jd_link_extractor import main as scrape_job_links
from etl.transformer.process_prediction_result import main_unified_embedding_prep
from etl.transformer.process_embedding_result import main as main_embedding_result_processing

# Configure logging
logger = logging.getLogger(__name__)




# Default arguments
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
}


################################
#DAG 1: Continuous JD Scraping #
################################

def extract_jd_links_task(**kwargs):
    """Extract job links from company websites"""
    logger.info("Starting job link extraction")
    return scrape_job_links()
    

def extract_jd_content_task(**kwargs):
    """Extract job content from job links"""
    logger.info("Starting job content extraction")
    jd_files_count = extract_job_content()
    logger.info(f"Extracted content for {jd_files_count} job descriptions")
    return jd_files_count

def run_batch_prediction_task(**kwargs):
    """Run batch prediction on job descriptions"""
    logger.info("Starting batch prediction")
    return run_batch_prediction()


def process_prediction_results_task(**kwargs):
    """Process prediction results with PySpark"""
    logger.info("Starting prediction results processing")
    return main_unified_embedding_prep()

def process_embedding_results_task(**kwargs):
    """Run batch embedding on processed prediction results"""
    logger.info("Starting batch embedding")
    return main_embedding_result_processing()


################################
#DAG 1: Continuous JD Scraping #
################################

dag_jd_link_scraping = DAG(
    'jd_link_scraping',
    default_args=default_args,
    description='Continuously scrape JDs from company websites and run batch prediction',
    schedule='0 */4 * * *',  # Run every 4 hours
    start_date=datetime.now(),  # ✅ Use this
    tags=['jd-processing'],
    catchup=False,
)

# Define tasks for the JD scraping DAG
extract_jd_links = PythonOperator(
    task_id='extract_jd_links',
    python_callable=extract_jd_links_task,
    dag=dag_jd_link_scraping,
)

########################################
#DAG 2: Continuous JD content Scraping #
########################################

dag_jd_content_scraping = DAG(
    'jd_content_scraping',
    default_args=default_args,
    description='Continuously scrape JDs from company websites and run batch prediction',
    schedule='0 */2 * * *',  # Run every 2 hours
    start_date=datetime.now(),  # ✅ Use this
    tags=['jd-processing'],
    catchup=False,
)
extract_jd_content = PythonOperator(
    task_id='extract_jd_content',
    python_callable=extract_jd_content_task,
    dag=dag_jd_content_scraping,
)


########################################
#DAG 3: JD Predication process #########
########################################

dag_jd_prediction_processing = DAG(
    'jd_predication_processing',
    default_args=default_args,
    description='Continuously scrape JDs from company websites and run batch prediction',
    schedule='0 * * * *',  # Run every 1 hours
    start_date=datetime.now(),  # ✅ Use this
    tags=['jd-processing'],
    catchup=False,
)

run_jd_batch_prediction = PythonOperator(
    task_id='run_batch_prediction',
    python_callable=run_batch_prediction_task,
    dag=dag_jd_prediction_processing,
)


###############################################
# DAG 4: Preparing for embedding processing   #
###############################################

dag_prediction_processing = DAG(
    'jd_prediction_results',
    default_args=default_args,
    description='Process prediction results with PySpark',
    schedule='0 * * * *',  # Run every 1 hours
    start_date=datetime.now(),  # ✅ Use this
    tags=['jd-processing'],
    catchup=False,
)

process_prediction_results = PythonOperator(
    task_id='process_prediction_results',
    python_callable=process_prediction_results_task,
    dag=dag_prediction_processing,
)

#########################################
# DAG 5: Process Embedding Results      #
#########################################

dag_embedding_result_processing = DAG(
    'jd_embedding_results',
    default_args=default_args,
    description='Process embedding results with PySpark',
    schedule='0 * * * *',  # Run every 1 hours
    start_date=datetime.now(),
    tags=['jd-processing'],
    catchup=False,
)

process_embedding_results = PythonOperator(
    task_id='process_embedding_results',
    python_callable=process_embedding_results_task,
    dag=dag_embedding_result_processing,
)
 
