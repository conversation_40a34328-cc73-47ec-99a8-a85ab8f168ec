[{"jd_link": "https://careers.cibirix.com/jobs/Careers/617903000001320019/Node-Js-Developer?source=CareerSite", "company_id": 3317, "source": 3, "skills": "", "title": "Node.Js Developer", "location": "Indore, India   | Posted on 10/22/2024", "location_type": null, "job_type": "full_time", "min_experience": 3, "max_experience": 3, "apply_link": "https://careers.cibirix.com/jobs/Careers/617903000001320019/Node-Js-Developer?source=CareerSite", "description": "Job listing Job details Job Information Date Opened 10/22/2024 Job Type Full time Industry IT Services City Indore State/Province Madhya Pradesh Country India Zip/Postal Code 452010 Job Description A Node. js developer is a software engineer who specializes in developing applications using the Node. js runtime environment. Node. js is an open-source, server-side JavaScript runtime that allows developers to build scalable and high-performance networks applications. As a Node. js developer, your primary focus will be on developing server-side logic, managing the interchange of data between the server and users, and integrating external systems or services into the application. Role: Node. js Developer Responsibilities:Develop server-side logic using Node. js to power scalable and high-performance applications. Design and implement APIs and web services for seamless data interchange between the server and clients(Front End). Collaborate with front-end developers to integrate user-facing elements with server-side logic. Optimize applications for maximum performance, scalability, and reliability. Implement security and data protection measures to ensure the integrity and privacy of user information. Debug issues that arise in the development, testing, and production environments. Stay up-to-date with emerging trends and technologies in the Node. js and JavaScript. Requirements Proficiency in Node. js and JavaScript, with a solid understanding of its ecosystem and core concepts. Experience in building server-side applications using frameworks such as Express. js. Knowledge of databases and SQL/MySql, including design and optimization. Experience with version control systems, such as Git. Understanding of asynchronous programming. Familiarity with deployment and hosting of Node. js applications in cloud environments (like AWS). Strong problem-solving skills and attention to detail. Minimum 3 years of experience. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.cibirix.com/jobs/Careers/617903000001322027/SEO-Analyst?source=CareerSite", "company_id": 3317, "source": 3, "skills": "", "title": "SEO Analyst", "location": "Indore, India   | Posted on 10/22/2024", "location_type": null, "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://careers.cibirix.com/jobs/Careers/617903000001322027/SEO-Analyst?source=CareerSite", "description": "Job listing Job details Job Information Date Opened 10/22/2024 Job Type Full time Work Experience 5+ years Industry IT Services City Indore State/Province Madhya Pradesh Country India Zip/Postal Code 452010 Job Description We are seeking a dedicated and detail-oriented SEO Analyst to join our dynamic team. The SEO Analyst will work closely with our SEO, content, and digital marketing teams to enhance our clients' search engine ranking and overall SEO strategy. The successful candidate should be well-versed in SEO best practices, keyword research, intent research, and web analytics. Responsibilities :- ● Conduct keyword research using dedicated software and tools, and coordinate with the content team to optimize content. ● Analyze websites for improvements, perform comprehensive SEO site audits, and execute changes for SEO optimization. ● Develop and implement link-building strategies. ● Work with the web development team to ensure SEO best practices are properly implemented on newly developed code. ● Conduct on-page and off-page optimization efforts. ● Evaluate and maintain tracking and reporting on key SEO metrics, and provide actionable insights. ● Stay up-to-date with the latest trends and changes in SEO and major search engine algorithms. ● Work on quality link-building strategies along with the recent updates based on the business requirements. ● Assist in developing and implementing our content strategy. ● Work collaboratively with other teams to align SEO strategies across various channels. Requirements● Bachelor's degree in marketing, business, IT, or a related field. ● Proven experience in a similar role. ● Understanding of ranking factors and search engine algorithms. ● Proficiency in web analytics software and keyword tools. ● Experience with data-driven SEO analysis and optimization. ● Proficient in Google Analytics, Google Search Console, and Bing Webmaster Tools. ● Excellent written and verbal communication skills. ● Strong organizational, time management, and analytical skills. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.cibirix.com/jobs/Careers/617903000001322054/React-Developer?source=CareerSite", "company_id": 3317, "source": 3, "skills": "", "title": "React Developer", "location": "Indore, India   | Posted on 10/22/2024", "location_type": null, "job_type": "full_time", "min_experience": 3, "max_experience": null, "apply_link": "https://careers.cibirix.com/jobs/Careers/617903000001322054/React-Developer?source=CareerSite", "description": "Job listing Job details Job Information Date Opened 10/22/2024 Job Type Full time Industry IT Services City Indore State/Province Madhya Pradesh Country India Zip/Postal Code 452010 Job Description We need an experienced candidate with 3+ years of in-hand experience in React and must have worked on the project from scratch. Strong proficiency in JavaScript, including DOM manipulation and the JavaScript object model. Thorough understanding of React. js and its core principles. Experience with React. js workflows (such as Flux or Redux). Knowledge of RESTful APIsKnowledge of modern authorization mechanisms, such as JSON Web TokenExperience with data structure librariesExperience with modern front-end build pipelines and toolsExperience with common front-end development tools and Best practices. Ability to understand business requirements and translate them into technical requirementsAbility to benchmark and optimize applications. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.cibirix.com/jobs/Careers/617903000001322080/Laravel-Developer?source=CareerSite", "company_id": 3317, "source": 3, "skills": "", "title": "<PERSON><PERSON>", "location": "Indore, India   | Posted on 10/22/2024", "location_type": null, "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://careers.cibirix.com/jobs/Careers/617903000001322080/Laravel-Developer?source=CareerSite", "description": "Job listing Job details Job Information Date Opened 10/22/2024 Job Type Full time Industry IT Services City Indore State/Province Madhya Pradesh Country India Zip/Postal Code 452010 Job Description Strong knowledge of PHP Laravel web framework Proficient understanding of AWS services, EC2, RDS, and S3 Strong knowledge of Laravel REST API Understanding the fully synchronous behavior of PHP Understanding of MVC design patterns Good understanding of front-end technologies, such as JavaScript, HTML5, and CSS3 Knowledge of object-oriented PHP programming Understanding accessibility and security compliance Strong knowledge of the common PHP or web server exploits and their solutions Understanding fundamental design principles behind a scalable application User authentication and authorization between multiple systems, servers, and environments Integration of multiple data sources and databases into one system Familiarity with limitations of PHP as a platform and its workarounds Creating database schemas that represent and support business processes Strong knowledge of MySQL and NoSQL as a plus Proficient understanding of code versioning tools, such as Git I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.cibirix.com/jobs/Careers/617903000001330003/Wordpress-Developer?source=CareerSite", "company_id": 3317, "source": 3, "skills": "", "title": "Wordpress Developer", "location": "Indore, India   | Posted on 10/22/2024", "location_type": null, "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://careers.cibirix.com/jobs/Careers/617903000001330003/Wordpress-Developer?source=CareerSite", "description": "Job listing Job details Job Information Date Opened 10/22/2024 Job Type Full time Industry IT Services City Indore State/Province Madhya Pradesh Country India Zip/Postal Code 452001 Job Description Strong understanding of PHP back-end development. In and out understanding of WordPress and theme - plugin implementations. Much has experience and knowledge in improving WordPress performance. Meeting with website functional requirements. Experienced in content structuring and code optimization. Good understanding of front-end technologies, including HTML5, CSS3, JavaScript, jQuery. Knowledge of code versioning tools. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.cibirix.com/jobs/Careers/617903000001718036/PPC-Analyst?source=CareerSite", "company_id": 3317, "source": 3, "skills": "", "title": "PPC Analyst", "location": "Indore, India   | Posted on 10/22/2024", "location_type": null, "job_type": "full_time", "min_experience": 4, "max_experience": 8, "apply_link": "https://careers.cibirix.com/jobs/Careers/617903000001718036/PPC-Analyst?source=CareerSite", "description": "Job listing Job details Job Information Date Opened 10/22/2024 Job Type Full time Work Experience 5+ years Industry IT Services City Indore State/Province Madhya Pradesh Country India Zip/Postal Code 452001 Job Description · Job Description:-The PPC / Paid Search is responsible for administering and managing Pay per Click media strategies for clients. She/he should be able to quickly understand & support initiatives that will contribute to the goals and success of client campaigns. Perform daily account management on Google AdWords, Facebook, Bing and other search platforms. Assist in the maintenance and monitoring of keyword bids, Account daily and monthly budget caps, impression shares, quality score & other important account metrics. Provide creative copy suggestions and graphical ad templates. Assist in the management of Display network placement lists on AdWords & through other contextual advertising platforms. Can devise strategies to increase ROI – Is good at increasing CTR & reducing the cost per click. Support the generation of new paid search campaigns, ad groups, and accounts and aid in the creation of new paid search marketing initiatives. Able to understand the business website thoroughly to implement a strategic plan for Google AdWordsKeep pace with search engine and PPC industry trends and developmentsShould have a minimum 4-8 years of experience in the same domain. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.cibirix.com/jobs/Careers/617903000001718076/Digital-Marketing-Manager?source=CareerSite", "company_id": 3317, "source": 3, "skills": "", "title": "Digital Marketing Manager", "location": "Indore, India   | Posted on 10/22/2024", "location_type": null, "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://careers.cibirix.com/jobs/Careers/617903000001718076/Digital-Marketing-Manager?source=CareerSite", "description": "Job listing Job details Job Information Date Opened 10/22/2024 Job Type Full time Work Experience 5+ years Industry IT Services City Indore State/Province Madhya Pradesh Country India Zip/Postal Code 452001 Job Description · Experience with tracking and improving critical KPIs on a dashboard. · Content is going to be a huge part of our marketing efforts. Having experience in driving relevant traffic from content and measuring conversion and ROI from it is required. · Strong understanding of how to drive relevant traffic from channels like Facebook, Instagram, Pinterest, search – both organic and paid. · Practical experience with email marketing is a must. Proficiency with marketing tools & technologies like email automation, landing page design and instrumentation, and A/B testing are a must. · Strong sense of product as you’ll be a key influencer in how the product shapes over time. · Creative thinker, story-teller and problem solver with attention to detail. · Bachelor’s degree, preferably a master’s degree is required. · Hands on experience in the eCommerce domain, especially on the seller side. · Understand how to build and nurture a community of users, advocates and influencers and use this to amplify the word of mouth for the product. · Exceptional writing skills, ability to edit and proofread content for clarity, grammar and accuracy. · Hands on experience or comfortable with any or all of the following: SQL, HTML, CSS, JavaScript or programming in any language is a strong plus. · Any experience with data sciences and analytics, R or python. · Knowledge of or experience with UI/UX, graphics design or product management. · US education is a strong plus. All customers are in the US to having spent time there and cultural familiarity is helpful. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.cibirix.com/jobs/Careers/617903000001745012/Video Editor?source=CareerSite", "company_id": 3317, "source": 3, "skills": "", "title": "Video Editor", "location": "Remote Job   | Posted on 10/22/2024", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://careers.cibirix.com/jobs/Careers/617903000001745012/Video Editor?source=CareerSite", "description": "Job listing Job details Job Information Date Opened 10/22/2024 Job Type Full time Industry Entertainment Remote Job Job Description This is a remote position. We are looking for a motivated and energetic Video editor & graphic designer to help us build our and our clients’ brands. Responsibilities: Manipulate and edit film pieces in a seamless manner for a seamless viewing experience. Understand the needs and specifications of the production team based on the provided brief. Review shooting script and raw footage to create a shot decision list considering scene value and continuity. Trim footage segments and arrange them to create the desired sequence. Incorporate music, dialogs, subtitles, motion graphics, and special effects. Generate rough and final cuts of the video. Enhance and correct lighting, coloring, and any faulty footage. Enhance audio quality. Ensure logical sequencing and smooth transitions. Collaborate with the content team throughout the production to post-production process. Continuously explore and implement new editing technologies and industry best practices to enhance efficiency. Requirements Requirements and skills: Proven work experience as a Video Editor. Strong proficiency with digital technology and editing software packages such as Lightworks, Premiere, After Effects, and Final Cut. Demonstrable video editing skills with a solid portfolio showcasing previous work. Thorough understanding of timing, motivation, and continuity in editing. Familiarity with special effects, 3D, 2D, and compositing techniques. Creative mindset and storytelling abilities. Ability to work under tight deadlines. Ability to translate ideas into complete projects. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.cibirix.com/jobs/Careers/617903000001805001/UI-UX-Designer?source=CareerSite", "company_id": 3317, "source": 3, "skills": "", "title": "UI/UX Designer", "location": "Indore, India   | Posted on 10/17/2024", "location_type": null, "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://careers.cibirix.com/jobs/Careers/617903000001805001/UI-UX-Designer?source=CareerSite", "description": "Job listing Job details Job Information Date Opened 10/17/2024 Job Type Full time Work Experience 5+ years Industry IT Services City Indore State/Province Madhya Pradesh Country India Zip/Postal Code 452010 Job Description Job Description:- Overseeing all digital projects and being responsible for the overall quality of the design deliverable. Directing the creative team in the production of digital assets and other deliverables online and in print. Communicating and creating an overall creative team vision by defining the direction and goals for the group. Setting and maintaining creative standards for the departmentWriting project creative briefs especially for web and mobile layouts (the train of thoughts)Adhering to compliance procedures and internal/ operational risk controls in accordance with all applicable regulatory standards, requirements, and policy. RequirementsKEY SKILLS:-· Adobe Photoshop, Adobe Illustrator, Adobe In Design, yperite Fonts, Layout Design, UI and UX Design, Graphic Design. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.cibirix.com/jobs/Careers/617903000001807008/Content-Writer?source=CareerSite", "company_id": 3317, "source": 3, "skills": "", "title": "Content Writer", "location": "Indore, India   | Posted on 10/18/2024", "location_type": null, "job_type": "full_time", "min_experience": 4, "max_experience": 4, "apply_link": "https://careers.cibirix.com/jobs/Careers/617903000001807008/Content-Writer?source=CareerSite", "description": "Job listing Job details Job Information Date Opened 10/18/2024 Job Type Full time Industry IT Services City Indore State/Province Madhya Pradesh Country India Zip/Postal Code 452010 Job Description Responsibilities: - Writing, editing, and proofreading content. Managing a content team consisting of writers, graphic designers, video graphers, etc. Formulating a cross-platform content strategy. Brainstorm with team members to develop new ideas. Building social presence followed by Social Media channels. Provide editorial, creative, and technical support. Track web analytics to ascertain content engagement levels and make new strategies to bring results. Manage content across all platforms. RequirementsRequirements: -Master's degree in communications, journalism, English (British English)4 years of agency experience or in a similar role. Adept at keyword placement and SEO best practices. Excellent written and verbal communication skills. Proficiency with popular content management systems. Experience with social media management. Creativity and the ability to develop original content. Ability to develop content that provokes engagement. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.cibirix.com/jobs/Careers/617903000001807023/Three-js-WebGI-Developer?source=CareerSite", "company_id": 3317, "source": 3, "skills": "", "title": "Three.js WebGI Developer", "location": "Indore, India   | Posted on 10/18/2024", "location_type": null, "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://careers.cibirix.com/jobs/Careers/617903000001807023/Three-js-WebGI-Developer?source=CareerSite", "description": "Job listing Job details Job Information Date Opened 10/18/2024 Job Type Full time Industry IT Services City Indore State/Province Madhya Pradesh Country India Zip/Postal Code 452010 Job Description Job Description :-• Build efficient, testable, and reusable WebGL/3D models with Three. Js and JavaScript. • Adapting graphics technologies and architectures traditionally used in games / vfx to data visualization. • Compatible with Augmented Reality + VR • Proficiency with JavaScript and HTML5 • Experience with Three. js, WebGL, and Canvas/CSS animation • Knowledge about browser-based 3D rendering • Expertise in 3D graphics • Working understanding of the level of detail when visualizing large data sets • Understanding fundamental design principles behind a scalable application • Ability to learn new technologies quickly • Proficient understanding of code versioning tools, such as Git I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.cibirix.com/jobs/Careers/617903000002665093/Social-Media?source=CareerSite", "company_id": 3317, "source": 3, "skills": "", "title": "Social Media", "location": "Indore, India   | Posted on 06/26/2025", "location_type": null, "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://careers.cibirix.com/jobs/Careers/617903000002665093/Social-Media?source=CareerSite", "description": "Job listing Job details Job Information Date Opened 06/26/2025 Job Type Full time Industry IT Services City Indore State/Province Madhya Pradesh Country India Zip/Postal Code 452007 Job Description · Creating written pieces that are sharp, memorable, and effective at prompting readers to act. · Collaborating with Videographers and Multimedia Designers to ensure that posts are engaging. · Optimizing content for publication across various social media sites. · Scheduling social media posts for release at optimal times. · Monitoring users' engagement with and feedback on every post. · Analysing and reporting on the effects of publications. · Maintaining a secure database of all login credentials. · Remaining abreast of changes to all pertinent social media applications. · Exploring the potential value of social media sites that are not yet in our repertoire. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.cibirix.com/jobs/Careers/617903000003038187/Gatsby-Developer?source=CareerSite", "company_id": 3317, "source": 3, "skills": "", "title": "Gatsby Developer", "location": "Indore, India   | Posted on 06/25/2025", "location_type": null, "job_type": "full_time", "min_experience": 4, "max_experience": 5, "apply_link": "https://careers.cibirix.com/jobs/Careers/617903000003038187/Gatsby-Developer?source=CareerSite", "description": "Job listing Job details Job Information Date Opened 06/25/2025 Job Type Full time Work Experience 4-5 years Industry IT Services City Indore State/Province Madhya Pradesh Country India Zip/Postal Code 452002 Job Description Develop and maintain reusable and maintainable React componentsDevelop and maintain Gatsby. js applications, including leveraging Gatsby CLI and pluginsDevelop and maintain Next. js applications, including utilizing Next. js API routes and data fetching techniquesDesign and implement Static Site Generators (SSGs) for improved performance and SEODevelop and maintain content management systems (CMS) integrations for dynamic content managementDesign and implement responsive UIs using CSS frameworks like Tailwind CSS or BootstrapImplement SEO best practices to optimize website content and ranking in search engine resultsWrite clean and maintainable JavaScript codeIntegrate third-party libraries and APIsStay up-to-date with the latest industry trends and technologiesImplement Git version control and maintain/manage CI/CD processDocument the code and processes for future reference and training purposesProject progress reports and updates to stakeholders I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.cibirix.com/jobs/Careers/617903000003057156/HTML-Developer?source=CareerSite", "company_id": 3317, "source": 3, "skills": "", "title": "HTML Developer", "location": "Indore, India   | Posted on 06/25/2025", "location_type": null, "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://careers.cibirix.com/jobs/Careers/617903000003057156/HTML-Developer?source=CareerSite", "description": "Job listing Job details Job Information Date Opened 06/25/2025 Job Type Full time Industry IT Services City Indore State/Province Madhya Pradesh Country India Zip/Postal Code 452002 Job Description Writing efficient and well-structured HTML, CSS, and JavaScript code for websites and web applications. Collaborating with designers to translate visual concepts into functional website features. Developing and testing user interfaces, ensuring optimal performance across all supported browsers and devices. Implementing responsive design to make websites accessible from various devices. Debugging websites and optimizing performance. Coding the entire HTML site from end to end. Working with back-end developers to integrate server-side code with client-side websites and web applications. Staying updated on emerging technologies and applying new knowledge to future projects. Maintaining and improving websites and web applications. Documenting work and code for future reference and debugging. Debugging code and front-end web applications. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.cibirix.com/jobs/Careers/617903000003482597/QA-Engineer?source=CareerSite", "company_id": 3317, "source": 3, "skills": "", "title": "QA Engineer", "location": "Indore, India   | Posted on 06/26/2025", "location_type": null, "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://careers.cibirix.com/jobs/Careers/617903000003482597/QA-Engineer?source=CareerSite", "description": "Job listing Job details Job Information Date Opened 06/26/2025 Job Type Full time Work Experience 5+ years Industry IT Services City Indore State/Province Madhya Pradesh Country India Zip/Postal Code 452010 Job Description ROLE / SKILLS:-Responsible for applying the principles and practices of software quality assurance throughout the Software development cycle. Analyzing the project risk & identification of the project requirements. Undertake internal quality audits (Coordinate with Graphic designer, UI Developer, and Developer). Perform Testing methods on the Web (Chrome, Firefox, IE, and Safari ). Perform Testing methods on iOS application and Android applications. Responsible for performing Automation testing by using the Selenium tool. Run tests to identify problems or deficiencies in products/projects. Perform Internal Quality Audits. Track all software-related metrics in terms of schedule, effort, defects, etc. I'm interested", "ctc": null, "currency": null, "meta": {}}]