[{"jd_link": "https://www.augustahitech.com/careers/6000150759-SAP%20GTS%20Consultant", "company_id": 3319, "source": 3, "skills": "", "title": "SAP GTS Consultant", "location": "", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": 5, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000150759-001AVM-%20SAP%20GTS%20Consultant\"", "description": "Refer to a Friend? Apply Now! SAP GTS ConsultantIndustry: ITQualification: Any DegreeRequired Skills: SAP GTS, SAP SD , SAP MM, SAP ECCWorking Shift: 2 PM IST to 11 PM ISTCity: CoimbatoreCountry: IndiaName of the position: SAP GTS ConsultantLocation: RemoteNo. of resources needed : 01Mode: FulltimeYears of experience: 5+ Years Shift : UK shift Overview:We are looking for an experienced SAP GTS professional with a strong background in system integration, implementation, and support. The ideal candidate will have a deep understanding of SAP ECC–GTS integration and sound process knowledge in SAP SD and MM modules. Responsibilities:Participate in multiple SAP GTS implementations and provide ongoing support. Manage and troubleshoot integrations between SAP ECC and SAP GTS systems. Work closely with SAP SD and MM functional teams to align processes and resolve issues. Handle SAP GTS Customs functionalities, especially communication messages and PDF document configurations. Qualifications:Minimum 5 years of hands-on experience in SAP GTS. Proven experience in SAP GTS implementations and support projects. Strong knowledge of SAP ECC integration with GTS. Good process understanding of SAP SD (Sales and Distribution) and MM (Materials Management). Experience with SAP GTS Customs, including communication messages and PDF document handling. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000162938-Salesforce%20Developer", "company_id": 3319, "source": 3, "skills": "", "title": "Salesforce Developer", "location": "", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000162938-001AZM%20-%20Salesforce%20Developer\"", "description": "Refer to a Friend? Apply Now! Salesforce DeveloperIndustry: ITQualification: Any DegreeRequired Skills: SalesforceWorking Shift: 2 PM to 11 PM ISTCity: CoimbatoreCountry: IndiaName of the position: Salesforce DeveloperLocation: RemoteNo. of resources needed for this position: 01Mode: FulltimeYears of experience: 5+ YearsOVERVIEW :We are seeking a skilled Salesforce Developer to design, develop, and implement customized solutions within the Salesforce platform. This role involves analyzing Unlimited org code components, configuring workflows, troubleshooting issues, and ensuring optimal system performance. The ideal candidate should have strong Salesforce CRM expertise, application development skills, and problem-solving abilities to build scalable and dynamic solutions that meet evolving business needs. RESPONSIBILITIES :Develop and implement customized solutions within the Salesforce platform. Analyze and optimize Unlimited org code components and configurations. Design and configure customer workflows to enhance business processes. Troubleshoot and resolve system errors for seamless functionality. Collaborate with stakeholders to gather and translate business requirements into technical solutions. Ensure best practices in Salesforce application development for scalability and maintainability. Continuously enhance and optimize Salesforce applications to adapt to changing business needs. QUALIFICATIONS :Proven experience as a Salesforce Developer with expertise in Salesforce CRM platforms. Strong application development skills, including Apex, Visualforce, and Lightning Web Components (LWC). Experience in Salesforce configuration, customization, and integrations. Ability to analyze and optimize code components for performance and efficiency. Strong problem-solving skills to address complex software challenges. Experience in creating dynamic and scalable Salesforce applications. Excellent communication and collaboration skills to work with cross-functional teams. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000163025-Database%20Administrator", "company_id": 3319, "source": 3, "skills": "", "title": "Database Adminstrator", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000163025-001AHI%20-%20Database%20Administrator\"", "description": "Refer to a Friend? Apply Now! Database AdminstratorIndustry: ITQualification: Any DegreeRequired Skills: SQL Server, Oracle, MySQL, PostgreSQL, or MongoDBWorking Shift: Rotational ShiftCity: CoimbatoreCountry: India Name of the position: Database Administrator Location: Coimbatore No. of resources needed : 02Mode: Fulltime Years of experience: 5+ Years Shift : Rotational shift OVERVIEW :We are seeking a highly skilled and dedicated DBA to join our IT team. The DBA will be responsible for the design, implementation, maintenance, and optimization of the database infrastructure, ensuring high availability, performance, and security of database systems. RESPONSIBILITIES :• Design, implement, and maintain database infrastructure, including servers, storage, and networking. • Plan and execute database server upgrades and patches. • Manage database storage, including capacity planning and allocation. • Design, implement, and maintain database infrastructure, including servers, storage, and networking. • Plan and execute database server upgrades and patches. • Manage database storage, including capacity planning and allocation. • Implement and maintain database security policies and procedures. • Monitor database security logs and alerts. • Develop and maintain database backup and recovery procedures. • Ensure regular backups are performed and stored securely. QUALIFICATIONS:• Bachelor’s degree in computer science, Information Technology, or a related field. • Strong understanding of database technologies (e. g. , Oracle, SQL Server, MySQL, PostgreSQL). • Experience with database server operating systems (e. g. , Linux, Windows). • Knowledge of database storage technologies (e. g. , SAN, NAS). • Experience with database performance tuning and optimization. • Strong understanding of database security principles and practices. • Experience with database backup and recovery procedures. • Excellent problem-solving and communication skills. Work Environment:• Office-based with occasional on-site visits and remote work flexibility. • May require occasional evening or weekend work for system upgrades or troubleshooting. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000163044-Senior%20EDI%20Developer", "company_id": 3319, "source": 3, "skills": "", "title": "EDI Developer", "location": "", "location_type": null, "job_type": null, "min_experience": 10, "max_experience": 10, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000163044-005ADT%20-%20Senior%20EDI%20Developer\"", "description": "Refer to a Friend? Apply Now! EDI DeveloperIndustry: ITQualification: Bachelor's in computer scienceRequired Skills: DellBoomi, EDI standards, including X12 and EDIFACTWorking Shift: PSTCity: ArgentinaCountry: ArgentinaThis position is open to candidates based in London, Argentina or Brazil. Job Description:Seeking an experienced Senior EDI Developer with over 10 years of expertise in EDI tools, particularly DellBoomi (certified), to join our team. The ideal candidate will have extensive experience in the Logistics and Transportation industry and be well-versed in various EDI standards, including X12 and EDIFACT. Knowledge of VL Trader is a significant plus. Key Responsibilities:Lead the design, development, and implementation of EDI solutions using DellBoomi. Manage and optimize EDI workflows and integrations within the logistics and transportation sector. Collaborate with cross-functional teams to ensure seamless EDI transactions across multiple systems and platforms. Work with various EDI standards, including X12 and EDIFACT, to support business processes and data exchange. Provide technical leadership and mentorship to junior EDI developers. Troubleshoot and resolve EDI-related issues promptly to ensure smooth business operations - (Very important)Stay updated on industry trends and emerging technologies to continuously improve EDI solutions. Utilize VL Trader (if applicable) to enhance EDI capabilities and performance. Qualifications:10+ years of experience in EDI development and integration, with a strong focus on DellBoomi. Proven experience in the logistics and transportation industry. In-depth knowledge of EDI standards, including X12 and EDIFACT. Familiarity with VL Trader is highly desirable. Strong problem-solving skills and attention to detail. Excellent communication and collaboration skills. Ability to work independently and lead projects from start to finish. Preferred Skills:Experience with cloud-based EDI solutions. Knowledge of supply chain management systems and processes. Familiarity with other EDI tools and platforms. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000163088-Azure%20Engineer", "company_id": 3319, "source": 3, "skills": "", "title": "Azure Engineer", "location": "", "location_type": "hybrid", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000163088-005AH%20-%20Azure%20Engineer\"", "description": "Refer to a Friend? Apply Now! Azure EngineerIndustry: ITQualification: Bachelor's in computer scienceRequired Skills: Microsoft Azure, IaaS , PaaSWorking Shift: UK time zoneCity: LondonCountry: United KingdomTitle: Azure EngineerDuties: Manage and monitor the whole Infrastructure estate. Manage and administer the Microsoft Azure cloud environment, including provisioning, configuration, performance monitoring, policy governance and securityDesign, develop, and implement highly available, multi-region solutions within Microsoft AzureAnalyse existing operational standards, processes, and/or governance to identify and implement improvementsMigrate existing infrastructure services to cloud-based solutionsManage security and access controls of cloud-based solutionsDevelop and implement policy-driven data protection best practices to ensure cloud solutions are protected from data lossSupport cloud adoption of applications as they are being transformed and/or modernisedEnsure all infrastructure components meet proper performance and capacity standardsParticipate in an out-of-work to monitor infrastructure issues and resolve technical escalations. Skills:5+ years of Microsoft Azure experience involving design, deployment, configuration and optimization3+ years of experience with IaaS and PaaS solutionsExperience in the design and operation of medium to large scale enterprise infrastructure, databases and application systemsExperience working within an Agile frameworkExperience scripting with Perl, Python and PowerShellExperience with Cloud and AzureCompetence in a wide range of IT skills including networking, systems administration, data protection, information security and CI/CD toolingEnthusiastic learner with the ability to teach and mentor teammates and cross functional partners Experience: 4 + years as Azure EngineerQualification(s): Any graduate / Bachelor’s degree in Computer ScienceSalary: GBP 50,000. 00 - 60,000. 00 Gross Per AnnumLocation: London (Hybrid)Job Start date: 02-04-2025Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000163178-EDI%20Business%20Analyst", "company_id": 3319, "source": 3, "skills": "", "title": "EDI Business Analyst", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 7, "max_experience": 7, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000163178-005ACE%20-%20EDI%20Business%20Analyst\"", "description": "Refer to a Friend? Apply Now! EDI Business AnalystIndustry: ITQualification: Bachelor's in computer scienceRequired Skills: EDICity: Marseille, FranceCountry: FranceThis position is open to candidates based in Marseille, France· Location: Marseille preferably. (Remote 2d/5) · Language: Fluent English mandatory · Experience: 5 to 7 years in a similar role. · Start Date: Asap Consultant Role and Responsibilities: · Knowledge of logistics industry specifically on air & ocean freight and customs domain · Skills in mapping data between different systems and ensuring seamless integration · Ability to troubleshoot and resolve issues related to data exchange and system integration · Ensuring accuracy in data transmission and compliance with industry standards · Experience in defining and managing EDI test cases, system testing, and supporting UAT · Experience with EDI software tools such as IBM Sterling Integrator, TIBCO, WSO2 etc. · Identify and document business requirements for downstream digital solutions · Participate in solution ideation, design sprints and user interviews to gather and synthesize information. · Benchmark industry best-practices and exiting solutions in the market. · Assist the product owner in detailing features and user stories of the digital solutions. ensuring that user stories are well-prepared for design and development. · Translate end-user requirements into features and software functionalities and document them in industry standard formats. · Define clear acceptance criteria and ensure user stories are ready for design and development. The EDI Business Analyst will report to the EDI Product Owner and will collaborate with various stakeholders, including IT and Business teams. Required Skills and Expertise: · Understanding the technical aspects of EDI, including standards like ANSI X12, EDIFACT, and XML-based EDI · Excellent communication is fundamental. This includes articulating ideas clearly, active listening, and ensuring shared understanding among team members and stakeholders · A focus on continuous improvement for both the team and personal development. Encouraging regular reflection on work practices and fostering a culture of growth · Managing EDI projects, including planning, execution, and monitoring progress Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000164630-Fullstack%20Developer%20(Remote)", "company_id": 3319, "source": 3, "skills": "", "title": "Fullstack Developer", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000164630-005APM%20-%20Fullstack%20Developer%20(Remote)\"", "description": "Refer to a Friend? Apply Now! Fullstack DeveloperIndustry: ITQualification: Bachelor's in computer scienceRequired Skills: . NET Framework, Full-Stack Development, AngularWorking Shift: EST/CSTCity: FloridaCountry: United StatesJob Description: We are looking for a full-stack developer with migration experience in Angular. Responsible for: Work Independently. Experience in rewriting/Migration from Angular JS version to a newest version (16 or 18) Developing/Maintaining frontend and backend features. Seeing a project through from conception to finished product. Designing and developing APIs. Meeting both technical and consumer needs. Maintaining and architecting microservices Working with git for version control Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000164718-Dell%20Boomi%20Developer", "company_id": 3319, "source": 3, "skills": "", "title": "<PERSON>i <PERSON>elo<PERSON>", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000164718-005APM%20-%20Dell%20Boomi%20Developer\"", "description": "Refer to a Friend? Apply Now! Dell Boomi DeveloperIndustry: ITQualification: Bachelor's in computer scienceRequired Skills: <PERSON><PERSON><PERSON>, Oracle SOAWorking Shift: ESTCity: FloridaCountry: United StatesJOB DESCRIPTION:The ideal candidate for this position must possess a highly specialized skill set centered around migration from Oracle SOA products to the Boomi integration platform. The company, Augusta Hitech, is seeking consultants or technical experts with proven experience in handling complex integration projects, particularly those involving the transformation of services from Oracle Fusion Middleware (including Oracle SOA Suite and Oracle Service Bus) into Boomi on the MCS Cloud platform. The candidate will be responsible for managing and executing large-scale migration projects involving approximately 250 complex integration services and APIs transitioning from Oracle SOA to Boomi. This role demands immediate availability and the ability to operate efficiently under strict deadlines, as the client has already completed around 40% of the migration and is targeting rapid completion. Primary responsibilities include: · Conducting technical assessments and hands-on migration of complex custom services and integrations. · Utilizing expertise in XML technologies, specifically XSLT (Extensible Stylesheet Language Transformations) and XSD (XML Schema Definition), to transform and convert integration logic. · Ensuring migrant services are fully functional and capable of supporting high throughput environments (e. g. , handling 12 million transactions daily). · Collaborating closely with the client’s internal teams while maintaining a consultative approach focused on delivering tangible results rather than generic consulting discussions. · Maintaining transparent communication around capabilities and project progress without generating unnecessary meetings or promises without substance. Must-Have Qualifications: · Extensive, demonstrable experience in Oracle Fusion Middleware platforms, particularly with Oracle SOA Suite and Oracle Service Bus. · Proven track record of migrating complex integration services and APIs from Oracle SOA to Boomi AtomSphere. · Strong technical expertise in XSLT and XSD for transforming and adapting integration payloads and services. · Thorough understanding of Boomi’s cloud-based integration platform (MCS Cloud environment) and best practices for implementation. · Ability to work with complex service architectures and legacy middleware components. · Experience in the transportation and logistics industry or similar high-transaction environments is highly desirable. · Capacity to deliver within aggressive timelines and strict project demand cycles. · Strong communication skills to provide candid, focused updates; avoid excessive, non-value-adding meetings. · Self-driven and honest about capabilities to ensure alignment with client expectations and prevent resource misalignment, emphasizing quality over quantity. · Candidate must possess good hands on working in both Oracle SOA & related Integration platforms and Boomi for at least a couple of years in each. The role demands committed professionals with deep, hands-on technical skills and strategic understanding of migration from Oracle’s middleware suite to Boomi’s modern integration platform. Candidates must demonstrate real use cases and outcomes of similar migration projects to be considered. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165085-QA%20Manual%20Tester", "company_id": 3319, "source": 3, "skills": "", "title": "QA Manual Tester", "location": "", "location_type": null, "job_type": null, "min_experience": 4, "max_experience": 4, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165085-001ASR%20-%20QA%20Manual%20Tester\"", "description": "Refer to a Friend? Apply Now! QA Manual TesterIndustry: ITQualification: Any DegreeRequired Skills: Manual TestingWorking Shift: 2 PM IST to 11 PM ISTCity: CoimbatoreCountry: IndiaName of the position: QA Manual TesterLocation: CoimbatoreNo. of resources needed : 01 Mode: FulltimeYears of experience: 4+ Years Overview :We are looking for a detail-oriented QA Manual Tester with over 4 years of hands-on experience in functional, regression, and integration testing. The ideal candidate should be skilled in creating and executing test cases, identifying bugs, and collaborating with cross-functional teams to ensure product quality and timely delivery. Responsibilities :Understand business requirements and translate them into detailed test scenarios and test casesPerform manual testing of web, mobile, or desktop applications (based on the product)Execute test cases and document test results, bugs, and issues clearlyConduct regression, functional, integration, and smoke testingWork closely with developers, BAs, and product teams to clarify requirements and resolve issuesTrack defects using tools like JIRA, Bugzilla, or similarEnsure test coverage and consistency across releasesParticipate in test planning, effort estimation, and status reportingRequired Skills :4+ years of experience in manual testing in an Agile/Scrum environmentStrong understanding of SDLC and STLCHands-on experience with test case management tools like TestRail, Zephyr, or ExcelExperience with bug tracking tools (JIRA, Mantis, etc. )Good analytical and problem-solving skillsApply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000164925-Assistant%20SDM", "company_id": 3319, "source": 3, "skills": "", "title": "Assistant SDM", "location": "", "location_type": "remote", "job_type": null, "min_experience": 6, "max_experience": 8, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000164925-001AHI%20-%20Assistant%20SDM\"", "description": "Refer to a Friend? Apply Now! Assistant SDMIndustry: ITQualification: Any DegreeRequired Skills: BMC Connect ITWorking Shift: 2 PM IST to 11 PM ISTCity: TamilnaduCountry: CoimbatoreName of the position: Assistant SDMLocation: Remote No. of resources needed : 01 Mode: FulltimeYears of experience: 6+ Years Shift : UK shiftOVERVIEW : We're looking for a seasoned ITSM Integration Specialist to join our team. If you have a knack for connecting IT Service Management solutions and a strong background in tools like BMC Connect IT (or similar), we want to hear from you! You'll be at the heart of integrating our ITSM platforms with other key enterprise systems, automating critical workflows, and ensuring smooth data flow across our digital landscape. RESPONSIBILITIES : Design, build, and manage integrations using BMC Connect IT, ServiceNow, Freshservice, and other ITSM tools. Partner with IT and business stakeholders to define integration needs and deliver robust solutions. Develop, test, and implement automated workflows and data transformations. Create and maintain clear documentation for technical processes, integrations, and operational procedures. Ensure all integrations adhere to enterprise security and data governance policies. Support ITSM platform upgrades, patching, and enhancements. Mentor and provide technical leadership to junior team members. QUALIFICATIONS: 6-8 years of hands-on experience in an ITSM environment. Proven expertise with BMC Connect IT (Atrium Integrator) or similar integration tools. Deep understanding of the ITIL framework and core ITSM processes (Incident, Change, Problem, CMDB, etc. ). Proficiency in scripting languages (e. g. , JavaScript, Shell). Solid experience with REST/SOAP APIs and data transformation techniques. Familiarity with ITSM platforms like ServiceNow or BMC Remedy. Excellent analytical, communication, and documentation abilities. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165089-Fullstack%20Engineer", "company_id": 3319, "source": 3, "skills": "", "title": "Fullstack Engineer", "location": "", "location_type": null, "job_type": null, "min_experience": 1, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165089-001AHI%20-%20Fullstack%20Engineer\"", "description": "Refer to a Friend? Apply Now! Fullstack EngineerIndustry: ITQualification: Any DegreeRequired Skills: Node JS, React JS, HTML, CSS, JavascriptWorking Shift: 10 AM IST to 7 PM ISTCity: CoimbatoreCountry: IndiaName of the position: Fullstack EngineerLocation: CoimbatoreNo. of resources needed : 01 Mode: FulltimeYears of experience: 4+ Years Shift : UK shiftJob Summary:We are seeking an experienced Full Stack Engineer to join our team. The ideal candidate will have a strong background in Node. js and React. js, with a proven track record of delivering high-quality software solutions. Key Responsibilities:Design, develop, and scalable, efficient, and secure full-stack applications using Node. js and React. js. Collaborate with cross-functional teams to identify and prioritize project requirements, and deliver solutions that meet business needs. Develop and maintain high-quality code, adhering to industry standards and best practices. Participate in code reviews, ensuring that code is maintainable, efficient, and meets technical standards. Troubleshoot and resolve technical issues, and provide technical guidance to junior team members. Stay up-to-date with industry trends, emerging technologies, and best practices, and apply this knowledge to improve our software development processes. Requirements:1+ years of experience in Node. js and React. js development. Strong understanding of JavaScript, HTML, and CSS. Experience with database management systems, such as MongoDB or MySQL. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165112-RPA%20Engineer", "company_id": 3319, "source": 3, "skills": "", "title": "RPA Engineer", "location": "", "location_type": null, "job_type": null, "min_experience": 1, "max_experience": 1, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165112-001AHI%20-%20RPA%20Engineer\"", "description": "Refer to a Friend? Apply Now! RPA EngineerIndustry: ITQualification: Any DegreeRequired Skills: Ui PathWorking Shift: 2 PM IST to 11 PM ISTCity: CoimbatoreCountry: IndiaName of the position: RPA EngineerLocation: CoimbatoreNo. of resources needed : 01 Mode: FulltimeYears of experience: 1+ Years Shift : UK shiftJob Summary:We are seeking a motivated and detail-oriented RPA Engineer with at least 1 year of hands-on experience in UiPath. The ideal candidate will be responsible for developing, testing, and deploying RPA solutions to automate repetitive business processes across departments. Key Responsibilities:Design, develop, and deploy RPA solutions using UiPathCollaborate with business analysts and process owners to understand automation requirementsCreate and maintain process documentation, including PDD and SDDPerform unit testing and debugging of botsMonitor and maintain bots in production and resolve issues as they ariseFollow best practices in coding standards, error handling, and securityParticipate in code reviews and contribute to reusable bot componentsKeep up to date with new features and updates in UiPath platformRequirements:1+ years of hands-on experience in UiPath RPA developmentSolid understanding of RPA lifecycle, from analysis to deploymentExperience in developing bots with UiPath Studio and OrchestratorKnowledge of selectors, queues, RE Framework, and exception handlingApply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165153-Senior%20Fullstack%20Developer", "company_id": 3319, "source": 3, "skills": "", "title": "Fullstack Developer", "location": "", "location_type": "remote", "job_type": "part_time", "min_experience": 4, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165153-005APM%20-%20Senior%20Fullstack%20Developer\"", "description": "Refer to a Friend? Apply Now! Fullstack DeveloperIndustry: ITQualification: Bachelor's in computer scienceRequired Skills: . NET Framework, Full-Stack Development, Angular 16 or 18Working Shift: CSTCity: FloridaCountry: United StatesName of the position: Sr. Fullstack Developer Time Zone: CST - US Remote: Yes Duration of the Contract: 12 months with possible extension Years of experience: 8+ Yrs. of experience as a software developer and 4+ Yrs. of experience as an Angular Main Skills: Full Stack and Angular (JS and new versions 16 and 18) and . NET Required:-Angular 16+ C# . NET Framework 4. x C# . NET 6+ Entity Framework Core/6 Worked with REST APIs MS SQL Required additional experience: Azure Cloud Services Experience upgrading from AngularJS to Angular 16+ Angular package upgrades Nuget package upgrades Experience with Telerik libraries Responsible for: Work Independently. Experience in rewriting/Migration from AngularJS version to the newest version (16 or 18) Developing/Maintaining frontend and backend features. Seeing a project through from conception to finished product. Designing and developing APIs. Meeting both technical and consumer needs. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/**********-QA%20Salesforce%20Engineer%20(Remote)", "company_id": 3319, "source": 3, "skills": "", "title": "Fullstack Developer", "location": "", "location_type": "remote", "job_type": "part_time", "min_experience": 5, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/**********-005APM%20-%20QA%20Salesforce%20Engineer%20(Remote)\"", "description": "Refer to a Friend? Apply Now! Fullstack DeveloperIndustry: ITQualification: Bachelor's in computer scienceRequired Skills: Salesforce, QA testing, HealthCloudWorking Shift: CSTCity: FloridaCountry: United States Name of the Position - QA Salesforce EngineerTime Zone: CST - US Remote: Yes Duration of the Contract: 12 months with possible extension Years of experience: 10+ Yrs. of experience Requirements: Executes test cases under varying circumstances Documents and evaluates test results Detects, logs, and reports program bugs and glitches Design, develop, and maintain custom Salesforce applications Tracks defects and helps troubleshoot errors Review test procedures and develop test scripts Partners with engineers to drive QA efforts Qualifications: 10+ Yrs. of QA testing experience 5+ Yrs. of Salesforce experience Experience in Agile frameworks and regression testing is desired High attention to detail and solid analytical skills Proficient in test management software Good working knowledge of programming languages Experience with HealthCloud and/or V12 is a huge plus Outstanding written and verbal communication skills Able to work independently Bachelor’s degree in Computer Science, Software Engineering, or related field Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/**********-Data%20Scientist%20Intern", "company_id": 3319, "source": 3, "skills": "", "title": "Data Scientist Intern", "location": "", "location_type": null, "job_type": "internship", "min_experience": null, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/**********-001AHI%20-%20Data%20Scientist%20Intern\"", "description": "Refer to a Friend? Apply Now! Data Scientist InternIndustry: ITQualification: Any DegreeRequired Skills: Python, R, SQLWorking Shift: UK TimezoneCity: LondonCountry: United KingdomName of the position: Data Scientist InternLocation: UKNo. of resources needed : 01 Mode: InternshipJob Summary:Join our team as a Data Scientist Intern and contribute to the development of data-driven solutions that drive business growth and innovation. As a key member of our analytics team, you will work closely with cross-functional teams to design, implement, and analyze data-driven projects that inform business decisions. Key Responsibilities:Analyze complex data sets to identify trends, patterns, and insights that inform business decisionsDevelop and maintain databases, data visualizations, and statistical models to support data-driven decision makingCollaborate with cross-functional teams to design and implement data-driven projects that drive business growth and innovationCommunicate complex data insights and recommendations to both technical and non-technical stakeholdersStay up-to-date with industry trends and emerging technologies in data science and analyticsRequirements:Master's or Bachelor's degree in Computer Science, Statistics, Mathematics, or related fieldProficiency in programming languages such as Python, R, or SQLExperience with data visualization tools such as Tableau, Power BI, or D3. jsStrong understanding of statistical concepts and machine learning algorithmsApply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165260-UI%20Path%20Process%20Mining%20Consultant", "company_id": 3319, "source": 3, "skills": "", "title": "UI Path Process Mining Consultant", "location": "", "location_type": null, "job_type": "contract", "min_experience": 3, "max_experience": 20, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165260-003AHI%20-%20UI%20Path%20Process%20Mining%20Consultant\"", "description": "Refer to a Friend? Apply Now! UI Path Process Mining ConsultantIndustry: ITQualification: Any DegreeRequired Skills: UI Path, Celonis, SignavioWorking Shift: 7PM to 4AM ISTCity: CoimbatoreCountry: IndiaName of the position: UI Path Process Mining ConsultantLocation: RemoteNo. of resources needed : 01Mode: FreelanceYears of experience: 15+ Years Shift : US shift Job Summary:We are looking for a seasoned UI Path Process Mining Consultant with 15–20 years of experience in the manufacturing industry. The ideal candidate will have a deep understanding of manufacturing operations, extensive experience in process optimization, and hands-on expertise with UI Path Process Mining. Familiarity with QAD ERP systems and strong integration knowledge is essential. This role will involve working with cross-functional stakeholders to uncover inefficiencies, enable process transparency, and drive data-led decision-making for continuous improvement. Key Responsibilities:Lead comprehensive Process Mining initiatives using UI Path across manufacturing business functions. Partner with stakeholders across operations, quality, supply chain, and IT to map and analyze critical processes. Integrate various enterprise systems, especially QAD ERP, MES, and PLM platforms, with the Process Mining tool. Interpret large volumes of data to identify bottlenecks, redundancies, and areas suitable for automation. Create visual dashboards, KPIs, and actionable reports to support executive decision-making. Collaborate with CoE teams to translate mined insights into automated process pipelines. Ensure compliance with audit and governance frameworks through detailed documentation and process transparency. Guide junior team members and act as a domain expert in manufacturing and process transformation. Required Skills & Qualifications:Bachelor’s/Master’s degree in Engineering, Computer Science, Operations, or a related discipline. 15–20 years of experience in the manufacturing sector, with a focus on process improvement, automation, or digital transformation. Minimum 3–5 years of hands-on experience with UI Path Process Mining or similar tools (e. g. , Celonis, Signavio). Proven experience with QAD ERP and understanding of its data structures and process flows. Deep knowledge of manufacturing business processes – including production, quality, logistics, and supply chain. Strong skills in SQL, data modeling, and business intelligence/reporting tools. Excellent communication skills with the ability to influence cross-functional stakeholders. Strategic mindset with a track record of driving value through data and automation. Preferred Qualifications:UI Path certifications in Process Mining, Automation, or RPA. Exposure to Lean, Six Sigma, or other process improvement frameworks. Experience leading digital transformation projects in a manufacturing environment. Familiarity with integration platforms and data lakes. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165261-JDE%20Consultant", "company_id": 3319, "source": 3, "skills": "", "title": "JDE Consultant", "location": "", "location_type": null, "job_type": "contract", "min_experience": 7, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165261-002AZM%20-%20JDE%20Consultant\"", "description": "Refer to a Friend? Apply Now! JDE ConsultantIndustry: ITQualification: Any DegreeRequired Skills: JDEWorking Shift: 2PM to 11PM ISTCity: CoimbatoreCountry: IndiaName of the position: JDE ConsultantLocation: RemoteNo. of resources needed : 01Mode: ContractYears of experience: 7+ Years Shift : UK shift Overview:We are seeking a seasoned JD Edwards (JDE) ERP Functional Consultant with 7+ years of hands-on experience in JD Edwards EnterpriseOne, ReportsNow, Showcase Query, and the ScrumMaster framework. This role is crucial for delivering robust, customized JDE solutions to meet critical business needs, ensuring seamless integration, system optimization, and functional efficiency across multiple modules. Responsibilities:Deliver JD Edwards ERP functional solutions by analyzing business needs, budgets, staffing, risks, and delivery strategies. Design and implement functional enhancements across Oracle JD Edwards EnterpriseOne ERP modules. Create and manage functional specifications, data models, and conduct Fit-Gap analyses. Utilize and support JD Edwards technical tools such as FDA, RDA, Event Rules, NER, BSFNs, BSSV, RTE, ReportsNow, and Showcase Query. Configure and optimize distribution, manufacturing, and planning modules. Integrate JD Edwards with external platforms such as PLM systems, e-commerce platforms, other ERPs, transportation and warehouse management systems, and EDI systems. Execute functional unit testing, system integration testing, and user acceptance testing. Support testing and agile project delivery using Power BI, ScrumMaster framework, and Agile methodology. Develop and manage user access controls, segregation of duties, user profiles, and security matrices. Qualifications:10+ years of relevant experience in JD Edwards EnterpriseOne. Proficient in ReportsNow, Showcase Query, and Oracle JDE ERP functional modules. Experience with JDE technical elements: FDA, RDA, Event Rules, NER, BSFNs, BSSV, RTE. Strong understanding of EDI and external system integrations. Knowledge of Agile and Scrum frameworks; ScrumMaster certification is a plus. Experience in Power BI for reporting and testing support. Demonstrated ability to perform Fit-Gap analysis and configure enterprise-wide functional solutions. Proven track record in developing secure user roles, responsibilities, and access matrices. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165276-Intern%20Data%20Scientist", "company_id": 3319, "source": 3, "skills": "", "title": "Intern Data Scientist", "location": "", "location_type": null, "job_type": "internship", "min_experience": null, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165276-001AHI%20-%20Intern%20Data%20Scientist\"", "description": "Refer to a Friend? Apply Now! Intern Data ScientistIndustry: ITQualification: Any DegreeRequired Skills: Python, R, SQLWorking Shift: 10am to 7pm ISTCity: CoimbatoreCountry: IndiaName of the position: Data Scientist InternLocation: CoimbatoreNo. of resources needed : 01 Mode: Internship to FTEJob Summary:Join our team as a Data Scientist Intern and contribute to the development of data-driven solutions that drive business growth and innovation. As a key member of our analytics team, you will work closely with cross-functional teams to design, implement, and analyze data-driven projects that inform business decisions. Key Responsibilities:Analyze complex data sets to identify trends, patterns, and insights that inform business decisionsDevelop and maintain databases, data visualizations, and statistical models to support data-driven decision makingCollaborate with cross-functional teams to design and implement data-driven projects that drive business growth and innovationCommunicate complex data insights and recommendations to both technical and non-technical stakeholdersStay up-to-date with industry trends and emerging technologies in data science and analyticsRequirements:Master's or Bachelor's degree in Computer Science, Statistics, Mathematics, or related fieldProficiency in programming languages such as Python, R, or SQLExperience with data visualization tools such as Tableau, Power BI, or D3. jsStrong understanding of statistical concepts and machine learning algorithmsApply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165277-Data%20Engineer", "company_id": 3319, "source": 3, "skills": "", "title": "Data Engineer", "location": "", "location_type": null, "job_type": "part_time", "min_experience": 3, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165277-002AVM%20-%20Data%20Engineer\"", "description": "Refer to a Friend? Apply Now! Data EngineerIndustry: ITQualification: Any DegreeRequired Skills: Python, Pandas, SQLWorking Shift: 2PM to 11PM ISTCity: CoimbatoreCountry: IndiaName of the position: Data EngineerLocation: RemoteNo. of resources needed : 01Mode: Contract (3 Months with Possible Extension)Years of experience: 4+ Years Shift : UK shift Job Summary:We are looking for a highly motivated and detail-oriented Data Engineer with a strong background in data cleansing, Python scripting, and SQL to join our team. The ideal candidate will play a critical role in ensuring data quality, transforming raw datasets into actionable insights, and supporting data-driven decision-making across the organization. Key Responsibilities:Design and implement efficient data cleansing routines to remove duplicates, correct anomalies, and validate data integrity. Write robust Python scripts to automate data processing, transformation, and integration tasks. Develop and optimize SQL queries for data extraction, aggregation, and reporting. Work closely with data analysts, business stakeholders, and engineering teams to understand data requirements and deliver clean, structured datasets. Build and maintain data pipelines that support large-scale data processing. Monitor data workflows and troubleshoot issues to ensure accuracy and reliability. Contribute to documentation of data sources, transformations, and cleansing logic. Requirements:Bachelor’s degree in Computer Science, Information Systems, Engineering, or a related field. 3+ years of hands-on experience in data engineering, with a focus on data quality and cleansing. Strong proficiency in Python, including libraries like Pandas and NumPy. Expert-level knowledge of SQL and working with relational databases (e. g. , PostgreSQL, MySQL, SQL Server). Familiarity with data profiling tools and techniques. Excellent problem-solving skills and attention to detail. Good communication and documentation skills. Preferred Qualifications:Experience with cloud platforms (AWS, Azure, GCP) and data services (e. g. , S3, BigQuery, Redshift). Knowledge of ETL tools like Apache Airflow, Talend, or similar. Exposure to data governance and data cataloging practices. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165307-Data%20Engineer", "company_id": 3319, "source": 3, "skills": "", "title": "Data Engineer", "location": "", "location_type": null, "job_type": "part_time", "min_experience": 3, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165307-002AJM%20-%20Data%20Engineer\"", "description": "Refer to a Friend? Apply Now! Data EngineerIndustry: ITQualification: Any DegreeRequired Skills: Python, Pandas, SQLWorking Shift: 2PM to 11PM ISTCity: CoimbatoreCountry: IndiaName of the position: Data EngineerLocation: RemoteNo. of resources needed : 01Mode: Contract (2 Months with Possible Extension)Years of experience: 3+ Years Shift : UK shift Job Summary:We are looking for a highly motivated and detail-oriented Data Engineer with a strong background in data cleansing, Python scripting, and SQL to join our team. The ideal candidate will play a critical role in ensuring data quality, transforming raw datasets into actionable insights, and supporting data-driven decision-making across the organization. Key Responsibilities:Design and implement efficient data cleansing routines to remove duplicates, correct anomalies, and validate data integrity. Write robust Python scripts to automate data processing, transformation, and integration tasks. Develop and optimize SQL queries for data extraction, aggregation, and reporting. Work closely with data analysts, business stakeholders, and engineering teams to understand data requirements and deliver clean, structured datasets. Build and maintain data pipelines that support large-scale data processing. Monitor data workflows and troubleshoot issues to ensure accuracy and reliability. Contribute to documentation of data sources, transformations, and cleansing logic. Requirements:Bachelor’s degree in Computer Science, Information Systems, Engineering, or a related field. 3+ years of hands-on experience in data engineering, with a focus on data quality and cleansing. Strong proficiency in Python, including libraries like Pandas and NumPy. Expert-level knowledge of SQL and working with relational databases (e. g. , PostgreSQL, MySQL, SQL Server). Familiarity with data profiling tools and techniques. Excellent problem-solving skills and attention to detail. Good communication and documentation skills. Preferred Qualifications:Experience with cloud platforms (AWS, Azure, GCP) and data services (e. g. , S3, BigQuery, Redshift). Knowledge of ETL tools like Apache Airflow, Talend, or similar. Exposure to data governance and data cataloging practices. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165370-Senior%20Data%20Engineer", "company_id": 3319, "source": 3, "skills": "", "title": "Senior Data Engineer", "location": "", "location_type": null, "job_type": "part_time", "min_experience": 8, "max_experience": 8, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165370-002ACH%20-%20Senior%20Data%20Engineer\"", "description": "Refer to a Friend? Apply Now! Senior Data EngineerIndustry: ITQualification: Any DegreeRequired Skills: AI Implementations, Python, AWS or GCPWorking Shift: 2PM to 11PM ISTCity: CoimbatoreCountry: IndiaName of the position: Senior Data EngineerLocation: RemoteNo. of resources needed : 02Mode: Contract (3 Months with Possible Extension)Years of experience: 8+ Years Shift : UK shift Job Summary:Seeking an experienced Senior Data Engineer to lead the design, development, and implementation of large-scale data lakes and integrations on AWS or GCP. The ideal candidate will have a strong background in AI and a minimum of 8 years of experience in data engineering. Key Responsibilities:Design and develop scalable data architectures for data lakes on AWS or GCPImplement data integration pipelines using various tools and technologiesCollaborate with cross-functional teams to identify business requirements and develop data solutionsDevelop and maintain high-quality, well-documented codeEnsure data security, governance, and complianceOptimize data processing and storage for improved performance and cost-effectivenessRequirements:8+ years of experience in data engineering with a focus on AI implementationsStrong expertise in designing and developing data lakes on AWS or GCPProficiency in programming languages such as Python, Java, or ScalaExperience with data integration tools such as Apache Beam, Apache NiFi, or AWS GlueKnowledge of data security, governance, and compliance best practices Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165371-Senior%20Fullstack%20Engineer", "company_id": 3319, "source": 3, "skills": "", "title": "Senior Fullstack Engineer", "location": "", "location_type": null, "job_type": "part_time", "min_experience": 8, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165371-002ACH%20-%20Senior%20Fullstack%20Engineer\"", "description": "Refer to a Friend? Apply Now! Senior Fullstack EngineerIndustry: ITQualification: Any DegreeRequired Skills: React JS, . NetWorking Shift: 2PM to 11PM ISTCity: CoimbatoreCountry: IndiaName of the position: Senior Full Stack Engineer Location: RemoteNo. of resources needed: 02Mode: Contract (3 Months with Possible Extension)Years of experience: 8+ Years Shift: UK shift Overview:We are seeking a highly skilled Full Stack Engineer to join our team. The ideal candidate will have a strong background in both front-end and back-end development, with expertise in ReactJS and . NET. Key Responsibilities:Design, develop, and deploy scalable, efficient, and secure front-end and back-end applications using ReactJS and . NET. Collaborate with cross-functional teams to identify and prioritize project requirements, and deliver high-quality solutions. Troubleshoot and resolve complex technical issues, and implement best practices to improve system performance and reliability. Stay up-to-date with industry trends and emerging technologies, and recommend innovative solutions to improve our technology stack. Requirements:8+ years of experience in software development, with a strong background in both front-end and back-end development. Expertise in ReactJS and . NET, with a strong understanding of their respective ecosystems. Proficiency in JavaScript, HTML, CSS, and SQL. Experience with agile development methodologies, and version control systems like Git. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165386-%20UI%20Path%20Process%20Mining%20Consultant%20(Remote)", "company_id": 3319, "source": 3, "skills": "", "title": "Fullstack Developer", "location": "", "location_type": null, "job_type": "contract", "min_experience": 3, "max_experience": 20, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165386-005AHC%20-%20%20UI%20Path%20Process%20Mining%20Consultant%20(Remote)\"", "description": "Refer to a Friend? Apply Now! Fullstack DeveloperIndustry: ITQualification: Bachelor's in computer scienceRequired Skills: UI Path, RPA, manufacturing, QAD ERP, SQL, Celonis, SignavioWorking Shift: UK time zoneCity: UKCountry: United KingdomThis position is available to applicants from both the United Kingdom and Latin America (LATAM) Job Summary: We are looking for a seasoned UI Path Process Mining Consultant with 15–20 years of experience in the manufacturing industry. The ideal candidate will have a deep understanding of manufacturing operations, extensive experience in process optimization, and hands-on expertise with UI Path Process Mining. Key Responsibilities: Lead comprehensive Process Mining initiatives using UI Path across manufacturing business functions. Partner with stakeholders across operations, quality, supply chain, and IT to map and analyze critical processes. Integrate various enterprise systems, especially QAD ERP, MES, and PLM platforms, with the Process Mining tool. Interpret large volumes of data to identify bottlenecks, redundancies, and areas suitable for automation. Create visual dashboards, KPIs, and actionable reports to support executive decision-making. Collaborate with CoE teams to translate mined insights into automated process pipelines. Ensure compliance with audit and governance frameworks through detailed documentation and process transparency. Guide junior team members and act as a domain expert in manufacturing and process transformation. Required Skills & Qualifications: Minimum 3–5 years of hands-on experience with UI Path Process Mining or similar tools (e. g. , Celonis, Signavio). Proven experience with QAD ERP and understanding of its data structures and process flows. Deep knowledge of manufacturing business processes – including production, quality, logistics, and supply chain. Strong skills in SQL, data modeling, and business intelligence/reporting tools. Excellent communication skills with the ability to influence cross-functional stakeholders. Preferred Qualifications: UI Path certifications in Process Mining, Automation, or RPA. Exposure to Lean, Six Sigma, or other process improvement frameworks. Experience leading digital transformation projects in a manufacturing environment. Familiarity with integration platforms and data lakes. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165482-Cypress%20QA%20Automation%20Engineer", "company_id": 3319, "source": 3, "skills": "", "title": "Cypress QA Automation Engineer", "location": "", "location_type": null, "job_type": null, "min_experience": 3, "max_experience": 3, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165482-005APM%20-%20Cypress%20QA%20Automation%20Engineer\"", "description": "Refer to a Friend? Apply Now! Cypress QA Automation EngineerIndustry: ITQualification: Bachelor's in computer scienceRequired Skills: Cypress, API, Azure DevOps Test Plan or JIRAWorking Shift: CSTCity: FloridaCountry: United StatesWe are looking for a detail-oriented and technically proficient Cypress QA Automation Engineer to enhance our QA automation initiative. In this role, you will leverage your experience to design, develop, and maintain automation frameworks, ensuring high-quality software delivery. Key Responsibilities: Develop, execute, and maintain automated test scripts using Cypress for web applications and APIs. Maintain and update automation framework and tools to keep them efficient and aligned with the latest technologies and requirements. Work closely with the development and QA teams to comprehensive test coverage. Integrate automation scripts into the Azure DevOps CI/CD pipeline. Debug, troubleshoot, and resolve automation-related issues and failures. Mandatory Qualifications and Skills: 8+ years of experience in QA automation, with at least 3 years of hands-on experience in Cypress. Proficiency in JavaScript/TypeScript for developing test scripts. Knowledge of version control systems like Git. Hands-on experience integrating automation scripts into CI/CD pipelines. Familiarity with test management tools (Azure DevOps Test Plan or JIRA). Strong understanding of web technologies (HTML, CSS, JavaScript) and RESTful APIs. Preferred: Exposure to additional testing tools like Postman, Selenium, or Playwright. Familiarity with tools like Postman, Swagger, or similar for API testing. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165400-Senior%20Anaplan%20Modeler", "company_id": 3319, "source": 3, "skills": "", "title": "Senior Anaplan Modeler", "location": "", "location_type": null, "job_type": "part_time", "min_experience": 3, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165400-002AMN%20-%20Senior%20Anaplan%20Modeler\"", "description": "Refer to a Friend? Apply Now! Senior Anaplan ModelerIndustry: ITQualification: Any DegreeRequired Skills: Anaplan Model BuildingWorking Shift: 2PM to 11PM ISTCity: CoimbatoreCountry: IndiaName of the position: Senior Anaplan Modeler Location: RemoteNo. of resources needed: 01Mode: Contract Years of experience: 8+ Years Shift: UK shift About the Role: We are seeking a highly skilled Senior Anaplan Modeler to lead the design, development, and optimization of enterprise-grade planning models using the Anaplan platform. This role is pivotal in supporting cross-functional business planning initiatives, providing thought leadership in model architecture, and mentoring junior modelers. The ideal candidate will bring deep expertise in business planning processes, data modeling, and Anaplan best practices. Key Responsibilities: Design, build, and maintain complex, scalable Anaplan models aligned with business processes. Partner with stakeholders to gather and analyze business requirements and translate them into model design. Develop and optimize modules, lists, dashboards, and workflows using Anaplan best practices. Lead large-scale implementations, model revisions, and system integration efforts. Own and manage data integration between Anaplan and external systems (ERP, CRM, etc. ). Ensure adherence to Anaplan model-building standards and governance. Provide technical leadership and mentorship to junior Anaplan modelers. Conduct unit testing, support user acceptance testing (UAT), and prepare documentation. Act as a subject matter expert (SME) on Anaplan capabilities and planning solutions. Collaborate with cross-functional teams across finance, supply chain, sales, and IT. Required Skills & Qualifications: 8+ years of experience in business planning, financial modeling, or enterprise performance management. Minimum 3+ years of hands-on experience in Anaplan model building. Strong command of Anaplan’s modeling language, model optimization, and workspace management. Deep understanding of business domains such as FP&A, supply chain, or sales operations. Strong analytical and problem-solving skillsAbility to translate business needs into technical solutions. Excellent communication, stakeholder engagement, and documentation skills. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/**********-%C2%A0JDE%20Manufacturing%20Functional%20Consultant%20(Remote)", "company_id": 3319, "source": 3, "skills": "", "title": "JDE Manufacturing Functional Consultant", "location": "", "location_type": null, "job_type": "contract", "min_experience": 8, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/**********-005APM%20-%20%C2%A0JDE%20Manufacturing%20Functional%20Consultant%20(Remote)\"", "description": "Refer to a Friend? Apply Now! JDE Manufacturing Functional ConsultantIndustry: ITQualification: Bachelors in Computer ScienceRequired Skills: <PERSON><PERSON>, JDE ERP , Data Management, Product Costing, Manufacturing Accounting, Shop Floor Control, Material Planning, Forecasting and Quality Management modules Working Shift: CSTCity: FloridaCountry: United StatesWe are seeking a JD Edwards ERP professional with 8+ years in IT and expertise as an Oracle Cross Functional Consultant across JDE SCM & Manufacturing modules. What you’ll bring:✅ Strong experience with large-scale implementations in:Product Data ManagementProduct CostingManufacturing AccountingShop Floor ControlMaterial Planning & ForecastingQuality Management✅ Hands-on expertise in:JD Edwards Manufacturing modules (Product Costing, Product Data Management, Work Order Management, Shop Floor Management)Mapping JDE with existing systems, setups, user trainingCreating and executing test scripts (unit, integration, UAT)System functional specifications and business process documentationUser manual and test script documentation. ✅ Knowledge of validated systems and quality guidelines✅ Experience in creating test traceability matrices✅ Strong knowledge of EnterpriseOne 9. 0 and 9. 1, including design, development, and unit testing. Who you are:🌟 A team player with excellent analytical, presentation, communication, and time management skills🌟 Able to work cross-functionally with stakeholders to deliver high-quality JDE manufacturing solutions🌟 Passionate about process optimization and end-user enablement. If you are looking to leverage your JD Edwards Manufacturing expertise in a dynamic environment where your contribution will have a significant impact, we’d love to connect! Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165489-Data%20Engineer", "company_id": 3319, "source": 3, "skills": "", "title": "Data Engineer", "location": "", "location_type": null, "job_type": "part_time", "min_experience": 5, "max_experience": 7, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165489-002ACH%20-%20Data%20Engineer\"", "description": "Refer to a Friend? Apply Now! Data EngineerIndustry: ITQualification: Any DegreeRequired Skills: AWS, DomoWorking Shift: 2PM to 11PM ISTCity: CoimbatoreCountry: IndiaName of the position: Data EngineerLocation: RemoteNo. of resources needed: 01Mode: Contract Years of experience: 5-7 Years Shift: UK shift Overview :We are seeking a skilled and results-driven Data Engineer with 5–7 years of experience to design, build, and maintain scalable data pipelines and analytics solutions. The ideal candidate will have hands-on expertise in AWS (including Redshift and Glue), Azure SQL Server, and data ingestion frameworks, along with strong SQL skills and experience with BI tools like Domo. Key Responsibilities:Design, develop, and optimize scalable data ingestion pipelines using AWS Glue and related AWS services. Build and manage data warehouses and data lakes on AWS Redshift, ensuring efficient storage and retrieval. Integrate data from various sources into Azure SQL Server and cloud environments. Write complex, efficient SQL queries for data extraction, transformation, and reporting. Collaborate with analysts and business stakeholders to deliver data solutions through Domo dashboards and reports. Ensure data quality, consistency, and governance across platforms. Monitor pipeline performance and troubleshoot issues to ensure reliability and scalability. Required Qualifications:5–7 years of professional experience in Data Engineering or related roles. Strong hands-on experience with AWS services, particularly Redshift and Glue. Proficient in Azure SQL Server and advanced SQL querying techniques. Experience with end-to-end data ingestion, ETL/ELT development, and orchestration. Familiarity with BI tools, especially Domo, for dashboard development and data visualization. Ability to work independently and collaboratively in a fast-paced, cloud-native environment. Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165493-BI%20Engineer", "company_id": 3319, "source": 3, "skills": "", "title": "BI Engineer", "location": "", "location_type": null, "job_type": "part_time", "min_experience": 5, "max_experience": 7, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165493-002ACH-%20BI%20Engineer\"", "description": "Refer to a Friend? Apply Now! BI EngineerIndustry: ITQualification: Any DegreeRequired Skills: Domo, AWS, SQLWorking Shift: 2PM to 11PM ISTCity: CoimbatoreCountry: IndiaName of the position: BI EngineerLocation: RemoteNo. of resources needed: 01Mode: Contract Years of experience: 5-7 Years Shift: UK shift Overview :We are looking for a BI Engineer with 5–7 years of experience to support data-driven decision-making in a dynamic retail environment. This role will focus on designing efficient data pipelines, enabling business insights through advanced analytics, and delivering impactful dashboards using Domo. Responsibilities:Design and maintain scalable data pipelines using AWS Glue and Redshift. Integrate retail data sources into Azure SQL Server and manage data flows for reporting. Develop and optimize SQL queries to support business metrics and KPIs. Build interactive and self-service dashboards in Domo for merchandising, sales, and supply chain teams. Collaborate with business and data teams to gather requirements and translate them into data models and visualizations. Ensure data accuracy, integrity, and performance across all BI solutions. Key Skills:AWS Redshift & GlueAzure SQL ServerRetail data ingestion & transformationAdvanced SQL (performance tuning, complex joins)Domo (card building, ETL, storytelling) Apply Now!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.augustahitech.com/careers/6000165518-RPA%20Engineer", "company_id": 3319, "source": 3, "skills": "", "title": "RPA Engineer", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "mailto:?subject=Augusta Hitech Careers&body=Hey, I wanna share this job posting with you: \"https://www.augustahitech.com/careers/6000165518-002AHI%20-%20RPA%20Engineer\"", "description": "Refer to a Friend? Apply Now! RPA EngineerIndustry: ITQualification: Any DegreeRequired Skills: UiPathWorking Shift: 11am to 7pm ISTCity: Mumbai, Bangalore, Chennai, Coimbatore, Noida, Gurgaon, DelhiCountry: India Name of the position: RPA Developer Location: Coimbatore / Remote No. of resources needed : 01Mode: Contract - 45 daysYears of experience: 5+ Years Shift : 11am to 8pm IST OVERVIEW : We are seeking a proactive and skilled RPA Support & Enhancement Engineer with UiPath skill to join our automation team. This role involves managing production support, implementing minor enhancements, and ensuring smooth bot operations post-deployment. The ideal candidate will have strong analytical skills, experience with RPA tools, and a collaborative mindset to work with stakeholders and SMEs. RESPONSIBILITIES : Understand business processes and review post-deployment (hyper-care) support requirements. Collaborate with stakeholders to define SLAs and provide regular status updates. Monitor, maintain, and troubleshoot RPA bots in production environments. Analyze logs and perform root cause analysis to identify performance improvements. Implement and deploy minor enhancements and approved change requests. Work with SMEs to align bots with process or application changes. Manage bot schedules, deployments, and access via the Orchestrator. Prepare reports on bot performance, utilization, and issue tracking. Provide effort estimation and analysis for new automation requests. QUALIFICATIONS: Bachelor's degree in Computer Science, Information Technology, or a related field. Proven experience in RPA support and operations (preferably with UiPath). Strong understanding of production bot management, monitoring, and SLA-based support. Experience in handling logs, performance reports, and failure analysis. Ability to implement minor enhancements and manage change requests efficiently. Familiarity with RPA Orchestrator platforms for bot scheduling and deployment. Excellent communication, reporting, and stakeholder collaboration skills. Strong analytical and problem-solving abilities. Apply Now!", "ctc": null, "currency": null, "meta": {}}]