[{"jd_link": "https://ficustechnologies.com/careers/full-stack-wordpress-developer/", "company_id": 3326, "source": 3, "skills": "", "title": "Full stack WordPress developer", "location": "", "location_type": "flexible", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://ficustechnologies.com/careers/full-stack-wordpress-developer/", "description": "Home Services All services Web DevelopmentFor creating custom websites and applications. Mobile DevelopmentCreating custom mobile apps for iOS and Android. IT OutstaffingBest IT professionals to complete client requests IT RecruitmentSolutions that help find the right talent. Digital Product DesignFor creating innovative user experiences. End-to-End DevelopmentComplete project lifecycle from concept to launch. IT Consulting and Digital AdvisoryProvide expert advice and solutions to businesses. Quality AssuranceComplete and rigorous testing and analysis. Software Re-engineering And MaintenanceSoftware for improving performance and maintenance. Case Studies Industries All industries FintechInnovation driving financial technology advancements. RetailCustomer-focused solutions for shopping experiences. HealthcareImproving patient care through technology. Media and EntertainmentEnhancing content consumption experiences. EdtechTransforming education through technology. TravelStreamlining and personalizing travel experiences. AutomotiveRobust solutions to boost the automotive industry BlockchainEnhance your security and data management Company Company About UsOur history, mission, and who we are. CareersFind the best opportunities for yourself as an IT specialist. BlogRead about latest trends in software development industry. Contact Home Careers Full stack WordPress. . . Full stack WordPress developerAre you ready to take your Full stack WordPress developer career to the next level? Ficus Technologies, a leader in cutting-edge tech solutions, is seeking a Full stack WordPress developer to join our dynamic team. $1800-2500RemoteFull-time Position overview We are looking for a Full Stack WordPress Developer to join our team, who will play a crucial role in the development and support of web projects on the WordPress platform for both internal and external clients. Your primary responsibility will be to create high-quality and highly functional websites based on WordPress, ensuring they meet the needs and expectations of our clients. Responsibilities: Developing new websites on WordPress. Maintain existing websites. Create plugins and enhance functionalities of existing ones. Designing responsive websites of various complexity based on approved design layouts. Working with 3rd party APIs. Optimize and enhance the performance of websites. Requirements: Proficient in WordPress, capable of creating both custom templates and working with pre-made templates. Experience in writing and understanding the functionality of WordPress plugins, as well as maintaining existing plugins. Knowledge of HTML, CSS (LESS/SCSS), with expertise in semantic, responsive, and cross-browser web development. Experience with WebPack or Gulp. Proficiency in JavaScript (native and jQuery), AJAX (jQuery, Ajax, Fetch). Knowledge of PHP 8+ (OOP, SOLID). Understanding of MySQL, with experience in query optimization and database management. Familiarity with Agile methodology and experience in project management systems like Jira and Trello. Understanding of SEO principles and the ability to implement them in WordPress website development. Nice to have: A basic understanding of web server operations (Nginx). Experience working with WooCommerce, ACF (Advanced Custom Fields), Gutenberg, Elementor, WordPress Multisite, WordPress Rest API, WP CLI, Git, Docker. What we offer Join us at Ficus Technologies, where your creativity and expertise will shape the future of digital experiences. Be part of a team that values innovation and user-centric design. Apply now and embark on an exciting journey with us! Flexible ScheduleStart your workday between 8 and 11 a. m. with our flexible schedule. Fully Remote WorkWork from anywhere and enjoy flexibility and autonomy. Paid Vacation and Sick LeaveOur comprehensive benefits package includes paid vacation and sick leave. Company-Sponsored TrainingEnhance your skills through training sponsored by the company to advance your career. Competitive SalaryEnjoy a highly competitive salary with opportunities for career growth based on your performance. Creative FreedomHave the creative freedom to shape the future of digital experiences. Friendly Team and Collaborative Work EnvironmentBe part of a collaborative work culture with a dynamic and friendly team. Stable EmploymentSecure your future with stable job opportunities in our reliable company. International ProjectsJoin some of the most exciting international projects and broaden your career horizons. Apply for the vacancy Your full name* Your email address* Your phone number Your location Tell us about yourself Attach your resume here (or leave a link to your LinkedIn profile above)* Select a document on your computer or drag and drop it here Allowed formats: . pdf, . doc, . docx, . odt, . rtf, . txt. File size must not exceed 25MB. I have read and accepted the Privacy Statment and Terms & Conditions. * Apply Ficus Technologies is a software development and IT recruiting company that helps large companies, and promising startups bring ideas of any size to life. The company is trusted by over 250+ companies worldwide and provides expert services for building MVPs, developing software products from scratch, and scaling teams. We form a flexible pipeline that adapts to the individual client's needs to deliver a result that allows you to achieve all your business goals. Ficus Technologies is a software development and IT recruiting company that helps large companies, and promising startups bring ideas of any size to life. The company is trusted by over 250+ companies worldwide and provides expert services for building MVPs, developing software products from scratch, and scaling teams. We form a flexible pipeline that adapts to the individual client's needs to deliver a result that allows you to achieve all your business goals. Ukraine Uhorska St, 14, Lviv, 79034See on Google Maps United States1555 Yellowheart Way,Hollywood, FL, 33019See on Google Maps info@ficustechnologies. com ServicesWeb Development Mobile Development IT Outstaffing IT Recruitment Digital Product Design End-to-End Development IT Consulting and Digital Advisory Quality Assurance Re-engineering and Maintenance IndustriesFintech Retail Healthcare Travel Media & Entertainment Edtech Blockchain Automotive CompanyCase Studies About us Careers Blog Cookie Policy Privacy Statement Terms & Conditions US Residents Privacy Statement © 2012-2025 Ficus Technologies Inc. All rights reserved", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://ficustechnologies.com/careers/senior-react-node-js-developer/", "company_id": 3326, "source": 3, "skills": "", "title": "Senior React/Node.js developer", "location": "", "location_type": "flexible", "job_type": null, "min_experience": 5, "max_experience": 5, "apply_link": "https://ficustechnologies.com/careers/senior-react-node-js-developer/", "description": "Home Services All services Web DevelopmentFor creating custom websites and applications. Mobile DevelopmentCreating custom mobile apps for iOS and Android. IT OutstaffingBest IT professionals to complete client requests IT RecruitmentSolutions that help find the right talent. Digital Product DesignFor creating innovative user experiences. End-to-End DevelopmentComplete project lifecycle from concept to launch. IT Consulting and Digital AdvisoryProvide expert advice and solutions to businesses. Quality AssuranceComplete and rigorous testing and analysis. Software Re-engineering And MaintenanceSoftware for improving performance and maintenance. Case Studies Industries All industries FintechInnovation driving financial technology advancements. RetailCustomer-focused solutions for shopping experiences. HealthcareImproving patient care through technology. Media and EntertainmentEnhancing content consumption experiences. EdtechTransforming education through technology. TravelStreamlining and personalizing travel experiences. AutomotiveRobust solutions to boost the automotive industry BlockchainEnhance your security and data management Company Company About UsOur history, mission, and who we are. CareersFind the best opportunities for yourself as an IT specialist. BlogRead about latest trends in software development industry. Contact Home Careers Senior React/Node. js. . . Senior React/Node. js developerAre you ready to take your Full Stack Developer career to the next level? Ficus Technologies, a leader in cutting-edge tech solutions, is seeking a Senior React/Node. js developer to join our dynamic team. $3000 – 3500RemoteFull-time Position overview We are looking for a Full Stack Developer to join our team, who will play a crucial role in the development and support of web projects on the React and Node. js frameworks for both internal and external clients. Your primary responsibility will be to create high-quality and highly functional web applications, ensuring they meet the needs and expectations of our clients. Collaborate closely with team members, including product managers and QA personnel, to collectively achieve team goals and ensure the successful and seamless delivery of projects. Responsibilities: Implement new functionality for backend/frontend-based application based on Node. js runtime within frameworks and libraries Integration of third-party services and support of existing ones Provide accurate project estimates and validate the estimates of team members, contributing to effective project planning and execution Actively engage in developing architecture, approaches etc. Conduct code reviews and share knowledge with the team Requirements: At least 5 years of experience in Node. js runtime, SQL/noSQL and cloud technologies English B2+ Teamwork experience Deep knowledge of the JavaScript/TypeScript languages Working with layouts and styles based pre- and post CSS processors Strong understanding of SPA functionality based on React. js library Strong knowledge of data management systems like MobX, Redux or Context-based models and understanding the difference Use of external libraries for data management, such as Redux-Toolkit, Redux-Persist Thunk, Reselect, etc. Experience with Drag-n-Drop functionality (native or libraries like Beautiful-DnD) Experience developing Canvas functionality (native or libraries like fabric. js, konva. js) Work with chart libraries such as Chart. js, Recharts, ApexChart, etc. Experience developing UI with Storybook framework, Prime-React, React-Bootstrap, MUI, AntD or similar. Ability to compare different UI libraries Use of different libraries for forms functionality such as Formic React-Form etc. Use of the data request libraries like React-Query, Axios, Superagent, Got, Apollo, GraphQL on your projects. Experience making pixel perfect CSS-layouts Experience with React-Virtualized or React-window for optimization Practical usage of Google-Analytics, Google-Insights or similar Strong understanding of SSR techniques based on Next. js or Gatsby. Understanding the difference Familiarity with serverless architecture Knowledge of REST, gRPC, SOAP, GraphQL architectures Familiarity with Docker, Docker compose etc. An excellent knowledge of SOLID (SRP, OCP, LSP, ISP, DIP), DRY, SLAP, KISS Familiarity with Agile methodologies, such as Scrum, Kanban Experience with RDBMS like PostgreSQL, MySQL, MariaDB Experience with noSQL like Redis, Memcached Practical experience in basic CI/CD configuring Strong knowledge of UNIX-based OS Understanding features of Express, Koa, Nest, and their differences Deep understanding of testing and experience in testing frameworks Nice to have: Experience with other frontend frameworks like Vue or Angular Experience with languages other than the JS/TS Reference from a previous employer Share public links from the successful project you were engaged in Experience with mentoring and training junior developers Experience in developing open-source projects Links to public profiles on GitHub, LinkedIn, Djinni, etc What we offer Join us at Ficus Technologies, where your creativity and expertise will shape the future of digital experiences. Be part of a team that values innovation and user-centric design. Apply now and embark on an exciting journey with us! Flexible ScheduleStart your workday between 8 and 11 a. m. with our flexible schedule. Fully Remote WorkWork from anywhere and enjoy flexibility and autonomy. Paid Vacation and Sick LeaveOur comprehensive benefits package includes paid vacation and sick leave. Company-Sponsored TrainingEnhance your skills through training sponsored by the company to advance your career. Competitive SalaryEnjoy a highly competitive salary with opportunities for career growth based on your performance. Creative FreedomHave the creative freedom to shape the future of digital experiences. Friendly Team and Collaborative Work EnvironmentBe part of a collaborative work culture with a dynamic and friendly team. Stable EmploymentSecure your future with stable job opportunities in our reliable company. International ProjectsJoin some of the most exciting international projects and broaden your career horizons. Apply for the vacancy Your full name* Your email address* Your phone number Your location Tell us about yourself Attach your resume here (or leave a link to your LinkedIn profile above)* Select a document on your computer or drag and drop it here Allowed formats: . pdf, . doc, . docx, . odt, . rtf, . txt. File size must not exceed 25MB. I have read and accepted the Privacy Statment and Terms & Conditions. * Apply Ficus Technologies is a software development and IT recruiting company that helps large companies, and promising startups bring ideas of any size to life. The company is trusted by over 250+ companies worldwide and provides expert services for building MVPs, developing software products from scratch, and scaling teams. We form a flexible pipeline that adapts to the individual client's needs to deliver a result that allows you to achieve all your business goals. Ficus Technologies is a software development and IT recruiting company that helps large companies, and promising startups bring ideas of any size to life. The company is trusted by over 250+ companies worldwide and provides expert services for building MVPs, developing software products from scratch, and scaling teams. We form a flexible pipeline that adapts to the individual client's needs to deliver a result that allows you to achieve all your business goals. Ukraine Uhorska St, 14, Lviv, 79034See on Google Maps United States1555 Yellowheart Way,Hollywood, FL, 33019See on Google Maps info@ficustechnologies. com ServicesWeb Development Mobile Development IT Outstaffing IT Recruitment Digital Product Design End-to-End Development IT Consulting and Digital Advisory Quality Assurance Re-engineering and Maintenance IndustriesFintech Retail Healthcare Travel Media & Entertainment Edtech Blockchain Automotive CompanyCase Studies About us Careers Blog Cookie Policy Privacy Statement Terms & Conditions US Residents Privacy Statement © 2012-2025 Ficus Technologies Inc. All rights reserved", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://ficustechnologies.com/careers/senior-node-js-developer/", "company_id": 3326, "source": 3, "skills": "", "title": "Senior Node.js developer", "location": "", "location_type": "flexible", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://ficustechnologies.com/careers/senior-node-js-developer/", "description": "Home Services All services Web DevelopmentFor creating custom websites and applications. Mobile DevelopmentCreating custom mobile apps for iOS and Android. IT OutstaffingBest IT professionals to complete client requests IT RecruitmentSolutions that help find the right talent. Digital Product DesignFor creating innovative user experiences. End-to-End DevelopmentComplete project lifecycle from concept to launch. IT Consulting and Digital AdvisoryProvide expert advice and solutions to businesses. Quality AssuranceComplete and rigorous testing and analysis. Software Re-engineering And MaintenanceSoftware for improving performance and maintenance. Case Studies Industries All industries FintechInnovation driving financial technology advancements. RetailCustomer-focused solutions for shopping experiences. HealthcareImproving patient care through technology. Media and EntertainmentEnhancing content consumption experiences. EdtechTransforming education through technology. TravelStreamlining and personalizing travel experiences. AutomotiveRobust solutions to boost the automotive industry BlockchainEnhance your security and data management Company Company About UsOur history, mission, and who we are. CareersFind the best opportunities for yourself as an IT specialist. BlogRead about latest trends in software development industry. Contact Home Careers Senior Node. js devel. . . Senior Node. js developerAre you ready to take your Backend developer career to the next level? Ficus Technologies, a leader in cutting-edge tech solutions, is seeking a Senior Node. js developer to join our dynamic team. $2750 – 3000RemoteFull-time Position overview We are looking for a Backend Developer to join our team, who will work in tandem with the Product and Business teams to craft sophisticated solutions for business challenges, providing an array of technical approaches, design concepts, prototypes, and detailed technical plans. Take on the responsibility of estimating the effort required for various tasks, user stories, and large-scale epics. Play a pivotal role in leading and mentoring junior developers, fostering an environment of continuous learning and development, and assisting in the integration of new developers into the team. Engage in and lead technical discussions, disseminating knowledge and best practices, and exerting influence over critical technical decisions such as processes, design patterns, and overall system architecture. Responsibilities: Implementation of new functionality in the Node. js runtime Integration of third-party services and support of existing ones Provide accurate project estimates and validate the estimates of team members, contributing to effective project planning and execution Actively engage in developing architecture, approaches, etc. Conduct code reviews and share knowledge with the team Managing AWS or similar strict cloud-oriented infrastructures Requirements: At least 5+ years of experience in Node. js runtime, SQL/noSQL and cloud technologies English B2+ Teamwork experience Experience with monolith and microservices architectures In-depth knowledge of the Javascript / Typescript, including streaming, error handling etc. Understanding the functionality of message brokers, especially Apache Kafka, RabbitMQ, etc. Familiarity with serverless architecture and cloud-native development patterns Strong understanding of AWS and/or GCP platform services or similar clouds Knowledge in REST, gRPC, SOAP, GraphQL architectures, may include some custom solutions Familiarity with Kubernetes, Docker, etc. Excellent knowledge of SOLID (SRP, OCP, LSP, ISP, DIP), DRY, SLAP, KISS Familiarity with Agile methodologies, such as Scrum, Kanban Experience with RDBMS like PostgreSQL, MySQL, SQLite, MariaDB Experience with noSQL like Redis, Memcached Experience with ORM/ODM libraries Practical experience in basic CI/CD configuring Strong knowledge of UNIX-based OS Experience with OAuth 2. 0 Understanding features of Express, Koa, Nest, and their differences Deep understanding of testing and experience in testing frameworks Nice to have: Experience with languages other than the JS/TS Reference from a previous employer Share public links from the successful project you were engaged in Experience with mentoring and training junior developers Experience in developing open-source projects Links to public profiles on GitHub, LinkedIn, Djinni, etc. What we offer Join us at Ficus Technologies, where your creativity and expertise will shape the future of digital experiences. Be part of a team that values innovation and user-centric design. Apply now and embark on an exciting journey with us! Flexible ScheduleStart your workday between 8 and 11 a. m. with our flexible schedule. Fully Remote WorkWork from anywhere and enjoy flexibility and autonomy. Paid Vacation and Sick LeaveOur comprehensive benefits package includes paid vacation and sick leave. Company-Sponsored TrainingEnhance your skills through training sponsored by the company to advance your career. Competitive SalaryEnjoy a highly competitive salary with opportunities for career growth based on your performance. Creative FreedomHave the creative freedom to shape the future of digital experiences. Friendly Team and Collaborative Work EnvironmentBe part of a collaborative work culture with a dynamic and friendly team. Stable EmploymentSecure your future with stable job opportunities in our reliable company. International ProjectsJoin some of the most exciting international projects and broaden your career horizons. Apply for the vacancy Your full name* Your email address* Your phone number Your location Tell us about yourself Attach your resume here (or leave a link to your LinkedIn profile above)* Select a document on your computer or drag and drop it here Allowed formats: . pdf, . doc, . docx, . odt, . rtf, . txt. File size must not exceed 25MB. I have read and accepted the Privacy Statment and Terms & Conditions. * Apply Ficus Technologies is a software development and IT recruiting company that helps large companies, and promising startups bring ideas of any size to life. The company is trusted by over 250+ companies worldwide and provides expert services for building MVPs, developing software products from scratch, and scaling teams. We form a flexible pipeline that adapts to the individual client's needs to deliver a result that allows you to achieve all your business goals. Ficus Technologies is a software development and IT recruiting company that helps large companies, and promising startups bring ideas of any size to life. The company is trusted by over 250+ companies worldwide and provides expert services for building MVPs, developing software products from scratch, and scaling teams. We form a flexible pipeline that adapts to the individual client's needs to deliver a result that allows you to achieve all your business goals. Ukraine Uhorska St, 14, Lviv, 79034See on Google Maps United States1555 Yellowheart Way,Hollywood, FL, 33019See on Google Maps info@ficustechnologies. com ServicesWeb Development Mobile Development IT Outstaffing IT Recruitment Digital Product Design End-to-End Development IT Consulting and Digital Advisory Quality Assurance Re-engineering and Maintenance IndustriesFintech Retail Healthcare Travel Media & Entertainment Edtech Blockchain Automotive CompanyCase Studies About us Careers Blog Cookie Policy Privacy Statement Terms & Conditions US Residents Privacy Statement © 2012-2025 Ficus Technologies Inc. All rights reserved", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://ficustechnologies.com/careers/trainee-software-engineer/", "company_id": 3326, "source": 3, "skills": "", "title": "Trainee software engineer", "location": "", "location_type": "flexible", "job_type": "internship", "min_experience": null, "max_experience": null, "apply_link": "https://ficustechnologies.com/careers/trainee-software-engineer/", "description": "Home Services All services Web DevelopmentFor creating custom websites and applications. Mobile DevelopmentCreating custom mobile apps for iOS and Android. IT OutstaffingBest IT professionals to complete client requests IT RecruitmentSolutions that help find the right talent. Digital Product DesignFor creating innovative user experiences. End-to-End DevelopmentComplete project lifecycle from concept to launch. IT Consulting and Digital AdvisoryProvide expert advice and solutions to businesses. Quality AssuranceComplete and rigorous testing and analysis. Software Re-engineering And MaintenanceSoftware for improving performance and maintenance. Case Studies Industries All industries FintechInnovation driving financial technology advancements. RetailCustomer-focused solutions for shopping experiences. HealthcareImproving patient care through technology. Media and EntertainmentEnhancing content consumption experiences. EdtechTransforming education through technology. TravelStreamlining and personalizing travel experiences. AutomotiveRobust solutions to boost the automotive industry BlockchainEnhance your security and data management Company Company About UsOur history, mission, and who we are. CareersFind the best opportunities for yourself as an IT specialist. BlogRead about latest trends in software development industry. Contact Home Careers Trainee software eng. . . Trainee software engineerAre you ready to take your Trainee software engineer career to the next level? Ficus Technologies, a leader in cutting-edge tech solutions, is seeking Trainee software engineers to join our dynamic team. RemoteFull-time Position overview We are looking for a motivated and enthusiastic Trainee Software Engineer to join our dynamic team. This entry-level position is ideal for recent graduates or individuals in the early stages of their software development careers who are eager to learn and contribute to our ongoing projects. Responsibilities: Follow a structured learning plan designed to progressively enhance skills in specific programming languages, tools, or frameworks. Complete assigned coursework and training modules within designated time frames. Actively engage in a mentorship program, seeking guidance from more experienced engineers. Regularly meet with a mentor to discuss progress, challenges, and career goals. Participate in hands-on sessions to apply learned concepts in real-world scenarios. Regularly read industry publications, technical books, and research papers to stay updated on new technologies and best practices. Apply creative thinking and coding skills to build solutions and prototypes under time constraints. Requirements: Good theoretical knowledge in some of the popular frameworks or libraries like React / Vue / Angular / Nest / Next / Express etc. Good theoretical understanding of the ecosystem for your favorite framework or library. For example TypeORM, Yup, Redux, VueRouter, RxJS, Chart. js, Socket. io etc. Good knowledge in JS/TS runtime Understanding some of the universal technical terms and technologies like SPA, SSR, Git, NPM, Webpack, Docker, HTTP etc. For backend oriented devs also critical to have an understanding of one of the SQL/NoSQL databases For frontend oriented devs also critical to have practical experience with creating layouts and grids using HTML/CSS/PostCSS etc Nice to have: Perfect English speaking skills is required, because all the process of technical interview will be in English Experience with Jira Commercial experience in software engineering will have a significant impact on the choice High level of self-organization, understanding of corporate culture and ethics Theoretical understanding of the cryptocurrencies area and blockchain technologies What we offer Join us at Ficus Technologies, where your creativity and expertise will shape the future of digital experiences. Be part of a team that values innovation and user-centric design. Apply now and embark on an exciting journey with us! Flexible ScheduleStart your study day between 8 and 11 a. m. with our flexible schedule. Fully Remote InternshipWork from anywhere and enjoy flexibility and autonomy. Company-Sponsored TrainingEnhance your skills through training sponsored by the company to advance your career. Creative FreedomHave the creative freedom to shape the future of digital experiences. Friendly Team and Collaborative Work EnvironmentBe part of a collaborative work culture with a dynamic and friendly team. International ProjectsJoin some of the most exciting international projects and broaden your career horizons. Apply for the vacancy Your full name* Your email address* Your phone number Your location Tell us about yourself Attach your resume here (or leave a link to your LinkedIn profile above)* Select a document on your computer or drag and drop it here Allowed formats: . pdf, . doc, . docx, . odt, . rtf, . txt. File size must not exceed 25MB. I have read and accepted the Privacy Statment and Terms & Conditions. * Apply Ficus Technologies is a software development and IT recruiting company that helps large companies, and promising startups bring ideas of any size to life. The company is trusted by over 250+ companies worldwide and provides expert services for building MVPs, developing software products from scratch, and scaling teams. We form a flexible pipeline that adapts to the individual client's needs to deliver a result that allows you to achieve all your business goals. Ficus Technologies is a software development and IT recruiting company that helps large companies, and promising startups bring ideas of any size to life. The company is trusted by over 250+ companies worldwide and provides expert services for building MVPs, developing software products from scratch, and scaling teams. We form a flexible pipeline that adapts to the individual client's needs to deliver a result that allows you to achieve all your business goals. Ukraine Uhorska St, 14, Lviv, 79034See on Google Maps United States1555 Yellowheart Way,Hollywood, FL, 33019See on Google Maps info@ficustechnologies. com ServicesWeb Development Mobile Development IT Outstaffing IT Recruitment Digital Product Design End-to-End Development IT Consulting and Digital Advisory Quality Assurance Re-engineering and Maintenance IndustriesFintech Retail Healthcare Travel Media & Entertainment Edtech Blockchain Automotive CompanyCase Studies About us Careers Blog Cookie Policy Privacy Statement Terms & Conditions US Residents Privacy Statement © 2012-2025 Ficus Technologies Inc. All rights reserved", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://ficustechnologies.com/careers/ui-ux-designer/", "company_id": 3326, "source": 3, "skills": "", "title": "UX/UI Designer", "location": "", "location_type": "flexible", "job_type": null, "min_experience": 2, "max_experience": 2, "apply_link": "https://ficustechnologies.com/careers/ui-ux-designer/", "description": "Home Services All services Web DevelopmentFor creating custom websites and applications. Mobile DevelopmentCreating custom mobile apps for iOS and Android. IT OutstaffingBest IT professionals to complete client requests IT RecruitmentSolutions that help find the right talent. Digital Product DesignFor creating innovative user experiences. End-to-End DevelopmentComplete project lifecycle from concept to launch. IT Consulting and Digital AdvisoryProvide expert advice and solutions to businesses. Quality AssuranceComplete and rigorous testing and analysis. Software Re-engineering And MaintenanceSoftware for improving performance and maintenance. Case Studies Industries All industries FintechInnovation driving financial technology advancements. RetailCustomer-focused solutions for shopping experiences. HealthcareImproving patient care through technology. Media and EntertainmentEnhancing content consumption experiences. EdtechTransforming education through technology. TravelStreamlining and personalizing travel experiences. AutomotiveRobust solutions to boost the automotive industry BlockchainEnhance your security and data management Company Company About UsOur history, mission, and who we are. CareersFind the best opportunities for yourself as an IT specialist. BlogRead about latest trends in software development industry. Contact Home Careers UX/UI Designer UX/UI DesignerAre you ready to take your UI/UX Design career to the next level? Ficus Technologies, a leader in cutting-edge tech solutions, is seeking UI/UX Designer to join our dynamic team. RemoteFull-time Position overview As a UX/UI Designer, you will be responsible for driving the design process from concept to final implementation. You will collaborate closely with cross-functional teams to understand project requirements, conduct user research, create wireframes and prototypes, and deliver exceptional visual designs. Your work will enhance user satisfaction and ensure that our products are intuitive and aesthetically appealing. Responsibilities: Work closely with developers to ensure successful design implementation. Conduct user research to gain insights into user behavior and preferences. Create wireframes, user flows, and interactive prototypes to communicate design concepts. Develop high-fidelity UI designs that align with our brand and product goals. Test and iterate on designs based on user feedback and usability testing. Stay current with industry trends and best practices in UX/UI design. Design banners, posts, storyboards, emails, etc. Work with English-speaking clients to understand their needs. You should be able to elicit project details from the client and clearly articulate your design decisions. Requirements: At least 2 years of experience as a UI/UX designer. Proven experience as a UX/UI Designer with a strong portfolio showcasing your work. Proficiency in industry-standard design tools such as Figma, Sketch, Adobe XD, or similar software. Strong knowledge of user experience (UX) and user interface (UI) design principles, including usability, accessibility, and responsive design. Ability to create wireframes, user flows, and interactive prototypes to communicate design concepts effectively. Capability to design high-fidelity user interfaces that align with brand guidelines and project goals. Excellent communication and collaboration skills. English – Intermediate level or higher. Ability to work in a fast-paced, agile environment. Problem-solving skills and attention to detail. Ability to estimate needed time and prioritize design tasks. Excellent organizational skills and ability to prioritize in order to meet deadlines. Experience in creating visual content for social networks (Behance, LinkedIn, Instagram, Facebook, etc. ). Nice to have: Experience in designing for mobile applications. Familiarity with front-end development technologies (HTML, CSS, JavaScript). Experience working with design systems and pattern libraries. Interaction design skills, including micro-interactions and animations. Experience in video editing and adding logos to videos. What we offer Join us at Ficus Technologies, where your creativity and expertise will shape the future of digital experiences. Be part of a team that values innovation and user-centric design. Apply now and embark on an exciting journey with us! Flexible ScheduleStart your workday between 8 and 11 a. m. with our flexible schedule. Fully Remote WorkWork from anywhere and enjoy flexibility and autonomy. Paid Vacation and Sick LeaveOur comprehensive benefits package includes paid vacation and sick leave. Company-Sponsored TrainingEnhance your skills through training sponsored by the company to advance your career. Competitive SalaryEnjoy a highly competitive salary with opportunities for career growth based on your performance. Creative FreedomHave the creative freedom to shape the future of digital experiences. Friendly Team and Collaborative Work EnvironmentBe part of a collaborative work culture with a dynamic and friendly team. Stable EmploymentSecure your future with stable job opportunities in our reliable company. International ProjectsJoin some of the most exciting international projects and broaden your career horizons. Apply for the vacancy Your full name* Your email address* Your phone number Your location Tell us about yourself Attach your resume here (or leave a link to your LinkedIn profile above)* Select a document on your computer or drag and drop it here Allowed formats: . pdf, . doc, . docx, . odt, . rtf, . txt. File size must not exceed 25MB. I have read and accepted the Privacy Statment and Terms & Conditions. * Apply Ficus Technologies is a software development and IT recruiting company that helps large companies, and promising startups bring ideas of any size to life. The company is trusted by over 250+ companies worldwide and provides expert services for building MVPs, developing software products from scratch, and scaling teams. We form a flexible pipeline that adapts to the individual client's needs to deliver a result that allows you to achieve all your business goals. Ficus Technologies is a software development and IT recruiting company that helps large companies, and promising startups bring ideas of any size to life. The company is trusted by over 250+ companies worldwide and provides expert services for building MVPs, developing software products from scratch, and scaling teams. We form a flexible pipeline that adapts to the individual client's needs to deliver a result that allows you to achieve all your business goals. Ukraine Uhorska St, 14, Lviv, 79034See on Google Maps United States1555 Yellowheart Way,Hollywood, FL, 33019See on Google Maps info@ficustechnologies. com ServicesWeb Development Mobile Development IT Outstaffing IT Recruitment Digital Product Design End-to-End Development IT Consulting and Digital Advisory Quality Assurance Re-engineering and Maintenance IndustriesFintech Retail Healthcare Travel Media & Entertainment Edtech Blockchain Automotive CompanyCase Studies About us Careers Blog Cookie Policy Privacy Statement Terms & Conditions US Residents Privacy Statement © 2012-2025 Ficus Technologies Inc. All rights reserved", "ctc": null, "currency": null, "meta": {}}]