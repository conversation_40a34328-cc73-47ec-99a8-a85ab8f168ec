[{"jd_link": "https://softwareforgood.com/job-post-contract-designer/", "company_id": 3329, "source": 3, "skills": "", "title": "Job Post: Contract Designer", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://softwareforgood.com/job-post-contract-designer/", "description": "Passionate about user interface and graphic design? Searching for opportunities to grow your career and use your creative powers for good? We want to meet you! At Software for Good, we bring people and technology together to solve complex world problems like climate change, housing, health care, human rights, renewable energy, and education. We are a fully remote digital agency founded in Minneapolis, and we develop web and mobile applications for clients including social enterprises, nonprofits, startups, and educational and government entities. We are expanding our contractor network and looking to connect with designers who are eager to work on inclusive, ethical technology that has a positive social impact. Our ideal collaborator is enthusiastic about our mission, looking for an opportunity to flex their design skills, and thoughtful about the impact of technology and the biases of people who create it. We’re looking for new skills and perspectives to add to our contractor network. We strive to include people from underrepresented backgrounds, and would love to know what specific talents you could bring to our mission-driven client projects. Contract designers help our team develop beautiful, usable, empathy-driven, and impactful digital experiences. Generally, we extend the opportunity for work on an as-needed basis, and aren’t able to guarantee a regular number of hours. The work will likely include: • Creating design mockups of digital experiences in Sketch, InVision, Figma, Photoshop, Webflow, and/or the tools you are most comfortable with • Creating responsive web designs that put users’ needs first • Working closely with our product strategy team — including a product owner and UX and content strategists — to plan and review the design work, making sure it fits with the overall strategic vision for the project and aligns with the mission of Software for Good • Working with clients to understand their goals and make their ideas tangible • Incorporating feedback from both clients and potential users • Contributing ideas to overall product success, including making recommendations based on common design patterns and best practices • Working with our team to evaluate designs for inclusion and accessibility Wondering if this opportunity is a good fit for you? We think we have a lot to offer, including the chance to do meaningful work alongside people who really care and a positive, casual work environment with a fully remote team. You can see some of our past projects here. Software for Good’s company values are: • Open and honest communication • Holding ourselves and others accountable for high-integrity work • High employee quality of life • Collaboration • Social justice and inclusion Tell us why you’re interested in the role by sending the following to hello@softwareforgood. com: • Your portfolio • Your contracting rate • A little bit about why you’re interested in Software for Good, including any personal causes or experience that draw you to for-good work (we may reach out for future projects based on your particular interest in the client’s mission)", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://softwareforgood.com/job-post-contract-front-end-developer/", "company_id": 3329, "source": 3, "skills": "", "title": "Job Post: Contract Front-End Developer", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://softwareforgood.com/job-post-contract-front-end-developer/", "description": "At Software for Good, we believe that technology should be built with love. We bring people and technology together to solve complex world problems like climate change, housing, health care, human rights, renewable energy, and education. We are a mission-driven team of technologists building Web and mobile applications with clients from startups to social enterprises to nonprofits to educational and government entities. We have a deep commitment to the people we serve. We want to meet you! We are looking to connect with front-end developers who want to put their skills to use building technology that helps people live more fully and freely. We are currently seeking collaborators for potential long-term contracts who share our values and can partner with our clients to harness the power of technology for shared abundance and liberation. Working with Software for Good means the opportunity to provide our clients thoughtful guidance on the impacts of technology—intended and unintended—and finding the most effective ways to reach their goals. We’ve partnered with some amazing organizations to build software to connect people to housing, deliver mental health care online, transform food systems, and more. You can see some of our past projects here. Some of the skills we’re looking for include: • WordPress and/or Drupal development • React or React Native development • TypeScript • Tailwind CSS or other CSS frameworks • Expertise in building accessible experiences following WCAG standards • Experience creating responsive websites that put users’ needs first • Close collaboration with clients to understand their goals, make their ideas tangible, and make sure functionality aligns with the project’s strategic vision We’re looking for new skills and perspectives to add to our network. We strive to include people from underrepresented backgrounds, and would love to know what specific talents you could bring to our mission-driven client projects. We work fully remotely. Generally, we extend the opportunity for work on an as-needed basis, and aren’t able to guarantee a regular number of hours in advance. For legal and payment reasons, we are only able to work with people authorized to work in the United States. Our contracting rates are $75–$115/hour based on level of experience. Wondering if this opportunity is a good fit for you? We think we have a lot to offer, including the chance to do meaningful work alongside people who really care and a positive work environment with a fully remote team. Tell us why you’re interested in the role by sending the following to jobs@softwareforgood. com: • Your resume or portfolio • Contractor rate • A little bit about why you’re interested in Software for Good, and how you can contribute to our mission Our team will follow up with you to learn more and share potential opportunities.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://softwareforgood.com/job-post-contract-mobile-developer/", "company_id": 3329, "source": 3, "skills": "", "title": "Job Post: Contract Mobile Developer", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://softwareforgood.com/job-post-contract-mobile-developer/", "description": "At Software for Good, we believe that technology should be built with love. We bring people and technology together to solve complex world problems like climate change, housing, health care, human rights, renewable energy, and education. We are a mission-driven team of technologists building Web and mobile applications with clients from startups to social enterprises to nonprofits to educational and government entities. We have a deep commitment to the people we serve. We want to meet you! We are looking to connect with mobile developers who want to put their skills to use building technology that helps people live more fully and freely. We are currently seeking collaborators for potential long-term contracts who share our values and can partner with our clients to harness the power of technology for shared abundance and liberation. Working with Software for Good means the opportunity to provide our clients thoughtful guidance on the impacts of technology—intended and unintended — and finding the most effective and ethical ways to reach their goals. We’ve partnered with some amazing organizations to build software to connect people to housing, deliver mental health care online, transform food systems, and more. You can see some of our past projects here. Some of the skills we’re looking for include: • iOS and Android native development • React Native and Expo development • Experience creating responsive applications that put users’ needs first • Close collaboration with clients to understand their goals, make their ideas tangible, and make sure functionality aligns with the project’s strategic vision • Evaluating products and features for inclusion and accessibility We’re looking for new skills and perspectives to add to our network. We strive to include people from underrepresented backgrounds, and would love to know what specific talents you could bring to our mission-driven client projects. We work fully remotely. Generally, we extend the opportunity for work on an as-needed basis, and aren’t able to guarantee a regular number of hours in advance. For legal and payment reasons, we are only able to work with people authorized to work in the United States. Our contracting rates are $75–$115/hour based on level of experience. Wondering if this opportunity is a good fit for you? We think we have a lot to offer, including the chance to do meaningful work alongside people who really care and a positive work environment with a fully remote team. Tell us why you’re interested in the role by sending the following to jobs@softwareforgood. com: • Your resume and/or portfolio • Contractor rate • A little bit about why you’re interested in Software for Good, and how you can contribute to our mission Our team will follow up with you to learn more and share potential opportunities.", "ctc": null, "currency": null, "meta": {}}]