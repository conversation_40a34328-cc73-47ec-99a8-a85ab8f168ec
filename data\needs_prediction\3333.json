[{"jd_link": "https://recruiting.paylocity.com/Recruiting/Jobs/Details/2196308", "company_id": 3333, "source": 3, "skills": "", "title": "Software Engineering Team", "location": "Kansas City Metro, KS", "location_type": "hybrid", "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://recruiting.paylocity.com/Recruiting/Jobs/Apply/2196308", "description": "Apply Job Type Full-time Description Leawood, KS or Denver, CO 27Global is a rapidly growing company in the dynamic industry of software, cloud, and data engineering. We pride ourselves on the quality of the services we deliver, the clients we serve, and the strength of our culture. Our commitment to our employees is evidenced by our consecutive Best Places to Work awards. 27Global is always on the lookout for talented individuals to join our team. Regardless of our current job openings, we’re constantly planning for future work so we encourage you to submit your resume for consideration. As a member of 27Global’s Software Engineering Team, you’ll play a crucial role in helping bring custom software solutions to our clients. Our Software Engineering team is composed of skilled Technical Architects and Software Engineers who design and develop highly complex applications with the goal of streamlining business processes and improving overall user experience for our clients and their clients. If you’re interested in gaining exposure to new and exciting projects in a wide variety of industries using cutting-edge technology stacks, creating a competitive advantage for our clients, and working at an award-winning Best Place to Work, then submit your application today, and let's embark on this journey together! Requirements What you bring: 5+ years of full stack development experience leveraging tools such as . Net or other OOP languages and full stack JavaScript frameworksExperience with modern web authentication and authorization protocols for end users and system actorsExperience coordinating with and/or managing offshore software development teams, including expertise in communicating tech designs using a variety of forms such as diagrams, workflows, component interaction, verbal and writtenExperience working in n-tier, decoupled, micro-service, and cloud-native architectures (AWS/Azure)Experience with domain driven designExperience implement and maintain data solutions according to the design specifications in cloud platforms (Azure or AWS) using a variety of resources such as DataBricks, Azure Data Factory, AWS Glue, Lambda Functions, Azure Synapse, Redshift, Power BI, Tableau, SQL, NoSQL, Data Lake, etcWays to stand out: Experience working in an IT Consulting organizationExperience creating custom software solutionsHands-on data pipeline experience with Azure/AWS Experience in data streaming such as Kafka and Spark Streaming Familiar with messaging queues: ActiveMQ or RabbitMQ or similarUnderstanding of big data concepts and knowledge of big data languages/tools such as Hadoop, Kotlin, Spark, GraphDBMobile development experienceWhy 27G? :Four-time award winner of Best Place to Work by the Kansas City Business Journal A casual and fun small business work environment Competitive compensation, benefits, time off, profit sharing, and quarterly bonus potentialCommitment to continued learning through development, research, and certificationsAt 27Global, we follow a hybrid work model, allowing employees to work both remotely and in-office as needed, therefore, candidates should be located in the Kansas City or Denver Metro areas. Also, please note that currently we do not sponsor immigration visas for prospective employees. We appreciate your understanding and thank you for considering 27Global as a potential workplace.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://recruiting.paylocity.com/Recruiting/Jobs/Details/2208765", "company_id": 3333, "source": 3, "skills": "", "title": "Cloud Engineering Team", "location": "Kansas City Metro, KS", "location_type": "hybrid", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://recruiting.paylocity.com/Recruiting/Jobs/Apply/2208765", "description": "Apply Job Type Full-time Description Leawood, KS or Denver, CO 27Global is a rapidly growing company in the dynamic industry of software, cloud, and data engineering. We pride ourselves on the quality of the services we deliver, the clients we serve, and the strength of our culture. Our commitment to our employees is evidenced by our consecutive Best Places to Work awards. 27Global is always on the lookout for talented individuals to join our team. Regardless of our current job openings, we’re constantly planning for future work so we encourage you to submit your resume for consideration. As a member 27Global’s Cloud Engineering Team, you’ll play a key role in helping our clients modernize their IT infrastructure and help them pursue next-generation cloud innovation. Our Cloud Engineering team is composed of skilled Cloud Architects, Cloud Engineers, and Site Reliability Engineers and are committed to keeping up with emerging technologies, platforms, and applications, to maintain our competitive advantage in the design, development, and implementation of large-scale projects in the cloud. If you’re interested in gaining exposure to new and exciting projects in a wide variety of industries using cutting-edge technology stacks, creating a competitive advantage for our clients, and working at an award-winning Best Place to Work, then submit your application today, and let's embark on this journey together! Requirements What you bring: Experience designing, building, and managing AWS or Azure cloud infrastructure at scale with Terraform. Experience with CI/CD Pipelines (Github Actions, Azure Devops, AWS Code Pipeline, GitLabCI)Experience configuring and troubleshooting networking, Windows/Linux OS, and various cloud services. Experience with writing and troubleshooting scripts (Python, Powershell, BASH)Demonstrated experience with containers, microservice architectures, and orchestration (Docker, Kubernetes)Experience with deploying/configuring management tools (Puppet, Chef, and Ansible)Experience with deploying/configuring relational and NoSQL databases (MSSQL, MySQL, PostgreSQL, MongoDB)Ways to stand out: Professional Services experienceExperience in a client-facing role, working directly with clients from multiple levels of the organization; often presenting and documenting client environment suggestions and improvementsExperience working with dispersed teams both on-shore and off-shoreExperience with cloud security, such as setting security controls (security groups, profiles, permissions, and key management strategy). Why 27G? :Four-time award winner of Best Place to Work by the Kansas City Business Journal A casual and fun small business work environment Competitive compensation, benefits, time off, profit sharing, and quarterly bonus potentialCommitment to continued learning through development, research, and certificationsAt 27Global, we follow a hybrid work model, allowing employees to work both remotely and in-office as needed, therefore, candidates should be located in the Kansas City or Denver Metro areas. Also, please note that currently we do not sponsor immigration visas for prospective employees. We appreciate your understanding and thank you for considering 27Global as a potential workplace.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://recruiting.paylocity.com/Recruiting/Jobs/Details/2208736", "company_id": 3333, "source": 3, "skills": "", "title": "Consulting Team", "location": "", "location_type": "hybrid", "job_type": "full_time", "min_experience": 2, "max_experience": null, "apply_link": "https://recruiting.paylocity.com/Recruiting/Jobs/Apply/2208736", "description": "Apply Job Type Full-time Description Leawood, KS or Denver, CO 27Global is a rapidly growing company in the dynamic industry of software, cloud, and data engineering. We pride ourselves on the quality of the services we deliver, the clients we serve, and the strength of our culture. Our commitment to our employees is evidenced by our consecutive Best Places to Work awards. 27Global is always on the lookout for talented individuals to join our team. Regardless of our current job openings, we’re constantly planning for future work so we encourage you to submit your resume for consideration. As a member of 27Global's Consulting Team, you’ll be working with both internal and client teams in a fast-paced and exciting project environment. Our Consulting Team is composed of Consultants, Senior Consultants, Managers, and Directors who all have experience in software development. They remain the main point of contact for our clients throughout the project lifecycle, translating their designs into technical specifications for web and mobile applications using an Agile software development methodology. If you’re interested in gaining exposure to new and exciting projects in a wide variety of industries using cutting-edge technology stacks, creating a competitive advantage for our clients, and working at an award-winning Best Place to Work, then submit your application today, and let's embark on this journey together! Requirements What you bring: 2+ years of development experience leveraging tools such as: Modern programming languages (e. g. C#, JavaScript) Scripting languages (PowerShell, Bash, etc. ) Databases, SQL, queries, and data normalization Experience working for a Consulting firm, creating custom software solutionsExperience driving the Agile software development process by leveraging product backlog, retrospectives, status reports and testingDemonstrated ability to build strong client relationships, set realistic expectations and effectively communicate complex or technical issues to both technical and non-technical audiencesExperience working in a team environment in a fast-paced project environment, including the ability to coordinate software design, development, and testing between onshore and offshore teamsWays to stand out: Experience in UI/UX design and/or development User Research Wireframing/Prototyping Storyboarding Usability and Accessibility Testing Experience using design tools such as Sketch, Adobe XD, Figma, or similarStrong understanding of user-centered design principles. Experience with HTML, CSS, and basic front-end development conceptsExperience with micro-service, and cloud-native architecturesExperience with CI/CD tools and DevOps principlesWhy 27G? :Four-time award winner of Best Place to Work by the Kansas City Business Journal A casual and fun small business work environment Competitive compensation, benefits, time off, profit sharing, and quarterly bonus potentialCommitment to continued learning through development, research, and certificationsAt 27Global, we follow a hybrid work model, allowing employees to work both remotely and in-office as needed, therefore, candidates should be located in the Kansas City or Denver Metro areas. Also, please note that currently we do not sponsor immigration visas for prospective employees. We appreciate your understanding and thank you for considering 27Global as a potential workplace.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://recruiting.paylocity.com/Recruiting/Jobs/Details/3380582", "company_id": 3333, "source": 3, "skills": "", "title": "Consultant", "location": "", "location_type": "onsite", "job_type": "contract", "min_experience": 2, "max_experience": null, "apply_link": "https://recruiting.paylocity.com/Recruiting/Jobs/Apply/3380582", "description": "Apply Description 27Global is a rapidly growing company in the dynamic industry of software, cloud, and data engineering. We pride ourselves on the quality of the services we deliver, the clients we serve, and the strength of our culture. Our commitment to our employees is evident by our consecutive Best Places to Work awards. As a 27Global Consultant, you’ll be responsible for working with our clients to design custom software solutions, translating their designs into technical specifications for web and mobile applications using an Agile software development methodology. You’ll work independently and with teams in a fast-paced and exciting project environment. You can expect exposure to new and exciting projects for clients in a wide variety of industries using cutting-edge technology stacks, a culture of continuous improvement, and abundant opportunities to grow your skills and career. Consultants at 27Global are energetic, ambitious, and expected to rise through the ranks of Consultant, Sr. Consultant, and Lead Consultant and/or Consulting Manager as they build their skills and expertise. Joining 27Global as a Consultant is an exciting high-growth opportunity offering a competitive base salary, performance bonuses, and variable compensation. Your Role: Guide clients in the definition of software solutions and translate into technical specifications in a Web and/or Mobile development environmentGrow and practice skills in UI/UX, data engineering and analytics, application architecture, cloud engineering, and other critical subjects while defining product requirements for client stakeholdersBecome an expert at driving an Agile software development process by leveraging the product backlog, retrospectives, status reports, testing, etc. Maintain excellent client relationships by taking ownership of product quality and project status in each part of the SDLCDevelop subject matter expertise in the functional domain of client projectsDefine and perform tests for software quality assuranceCoordinate software design, development, and testing between onshore and offshore teamsWork independently and in teams in a fast-paced and sometimes ambiguously defined project environmentWhat you bring: Bachelor’s of Science degree in Computer Science, Information Technology, or Engineering2+ years of application development experiencePractical knowledge of at least one of the following: Modern programming languages (e. g. C#, JavaScript) Scripting languages (e. g. PowerShell, Bash, etc. ) Databases, SQL, queries, and data normalization Experience with Agile development methodologiesExcellent communication skills, written and verbalAbility to context switch and work on a variety of projects over specified periods of time Ability to work at onsite 27Global offices in either Leawood, KS or Denver, CO with hybrid work flexibility after 90 days, and occasionally onsite at client officesFlexibility for occasional travel to client sites may be required, typically 1 week per quarter or lessLegal authorization to work in the United States and the ability to prove eligibility at the time of hireWays to stand out: Experience coordinating with and/or managing offshore software development teamsExperience in UI/UX design and/or development User Research Wireframing/Prototyping Storyboarding Usability and Accessibility Testing System Integrator and/or Software Company experienceExperience with Service Oriented and/or Micro-Service architecturesExperience with AWS, Azure clouds and/or cloud-native architecturesExperience with Databricks, Snowflake, AWS Glue, Azure Fabric, and other modern data toolsExperience with CI/CD tools and DevOps principlesWhy 27G? :Four-time award winner of Best Place to Work by the Kansas City Business Journal A casual and fun small business work environment Competitive compensation, benefits, time off, profit sharing, and quarterly bonus potentialDedicated time for learning, development, research, and certifications", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://recruiting.paylocity.com/Recruiting/Jobs/Details/3369920", "company_id": 3333, "source": 3, "skills": "", "title": "Cloud Engineer II", "location": "Leawood, KS", "location_type": "hybrid", "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://recruiting.paylocity.com/Recruiting/Jobs/Apply/3369920", "description": "Apply Job Type Full-time Description Leawood, KS or Denver, CO 27Global is a rapidly growing company in the dynamic industry of software, cloud, and data engineering. We pride ourselves on the quality of the services we deliver, the clients we serve, and the strength of our culture. Our commitment to our employees is evidenced by our consecutive Best Places to Work awards. As a member of 27Global's Cloud Engineering Team, you’ll play a key role in helping our clients modernize their IT infrastructure and help them pursue next-generation cloud innovation. Our Cloud Engineering team is composed of skilled Cloud Architects, Cloud Engineers, and Site Reliability Engineers with a commitment to keeping up with emerging technologies, platforms, and applications, to maintain our competitive advantage in the design, development, and implementation of large-scale projects in the cloud. As Cloud Engineer II, you’ll be serving as an escalation point for complex issues, while working closely with onshore and offshore engineers to design, plan, and implement cloud solutions. You will work under the guidance of senior engineers and architects to help build secure, scalable, and high-performing cloud environments. This role combines deep technical expertise with an emphasis on project leadership responsibilities. Joining 27Global as a Cloud Engineer is an exciting high-growth opportunity offering a competitive base salary, performance bonuses, and variable compensation. Key ResponsibilitiesCollaborate with cross-functional teams (onshore/offshore) to implement and maintain scalable and cost-optimized infrastructure in cloud environments. Deploy and maintain infrastructure-as-code using tools like Terraform. Support implementation of CI/CD pipelines and participate in troubleshooting deployment issues. Participate in client calls and provide technical insights on project progress. Assist in documenting cloud architectures and operational procedures. Participate in the on-call rotation to provide SRE support for production environments. What you bring: 5+ years of experience with cloud infrastructure (preferably AWS or Azure). Hands-on experience with Terraform and CI/CD tools (GitHub Actions, Azure DevOps, etc. ). Experience configuring Linux/Windows servers and core networking components. Experience writing scripts in Python, PowerShell, or BASH. Experience with a variety of containerization and orchestration tools (Docker, Kubernetes). Understanding of both relational and NoSQL databases (MySQL, PostgreSQL, MongoDB, etc. ). Effective communication skills with internal and client-facing teams. Ways to stand out:Experience in consulting or professional services. Familiarity with Databricks or data pipeline tools. Experience with compliance frameworks and cloud governance practices. Experience with presenting cloud solutions to technical and non-technical customers and internal audiences. Experience in a client-facing role, working directly with clients from multiple levels of the organization; often presenting and documenting client environment suggestions and improvements. Experience with cloud security, such as setting security controls (security groups, profiles, permissions, and key management strategy). Relevant certifications (eg. AWS Solutions Architect Associate, AWS DevOps Engineer, Azure Administrator Associate, Azure DevOps Engineer). Why 27G? :Four-time award winner of Best Place to Work by the Kansas City Business Journal A casual and fun small business work environment Competitive compensation, benefits, time off, profit sharing, and quarterly bonus potentialCommitment to continued learning through development, research, and certificationsAt 27Global, we follow a hybrid work model, allowing employees to work both remotely and in-office as needed, therefore, candidates should be located in the Kansas City or Denver Metro areas. Also, please note that currently we do not sponsor immigration visas for prospective employees. We appreciate your understanding and thank you for considering 27Global as a potential workplace.", "ctc": null, "currency": null, "meta": {}}]