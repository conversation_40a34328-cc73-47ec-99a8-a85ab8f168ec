[{"jd_link": "https://www.webgentechnologies.com/jobs/android-application-developer/", "company_id": 3347, "source": 3, "skills": "", "title": "Android Application Developer", "location": "", "location_type": null, "job_type": "full_time", "min_experience": 2, "max_experience": 5, "apply_link": "https://www.webgentechnologies.com/jobs/android-application-developer/", "description": "Job Description Posting position Android Application Developer Job Description Position : Android Application Developer Experience: 2 years to 5 years Job Description: We are seeking a skilled Android Developer to join our team and contribute to designing, developing, and maintaining cutting-edge mobile applications. The ideal candidate will play a key role in building high-performance, user-friendly apps while ensuring the highest quality and functionality. Key Responsibilities: Research, design, implement, and manage software programs for Android platforms. Evaluate new programs and suggest innovative solutions. Identify areas for improvement in existing applications and develop required modifications. Write and implement efficient, maintainable, and reusable code. Assess and determine the operational practicality of software solutions. Deploy software tools, processes, and metrics to improve efficiency. Maintain and upgrade existing systems to meet the evolving business and technical needs. Skills and Qualifications: Strong knowledge of Android SDK and handling diverse screen sizes. Proficiency in Android UI design principles and best practices. Solid understanding of mobile testing methodologies and practices. Experience in multithreading and concurrent programming. Comprehensive understanding of the mobile application lifecycle, including development, debugging, performance tuning, and deployment. Proficiency in Java and/or Kotlin. Strong problem-solving skills and attention to detail. Additional Preferences: Passion for creating innovative applications that enhance the user experience. Excellent communication and teamwork skills. Familiarity with Agile methodologies is a plus. <PERSON><PERSON> set Android Java / Kotlin Mobile Application lifecycle Qualification requirements B-Tech, M-Tech, BCA, MCA, Diploma in computer Science, Others Employment status Full-time Experience 2-5 Years", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.webgentechnologies.com/jobs/flutter-developer/", "company_id": 3347, "source": 3, "skills": "", "title": "Flutter Developer", "location": "", "location_type": null, "job_type": "full_time", "min_experience": 2, "max_experience": 5, "apply_link": "https://www.webgentechnologies.com/jobs/flutter-developer/", "description": "Job Description Posting position Flutter Developer Job Description Position : Flutter Developer Experience: 2 years to 5 years Primary Skills: Flutter, Dart, Familiarity with mobile app development (Android and/or iOS),third-party libraries and APIs About the Role: We are seeking a talented Flutter Developer with 2-5 years of experience to join our team. In this role, you’ll work closely with our design and development teams to build, maintain, and enhance mobile applications. You’ll have the opportunity to expand your skill set while contributing to exciting projects in a collaborative environment. Responsibilities: Develop and maintain cross-platform mobile applications for Android and iOS using Flutter. Collaborate with designers and back-end developers to create seamless user experiences. Optimize app performance and troubleshoot issues to ensure smooth functionality. Write clean, maintainable, and well-documented code. Implement new features, bug fixes, and enhancements based on client feedback. Conduct unit and integration testing to ensure app stability and quality. Requirements: 2-5 years of experience with Flutter and Dart programming language. Familiarity with mobile app development (Android and/or iOS). Understanding of the entire mobile development life cycle. Basic knowledge of REST APIs and integrating back-end services with mobile apps. Proficiency with Git and version control systems. Problem-solving mindset, and ability to adapt to changing requirements. Strong communication skills and ability to work in a team. Preferred Skills (Nice-to-Have): Experience with third-party libraries and APIs. Familiarity with Agile development methodologies. Knowledge of UI/UX design principles. Skill set Agile Development Android/iOS APIs Dart Flutter Qualification requirements B-Tech, M-Tech, BCA, MCA, Diploma in computer Science, Others Employment status Full-time Experience 2-5 Years", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.webgentechnologies.com/jobs/ios-developer/", "company_id": 3347, "source": 3, "skills": "", "title": "iOS Developer", "location": "", "location_type": null, "job_type": "full_time", "min_experience": 2, "max_experience": 6, "apply_link": "https://www.webgentechnologies.com/jobs/ios-developer/", "description": "Job Description Posting position iOS Developer Job Description Webgen Technologies is looking for an iOS developer who possesses a passion for pushing mobile technologies to the limits and will work with our team of talented professionals to design and build the next generation of the mobile applications. Position: iOS Developer Responsibilities: • Designing and building advanced applications for the iOS platform • Collaborating with cross-functional teams to define, design, and ship new features. • Work on bug fixing and improving application performance. Skills: • Knowledge of Swift and Objective C • Experience working with iOS frameworks such as Core Data, Core Animation, Core Graphics and Core Text • Experience with third-party libraries and APIs • Solid understanding of the full mobile development life cycle Skill set iOS iOS Development Objective C Swift Qualification requirements B-Tech, M-Tech, BCA, MCA, Diploma in computer Science, Others Employment status Full-time Experience 2-6 Years", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.webgentechnologies.com/jobs/ui-ux-designer/", "company_id": 3347, "source": 3, "skills": "", "title": "UI/UX Designer", "location": "", "location_type": null, "job_type": "full_time", "min_experience": 2, "max_experience": 8, "apply_link": "https://www.webgentechnologies.com/jobs/ui-ux-designer/", "description": "Job Description Posting position UI/UX Designer Job Description Webgen Technologies is looking for UI/UX Designer with following skills sets: Job Roles and Requirements: Can Identify the brand and the user. Can Research on customers requirement . Examine what you discovered. Come up with UI and UX strategies based on our target goals. Create and maintain digital assets, such as interface design files, wireframes, and interactive mockups using {{design and prototyping tools: e. g. , Sketch and Figma ) Make prototypes, wireframes, or site maps. Make use of user flow . Deliver the design answer to the customer or business to start Development Adopt a pleasing user interface to guarantee that web applications and other projects provide a great user experience. Consider the user experience when developing web applications, pages, and user interface designs. They are proficient programmers who work with HTML and graphic design software like Adobe Illustrator. Skills Requirements: • A bachelor’s degree and a minimum of 2 years UI/UX design experience for digital products or services. • A portfolio of professional UI/UX design work for both web and mobile platforms. • Working knowledge of the following technologies and software: Figma / Adobe XD, User Flow, High/Low-fidelity Wireframe, Prototyping, Illustrator, Photoshop •A team player but can work independently too . • Excellent written and verbal communication skills . • Competitor research and Problem solving skills through user experience Skill set Figma / Adobe XD High/Low-fidelity Wireframe Illustrator Photoshop Prototyping User Flow Qualification requirements B-Tech, M-Tech, BCA, MCA, Diploma in computer Science, Others Employment status Full-time Experience 2-8 Years", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.webgentechnologies.com/jobs/web-and-technical-content-writer/", "company_id": 3347, "source": 3, "skills": "", "title": "Web Content Writer", "location": "", "location_type": null, "job_type": "full_time", "min_experience": 2, "max_experience": 8, "apply_link": "https://www.webgentechnologies.com/jobs/web-and-technical-content-writer/", "description": "Job Description Posting position Web Content Writer Job Description We are looking for a talented and creative Web Content Writer to join our team. As a Web Content Writer, you will be responsible for creating engaging, informative, and high-quality technical content for our websites. You will work closely with our marketing team and other departments to ensure that our websites are filled with relevant, up-to-date, and informative content that engages our target audience. Responsibilities Create high-quality, engaging, and technical content for websites, including blog posts, product descriptions, landing pages, and more. Collaborate with the marketing team to develop content strategies aligned with business goals and the target audience. Conduct thorough research on various topics and industries to ensure accuracy and relevance in content. Optimize website content for search engines (SEO) to drive organic traffic. Edit and proofread content for accuracy, clarity, and consistency. Stay updated with industry trends and best practices in content writing and SEO. Work closely with other departments to ensure content aligns with the brand’s voice and messaging. Requirements Proven experience as a Web Content Writer or in a similar role. Strong understanding of Web3 and blockchain concepts. Exceptional writing, editing, and proofreading skills with keen attention to detail. Hands-on experience with SEO and content optimization techniques. Familiarity with content management systems (CMS) and web publishing platforms. Ability to conduct thorough research across diverse topics and industries. Excellent time management skills with the ability to prioritize tasks and meet deadlines. Strong communication skills and a collaborative mindset to work with cross-functional teams. <PERSON><PERSON> set Content marketing Content strategy Content writing Copywriting Proofreading SEO Qualification requirements B-Tech, M-Tech, BCA, MCA, Diploma in computer Science, Others Employment status Full-time Experience 2-8 Years", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.webgentechnologies.com/jobs/web-designer-developer/", "company_id": 3347, "source": 3, "skills": "", "title": "Web Designer/Developer", "location": "", "location_type": null, "job_type": "full_time", "min_experience": 2, "max_experience": 8, "apply_link": "https://www.webgentechnologies.com/jobs/web-designer-developer/", "description": "Job Description Posting position Web Designer/Developer Job Description About the Role: We are seeking a creative and detail-oriented Web Designer to join our team. In this role, you will design and develop websites that are visually appealing, user-friendly, and aligned with our clients’ requirements. Collaborating closely with developers and marketers, you’ll create high-quality web solutions using HTML, CSS, JavaScript, and WordPress. Key Responsibilities: Design and develop responsive, user-focused websites using HTML, CSS, and JavaScript. Customize and enhance WordPress themes and plugins to deliver tailored solutions. Collaborate with the development team to implement responsive design principles. Optimize websites for speed, performance, and compatibility across devices and browsers. Stay updated on industry trends and emerging technologies to ensure innovative solutions. Conduct rigorous testing and debugging to maintain high-quality performance. Communicate with clients and stakeholders to understand and translate their requirements into effective web designs. Qualifications and Skills: Proven experience as a Web Designer or in a similar role. Proficiency in HTML, CSS, JavaScript, and WordPress. Solid understanding of responsive design principles and best practices. Familiarity with Adobe Creative Suite (Photoshop, Illustrator) or similar design tools is a plus. Experience in creating wireframes, mockups, and prototypes to visualize concepts. Excellent problem-solving skills with keen attention to detail. Strong communication and team collaboration abilities. Capable of managing multiple projects independently while meeting deadlines. <PERSON><PERSON> set Adobe Creative Suite CSS HTML Javascript Responsive Design WordPress Qualification requirements B-Tech, M-Tech, BCA, MCA, Diploma in computer Science, Others Employment status Full-time Experience 2-8 Years", "ctc": null, "currency": null, "meta": {}}]