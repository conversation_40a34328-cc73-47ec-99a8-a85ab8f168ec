[{"jd_link": "https://xenonstack.jobs/jobs/security-engineer-azure", "company_id": 3348, "source": 3, "skills": "", "title": "Security Engineer- Azure", "location": "<PERSON><PERSON>", "location_type": "onsite", "job_type": null, "min_experience": 1, "max_experience": 2, "apply_link": "https://xenonstack.jobs/jobs/security-engineer-azure", "description": "Job Summary XenonStack’s SecOps team is looking for an experienced and knowledgeable System Security Engineer professional to join our SecOps team for preventing and mitigating security breaches that may arise within our company's computer systems. How You can help us - As a System Security Engineer, you will be responsible for planning, implementing and managing the overall system security strategy. Your focus will be on Network Security, Application Security and Infrastructure Security. Here’s why you’ll love working with us - ● Purposeful work culture and people oriented organization ● Exposure to working on enterprise national and international $million projects ● Complete job and employee security ● Warm, authentic and transparent communication ● Time to time jon feedback and exposure to different technologies ● Appreciation and rewards on performance achievements. Key Responsibilities - ● Work with the team to plan, prepare, execute, and summarize the security testing ● Review and correlate security logs ● Work with a team in delivering and implementing consistent test disciplines and processes using associated best practices across the program. ● Identify security vulnerabilities and issues, perform risk assessments, and evaluate remediation alternatives ● Artful communication skills and organizational savvy, to steer peers and leadership toward solutions that carefully balance business, risk, compliance, and engineering concerns. ● Eagerness to challenge the status quo, balanced with a reasonable and methodical approach to effecting change. ● Security testing- Penetration security/ Infrastructure security/ cloud security/Devsecops security/ Application security ● Collaborate and consult with peers, colleagues and managers to resolve issues. Technical Requirements - ● Understanding of security practices of various CSP(Cloud Service Provider). ● Understanding of Web Application n-tier architectures, threat modeling and secure coding practices. ● Understanding of security vulnerabilities and remediation detailed by organizations like OWASP, SANS, etc. ● Experience assessing Application Security - - development, implementation and maintenance of Application Security posture in an enterprise organization. ● Identifying, measuring and detailing risks and recommendations on the security controls ● Ability to analyze vulnerabilities appropriately characterizes threats, and provide sound remediation advice ● Familiarity with commercial testing applications (i. e. Burp, dbProtect, Acunetix, SonarQube) ● Knowledge of network protocols and network monitoring like \"sniffing\" (e. g. Wireshark, tcpdump) ● Knowledge of tools used for Thick clients, web application, and mobile security testing. ● Experience in SIEM and SOC solutions. ● Experience in two or more of the following: Rapid7 InsightVM, Crowdstrike, ProofPoint, Nexpose, Nessus, Qualys, Splunk, Endpoint Security, etc. ● Coding/scripting experience (Python, Ruby, C, Assembly, Bash, PowerShell, etc. ) Professional Attributes - ● Excellent communication skills ● Attention to detail ● Analytical mind and Problem Solving Aptitude ● Strong Organizational skills ● Visual Thinking Education : B. E/B. Tech in Computer Science or a related technical degree or M. S/M. Tech in Information Security. Experience : 1-2 years Location: Plot No. C-184, Sixth Floor 603 , Sector 75 Phase VIIIA, Mohali 160071 Work Engagement: In-Office Keywords AZUREWiresharkSecurity VulnerabilitiesSonarQubeCoding/Scripting experienceBurp SuiteSIEMSPLUNKSOC SolutionsBash ScriptingCSP Opportunities for Students & Fresh Graduates At Xenonstack, our interns, fresh graduates and early-career hires play a pivotal role in shaping the future of our company through their innovation and new approaches. Learn More Work at XenonStack We acknowledge individuals who wish to explore their capabilities with XenonStack, further adding value to our teams. Job Openings At XenonStack, We are committed to becoming the Most Value-Driven Cloud Native, Platform Engineering, and Decision Driven Analytics Company. At XenonStack, We have well-defined Career Progression Plans and Skill Development Program for an Individual to provide the best Launch Pad for the Career. Explore Us About Us Clients and Partners Reach Us Blogs Insights Contact Us function jobOpening(){ window. location. href='https://talent. xenonstack. com/jobs/Careers'; }", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://xenonstack.jobs/jobs/research-associate", "company_id": 3348, "source": 3, "skills": "", "title": "Research Associate", "location": "<PERSON><PERSON>", "location_type": "onsite", "job_type": null, "min_experience": 2, "max_experience": 2, "apply_link": "https://xenonstack.jobs/jobs/research-associate", "description": "Job Summary Looking for a Research graduate for working on AI ethics and Responsible AI to research on different aspects of AI Implementation. At XenonStack, We committed to become the Most Value Driven Cloud Native, Platform Engineering and Decision Driven Analytics Company. Our Consulting Services and Solutions towards the Neural Company and its Key Drivers. Here's why you'll love working with us - Purposeful work culture and people-oriented organization Exposure to working on enterprise national and international $million projects Complete job and employee security Warm, authentic and transparent communication From Time to time on feedback and exposure to different technologies Appreciation and rewards for performance achievements. How can you help? To be successful in this role, you must have strong research/ analytical skills, Problem-solving skills and be a self-starter with the ability to work independently. The individual we seek is adaptive and Curious to learn new tools and technologies. Job Responsibilities - Research on AI implementation as per Ethical and responsible framework Analysing Different Open SourceFrameworks for identifying bias and explainability of models Analysing data, gathering and comparing resources. Ensuring facts, and sharing findings with the whole research team. Adhering to required methodologies, performing fieldwork as needed, and keeping critical information confidential. Professional Attribute - Working on globally distributed teams Excellent Communication Skills Analytical mind and Problem-Solving Aptitude Ability to handle multiple tasks simultaneously Excellent problem-solving skills, along with the ability to work independently Education - PG: M. Tech in Any Specialization Doctorate: Ph. D/Doctorate in Any Specialization Minimum Masters in Technology and PhD graduates preferred Experience : 0 - 2 years Location: Plot No. C-184, Sixth Floor 603 , Sector 75 Phase VIIIA, Mohali 160071 Work Engagement: In-Office Keywords Research and DevelopmentStrong Communication SkillsStrategic ThinkingResearch AnalysisAnalytical SkillsProblem SolvingResearch Opportunities for Students & Fresh Graduates At Xenonstack, our interns, fresh graduates and early-career hires play a pivotal role in shaping the future of our company through their innovation and new approaches. Learn More Work at XenonStack We acknowledge individuals who wish to explore their capabilities with XenonStack, further adding value to our teams. Job Openings At XenonStack, We are committed to becoming the Most Value-Driven Cloud Native, Platform Engineering, and Decision Driven Analytics Company. At XenonStack, We have well-defined Career Progression Plans and Skill Development Program for an Individual to provide the best Launch Pad for the Career. Explore Us About Us Clients and Partners Reach Us Blogs Insights Contact Us function jobOpening(){ window. location. href='https://talent. xenonstack. com/jobs/Careers'; }", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://xenonstack.jobs/jobs/site-reliability-engineer-azure", "company_id": 3348, "source": 3, "skills": "", "title": "Site Reliability Engineer - Azure", "location": "<PERSON><PERSON>", "location_type": null, "job_type": null, "min_experience": 2, "max_experience": 2, "apply_link": "https://xenonstack.jobs/jobs/site-reliability-engineer-azure", "description": "Job Summary At XenonStack, We are committed to becoming the Most Value Driven Cloud Native, Platform Engineering and Decision Driven Analytics Company. Our Consulting Services and Solutions towards the Neural Company and its Key Drivers. Job Summary At Xenonstack , We are Building the World’s Largest site reliability engineers (SREs) team for DataOps, ModelOps and application Management to empower our users with a rich feature set, high availability, and stellar performance level to pursue their missions. Job Summary At Xenonstack , We are Building the World’s Largest site reliability engineers (SREs) team for DataOps, ModelOps and application Management to empower our users with a rich feature set, high availability, and stellar performance level to pursue their missions. As we expand our customer deployments, XenonStack SRE Team is currently seeking an experienced SRE to deliver insights from massive scale data in real time. Specifically, we are searching for someone who brings fresh ideas, demonstrates a unique and informed viewpoint, and enjoys collaborating with a cross-functional team to develop real-world solutions and positive user experiences at every interaction. Job Responsibilities Run the production environment by monitoring availability and taking a holistic view of system health Build software and systems to manage platform infrastructure and applications Improve reliability, quality, and time-to-market of our suite of software solutions Measure and optimize system performance, with an eye toward pushing our capabilities forward, getting ahead of customer needs, and innovating to continually improve Provide primary operational support and engineering for multiple large distributed software applications Gather and analyze metrics from both operating systems and applications to assist in performance tuning and fault finding Partner with development teams to improve services through rigorous testing and release procedures Participate in system design consulting, platform management, and capacity planning Create sustainable systems and services through automation and uplifts Balance feature development speed and reliability with well-defined service level objectives Technical Requirements Ability to program (structured and OO) with one or more high level languages, such as Python, Java, C/C++, Ruby, and JavaScript Experience with distributed storage technologies like NFS, HDFS, Ceph, S3 as well as dynamic resource management frameworks (Mesos, Kubernetes, Yarn) A proactive approach to spotting problems, areas for improvement, and performance bottlenecks Strong Hands on Experience on Linux or Windows Administrations Coding experience beyond simple scripts Strong Skills around release engineering and continuous delivery Professional Attributes Excellent Communication skills Attention to detail Analytical mind and Problem Solving Aptitude Strong Organizational Skills Visual Thinking Educational Qualification :Bachelor’s degree in computer science or other highly technical, scientific discipline Experience :1 - 2 years Keywords Capacity PlanningKubernetesPlatform ManagementSystem DesignAzure CloudC/C++LinuxPythonTestingJavaMesosReliabilityWindows AdministrationAzureJavaScriptMonitoringRuby Opportunities for Students & Fresh Graduates At Xenonstack, our interns, fresh graduates and early-career hires play a pivotal role in shaping the future of our company through their innovation and new approaches. Learn More Work at XenonStack We acknowledge individuals who wish to explore their capabilities with XenonStack, further adding value to our teams. Job Openings At XenonStack, We are committed to becoming the Most Value-Driven Cloud Native, Platform Engineering, and Decision Driven Analytics Company. At XenonStack, We have well-defined Career Progression Plans and Skill Development Program for an Individual to provide the best Launch Pad for the Career. Explore Us About Us Clients and Partners Reach Us Blogs Insights Contact Us function jobOpening(){ window. location. href='https://talent. xenonstack. com/jobs/Careers'; }", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://xenonstack.jobs/jobs/solution-architect-security-azure", "company_id": 3348, "source": 3, "skills": "", "title": "Solution Architect - Security- Azure", "location": "<PERSON><PERSON>", "location_type": "onsite", "job_type": null, "min_experience": 4, "max_experience": 6, "apply_link": "https://xenonstack.jobs/jobs/solution-architect-security-azure", "description": "Job Summary XenonStack’s SecOps team is looking for an experienced and knowledgeable System Security Engineer professional to join our SecOps team for preventing and mitigating security breaches that may arise within our company's computer systems. How You can help us - As a System Security Engineer, you will be responsible for planning, implementing and managing the overall system security strategy. Your focus will be on Network Security, Application Security and Infrastructure Security. Here’s why you’ll love working with us - ● Purposeful work culture and people oriented organisation ● Exposure to working on enterprise national and international $million projects ● Complete job and employee security ● Warm, authentic and transparent communication ● Time to time jon feedback and exposure to different technologies ● Appreciation and rewards on performance achievements. Key Responsibilities ● Work with the team to plan, prepare, execute, and summarize the security testing ● Review and correlate security logs ● Work with a team in delivering and implementing consistent test disciplines and processes using associated best practices across the program. ● Identify security vulnerabilities and issues, perform risk assessments, and evaluate remediation alternatives ● Artful communication skills and organizational savvy, to steer peers and leadership toward solutions that carefully balance business, risk, compliance, and engineering concerns. ● Eagerness to challenge the status quo, balanced with a reasonable and methodical approach to effecting change. ● Security testing- Penetration security/ Infrastructure security/ cloud security/Devsecops security/ Application security ● Collaborate and consult with peers, colleagues and managers to resolve issues. Technical Requirements - ● Understanding of security practices of various CSP(Cloud Service Provider). ● Understanding of Web Application n-tier architectures, threat modeling and secure coding practices. ● Understanding of security vulnerabilities and remediation detailed by organizations like OWASP, SANS, etc. ● Experience assessing Application Security - - development, implementation and maintenance of Application Security posture in an enterprise organization. ● Identifying, measuring and detailing risks and recommendations on the security controls ● Ability to analyze vulnerabilities appropriately characterizes threats, and provide sound remediation advice ● Familiarity with commercial testing applications (i. e. Burp, dbProtect, Acunetix, SonarQube) ● Knowledge of network protocols and network monitoring like \"sniffing\" (e. g. Wireshark, tcpdump) ● Knowledge of tools used for Thick clients, web application, and mobile security testing. ● Experience in SIEM and SOC solutions. ● Experience in two or more of the following: Rapid7 InsightVM, Crowdstrike, ProofPoint, Nexpose, Nessus, Qualys, Splunk, Endpoint Security, etc. ● Coding/scripting experience (Python, Ruby, C, Assembly, Bash, PowerShell, etc. ) Professional Attributes - ● Excellent communication skills ● Attention to detail ● Analytical mind and Problem Solving Aptitude ● Strong Organisational skills ● Visual Thinking Education : B. E/B. Tech in Computer Science or a related technical degree or M. Sc/M. Tech in Information Security. Experience : 4-6 years Location: Plot No. C-184, Sixth Floor 603 , Sector 75 Phase VIIIA, Mohali 160071 Work Engagement: In-Office Keywords Burp SuiteCSPSPLUNKAZUREWiresharkSIEMPython ScriptingSecurity VulnerabilitiesRubyPowershell ScriptingCloud SecurityBash ScriptingSonarqube Opportunities for Students & Fresh Graduates At Xenonstack, our interns, fresh graduates and early-career hires play a pivotal role in shaping the future of our company through their innovation and new approaches. Learn More Work at XenonStack We acknowledge individuals who wish to explore their capabilities with XenonStack, further adding value to our teams. Job Openings At XenonStack, We are committed to becoming the Most Value-Driven Cloud Native, Platform Engineering, and Decision Driven Analytics Company. At XenonStack, We have well-defined Career Progression Plans and Skill Development Program for an Individual to provide the best Launch Pad for the Career. Explore Us About Us Clients and Partners Reach Us Blogs Insights Contact Us function jobOpening(){ window. location. href='https://talent. xenonstack. com/jobs/Careers'; }", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://xenonstack.jobs/jobs/sr-security-engineer-azure", "company_id": 3348, "source": 3, "skills": "", "title": "Sr. Security Engineer- Azure", "location": "<PERSON><PERSON>", "location_type": "onsite", "job_type": null, "min_experience": 2, "max_experience": 4, "apply_link": "https://xenonstack.jobs/jobs/sr-security-engineer-azure", "description": "Job Summary XenonStack’s SecOps team is looking for an experienced and knowledgeable System Security Engineer professional to join our SecOps team for preventing and mitigating security breaches that may arise within our company's computer systems. How You can help us - As a System Security Engineer, you will be responsible for planning, implementing and managing the overall system security strategy. Your focus will be on Network Security, Application Security and Infrastructure Security. Here’s why you’ll love working with us - ● Purposeful work culture and people oriented organisation ● Exposure to working on enterprise national and international $million projects ● Complete job and employee security ● Warm, authentic and transparent communication ● Time to time jon feedback and exposure to different technologies ● Appreciation and rewards on performance achievements. Key Responsibilities - ● Work with the team to plan, prepare, execute, and summarise the security testing ● Review and correlate security logs ● Work with a team in delivering and implementing consistent test disciplines and processes using associated best practices across the program. ● Identify security vulnerabilities and issues, perform risk assessments, and evaluate remediation alternatives ● Artful communication skills and organizational savvy, to steer peers and leadership toward solutions that carefully balance business, risk, compliance, and engineering concerns. ● Eagerness to challenge the status quo, balanced with a reasonable and methodical approach to effecting change. ● Security testing- Penetration security/ Infrastructure security/ cloud security/Devsecops security/ Application security ● Collaborate and consult with peers, colleagues and managers to resolve issues. Technical Requirements - ● Understanding of security practices of various CSP(Cloud Service Provider). ● Understanding of Web Application n-tier architectures, threat modeling and secure coding practices. ● Understanding of security vulnerabilities and remediation detailed by organizations like OWASP, SANS, etc. ● Experience assessing Application Security - - development, implementation and maintenance of Application Security posture in an enterprise organization. ● Identifying, measuring and detailing risks and recommendations on the security controls ● Ability to analyze vulnerabilities appropriately characterizes threats, and provide sound remediation advice ● Familiarity with commercial testing applications (i. e. Burp, dbProtect, Acunetix, SonarQube) ● Knowledge of network protocols and network monitoring like \"sniffing\" (e. g. Wireshark, tcpdump) ● Knowledge of tools used for Thick clients, web application, and mobile security testing. ● Experience in SIEM and SOC solutions. ● Experience in two or more of the following: Rapid7 InsightVM, Crowdstrike, ProofPoint, Nexpose, Nessus, Qualys, Splunk, Endpoint Security, etc. ● Coding/scripting experience (Python, Ruby, C, Assembly, Bash, PowerShell, etc. ) Professional Attributes - ● Excellent communication skills ● Attention to detail ● Analytical mind and Problem Solving Aptitude ● Strong Organisational skills ● Visual Thinking Education : B. E/B. Tech in Computer Science or a related technical degree or M. S/M. Tech in Information Security. Experience : 2-4 years Location: Plot No. C-184, Sixth Floor 603 , Sector 75 Phase VIIIA, Mohali 160071 Work Engagement: In-Office Keywords SIEMBash ScriptingWiresharkCodingSPLUNKScriptingCSPPyhton ********************** SuitePowerShellAZURESecurity Vulnerabilities Opportunities for Students & Fresh Graduates At Xenonstack, our interns, fresh graduates and early-career hires play a pivotal role in shaping the future of our company through their innovation and new approaches. Learn More Work at XenonStack We acknowledge individuals who wish to explore their capabilities with XenonStack, further adding value to our teams. Job Openings At XenonStack, We are committed to becoming the Most Value-Driven Cloud Native, Platform Engineering, and Decision Driven Analytics Company. At XenonStack, We have well-defined Career Progression Plans and Skill Development Program for an Individual to provide the best Launch Pad for the Career. Explore Us About Us Clients and Partners Reach Us Blogs Insights Contact Us function jobOpening(){ window. location. href='https://talent. xenonstack. com/jobs/Careers'; }", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://xenonstack.jobs/jobs/solution-architect-site-reliability-engineer-azure", "company_id": 3348, "source": 3, "skills": "", "title": "Solution Architect - Site Reliability Engineer- Azure", "location": "<PERSON><PERSON>", "location_type": null, "job_type": null, "min_experience": 6, "max_experience": 6, "apply_link": "https://xenonstack.jobs/jobs/solution-architect-site-reliability-engineer-azure", "description": "Job Summary XenonStack’s SRE team is looking for a specialist who is responsible for availability, latency, performance, efficiency, change management, monitoring, emergency response, and capacity planning. ” Site reliability engineers create a bridge between development and operations by applying a software engineering mindset to system administration topics. Site Reliability Engineers Specialists are experts who handle the following responsibilities in a company: improving application lifecycle, evolving software systems to increase their reliability, monitoring application performance, and ensuring overall system health. An ideal Specialist should have strong decision making and situation management skills. At XenonStack, We committed to become the Most Value Driven Cloud Native, Platform Engineering and Decision Driven Analytics Company. Our Consulting Services and Solutions are towards the Neural Company and its Key Drivers. Key Responsibilities - Gathering Project Requirements from Stakeholders along with Business Analysts and Project Managers Break down complex problems and projects into manageable goals Handle High severity incident and situation. Designing high level Schematics of the infrastructure, tools and process needed Performing and in depth analysis of the possible risk and countermeasures for them Create a bridge between development and operations by applying software engineering mindset to system administration topics Configuration management platform understanding and experience (Chef/Puppet/Ansible) Release engineering, which involves defining best practices to ensure software releases are consistent and repeatable. Alerting, being on-call, and troubleshooting, along with emergency and incident response and postmortems. Know how best to monitor systems and react when things go wrong, constantly writing and rewriting response playbooks to reduce the time to fix any breakdown which may occur Involves documenting an incident, understanding all contributing root causes, and implementing future preventive actions. Highly developed skills in managing 24x7 production support comprising of Incident, Problem, Change management Troubleshooting Support Escalation On-Call Process Optimization Documenting Knowledge Optimizing SDLC (Software Development Life Cycle) Technical Requirements - Strong understanding of cloud-based architecture and cloud operations. Hands-on experience with Azure Experience in administration/build/management of Linux systems Foundational understanding of Infrastructure and Platform Technology stacks Strong understanding of Networking concepts and theories, such as different protocols (TCP/IP, UDP, ICMP, etc), MAC addresses, IP packets, DNS, OSI layers, and load balancing Working knowledge of Infrastructure and Application monitoring platforms Understanding of the core DevOps practices (CI/CD pipeline, release management etc. ) Ability to write code using any one modern programming language (Python, JavaScript, Ruby etc. ). Additional scripting skills are preferred Prior experience in Cloud management automation tools (Terraform/CloudFormation etc. ) is preferred Experience with source code management software and API automation is preferred. Deep Understanding of architecture and operations of Container Orchestration tools e. g Kubernetes Deep understanding of Know Applications i. e JAVA, Nodejs, Golang Deep understanding of Databases and SQL Strong understanding of BigData Infrastructure. Understanding of Incident management and Event Register Management Knowledge of SDLC methodologies and best practices including Waterfall Process, Agile methodologies, deployment automation, code reviews, and test-driven development Professional Attributes - Excellent communication skills Attention to detail Analytical mind and Problem Solving Aptitude Strong Organizational skills Visual Thinking Education : Technical Graduates ( BCA, BSC, B. TECH) , MCA, MSC AND M. TECH with strong data structures and algorithm skills Experience: 4 - 6 years Keywords SDLCSQLUDPTCP/IPNodejsICMPCI/CDPythonIP packetsKubernetesGolangPython,DatabasesJAVARuby Opportunities for Students & Fresh Graduates At Xenonstack, our interns, fresh graduates and early-career hires play a pivotal role in shaping the future of our company through their innovation and new approaches. Learn More Work at XenonStack We acknowledge individuals who wish to explore their capabilities with XenonStack, further adding value to our teams. Job Openings At XenonStack, We are committed to becoming the Most Value-Driven Cloud Native, Platform Engineering, and Decision Driven Analytics Company. At XenonStack, We have well-defined Career Progression Plans and Skill Development Program for an Individual to provide the best Launch Pad for the Career. Explore Us About Us Clients and Partners Reach Us Blogs Insights Contact Us function jobOpening(){ window. location. href='https://talent. xenonstack. com/jobs/Careers'; }", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://xenonstack.jobs/jobs/sr-site-reliability-engineer-azure", "company_id": 3348, "source": 3, "skills": "", "title": "Sr. Site Reliability Engineer- Azure", "location": "<PERSON><PERSON>", "location_type": null, "job_type": null, "min_experience": 4, "max_experience": 4, "apply_link": "https://xenonstack.jobs/jobs/sr-site-reliability-engineer-azure", "description": "Job Summary XenonStack’s SRE team is looking for a specialist who is responsible for availability, latency, performance, efficiency, change management, monitoring, emergency response, and capacity planning. ” Site reliability engineers create a bridge between development and operations by applying a software engineering mindset to system administration topics. SRE Specialists are experts who handle the following responsibilities in a company: improving application lifecycle, evolving software systems to increase their reliability, monitoring application performance, and ensuring overall system health. An ideal Specialist should have strong decision making and situation management skills. At XenonStack, We committed to become the Most Value Driven Cloud Native, Platform Engineering and Decision Driven Analytics Company. Our Consulting Services and Solutions are towards the Neural Company and its Key Drivers. Key Responsibilities - Gathering Project Requirements from Stakeholders along with Business Analysts and Project Managers Break down complex problems and projects into manageable goals Handle High severity incident and situation. Designing high level Schematics of the infrastructure, tools and process needed Performing and in depth analysis of the possible risk and countermeasures for them Create a bridge between development and operations by applying software engineering mindset to system administration topics Configuration management platform understanding and experience (Chef/Puppet/Ansible) Release engineering, which involves defining best practices to ensure software releases are consistent and repeatable. Alerting, being on-call, and troubleshooting, along with emergency and incident response and postmortems. Know how best to monitor systems and react when things go wrong, constantly writing and rewriting response playbooks to reduce the time to fix any breakdown which may occur Involves documenting an incident, understanding all contributing root causes, and implementing future preventive actions. Highly developed skills in managing 24x7 production support comprising of Incident, Problem, Change management Troubleshooting Support Escalation On-Call Process Optimization Documenting Knowledge Optimizing SDLC (Software Development Life Cycle) Technical Requirement - Strong understanding of cloud-based architecture and cloud operations. Hands-on experience with Azure Experience in administration/build/management of Linux systems Foundational understanding of Infrastructure and Platform Technology stacks Strong understanding of Networking concepts and theories, such as different protocols (TCP/IP, UDP, ICMP, etc), MAC addresses, IP packets, DNS, OSI layers, and load balancing Working knowledge of Infrastructure and Application monitoring platforms Understanding of the core DevOps practices (CI/CD pipeline, release management etc. ) Ability to write code using any one modern programming language (Python, JavaScript, Ruby etc. ). Additional scripting skills are preferred Prior experience in Cloud management automation tools (Terraform/CloudFormation etc. ) is preferred Experience with source code management software and API automation is preferred. Deep Understanding of architecture and operations of Container Orchestration tools e. g Kubernetes Deep understanding of Know Applications i. e JAVA, Nodejs, Golang Deep understanding of Databases and SQL Strong understanding of BigData Infrastructure. Understanding of Incident management and Event Register Management Knowledge of SDLC methodologies and best practices including Waterfall Process, Agile methodologies, deployment automation, code reviews, and test-driven development Professional Attributes - Excellent communication skills Attention to detail Analytical mind and Problem Solving Aptitude Strong Organizational skills Visual Thinking Experience - 2 to 4 years Education : Technical Graduates ( BCA, BSC, B. TECH) MCA, MSC AND M. TECH with strong data structure and algorithm skills Keywords DNSOSIDatabaseNodejsIP packetsLinuxShell ScriptingNetworkingJavaICMPProgrammingKubernetesJavascriptRubyUDPGolangPythonSystem Design Opportunities for Students & Fresh Graduates At Xenonstack, our interns, fresh graduates and early-career hires play a pivotal role in shaping the future of our company through their innovation and new approaches. Learn More Work at XenonStack We acknowledge individuals who wish to explore their capabilities with XenonStack, further adding value to our teams. Job Openings At XenonStack, We are committed to becoming the Most Value-Driven Cloud Native, Platform Engineering, and Decision Driven Analytics Company. At XenonStack, We have well-defined Career Progression Plans and Skill Development Program for an Individual to provide the best Launch Pad for the Career. Explore Us About Us Clients and Partners Reach Us Blogs Insights Contact Us function jobOpening(){ window. location. href='https://talent. xenonstack. com/jobs/Careers'; }", "ctc": null, "currency": null, "meta": {}}]