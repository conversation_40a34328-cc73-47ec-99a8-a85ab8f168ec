[{"jd_link": "https://career.spiralogics.com/jobs/62", "company_id": 3353, "source": 3, "skills": "", "title": "Database Administrator", "location": "", "location_type": null, "job_type": "full_time", "min_experience": 2, "max_experience": 3, "apply_link": "https://career.spiralogics.com/jobs/62", "description": "Mid Level 2 Positions Full-time Job Summary:Spiralogics is a leading custom software development company primarily based in the US, India and Nepal. We are an equal opportunity provider, with focus on engaging and inclusive work culture. We are currently looking a Database Administrator with minimum 3 years of experience to work with our lean but growing teams to provide quality oversight to our many projects. Primary Responsibility:Provides enterprise-wide DBA services for production systems and manages, installs, builds, and configures database environments to ensure availability, ease of management, stability, recovery, performance and scalability. Responsible for steady- state operation of the databases that use the customer and internal facing application, information delivery, and data warehouse development teams. Collaborates with business owners and IT team on data planning and development projects. Job Description:Maintenance of Oracle database and MS SQL Server database systems as needed. Performance monitoring, tuning, and optimization of database performance. Managing user access and security controls within the Oracle database. Troubleshooting and resolving database-related issues. Collaborating with developers and other IT staff to support application development and integration as needed. Updating database maintenance scripts as needed installs, configures and manages enterprise Oracle/MS-SQLServer database environments, including: development, test, training, sandbox and production. Supports DB and application code deployment, change management and end support. Monitors and tunes performance, working across application, server/storage, database and functional support areas to tune system for optimal performance. Works with management and security team to assist in developing, implementing and enforcing security policies. Creates and manages user and security profiles, ensuring application security policies and procedures are followed. Review database backup and recovery procedures in conjunction with system and application administrators for comprehensive data protection. Monitor backups and Database Replication. Analyze database security and make necessary changes to fulfill IT compliance requirements and support IT audit activities. Operates under irregular work schedules as needed, provides off-hours operational support. Other duties as requested. Job Qualifications & Experience:Minimum of 2 years of database administration experience managing databases in mission-critical application environments. Certification in Oracle is a plus. Oracle and SQL Server experience. AWS (PostgreSQL, Redshift) experience is a plus. Experience with additional tools and features: Oracle Grid Control, Partitioning, Data Guard, etc. Well versed in Structured Query Language (SQL); experience tuning application code for performance/scalability. Experience with SQL Server is a plus. Working knowledge of HP-UX, Linux or other enterprise Unix platform. Strong knowledge of MS Office Suite and email tools. Knowledge & Employment Standards:Excellent written and verbal communication skills. Ability to work in a team-oriented environment.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://career.spiralogics.com/jobs/87", "company_id": 3353, "source": 3, "skills": "", "title": "AI and LLM Developer", "location": "", "location_type": null, "job_type": "full_time", "min_experience": 18, "max_experience": null, "apply_link": "https://career.spiralogics.com/jobs/87", "description": "Mid Level 2 Positions Full-time Job Summary:Spiralogics is a leading custom software development company primarily based in the US, Nepal and India. With 18+ years of experience in tech industry, we go beyond the traditional application development process to deliver quality products. With an array of both in-house and client-based applications, we work with different technologies such as Dot NET, PHP, Python, Node JS, React, Angular JS, Java, Xamarin, iOS, Android, Flutter. We are seeking a highly skilled and innovative AI and LLM Developer to join our team. The ideal candidate will have hands-on experience in developing, fine-tuning, and deploying large language models (LLMs) and other AI/ML solutions. You will be working on cutting-edge AI applications to enhance user experience, streamline processes, and contribute to scalable AI-driven products. Key Responsibilities :Design, develop, and deploy AI solutions with a focus on large language models (LLMs), such as GPT, BERT, or similar models. Fine-tune and optimize pre-trained models for specific use cases to improve performance and efficiency. Integrate AI and LLM-based functionalities into applications, including chatbots, NLP tools, and automated content generation. Conduct research to stay up-to-date with the latest advancements in AI/ML, LLM architectures, and NLP technologies. Create and manage datasets for training, validation, and testing of AI models. Collaborate with cross-functional teams, including data scientists, software engineers, and product managers, to deliver AI-driven features. Monitor and improve the performance of deployed models through ongoing testing and feedback loops Qualifications and Skills:Bachelor’s or master’s degree in computer science, Data Science, AI/ML, or a related field. Proven experience in developing and deploying AI/ML models, especially large language models (LLMs). Proficiency in Python and AI/ML frameworks like TensorFlow, PyTorch, or Hugging Face. Strong knowledge of natural language processing (NLP) techniques, including text generation, summarization, and sentiment analysis. Experience in fine-tuning and customizing pre-trained LLMs for real-world applications. Familiarity with cloud platforms such as AWS, GCP, or Azure for AI/ML deployments. Strong problem-solving and analytical skills with the ability to optimize models for speed and accuracy. Experience with MLOps, version control (Git), and model lifecycle management is a plus.", "ctc": null, "currency": null, "meta": {}}]