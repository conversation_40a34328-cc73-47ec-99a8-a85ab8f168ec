[{"jd_link": "https://zazmic.com/vacancy/account-manager-usa-only/", "company_id": 3359, "source": 3, "skills": "", "title": "Account Manager (USA only)", "location": "USA\nSenior\nRemote\nTop Vacancy", "location_type": "flexible", "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://zazmic.com/vacancy/account-manager-usa-only/", "description": "USASenior Type: Remote Top VacancyDear Candidate,Before submitting your resume, please pay attention to the location – we will not be able to review your resume and provide feedback if you are not (in fact) located in the location of the vacancy. Thanks for understandingAbout Zazmic Zazmic. com is a leading provider of cutting-edge digital solutions, empowering businesses to innovate and grow. Our expertise spans software development, digital marketing, and cloud solutions, helping clients across industries achieve sustainable success. Join our dynamic team and be part of a company that values creativity, collaboration, and forward-thinking strategies. Job Summary: Zazmic. com is looking for a highly motivated Account Manager to drive revenue growth and build long-term customer relationships across multiple industries. This role is responsible for managing key accounts, identifying new business opportunities, and delivering value-driven solutions that align with our clients’ strategic goals. Key Responsibilities:Serve as a trusted advisor and primary point of contact for assigned customers. Drive customer satisfaction by coordinating ongoing projects and ensuring prompt response to customer inquiries. Demonstrate deep industry knowledge and business value across various verticals. Leverage industry connections to engage C-suite executives and expand business opportunities. Utilize a top-down sales approach to attract new accounts and establish relationships at VP level and above. Develop and execute strategies to drive growth in priority areas according to Zazmic’s sales initiatives. Build and maintain a strong sales pipeline to achieve revenue goals. Define and meet revenue objectives for each assigned account, ensuring consistent growth and profitability. Conduct monthly review meetings with customers to assess performance and identify opportunities. Lead internal weekly and monthly account reviews to evaluate progress and strategize for future growth. Identify new business opportunities within existing and potential customer accounts. Lead sales efforts to convert opportunities into long-term partnerships. Lead end-to-end sales engagements while ensuring compliance with internal processes and industry regulations. Negotiate contracts and agreements in accordance with company policies and ethical business standards. Required Experience:Master’s degree5+ years of software engineering services account management experienceProven track record in B2C and B2B sales with consultative value sellingStrong presentation and product demoing skillsDemonstrated experience in building relationships with C-suite executivesExcellent communication, negotiation, and interpersonal skillsWhy join to us:Work Anywhere: Embrace the freedom to work from anywhere in the world. Your office could be a beach, a cozy café, or wherever you feel most inspiredFlexibility: Wave goodbye to the 9-to-5 grind. We believe in a flexible working schedule that fits your life. Sponsored Education: We're invested in your growth. Enjoy sponsored education and training, ranging up to 50%. Personal Development: We're not just about work; we're about your growth. Craft your personal development plan and watch your career soar. Regular Salary Reviews: Your hard work won't go unnoticed. We conduct regular salary reviews to ensure you're fairly rewardedCareer Advancement: The sky's the limit! Move up the ladder based on your performance, and your career trajectory could surprise youCorporate Events: From team outings to memorable celebrations, we know how to have a good time togetherEnglish Classes: Enhance your language skills and open doors to global opportunities with our sponsored English classesHealth Matters: Your health is our priority. Get your annual flu shot on usWork Equipment: We provide top-notch tools. Receive a compensation of $600 for your work equipment Paid vacation, sick leaves Perks of being a Company Work from anywhere in the world Gifts to mark significant life events English classes with a native speakerMain numbers2015founded300+employees worldwide40current projects44locations", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://zazmic.com/vacancy/ai-ml-engineer-usa-only/", "company_id": 3359, "source": 3, "skills": "", "title": "AI/ML Engineer (USA and Ukraine Only)", "location": "Ukraine\nUSA\nMiddle\nOffice\nRemote", "location_type": "flexible", "job_type": "full_time", "min_experience": 4, "max_experience": null, "apply_link": "https://zazmic.com/vacancy/ai-ml-engineer-usa-only/", "description": "Ukraine, USAMiddle Type: Office, RemoteDear Candidate,Before submitting your resume, please pay attention to the location – we will not be able to review your resume and provide feedback if you are not (in fact) located in the location of the vacancy. Thanks for understandingAbout the projectWe partner closely with Google and collaborate with a diverse range of clients across industries. Our projects vary, from building predictive models for eCommerce platforms to anticipate consumer behavior, to helping production companies automate and enhance their inspection processes through AI-driven solutions. We work with a mix of pre-built models (e. g. , Google’s Gemini) and custom models, which we fine-tune and deploy based on specific client requirements. Your project allocation will be determined by your skill set and start date, ensuring a strong match for both your expertise and the current project needs. Our projects typically follow these phases:Workshop: In this phase, we engage with clients to understand their challenges, conduct research, and propose tailored AI solutions. Proof of Concept (PoC): Here, we build a prototype to validate the feasibility of the proposed solution. Development: This is where we transition into long-term projects, focusing on the full-scale development and implementation of the solution. While we primarily use Google Cloud Platform (GCP) infrastructure, we are also equipped to work with a variety of models and cloud services depending on the project’s requirements. Key Responsibilities:Develop algorithms that utilize deep learning and traditional methods in machine learningCreate experiments, algorithms and prototypes that not only yield high-accuracy but are also designed and engineered to scaleAnalyze and interpret data to identify patterns and insights to drive business decisionsOptimize machine learning algorithms for scalability, speed, and accuracyQualifications:4+ years in applying AI/ML principles to real-world applicationsDeploying models as REST APIs (Flask) and using TFServe (Tensorflow). Excellent proficiency in Python and SQLExperience with Generative AI OR / AND Traditional AIExperience in building Bigdata pipelines and proven track record of implementing Machine learning modelsUnderstand and explain model and automated decisions to business and technical stakeholdersExperience in Model development and deployment in GCP is big plusBroad Machine Learning experience - Algorithm Evaluation, Preparation, Analysis, Modeling and ExecutionExposure to Deep Learning, Reinforcement Learning and/or Time-Series techniques is a plusWhy join us: Work Anywhere: Embrace the freedom to work from anywhere in the world. Your office could be a beach, a cozy café, or wherever you feel most inspiredFlexibility: Wave goodbye to the 9-to-5 grind. We believe in a flexible working schedule that fits your life. Sponsored Education: We're invested in your growth. Enjoy sponsored education and training, ranging up to 50%. Personal Development: We're not just about work; we're about your growth. Craft your personal development plan and watch your career soarRegular Salary Reviews: Your hard work won't go unnoticed. We conduct regular salary reviews to ensure you're fairly rewardedCareer Advancement: The sky's the limit! Move up the ladder based on your performance, and your career trajectory could surprise youCorporate Events: From team outings to memorable celebrations, we know how to have a good time togetherEnglish Classes: Enhance your language skills and open doors to global opportunities with our sponsored English classesHealth Matters: Your health is our priority. Get your annual flu shot on usWork Equipment: We provide top-notch tools. Receive a compensation of $600 for your work equipment Perks of being a Company Work from anywhere in the world Gifts to mark significant life events English classes with a native speakerMain numbers2015founded300+employees worldwide40current projects44locations", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://zazmic.com/vacancy/basic-understanding-of-html-structure-ecommerce-compliance-or-invoicing-is-a-plus-however-training-will-be-provided/", "company_id": 3359, "source": 3, "skills": "", "title": "Operational Support (Kuala Lumpur only)", "location": "Kuala Lumpur\nLead\nRemote\nAverage Vacancy", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://zazmic.com/vacancy/basic-understanding-of-html-structure-ecommerce-compliance-or-invoicing-is-a-plus-however-training-will-be-provided/", "description": "Kuala LumpurLead Type: Remote Average VacancyDear Candidate,Before submitting your resume, please pay attention to the location – we will not be able to review your resume and provide feedback if you are not (in fact) located in the location of the vacancy. Thanks for understandingPOSITION SUMMARY:We are looking for an enthusiastic professional to lead a team of Specialists, providing high-quality support and operational expertise. Reporting to the department Manager, you will oversee a team tasked with analyzing detailed operational data to conduct quality audits and ensure accurate data reconciliation. Your responsibilities include ensuring the team adheres to network and advertiser guidelines in areas such as compliance monitoring, payment reconciliation, partnership quality analysis, and other operational tasks. Additionally, you will gather data and insights from your team to ensure they have the right tools and follow efficient processes. Key Responsibilities:Create and execute operations training for new team membersEnsure operational capacity is aligned to business KPIsDiscover and monitor advertisers’ information on websites and internal platforms utilizing internal toolingAudit publisher websites, social media presences, and submitted data to ensure complianceAnalyze existing data and augment with additional data collection and analysis where appropriateMaintain and build ongoing audit templates and ad-hoc audit templatesCreate new and keep existing written documentation up to datePropose improvements of system and processQualifications & Experience:English fluency (at least Upper-Intermediate)Some understanding of commercial operations and, ideally, experience estimating team capacity or advocating team needsDetail-oriented, thorough, and meticulousAttention to detail in order to identify and analyze errorsStrong time management and self-organization skills and ability to meet predetermined timeframesAbility to work independently and with a teamAbility to accurately interpret complex documents and online policiesBasic understanding of HTML structure, ecommerce compliance, or invoicing is a plus; however, training will be providedWhy join to us:Close cooperation with the development team and clientOpportunity to influence product developmentWe cover English classes (with a native speaker)Boost professional brand: you can participate in local conferences as a listener or as a speakerRegular team buildings: have fun with teammatesGifts for significant life events (marriage, childbirth)Tech and non-tech Zazmic Communities: support and share experience with each other Perks of being a Company Work from anywhere in the world Gifts to mark significant life events English classes with a native speakerMain numbers2015founded300+employees worldwide40current projects44locations", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://zazmic.com/vacancy/customer-engineer-google-cloud-platform/", "company_id": 3359, "source": 3, "skills": "", "title": "Customer Engineer – Google Cloud Platform", "location": "India\nMiddle\nSenior", "location_type": "flexible", "job_type": "full_time", "min_experience": 3, "max_experience": 5, "apply_link": "https://zazmic.com/vacancy/customer-engineer-google-cloud-platform/", "description": "The Customer Engineer (CE) is a technical expert with a strong foundation in software development who partners with the sales team to drive the adoption of Google Cloud infrastructure and products, with a particular focus on Ai, Infrastructure & Security. This role demands a deep understanding of GCP and the ability to translate complex technical concepts into solutions that address customer needs. The CE will act as a trusted advisor to clients, providing technical guidance, resolving challenges, and ensuring customer success. IndiaMiddle, SeniorDear Candidate,Before submitting your resume, please pay attention to the location – we will not be able to review your resume and provide feedback if you are not (in fact) located in the location of the vacancy. Key Responsibilities::Technical Pre-sales Support: Collaborate with the sales team to identify and qualify technical opportunities. Deliver technical presentations, demos, and proofs of concept to showcase the value of GCP solutions. Solution Design and Architecture: Work closely with clients to understand their business requirements and design scalable, secure, and cost-effective cloud architectures on GCP. Implementation and Deployment: Guide clients through the implementation and deployment of GCP solutions, ensuring smooth transitions and successful onboarding. Technical Troubleshooting: Diagnose and resolve technical issues encountered by clients during the implementation and operation of GCP solutions. Custom Development and Integration: Develop scripts, automation tools, or proof-of-concept applications to tailor GCP solutions to specific client needs. Customer Advocacy: Build strong relationships with clients, acting as their advocate within the organization and ensuring their technical needs are met. Knowledge Sharing: Stay up-to-date on GCP technologies and best practices. Share knowledge and expertise with colleagues and clients through training sessions and workshops. Required Experience::Hands-on GCP Experience: At least 3-5 years of experience working directly with Google Cloud Platform, including designing, deploying, and managing cloud infrastructure and applications. Software Development Skills: Proficiency in one or more programming languages (Python, Java, Go, etc. ) and experience with software development lifecycle. Cloud Expertise: Deep understanding of cloud computing concepts, including virtualization, networking, storage, and security. Experience with other major cloud providers (AWS, Azure) is a plus. Technical Skills: Proficiency in scripting languages (Python, Bash) and infrastructure-as-code tools (Terraform, Cloud Deployment Manager). Familiarity with DevOps practices and tools. Customer-Facing Skills: Excellent communication and presentation skills. Ability to build rapport with clients, understand their needs, and explain complex technical concepts in clear terms. Problem-Solving: Strong analytical and troubleshooting skills. Ability to identify and resolve technical issues efficiently and effectively. Sales Collaboration: Experience working closely with sales teams to support the sales cycle and drive technical closure. Desirable Qualifications::GCP Certifications: Professional Cloud Architect, Professional Data Engineer, or other relevant certifications. Industry Experience: Experience working in a customer-facing technical role within the IT or cloud industry. Vertical Expertise: Knowledge of specific industries or verticals, such as finance, healthcare, or retail, can be an advantage. Why join to us:Work Anywhere: Embrace the freedom to work from anywhere in the world. Your office could be a beach, a cozy café, or wherever you feel most inspiredFlexibility: Wave goodbye to the 9-to-5 grind. We believe in a flexible working schedule that fits your life. Sponsored Education: We're invested in your growth. Enjoy sponsored education and training, ranging up to 50%. Personal Development: We're not just about work; we're about your growth. Craft your personal development plan and watch your career soar. Regular Salary Reviews: Your hard work won't go unnoticed. We conduct regular salary reviews to ensure you're fairly rewardedCareer Advancement: The sky's the limit! Move up the ladder based on your performance, and your career trajectory could surprise youCorporate Events: From team outings to memorable celebrations, we know how to have a good time togetherEnglish Classes: Enhance your language skills and open doors to global opportunities with our sponsored English classesHealth Matters: Your health is our priority. Get your annual flu shot on usWork Equipment: We provide top-notch tools. Receive a compensation of $600 for your work equipment Paid vacation, sick leaves Perks of being a Company Work from anywhere in the world Gifts to mark significant life events English classes with a native speakerMain numbers2015founded300+employees worldwide40current projects44locations", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://zazmic.com/vacancy/cloud-delivery-lead/", "company_id": 3359, "source": 3, "skills": "", "title": "Cloud Delivery Lead (LATAM only)", "location": "Latin America\nLead\nRemote\nTop Vacancy\nAsia\nLead", "location_type": "flexible", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://zazmic.com/vacancy/cloud-delivery-lead/", "description": "Latin AmericaLead Type: Remote Top VacancyDear Candidate,Before submitting your resume, please pay attention to the location – we will not be able to review your resume and provide feedback if you are not (in fact) located in the location of the vacancy. Thanks for understandingWe are seeking an experienced and proactive Cloud Service Desk Lead to manage and guide our global Cloud Service Desk team. In this role, you will oversee daily operations, drive service excellence, and ensure a responsive and high-quality support experience for all Zazmic employees — from technical teams to executive leadership. ___________________We’re looking for a hands-on, technically sharp Cloud Delivery Lead to help drive day-to-day execution across multiple client environments. This role is ideal for a strong individual contributor with some team leadership or project ownership experience—someone who thrives in fast-moving environments and can confidently represent the team to clients. You’ll be responsible for keeping delivery on track, leading engineers through day-to-day priorities, resolving L1/L2 issues, and supporting client interactions—especially on onboarding and smaller-scale deals. You’ll also serve as the go-to in the absence of senior leadership and act as a bridge between technical execution and broader strategy. This is a high-impact, high-autonomy role in a growing, founder-led organization where you’ll have meaningful influence on operations, team culture, and customer success. Key ResponsibilitiesManage daily delivery of cloud migrations and operations projectsProvide leadership and direction to the engineering team, ensuring accountability and project timelines are met. Act as the point of escalation for Level 1 and 2 technical issues, troubleshooting and resolving problems hands-on. Lead the team when the senior leadership is unavailable, ensuring smooth operations and decision-making. Participate in client sales and onboarding calls, especially for smaller and mid-size accounts, representing the company professionally. Balance engineering execution with people and process leadership, ensuring team efficiency and morale. Implement and maintain CI/CD pipelines and infrastructure as code processes. Work with diverse cloud environments including Google, DigitalOcean, AWS, and Azure. Required Skills and Qualifications:Strong technical background in DevOps, Cloud Infrastructure, or SREExperience with at least one major cloud provider (Google Cloud, AWS, Azure, DigitalOcean preferred)Strong technical background with the ability to lead engineers and perform hands-on troubleshooting. Familiarity with CI/CD pipelines, infrastructure as code (Terraform, Ansible, Pulumi, etc. ), and automation toolsExcellent project management skills and ability to handle multiple projects simultaneously. Strong leadership and team management abilities. Excellent communication and client-facing skills. Ability to handle escalations and resolve technical issues effectively. Nice-to-Have Traits:Previous experience in a startup or founder-led environmentExperience in a multi-company or client-facing environment. Interest in sales engineering or technical presales work. Exposure to cost optimization or migration planning. Deep knowledge of Google Cloud Platform and Digital Ocean specifically. Passion for automating processes and improving efficiency. Comfortable working in a high-growth, fast-paced startup environment. Why join to us:Work Anywhere: Embrace the freedom to work from anywhere in the world. Your office could be a beach, a cozy café, or wherever you feel most inspiredFlexibility: Wave goodbye to the 9-to-5 grind. We believe in a flexible working schedule that fits your life. Sponsored Education: We're invested in your growth. Enjoy sponsored education and training, ranging up to 50%. Personal Development: We're not just about work; we're about your growth. Craft your personal development plan and watch your career soar. Regular Salary Reviews: Your hard work won't go unnoticed. We conduct regular salary reviews to ensure you're fairly rewardedCareer Advancement: The sky's the limit! Move up the ladder based on your performance, and your career trajectory could surprise youCorporate Events: From team outings to memorable celebrations, we know how to have a good time togetherEnglish Classes: Enhance your language skills and open doors to global opportunities with our sponsored English classesHealth Matters: Your health is our priority. Get your annual flu shot on usWork Equipment: We provide top-notch tools. Receive a compensation of $600 for your work equipment Paid vacation, sick leaves Perks of being a Company Work from anywhere in the world Gifts to mark significant life events English classes with a native speakerMain numbers2015founded300+employees worldwide40current projects44locations", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://zazmic.com/vacancy/cloud-service-desk-lead/", "company_id": 3359, "source": 3, "skills": "", "title": "Cloud Service Desk Lead", "location": "Asia\nLead\nRemote\nAverage Vacancy\nLatin America\nLead", "location_type": "flexible", "job_type": "full_time", "min_experience": 2, "max_experience": 5, "apply_link": "https://zazmic.com/vacancy/cloud-service-desk-lead/", "description": "AsiaLead Type: Remote Average VacancyDear Candidate,Before submitting your resume, please pay attention to the location – we will not be able to review your resume and provide feedback if you are not (in fact) located in the location of the vacancy. Thanks for understandingWe are seeking an experienced and proactive Cloud Service Desk Lead to manage and guide our global Cloud Service Desk team. In this role, you will oversee daily operations, drive service excellence, and ensure a responsive and high-quality support experience for all Zazmic employees — from technical teams to executive leadership. Key Responsibilities– Team Leadership: Lead, mentor, and manage the Cloud Service Desk team, ensuring optimal performance and accountability. – Operational Oversight: Oversee the day-to-day operations of the service desk, ensuring timely and effective resolution of incidents and service requests. – Customer Service Excellence: Maintain a high standard of customer service while enforcing SLAs and improving employee satisfaction. – Cloud Support Ownership: Act as the primary escalation point and contact for cloud- related support issues. Act as a team member and take care of ticket. – Account &amp; License Management: Manage user accounts, licenses, and access controls across cloud platforms. – Documentation &amp; SOPs: Create and maintain detailed knowledge base articles, troubleshooting guides, and standard operating procedures. – Cross-Functional Collaboration: Work closely with other Zazmic teams (engineering, IT, product) to resolve complex issues and improve processes. – Process Optimization: Identify and implement improvements to service workflows, tools, and reporting. – Reporting: Generate and deliver regular reports (daily, weekly, monthly, and quarterly) to monitor team performance and service metrics. – Change Management Support: Participate in change initiatives, assessing potential impact on users and support operations. Qualifications:Bachelor’s degree in Information Technology or a related field preferred; relevant certifications and equivalent experience will be considered. Minimum 5 years of experience in IT Service Desk operations, with at least 2 years in a leadership or supervisory role. Strong understanding of cloud platforms and related support tools. Proficiency in ticketing and service management platforms (e. g. , Jira, ServiceNow, Zendesk). Excellent leadership, communication, and interpersonal skills. Proven ability to work under pressure, manage multiple priorities, and deliver in a dynamic environment. Strong analytical and problem-solving skills with a keen eye for process improvement. Experience supporting distributed and global teams is a plus. Why join to us:Work Anywhere: Embrace the freedom to work from anywhere in the world. Your office could be a beach, a cozy café, or wherever you feel most inspiredFlexibility: Wave goodbye to the 9-to-5 grind. We believe in a flexible working schedule that fits your life. Sponsored Education: We're invested in your growth. Enjoy sponsored education and training, ranging up to 50%. Personal Development: We're not just about work; we're about your growth. Craft your personal development plan and watch your career soar. Regular Salary Reviews: Your hard work won't go unnoticed. We conduct regular salary reviews to ensure you're fairly rewardedCareer Advancement: The sky's the limit! Move up the ladder based on your performance, and your career trajectory could surprise youCorporate Events: From team outings to memorable celebrations, we know how to have a good time togetherEnglish Classes: Enhance your language skills and open doors to global opportunities with our sponsored English classesHealth Matters: Your health is our priority. Get your annual flu shot on usWork Equipment: We provide top-notch tools. Receive a compensation of $600 for your work equipment Paid vacation, sick leaves Perks of being a Company Work from anywhere in the world Gifts to mark significant life events English classes with a native speakerMain numbers2015founded300+employees worldwide40current projects44locations", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://zazmic.com/vacancy/full-stack-developer-python-react-with-aws/", "company_id": 3359, "source": 3, "skills": "", "title": "Full Stack Developer (Python/React with AWS) LATAM only", "location": "Latin America\nLead\nRemote\nTop Vacancy\nIndia\nPortugal\nMiddle\nLatin America\nPortugal\nMiddle", "location_type": "flexible", "job_type": "full_time", "min_experience": 4, "max_experience": 6, "apply_link": "https://zazmic.com/vacancy/full-stack-developer-python-react-with-aws/", "description": "Latin AmericaLead Type: Remote Top VacancyDear Candidate,Before submitting your resume, please pay attention to the location – we will not be able to review your resume and provide feedback if you are not (in fact) located in the location of the vacancy. Thanks for understanding Full Stack Developer (Python/React with AWS)We’re looking for an autonomous Full Stack Developer with strong Python/React skills and AWS cloud experience to take on development tasks within an existing code repository. About the RoleYou’ll join our team to handle the full development lifecycle, from design to deployment, for high-traffic web applications. We need someone who can independently tackle complex challenges, actively contribute to requirements discussions, and propose effective technical solutions. What You'll Do:Develop and maintain high-traffic web applications using Python and React. Design and implement REST APIs. Integrate with both internal and third-party APIs (e. g. , Stripe). Translate business requirements into technical specifications. Work extensively with the AWS cloud infrastructure. Participate in the entire development lifecycle, including testing and deployment. What We're Looking For:Experience: 5+ years developing Python/React applications within AWS infrastructure. Full Stack Proficiency: 4-6 years of experience working on both the frontend and backend of high-traffic web applications. Python Expertise: Extensive experience with Python and web frameworks like Flask/Django. API & ORM Skills: Extensive experience designing/building REST APIs and using ORMs such as SQLAlchemy. API Integrations: Experience consuming/integrating with APIs developed internally or provided by 3rd parties (e. g. , Stripe). JavaScript/React: Extensive experience with ES6/ReactJS and one or more JavaScript frameworks. Web Technologies: Extensive experience with HTML, CSS, SCSS. State Management: Experience with Flux/Redux, Saga, Flow. Analytical Thinking: Experience translating business requirements into technical requirements. Communication: Excellent verbal and written communication, presentation, and stakeholder management skills. Problem-Solving: Critical and analytical thinking skills with strong problem-solving abilities. Tools: Working knowledge of Google Workspace (Docs, Slides, Sheets). Adaptability: Ability to work effectively in a dynamic, rapidly changing, team-based environment. Why join to us:Work Anywhere: Embrace the freedom to work from anywhere in the world. Your office could be a beach, a cozy café, or wherever you feel most inspiredFlexibility: Wave goodbye to the 9-to-5 grind. We believe in a flexible working schedule that fits your life. Sponsored Education: We're invested in your growth. Enjoy sponsored education and training, ranging up to 50%. Personal Development: We're not just about work; we're about your growth. Craft your personal development plan and watch your career soar. Regular Salary Reviews: Your hard work won't go unnoticed. We conduct regular salary reviews to ensure you're fairly rewardedCareer Advancement: The sky's the limit! Move up the ladder based on your performance, and your career trajectory could surprise youCorporate Events: From team outings to memorable celebrations, we know how to have a good time togetherEnglish Classes: Enhance your language skills and open doors to global opportunities with our sponsored English classesHealth Matters: Your health is our priority. Get your annual flu shot on usWork Equipment: We provide top-notch tools. Receive a compensation of $600 for your work equipment Paid vacation, sick leaves Perks of being a Company Work from anywhere in the world Gifts to mark significant life events English classes with a native speakerMain numbers2015founded300+employees worldwide40current projects44locations", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://zazmic.com/vacancy/data-engineer-ii-dpes/", "company_id": 3359, "source": 3, "skills": "", "title": "Data Engineer II", "location": "India\nPortugal\nMiddle\nRemote\nAverage Vacancy", "location_type": "remote", "job_type": null, "min_experience": 4, "max_experience": null, "apply_link": "https://zazmic.com/vacancy/data-engineer-ii-dpes/", "description": "India, PortugalMiddle Type: Remote Average VacancyDear Candidate,Before submitting your resume, please pay attention to the location – we will not be able to review your resume and provide feedback if you are not (in fact) located in the location of the vacancy. Thanks for understanding About the project – №1 Travel platform in the world! We believe that we are better together, and at company we welcome you for who you are. Our workplace is for everyone, as is our people powered platform. At company, we want you to bring your unique identities, abilities, and experiences, so we can collectively revolutionize travel and together find the good out there. Our client, the world’s largest travel site, operates at scale with over 500 million reviews, opinions, photos, and videos reaching over 390 million unique visitors each month. We are a data driven company that leverages our data to empower our decisions. prject is extremely excited to play a pivotal role in supporting our travelers. About Data Eng – Analytics Engineering We’re looking for a Data Engineer with a strong foundation in data modeling to help strengthen the core of our data ecosystem. In this role, you’ll design and build trusted, well-structured datasets that power reporting, insights, and data informed decisions across the company. You will work closely with analytics, product, dsml, finance, marketing, and engineering teams to model key business concepts, enable self-service, and help define the data layer that supports key metrics and reporting across the company. This role is ideal for someone who enjoys translating business logic into clean, scalable data models and contributing to a modern data stack. What You Will DoBuilding data pipelines and ETL processes that interact with terabytes of data on leading platforms such as Snowflake and BigQuery. Collaborate with stakeholders from a variety of functions (analytics, dsml, marketing, engineering, product, etc. ) to collect business requirements and translate them into technical data model solutions. Design, build, and maintain efficient, scalable, and reusable data models in our cloud data warehouse (e. g. , Snowflake, BigQuery). Transform data across many sources into clean, curated, standardized, and trustworthy data products. Explore and analyze data using SQL and dashboards to understand trends, edge cases, and inconsistencies, and to ensure models align with evolving business needs. Ensure data quality through testing, observability tools, and proactive monitoring. Investigate and troubleshoot complex data issues, validate assumptions, and trace anomalies across multiple sources. Participate in code reviews and contribute to improving our data development standards and practices. What we are looking for:4+ years of data engineering or general software development experienceExperience in developing complex ETL processes from concept to implementation to deployment and operations, including SLA definition, performance measurements and monitoring. Proficiency in writing and optimizing SQL queries; data exploration skills with proven record of querying and analyzing large datasetsExperience in data design and data modeling with large, complex datasets (star/snowflake schema, slowly changing dimensions, normalization/denormalization trade-offs, etc. ). Experience with modern cloud data warehouses (Snowflake, BigQuery, Redshift, etc. ). Familiarity with BI tools and semantic layer principles (Looker, Tableau, Sigma, etc. ). Familiarity with basic software engineering best practices (CI/CD, testing, documentation). Nice to Have:Experience with data governanceExperience with data transformation toolsPrevious experience with an e-commerce platformExperience with orchestration tools (Airflow, Dagster), data quality and observability frameworks (ex:Monte Carlo), or Knowledge Graphs. Why join us:Ability to work remotely from anywhere in the worldClose cooperation with the development team and clientOpportunity to influence product developmentProfessional growth: the certification preparation course is free for our specialists. The company pays for two attempts to pass the exam, regardless of the exam resultWe cover English classes (with a native speaker)Boost professional brand: you can participate in local conferences as a listener or as a speakerRegular team buildings: have fun with teammatesGifts for significant life events (marriage, childbirth)Tech and non-tech Zazmic Communities: support and share experience with each other Perks of being a Company Work from anywhere in the world Gifts to mark significant life events English classes with a native speakerMain numbers2015founded300+employees worldwide40current projects44locations", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://zazmic.com/vacancy/salesforce-engineer-mexico-only/", "company_id": 3359, "source": 3, "skills": "", "title": "Salesforce Engineer (Mexico only)", "location": "Mexico\nMiddle\nRemote\nTop Vacancy", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://zazmic.com/vacancy/salesforce-engineer-mexico-only/", "description": "MexicoMiddle Type: Remote Top VacancyDear Candidate,Before submitting your resume, please pay attention to the location – we will not be able to review your resume and provide feedback if you are not (in fact) located in the location of the vacancy. Thanks for understanding About the project The client is a leader in exascale data center ecosystems, edge data center designs, industry-leading telecommunications solutions and next-generation technology innovation. The client has developed more than 700 issued and pending patent claims covering data center designs that have manifested into the company’s world-renowned data centers and technology solutions. We are currently seeking a skilled Salesforce Engineer to join our team. The ideal candidate will possess a deep understanding of Salesforce API integration, software development within JD Edwards ERP, and integration expertise with the AMI system (https://amistrategies. com/). This role will primarily focus on developing and maintaining integrations, both frontend and backend, leveraging Salesforce API to enhance our business processes. Key Responsibilities::Design, develop, and maintain Salesforce API integrations to support business requirementsCollaborate with cross-functional teams to understand integration needs and implement effective solutionsServe as an integration specialist, ensuring seamless communication between Salesforce, JD Edwards ERP, and the AMI systemPerform troubleshooting and debugging of integration issues as neededEnsure compliance with best practices and standards for integration developmentDevelop reusable and easily maintenanable LWCDevelop apex classes following best practicesDeploy changes between environments using gitlab and Jenkins pipelinesRequirements:5+ years of experience with SalesforceCI/CD deployments and code review using VS CODE, GIT, and JENKINSApp development for LWCs and Lightning FlowsDoesn’t need to be heavy APEX, but more focused on Lightning web components and lightning design systemUsing SCRATCH orgs to manage development and knows GITWhy join us:Ability to work remotely from anywhere in the worldClose cooperation with the development team and clientOpportunity to influence product developmentProfessional growth: the certification preparation course is free for our specialists. The company pays for two attempts to pass the exam, regardless of the exam resultWe cover English classes (with a native speaker)Boost professional brand: you can participate in local conferences as a listener or as a speakerRegular team buildings: have fun with teammatesGifts for significant life events (marriage, childbirth)Tech and non-tech Zazmic Communities: support and share experience with each other Perks of being a Company Work from anywhere in the world Gifts to mark significant life events English classes with a native speakerMain numbers2015founded300+employees worldwide40current projects44locations", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://zazmic.com/vacancy/intermediate-software-engineer-sales-support-portugal-olnly/", "company_id": 3359, "source": 3, "skills": "", "title": "Intermediate Software Engineer – Sales Support (Portugal and LATAM only)", "location": "Latin America\nPortugal\nMiddle\nRemote\nAverage Vacancy\nLatin America\nLead", "location_type": "flexible", "job_type": "full_time", "min_experience": 4, "max_experience": 4, "apply_link": "https://zazmic.com/vacancy/intermediate-software-engineer-sales-support-portugal-olnly/", "description": "Latin America, PortugalMiddle Type: Remote Average VacancyDear Candidate,Before submitting your resume, please pay attention to the location – we will not be able to review your resume and provide feedback if you are not (in fact) located in the location of the vacancy. Thanks for understanding Who we are:We believe that we are better together and welcome you for who you are. Our workplace is for everyone, just like our people-powered platform. We want you to bring your unique perspective and experiences so we can collectively revolutionize travel and discover the good out there together. Key Responsibilities:Work closely with internal teams across the organization to develop custom technical solutions for advertising partners. Embed with various teams, adapting to different projects and objectives while delivering high-quality solutions. Independently design and implement features across the stack (database, API, and UI). Build scalable and maintainable microservices and APIs. Develop interactive user interfaces using modern front-end technologies. Ensure high-quality software through automated testing and iterative improvements. Write thorough documentation to support newly developed features. Quickly learn and apply new technologies to solve customer-specific challenges. Monitor, support, and maintain features after release. What We’re Looking For:At least 4 years of experience in commercial software development. Proficiency in both front-end (HTML, CSS, JavaScript, React) and back-end (Java, GraphQL) technologies. Strong understanding of relational database design and querying. Ability to quickly adapt to new technologies and environments. Excellent problem-solving and analytical skills with a focus on simplicity and efficiency. High-quality verbal and written communication skills. Strong sense of ownership and urgency in delivering solutions. Previous experience supporting advertising or sales-focused solutions is a bonus. If you thrive in dynamic environments, enjoy working across different teams, and love solving complex technical challenges, we’d love to hear from you! Why join to us:Work Anywhere: Embrace the freedom to work from anywhere in the world. Your office could be a beach, a cozy café, or wherever you feel most inspiredFlexibility: Wave goodbye to the 9-to-5 grind. We believe in a flexible working schedule that fits your life. Sponsored Education: We're invested in your growth. Enjoy sponsored education and training, ranging up to 50%. Personal Development: We're not just about work; we're about your growth. Craft your personal development plan and watch your career soar. Regular Salary Reviews: Your hard work won't go unnoticed. We conduct regular salary reviews to ensure you're fairly rewardedCareer Advancement: The sky's the limit! Move up the ladder based on your performance, and your career trajectory could surprise youCorporate Events: From team outings to memorable celebrations, we know how to have a good time togetherEnglish Classes: Enhance your language skills and open doors to global opportunities with our sponsored English classesHealth Matters: Your health is our priority. Get your annual flu shot on usWork Equipment: We provide top-notch tools. Receive a compensation of $600 for your work equipment Paid vacation, sick leaves Perks of being a Company Work from anywhere in the world Gifts to mark significant life events English classes with a native speakerMain numbers2015founded300+employees worldwide40current projects44locations", "ctc": null, "currency": null, "meta": {}}]