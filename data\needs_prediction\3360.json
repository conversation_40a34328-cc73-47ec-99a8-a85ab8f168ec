[{"jd_link": "https://radiansys.com/career/angular-developer", "company_id": 3360, "source": 3, "skills": "", "title": "Angular Developer", "location": "Gurugram • India | Software Developer", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://radiansys.com/career/angular-developer", "description": "Apply Now We are looking for an experienced Front End Engineer with good development skills in Angular (6/8/9/11/12). Candidates should have hands-on experience in the Design, Development, and API implementation of the mobile apps. Role And Responsibilities: Develop modern software applications in collaboration with the UI Team Lead solution definition and guide teams through execution and implementation Required Skills and Capabilities: Good to have hands-on experience in testing tools like Jasmine/Karma Skills in one or more chosen frameworks/libraries like jQuery or Bootstrap HTML 5, CSS Module Web pack, Babel, NPM, Type Script, AG Grid, PTable Should have expert level knowledge of HTML5, CSS3, JavaScript/jQuery, Bootstrap, Angular Should have strong debugging skills on Developer Tools of the browser and Working with Cross Browser UI Good to have knowledge of other JS frameworks KnockoutJS, ExtJS, Ember JS, Backbone JS, RequireJS Should have experience in Response / Adoptive Web Design for desktop, tablet and mobile Should have experience in Agile Software Development projects Should have a solid understanding and experience with Object-Oriented design and development Experience in client communication and working with clients on requirements and delivery Should have knowledge and understanding of accessibility principles and techniques Should have good hands-on experience with Object-oriented JavaScript and Design Patterns Should have experience in publishing and consuming Services using REST API Should have knowledge on experience on Java, Web services using REST /SOAP over JSON is a plus Good to have hands-on experience in core java", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://radiansys.com/career/business-development-manager", "company_id": 3360, "source": 3, "skills": "", "title": "Business Development Manager", "location": "Gurugram • India | BDM", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://radiansys.com/career/business-development-manager", "description": "Apply Now We are looking for a Sales Manager for our Gurugram office who will be expected to perform in areas of Enterprise Sales, IT Software Solutions, and Services Sales. The candidate will majorly responsible for Account Hunting / Direct Sales for solution selling in multiple verticals hence need to be well versed with the dynamics of verticals such as Banking/Education/Insurance/Healthcare /Logistics & Transportation/Telecom/Manufacturing etc. Key Areas: Enterprise Mobility, Enterprise Application, Application Services, Business & Technical Consulting, Consultative Sales Role And Responsibilities: Experience in defining and developing mid and long-term tactical and strategic initiatives for various service offerings of the company Overall Sales Leadership for different international territories like the USA, Europe, Asia Pacific, and Australia Lead & manage inside sales team with campaign planning, targeted list building, outbound campaigning through email & calls, pre-sales & business analysts Drive the Proposal making, qualifying leads, negotiation, etc. Ability to Segment, Target, and Position Radiansys Services to the CXO Level Professionals in Leading Enterprises Develop Strategic and Key Account to Ensure Customer Satisfaction and Revenue Growth Ability to forge partnerships & strategic alliances for long-term association contributing to sustained growth & revenues for the company Create cross-functional collaboration with Delivery managers, Project managers & Business analysts & Technical architects Track industry trends, competition activities, and best practices Revenue responsibilities in excess of $1 Million Required Skills and Capabilities: 5+ years of experience in direct selling and sales management roles with the majority of experience in the USA Experience in sales of Software Automation, Custom Application, Mobile Apps, CRM Tools Accountable to meet Quarterly / Yearly Sales Targets Must be a well-networked professional who can win a new client's logos quickly Must have experience in Corporate Sales and good experience of handling large teams Strong business acumen and industry expertise Experience in managing and directing a sales team Completes sales cycle including preparing proposals, estimation, negotiation, and closure Should have exposure in the mobility domain", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://radiansys.com/career/bi-business-system-analyst-(bsa)", "company_id": 3360, "source": 3, "skills": "", "title": "BI Business System Analyst (BSA)", "location": "Bay Area, CA/Chicago, IL • United States | Business System Analyst", "location_type": null, "job_type": null, "min_experience": 7, "max_experience": null, "apply_link": "https://radiansys.com/career/bi-business-system-analyst-(bsa)", "description": "Apply Now Role And Responsibilities: 7+ Years of experience in BSA role with strong background and experience with Datawarehouse/ BI/Visualization, Insights & analytics, and Power BI. Can conduct/perform Design thinking sessions, Market research, data analysis, Business analysis, use case and KPI development with sr. business stakeholders, Data analysis, EDA- Exploratory data analysis Defining the vision and roadmap Negotiation skills to bridge business and technical vision Problem solving & Conflict resolution Responsible for communicating with stakeholders across the board, including customers, business managers, and the development team. Managing the product backlog Prioritization and release management Responsible for making complex data sets more accessible, understandable and usable, dashboarding and reporting and transforming, improving, and integrating data, depending on the business requirements. Work collaboratively with stakeholders and business partners to review requirements and architecture appropriate solutions. Responsible for translating business needs into technical solution with a iterative approach Provide leadership in strategic data and analytics assessment process and design roadmaps for implementation. Lead teams to develop Proof-of-Concept projects to validate new architectures and solutions. Required Skills and Capabilities: Can conduct/perform Design thinking sessions, Market research, data analysis, Business analysis, use case and KPI development with sr. business stakeholders, Data analysis, EDA- Exploratory data analysis Strong Visualization experience (PowerBI) Hands on SQL work experience Strong in SQL, Stored procedures, best practices for performance management Good understanding and experience of cloud environment, Security and Performance management.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://radiansys.com/career/blockchain-developer", "company_id": 3360, "source": 3, "skills": "", "title": "Blockchain Developer", "location": "Gurugram • India | Software Developer", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://radiansys.com/career/blockchain-developer", "description": "Apply Now We are looking for a blockchain developer who will be responsible for designing, implementing, and supporting a distributed blockchain-based network. Your primary responsibility will be analysing requirements, designing blockchain technology around a certain business model, and the building and launching of a blockchain network. Role And Responsibilities: Research, design, develop, and test blockchain technologies Brainstorm and help evaluate applications for new tools and technologies as they continually evolve Creating Smart Contracts Developing NFT marketplace Maintain and extend current client- and server-side applications responsible for integration and business logic Be involved in the global blockchain community work on implementing and integrating the latest improvement proposals Document new solutions as well as maintaining the existing ones Required Skills and Capabilities: Strong software development background Experience working with large codebases Experience working with open-source projects Proficiency in one of the following languages: C++, Java, JavaScript Strong knowledge of common algorithms and data structures Familiarity with basic cryptography Familiarity with P2P networks Strong knowledge of bitcoin-like blockchains Strong knowledge of concurrency and writing efficient and safe multi threaded code Strong knowledge of STL, C++11 Proficiency in the Qt Widgets module", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://radiansys.com/career/it-business-analyst", "company_id": 3360, "source": 3, "skills": "", "title": "IT Business Analyst", "location": "Gurugram • India | Business Analyst", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://radiansys.com/career/it-business-analyst", "description": "Apply Now We are looking for a business analyst to join our project team. You will work alongside other business analysts and report directly to the project manager. Your main tasks will include performing detailed requirements analysis, documenting processes, and performing some user acceptance testing. To succeed in this role, you should have a natural analytical way of thinking and be able to explain difficult concepts to non-technical users. Role And Responsibilities: Identifying potential customers, creating a pitch, and putting it forward Research and requirement analysis Create detail scope of work document along with proper documentation of every detail Decision making Required Skills and Capabilities: Having a good understanding of software technologies like React Native, React, PHP, Laravel, Node, iOS, Android, Python, etc. Proficient in all kinds of documentation Experienced in Writing User stories Planning and overseeing new marketing initiatives MS Office and Mockups experience highly preferred Strong written and verbal communication skills Excellent knowledge of software methodologies Ability to meet deadlines Excellent Team Player with strong communication, interpersonal and organizational skills", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://radiansys.com/career/node-developer", "company_id": 3360, "source": 3, "skills": "", "title": "Node Developer", "location": "Gurugram • India | Software Developer", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://radiansys.com/career/node-developer", "description": "Apply Now We are looking for a highly capable Node. js developer to optimize our web-based application performance. You will be collaborating with our front-end application developers for designing back-end components and integrating data storage and protection solutions. Role And Responsibilities: Developing and maintaining all server-side network components Ensuring optimal performance of the central database and responsiveness to front-end requests Collaborating with front-end developers on the integration of elements Designing customer-facing UI and back-end services for various business processes Developing high-performance applications by writing testable, reusable, and efficient code Implementing effective security protocols, data protection measures, and storage solutions Running diagnostic tests, repairing defects, and providing technical support Documenting Node. js processes, including database schemas, as well as preparing reports Recommending and implementing improvements to processes and technologies Keeping informed of advancements in the field of Node. js development Required Skills and Capabilities: Bachelor's degree in computer science, information science, or similar At least two years of experience as a Node. js developer Extensive knowledge of JavaScript, web stacks, libraries, and frameworks Knowledge of front-end technologies such as HTML5 and CSS3 Above grade interpersonal, communication, and collaboration skills Exceptional analytical and problem-solving aptitude Great organizational and time management skills Availability to resolve urgent web application issues outside of business hours", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://radiansys.com/career/python-django-developer", "company_id": 3360, "source": 3, "skills": "", "title": "Python/Django Developer", "location": "Gurugram • India | Software Developer", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://radiansys.com/career/python-django-developer", "description": "Apply Now We are looking for an experienced Backend Engineer with excellent development skills in Python and Django framework. Role And Responsibilities: Write effective, scalable code Develop cloud-based Python Django software products Develop back-end components to improve responsiveness and overall performance Integrate user-facing elements into applications Test and debug programs Participating in architectural, design, and product discussions Improve functionality of existing systems Implement security and data protection solutions Assess and prioritize feature requests Coordinate with internal teams to understand user requirements and provide technical Required Skills and Capabilities: Bachelor’s degree in computer programming, computer science, or a related field Expert in Python with knowledge of Django Web Framework Familiarity with some ORM (Object Relational Mapper) libraries Working knowledge of MongoDB and MySQL Fair understanding of building RESTful APIs Worked with Django application deployment on AWS, Heroku, GCP, Docker platforms Basic understanding of front-end technologies, such as JavaScript, HTML5, and CSS3 Understanding of accessibility and security compliance Knowledge of user authentication and authorization between multiple systems, servers, and environments Familiarity with event-driven programming in Python Able to create database schemas that represent and support business processes Strong unit test and debugging skills Proficient understanding of GitHub, JIRA, GSuite Critical thinker and problem-solving skills Great interpersonal and communication skills", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://radiansys.com/career/react-native-developer", "company_id": 3360, "source": 3, "skills": "", "title": "React Native Developer", "location": "Gurugram • India | Software Developer", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://radiansys.com/career/react-native-developer", "description": "Apply Now We are looking for a React Native developer interested in building performant mobile apps on both the iOS and Android platforms. The candidate must exhibit commitment to perfection and the will to play along equally throughout the process of product development. Role And Responsibilities: Responsible for building these applications along Coordinating with the teams responsible for other layers of the product infrastructure Required Skills and Capabilities: Sound knowledge of current JavaScript standards like ECMA Script 2015 (aka ES6), ECMA Script 2019 (aka ES10), experienced in React solutions Good understanding of JavaScript superset that adds strong typing i. e. , TypeScript Must have knowledge of React Native libraries like React-Router, Redux, Redux-Thunk, Redux-Saga, GraphQL Good understanding of Package managers like NMP and Yarn Knowledge of test runners- Jest, Mocha Excellent skills in HTML/CSS/JS Good understanding of object-oriented programming concepts Knowledge of the latest trends and technologies and the ability to use them in the apps Should have basic knowledge of Android/Java or iOS/Objective-C for making custom native plugins as and when needed Excellent team player with strong communication, interpersonal, and organizational skills Ability to learn new technologies and APIs Interested in long term commitment Strong written and verbal communication skills Ability to meet deadlines", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://radiansys.com/career/reactjs-vuejs-developer", "company_id": 3360, "source": 3, "skills": "", "title": "ReactJS/VueJS Developer", "location": "Gurugram • India | Software Developer", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://radiansys.com/career/reactjs-vuejs-developer", "description": "Apply Now We are looking for an experienced Front End Engineer with good development skills in React and Vue. Candidates should have hands-on experience in the Design, Development, and API implementation of the mobile apps. Role And Responsibilities: Developing new user-facing features using React. js and Material UI Building reusable components and front-end libraries for future use Translating designs and wireframes into high-quality code Required Skills and Capabilities: Experience with popular React. js workflows (such as Flux or Redux) Familiarity with RESTful APIs Knowledge of Test runners - Jest, Mocha Sound knowledge of current JavaScript standards like ECMA Script 2015 (aka ES6), ECMA Script 2019 (aka ES10) Highly proficient with Vue. js framework and its core principles such as components, reactivity, and the virtual DOM Familiarity with the Vue. js ecosystem, including Vue CLI, Vuex, Vue Router, and Nuxt. js Experience with common front-end development tools such as Babel, Webpack, NPM, etc. Familiarity with code versioning tools such as Git, SVN, and Mercurial Good understanding of Package managers NMP and Yarn Excellent skills in HTML/CSS/JS Good understanding of object-oriented programming concepts Knowledge of the latest trends, APIs and technologies and the ability to use them in the apps Ability to understand business requirements and translate them into technical requirements A knack for benchmarking and optimization Excellent Team Player with strong communication, interpersonal and organizational skills Interested in long term commitment Strong written and verbal communication skills Ability to meet deadlines", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://radiansys.com/career/sfdc-architect", "company_id": 3360, "source": 3, "skills": "", "title": "SFDC Architect", "location": "Bay Area, CA • United States | Sr Software Developer", "location_type": null, "job_type": null, "min_experience": 10, "max_experience": null, "apply_link": "https://radiansys.com/career/sfdc-architect", "description": "Apply Now Preferred location is Bay Area, however if the profile is really good but has a location constraint, we can still consider and accommodate the option of working remotely (anywhere in the US). Practice Lead - Salesforce Billing and Revenue Architect We are looking for a Practice Head & Principal Architect with expertise in Salesforce subscription billing, CPQ and Revenue modules, accounting systems and finance business processes to join our Professional Services team. You will have strong business acumen, data and technology experience in addition to possessing excellent strategy, business partnering and collaboration skills. You take the initiative to identify high impact projects and work cross functionally to execute and realize our business strategy. Role And Responsibilities: Design, architect, Implement and serve as a technical strategic advisor on Salesforce Sales and Revenue Cloud platform for our Clients. Establish trust and strong relationships with senior leaders, Ops, and other organizational units as it pertains to the finance business processes and systems. Establish strong collaboration with Clients on the Salesforce CPQ, Billing and Revenue Implementation to ensure end-to-end success of the Quote to Cash and Revenue Recognition processes. Act as an owner and ultimate escalation point for the systems you build and maintain. Identify, evaluate and recommend key technologies required to support and improve the business process centered on the billing platform and systems. Lead cross-functional system implementation efforts end to end. Craft comprehensive system design specifications that translate business requirements into technical requirements for implementation. Set expectations around technical feasibility of requested features and functionality, and partner to provide training and technical support as needed. Developing and executing on a strategy to drive install base sales growth. Strategize, Build, Recruit, Manage, Inspire and lead a team of Salesforce practice professionals. Coaching and developing your team through call shadowing, 1:1 coaching, and KPI/metrics reviews. Staying actively involved in the sales cycle and bringing in senior leadership and other additional resources as needed. Taking a data-driven approach to understanding your business and identifying opportunities for continuous improvement & demonstrating stellar pipeline management and forecasting skills. Qualifications: BS/MS degree Computer Science or an Accounting related experience 10+ years of CRM or ERP experience with core expertise in Salesforce CPQ, Billings and Revenue Cloud Must have Salesforce Certification, a Salesforce trailblazer and good hands-on experience on Salesforce Platform with good understanding on Billing, Subscriptions, Invoices and Payment Strong functional knowledge of Subscription Management and Billing Knowledge of Revenue Management is a Plus Knowledge of Other Billing platforms like Zuora Billing is a plus", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://radiansys.com/career/senior-business-analyst-salesforce", "company_id": 3360, "source": 3, "skills": "", "title": "Senior Business Analyst/Salesforce", "location": "Pacific / Eastern Time Zones • United States | Senior Business Analyst", "location_type": null, "job_type": "contract", "min_experience": 2, "max_experience": null, "apply_link": "https://radiansys.com/career/senior-business-analyst-salesforce", "description": "Apply Now We’re seeking a Senior Business Analyst who will work closely with the customer stakeholders and Solution Architect and Functional Consultant to deliver successful projects. Key Areas: Senior Business Analyst/Salesforce, CPQ, Salesforce Sales, Service, Marketing and Community Clouds, CPQ & Billing Role And Responsibilities: Conduct discovery and requirements gathering workshop in partnership with functional consultant <PERSON><PERSON> as the functional expert for Salesforce and stripe and guide customers towards achievable options through configurations and set expectations Excellent oral and verbal presentation skills and experience with driving sprint demos and creating completing deliverables including requirements, design, user stories, and testing documentation Work closely with the Solution Architect and Customer resources to ensure the system meets the needs of the Customer Stay abreast of marketplace solutions for delivering a comprehensive solutions with available off the shelf components Required Skills and Capabilities: Dependable team player with proactive communication skills Problem-solving skills with the ability to plan and (re)prioritize and manage change Attention to detail while understanding the big picture 2+ years’ experience as a Business Analyst on Salesforce or similar implementations Experience creating documentation for requirements, design, test, and training Experience with two or more: Salesforce Sales, Service, Marketing and Community Clouds, CPQ & Billing Empathize with and understand users experience for successful adoption Ideate and iterate with a strong bias for action and execution for faster releases Possess kid like curiosity and have an unquenchable thirst for learning and tinkering Do the right thing with honesty and integrity", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://radiansys.com/career/ruby-on-rails", "company_id": 3360, "source": 3, "skills": "", "title": "Ruby on Rails", "location": "Gurugram • India | Software Developer", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://radiansys.com/career/ruby-on-rails", "description": "Apply Now We are looking for a Ruby on Rails Developer to join our engineering team. In this role, you will be responsible for building API interfaces and backend infrastructure and collaborate with UI, connectivity and DevOps teams to come up with elegant solutions to complex problems. Role And Responsibilities: Designing and developing new web applications Maintaining and troubleshooting existing web applications Writing and maintaining reliable Ruby code Integrating data storage solutions Creating back-end components Identifying and fixing bottlenecks and bugs Integrating user-facing elements designed by the front-end team Connecting applications with additional web servers Maintaining APIs Required Skills and Capabilities: Bachelor’s degree in computer science, computer engineering, or related field Experience working with ruby on rails as well as libraries like Resque and RSpec Ability to write clean ruby code Proficiency with code versioning tools including Git, Github, SVN, and Mercurial Experience with AngularJS or BackboneJS Familiarity with MVC, Mocking, RESTful, and ORM Good understanding of front-end technologies including HTML5, JavaScript, and CSS3 Knowledge of server-side templating languages including Slim and Liquid Familiarity with testing tools", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://radiansys.com/career/us-it-recruiter", "company_id": 3360, "source": 3, "skills": "", "title": "US IT Recruiter", "location": "Gurugram • India | Technical Recruiter", "location_type": null, "job_type": "part_time", "min_experience": 1, "max_experience": 4, "apply_link": "https://radiansys.com/career/us-it-recruiter", "description": "Apply Now We are looking for a US IT Recruitment Professional with good experience to support our Direct Client Requirements. We provide an excellent working environment for both male and female recruiters. Please Note: This is a Night Shift job & the working hours will be 8:30 pm-5:30 am IST. Role And Responsibilities: Work on US IT requirements primarily Required Skills and Capabilities: Minimum 1-4 years of US IT recruitment experience Must have excellent communication skills and an amicable attitude Expert in sourcing resumes from job portals (JobDiva, Dice, Monster, Linked-in, etc. ) Expert in using Boolean search strings Good exposure in full cycle of technical recruitment which involves sourcing, screening and interviewing and placing candidates in contract, contract-to-hire, and permanent positions Good recruitment track-record of placing consultants in both W2 and Corp to Corp contracts Good understanding of contract types (W2, 1099, Corp to Corp) Excellent communication skills and ability to work with US Citizens / Green Card holders and EAD's for W2 Contracts Highly self-motivated", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://radiansys.com/career/software-quality-analyst", "company_id": 3360, "source": 3, "skills": "", "title": "Software Quality Analyst", "location": "Gurugram • India | Automation Engineer", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://radiansys.com/career/software-quality-analyst", "description": "Apply Now We are looking for a highly skilled test automation engineer to design automation tests. Test automation engineers' duties include designing automation scripts and finding solutions for automation problems. Role And Responsibilities: Review and analyse requirements, specifications, and technical design documents, providing timely feedback Develop detailed, comprehensive, and well-structured test plans and test cases Executing regression and automation suite. Prioritize and plan testing activities Play the role of test engineer: Design, develop, and execute automated tests Identify and report issues found, then verify that issues are resolved Perform regression testing Required Skills and Capabilities: Expertise in Core JAVA, C# or Python and OOPS concepts Expertise in Automation using Selenium/UFT/Test Complete/Protractor etc. Knowledge of Web Service Rest API testing Hands-on Mobile Automation tools is an added advantage Excellent understanding of the Testing Lifecycle and the different types of testing such as Functional and Integration Testing Knowledge of the different life cycle development models and methodologies used to develop large software-intensive platforms Ability to understand existing Test Processes and Plans and identify improvements and efficiencies. Experience with virtualised products/services/solutions Proven work experience in software quality assurance Strong knowledge of software QA methodologies, tools, and processes Hands-on experience with automated testing tools A keen eye for detail and a commitment to excellence Be willing to help your teammates, share your knowledge with them, and learn from them Be open to receiving constructive feedback and turning it into process improvements", "ctc": null, "currency": null, "meta": {}}]