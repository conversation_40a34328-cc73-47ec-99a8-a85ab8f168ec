[{"jd_link": "https://www.softbistro.com/careers/junior-wix-developer", "company_id": 3365, "source": 3, "skills": "", "title": "Junior WIX Developer (Remote)", "location": "", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.softbistro.com/careers/junior-wix-developer", "description": "Junior WIX Developer (Remote)We are looking for a passionate and motivated Junior SEO Specialist to join our team and help optimize our online presence. This position is ideal for someone who has a strong desire to learn, grow, and build a career in WIX and SEO. Job Description:As an SEO Specialist, you will play a key role in optimizing our website to increase visibility and improve search engine rankings. Our website is built on WIX, and we will provide comprehensive training on how to use the platform effectively for SEO. Key Responsibilities: Conduct keyword research to identify opportunities for website content optimization. Develop and maintain our current website on Wix Adapt pre-made templates to meet changing business needs Optimize design for different devices (potentially responsive Wix design templates) Ability to work with Web. Dev on Google’s requirements for Core Web Vitals and optimize LCP, CLS, INP, FCP, TTFP and other parameters of the website Monitor and analyze website performance using SEO tools such as Google Analytics, Google Search Console, Pagespeed. Web. Dev, SEMrush, or Ahrefs. Conduct on-page optimization efforts, including meta tags, meta descriptions, alt texts to all images, and internal linking on the WIX platform. Help implement successful on-page/off-page SEO strategies, including link building and outreach. Requirements: English – intermediate level (B1)+ Basic understanding of SEO principles and best practices. Familiarity with SEO tools such as Google Analytics, Google Search Console, SEMrush, Ahrefs, or similar. Strong analytical skills and attention to detail. Strong desire to learn and develop skills in SEO and digital marketing. Willingness to be trained on using WIX for SEO and content management. Preferred Qualifications: Completed certifications in SEO, digital marketing, or a related field. Experience with WIX or other website management systems (like WordPress or Shopify) is beneficial but not required. Familiarity or any experience working with Wix Basic knowledge of HTML/CSS Understanding of web design basics and UX/UI Keen eye for design Ability to work independently and in a team, with a proactive attitude What We Offer: Comprehensive training in SEO, digital marketing, and the WIX platform. Opportunities for career development and growth within the company. A positive and supportive work environment. Flexible work hours. Remote work with a flexible schedule ​Join us in making a difference in the fitness community while advancing your career in WIX and SEO!", "ctc": null, "currency": null, "meta": {}}]