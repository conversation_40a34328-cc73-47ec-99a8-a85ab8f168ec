[{"jd_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000000100084/Java-Developer?source=CareerSite", "company_id": 3374, "source": 3, "skills": "", "title": "Java Developer", "location": "Pune, India   | Posted on 08/01/2023", "location_type": null, "job_type": null, "min_experience": 2, "max_experience": null, "apply_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000000100084/Java-Developer?source=CareerSite", "description": "Job Description Job ResponsibilitiesDeveloping, deploying and maintaining Java/J2EE based web applicationsCreate solutions for the backend systems to support millions of usersEstimate the work efforts at varied levels and entail in design evaluation, determining the architectures, lay-out and style controlsDeploy the web applications on various types of production environment (Linux, Windows servers etc). Work in coordination with frontend developers in designing architecture and implement designing strategyCollaborate with people in other disciplines for the production and delivery of project deliverable. RequirementsJob Description:Hands-on Java experience minimum of 2+Yrs years. Hands-on experience Spring / Spring Boot / Spring MVC. Hands-on experience Hibernate. Hands-on experience on creating REST APIs. Databases (Must have) - PostgreSQL or MySQLMust have the capability to write complex SQL queries. Databases (Good to have) - MongoDB, Redis I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000000129013/Smart-Mobility-Transportation-Software-Sales-Consultant?source=CareerSite", "company_id": 3374, "source": 3, "skills": "", "title": "Smart Mobility Transportation Software Sales Consultant", "location": "Pune, India   | Posted on 08/07/2023", "location_type": null, "job_type": "part_time", "min_experience": 5, "max_experience": null, "apply_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000000129013/Smart-Mobility-Transportation-Software-Sales-Consultant?source=CareerSite", "description": "Job Description - Identify and prospect potential clients in the transportation industry, including transportation authorities, transit agencies, logistics companies, and ride-sharing platforms. - Understand client needs and challenges related to transportation operations, fleet management, mobility analytics, and customer experience. - Consult with clients to analyze their requirements and propose customized smart mobility software solutions that address their specific pain points and deliver measurable value. - Conduct product demonstrations and presentations to showcase the features, benefits, and capabilities of our smart mobility software solutions. - Collaborate with our product and technical teams to develop tailored solutions, provide accurate quotes, and create compelling proposals. - Build and maintain strong relationships with key stakeholders, including C-level executives, transportation managers, and IT decision-makers. - Stay updated on industry trends, emerging technologies, and regulatory developments in the smart mobility and transportation space. - Attend industry events, conferences, and trade shows to network, generate leads, and stay informed about market dynamics. - Meet or exceed sales targets by effectively managing the sales cycle, from lead generation to contract negotiation and closure. - Provide post-sales support and ensure client satisfaction, fostering long-term partnerships and identifying upselling opportunities. Requirements-Must have 5+ yrs of experience in Software Sales with a proven track record of success, preferably in the transportation or smart mobility industry. - In-depth knowledge of smart mobility technologies, transportation operations, and fleet management systems. - Strong understanding of mobility analytics, data-driven decision-making, and customer experience in the transportation sector. - Excellent communication, presentation, and consultative selling skills, with the ability to articulate complex concepts and solutions to diverse audiences. - Ability to build and maintain strong clients relationships, with a customer-centric approach. - Self-motivated, results-oriented, and able to work independently and as part of a team. - Familiarity with CRM software and sales tracking tools. - Willingness to travel to client locations as required. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000000473008/Project-Manager?source=CareerSite", "company_id": 3374, "source": 3, "skills": "", "title": "Project Manager", "location": "Pune, India   | Posted on 07/16/2024", "location_type": null, "job_type": "full_time", "min_experience": 4, "max_experience": 5, "apply_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000000473008/Project-Manager?source=CareerSite", "description": "Job Description Job ResponsibilitiesTo lead a team that comprises of project personnel; it includes designers, researchers and engineersT<PERSON> act as the first point of contact for clientsTo create a project plan with details and illustrations to identify the flow of activities needed in order to successfully conclude a projectAssign, schedule and review the project at regular intervals to analyze its progress in terms of quality guidelines and time constraintsManage project execution as per the project planIdentify, track, monitor and communicate project related issues and other variances, scope changes, contingencies that may crop up while implementation of ideasAssemble project scope and maintain projects related documentationsMaintain solid communication and better work relationships with the client and the teamExperience and SkillsWe are looking for overall 10+ years of experience and minimum 4-5 years in project management. Prior experience of Software Development is must ( Java/ . Net/ Frontend etc)Familiarity with scrum or similar SDLC processesBudget planning and management of projectFamiliar with using project collaboration tools like, Basecamp, JIRA, ZohoExperience with working towards continuous team improvement I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000000478001/Angular-Developer?source=CareerSite", "company_id": 3374, "source": 3, "skills": "", "title": "Angular Developer", "location": "Pune City, India   | Posted on 01/08/2024", "location_type": null, "job_type": null, "min_experience": 3, "max_experience": 6, "apply_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000000478001/Angular-Developer?source=CareerSite", "description": "Job Description We are seeking a skilled Angular Developer with 3-6 years of experience to join our development team. As an Angular Developer, you will be responsible for developing and maintaining web applications using Angular framework. You will work closely with cross-functional teams to translate business requirements into high-quality code and ensure the best possible performance, usability, and responsiveness of the applications. Responsibilities:Develop robust and scalable web applications using Angular framework. Collaborate with product owners, designers, and other developers to gather and refine requirements. Design and implement responsive user interfaces with a focus on usability and user experience. Write clean, maintainable, and efficient code following best practices and coding standards. Optimize application performance and ensure high scalability. Conduct thorough testing and debugging to ensure the application meets quality standards. Collaborate with the back-end development team to integrate front-end and back-end components. Stay up-to-date with the latest trends and technologies in Angular development and implement best practices. Participate in code reviews and provide constructive feedback to other team members. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000001880001/Business-Analyst?source=CareerSite", "company_id": 3374, "source": 3, "skills": "", "title": "Business Analyst", "location": "Pune, India   | Posted on 11/16/2023", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000001880001/Business-Analyst?source=CareerSite", "description": "Job Description Demonstrate experience in requirement elicitation, system analysis and translating business problems to functional specification • Thorough skills in creating wireframes, drafting user stories, creating proposals /presentations • Expertise in creating workflows / process maps / cross functional flowcharts • Proven track record in coordinating efficiently between business, technical and management teams • Contribute beyond BA role in business facing activities like presales or delivery roles like project coordination, testing I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000006477002/PHP-Developer?source=CareerSite", "company_id": 3374, "source": 3, "skills": "", "title": "PHP Developer", "location": "Pune, India   | Posted on 08/01/2023", "location_type": null, "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000006477002/PHP-Developer?source=CareerSite", "description": "Job Description Job Responsibilities: · Developing, deploying and maintaining PHP based web applications. · Create solutions for the backend systems to support millions of users. · Estimate the work efforts at varied levels and entail in design evaluation, determining the architectures, lay-out and style controls. · Deploy the web applications on various types of production environment (Linux, Windows servers etc). · Work in coordination with frontend developers in designing architecture and implement designing strategy. · Collaborate with people in other disciplines for the production and delivery of project deliverable. RequirementsExperience and Skills required:· Must have 3+ years of experience in developing web application using PHP technologies. · Must have experience in framework- Laravel. · Strong knowledge and experience in javascript and jQuery. · Expert knowledge of SQL and database technologies. · Sound Knowledge of web deployment concepts, web services such as REST and SOAP APIs. · Possess experience in developing social and location aware applications, which are scalable. · Prior experience in other technologies and frameworks such as Wordpress, Drupal, Magento, Joomla, etc. is an added advantage. One should be willing to work in Java also after provided training. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000006944023/Sales-Manager?source=CareerSite", "company_id": 3374, "source": 3, "skills": "", "title": "Sales Manager", "location": "Pune, India   | Posted on 03/07/2024", "location_type": null, "job_type": "part_time", "min_experience": 8, "max_experience": 12, "apply_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000006944023/Sales-Manager?source=CareerSite", "description": "Job Description The Senior Sales Manager is responsible for the sales in the USA region. Should be able to open new leads in the region and should be able to close deals independently. Generate new leads and direct clients for digital technology services. End to end sales management from opening a lead to maintaining relationships to solution handling to contract negotiations. Provide exemplary customer service/relationship management and consultative selling. Negotiate contract rates with clients, with a focus on company profitability goals and objectives. Engage with C-level, VP and Director-level contacts of mid-size and large companies. Attending relevant events and presenting Mobisoft’s competencies in events and networking forums. The ability to build strong customer relationships and new business pipeline generation is a key for a successful candidate. We are looking for a self-driven individual with a proven track record in IT services sales, proactive pipeline generation, competency sales, solutions and accelerators presentations, closing deals. Requirements8-12 years’ experience in selling IT services in the digital space. Experience in international selling to businesses in the US and other regions. Experience selling digital services for mobile, cloud, ODC is preferred. An excited hunter with a proven ability to close new business; prior experience growing or building sales territories. Must possess strong communication, interpersonal and relationship building skills. Intellectually curious - you ask open-ended questions and are genuinely interested in learning about your client's needs. Should have high energy, charisma and gravitas. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000006965077/Content-Strategist?source=CareerSite", "company_id": 3374, "source": 3, "skills": "", "title": "Content Strategist", "location": "Pune, India   | Posted on 08/31/2023", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": 5, "apply_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000006965077/Content-Strategist?source=CareerSite", "description": "Job Description We are looking for qualified and ambitious content writing professionals who are resourceful and sharp to deliver interactive content. Someone who believes in the co-existence of great content and incredible designs, and someone who is geared to sketch out editorial content through planning right from the initial strategy to implementing post-launch optimisation. Writing B2B and B2C content for happening technologies like mobile, cloud, web, wearable, etc. Strategize, research and execute high-level content. Tasks include generating engaging blog post, spec docs, web pages, case studies, online marketing material, white papers, video scripts, press releases, etc with strong knowledge of writing for different client personas. Collaborating with developers and designers and other technical heads of the company to generate technically sound content which has a universal reach. Writing engaging content from the reader's point of view. Staying abreast of the latest news, reports and developments of the industry, besides contributing towards ideas for the refinement and development of content strategy. RequirementsCandidate should possess 5 yrs+ years of experience in B2B/ technical writing. Mobility and cloud know-how would be a plus. Write engaging content to involve and inspire readers. We are looking for a writer of the highest caliber with international standards to write fascinating viral content. Can write on highly technical topics using simple, everyday language. Ability to edit for grammatical errors and areas of improvement in a given piece or write-up. Good to have prior exposure in Healthcare expertise. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000006965117/DevOps-Engineer?source=CareerSite", "company_id": 3374, "source": 3, "skills": "", "title": "DevOps Engineer", "location": "Pune, India   | Posted on 01/31/2024", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000006965117/DevOps-Engineer?source=CareerSite", "description": "Job Description · Continuous Integration and Continuous Delivery (CI/CD): Establishing and maintaining CI/CD pipelines to automate the build, testing, and deployment of applications. · Automation: Terraform, Cloud Formation, Bash. · Manage and setup Linux or Windows servers. · Monitoring and Logging: Setting up monitoring and logging solutions. · Scalability and High Availability: Designing and implementing solutions that ensure applications can scale up or down based on demand while maintaining high availability and fault tolerance. · Security and Compliance: Implementing security best practices by configuring access controls, encryption, and identity management. Ensuring compliance with industry standards and regulations. · Networking: Configuring and managing networking components such as Virtual Private Clouds (VPCs), subnets, security groups, and load balancers to create a secure and efficient network architecture. · Containerization and Orchestration: Working with container technologies like Docker and container orchestration platforms like Kubernetes to package and manage applications consistently across different environments. · Troubleshooting and Support: Diagnosing and resolving issues related to application and infrastructure performance, connectivity, and other technical challenges. · Version Control: Managing source code repositories and version control systems (e. g. , Git) to maintain codebase integrity and enable collaboration among development teams. · Disaster Recovery and Backup: Implementing strategies for data backup, disaster recovery, and business continuity to ensure data protection and minimal downtime in case of failures. · Cost Optimization: Monitoring and managing AWS resource usage to optimize costs and ensure efficient utilization of cloud resources. Requirements· Experience with automated deployments and source code / configuration management tools - GitHub Actions, Azure Pipelines, AWS OpsWorks, Jenkins, etc. is a must have. · Hands-on experience with AWS /Azure services or infrastructure architecture is a must have. · Hands-on experience with managing Linux servers, containers and Docker is a must have. Experience with Kubernetes is good to have. · Hands-on experience with Cloud formation or Terraform or Azure ARM templates is good to have. · Sound understanding of what it takes to have a production ready solution and operate in the DevOps model is required. · Good to have prior experience in Software Development (Java or Python). · AWS or Azure intermediate level certification is a must have. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000008054040/Inside-Sales-Executive?source=CareerSite", "company_id": 3374, "source": 3, "skills": "", "title": "Inside Sales Executive", "location": "Pune City, India   | Posted on 05/15/2024", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://mobisoftinfotech.zohorecruit.com/jobs/Current-Vacancies/326119000008054040/Inside-Sales-Executive?source=CareerSite", "description": "Job Description Roles and Responsibilities:Build relationships with prospective clientsMaintain consistent contact with existing clientsMake cold calls for new business leadsAnalyze the market and establish competitive advantagesKeep prospective client database updatedSupport in writing new business proposalsMaintain knowledge of all products and service offerings of the companyMake outbound cold emails and calls to high level decision making members e. g. CEO, Director, VP, GM etc. Should be target oriented with the ability to excel under pressureResponsible for generating potential business leadsResponsible for achieving day-to-day targetsShould be able to handle multiple projects/campaignsRequirementsDesired Candidate Profile:Excellent written and verbal communications skillsInternational exposure in B2B or B2C outbound calling would be an added advantage. Knowledge of MS Office & Advanced Excel Time management & Leadership skillsBalanced skill profile including attention to detail, problem solvingShould have at least one year of B2B/B2C International calling experienceCreative thinker with problem solving abilities and objection handlingExperience of working in Inside Sales or Lead Generation roles. Able to penetrate organizations through targeted email campaigns, cold calling, LinkedIn etc. and identify target audience. Experience in working on tools like Zoominfo, Lusha, Saleshandy, Sales Navigator etc. would be a plus. Interact with internal teams and ensure Meetings and Demonstrations are handled on time. I'm interested", "ctc": null, "currency": null, "meta": {}}]