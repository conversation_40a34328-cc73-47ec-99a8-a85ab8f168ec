[{"jd_link": "https://qsstechnosoft.freshteam.com/jobs/0K5jHJQwmjMR/python-ds-ai-ml", "company_id": 3376, "source": 3, "skills": "", "title": "Python - DS/AI/ML", "location": "Noida\n                \n              \n              \n                Work Type: \n                Full Time", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://qsstechnosoft.freshteam.com/jobs/0K5jHJQwmjMR/python-ds-ai-ml#applicant-form", "description": ". Submit Your Application You have successfully applied You have errors in applying Apply With Resume * First Name* Middle Name Last Name* Email* Mobile Phone Social Network and Web LinksProvide us with links to see some of your work (Git/ Dribble/ Behance/ Pinterest/ Blog/ Medium) + Employer + Add Employer Education + Add Education <div> <div style=\"width: 302px; height: 422px; position: relative;\"> <div style=\"width: 302px; height: 422px; position: absolute;\"> <iframe src=\"https://www. google. com/recaptcha/api/fallback? k=6Lfx0SoUAAAAABsmxQrh74rGRzw56n0foAz43LTc\" frameborder=\"0\" scrolling=\"no\" style=\"width: 302px; height:422px; border-style: none;\"> </iframe> </div> </div> <div style=\"width: 300px; height: 60px; border-style: none; bottom: 12px; left: 25px; margin: 0px; padding: 0px; right: 25px; background: #f9f9f9; border: 1px solid #c1c1c1; border-radius: 3px;\"> <textarea id=\"g-recaptcha-response\" name=\"g-recaptcha-response\" class=\"g-recaptcha-response\" style=\"width: 250px; height: 40px; border: 1px solid #c1c1c1; margin: 10px 25px; padding: 0px; resize: none;\" value=\"\"> </textarea> </div> </div> { \"@context\" : \"http://schema. org/\", \"@type\" : \"JobPosting\", \"url\" : \"https://qsstechnosoft. freshteam. com/jobs/0K5jHJQwmjMR/Python%20-%20DS%2FAI%2FML\", \"title\" : \"Python - DS/AI/ML\", \"description\" : \"&lt;p&gt;. &lt;/p&gt;\", \"datePosted\" : \"2025-06-26 13:01:28 UTC\", \"employmentType\" : \"FULL_TIME\", \"remote\" : \"false\", \"hiringOrganization\" : { \"@type\":\"Organization\", \"name\":\"QSS Technosoft Pvt. Ltd. \" }, \"jobLocation\" : { \"@type\": \"Place\", \"address\": { \"@type\": \"PostalAddress\", \"streetAddress\": \"\", \"addressRegion\": \"Noida\", \"postalCode\": \"\", \"addressLocality\": \"\", \"addressCountry\": \"India\" } } } var translation = { 'employer_title': `Designation`, 'employer_company': `Company/Business name`, 'employer_start_date': `Start date`, 'employer_end_date': `End date`, 'employer_is_current': `I currently work here`, 'employer_summary': `Summary`, 'employer_remove': `Remove this employer`, 'education_degree': `Degree`, 'education_field_of_study': `Field of study/major`, 'education_school_name': `Institution/school name`, 'education_start_date': `Start date`, 'education_end_date': `End date`, 'education_is_current': `Currently pursuing`, 'education_grade': `Grade`, 'education_remove': `Remove this degree` };", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://qsstechnosoft.freshteam.com/jobs/47XFcwHtE3U4/trainee-software-engineer", "company_id": 3376, "source": 3, "skills": "", "title": "Trainee - Software Engineer", "location": "Noida\n                \n              \n              \n                Work Type: \n                Full Time", "location_type": "remote", "job_type": "internship", "min_experience": 2, "max_experience": 2, "apply_link": "https://qsstechnosoft.freshteam.com/jobs/47XFcwHtE3U4/trainee-software-engineer#applicant-form", "description": "Are you a software engineering fresher raring to go and looking to kick-start your career in a growth oriented startup culture with a hunger to learn and build awesome software products? We could be a match! QSS Technosoft is looking for logically strong and dedicated B. E. /B. Tech. (CS/ IT/ECE) Freshers of 2021/2022 Batch for software developer position. Skills or basic Knowledge preferred: Basic knowledge of Javascript / NodeJS / Java, databases and frontend technologies. Eligibility Criteria: B. E. / B. Tech. (CS/ IT/ECE), 60% of aggregate throughout academics i. e. 10th class, 12th class, Graduation (2021/2022 batch only)Service Agreement and other terms: 2 years service agreement. Interview Process:- First Round: System test of 120 minutes (Aptitude and Programming Round)- Second Round: Technical Interview face to face- Third Round: HR InterviewRequired Technical skills:1. Good experience with backend technologies2. Knowledge on HTML, CSS, JavaScript, JQuery & AJAX. 3. Knowledge on any relational database e. g. MySQL4. Knowledge on Restful API development will be helpful5. Self-starter with good communication & problem solving skills. 6. Freshers who are trained, have interned somewhere, or certified on Java technologies will have an added advantage. Submit Your Application You have successfully applied You have errors in applying Apply With Resume * First Name* Middle Name Last Name* Email* Mobile Phone Social Network and Web LinksProvide us with links to see some of your work (Git/ Dribble/ Behance/ Pinterest/ Blog/ Medium) + Employer + Add Employer Education + Add Education <div> <div style=\"width: 302px; height: 422px; position: relative;\"> <div style=\"width: 302px; height: 422px; position: absolute;\"> <iframe src=\"https://www. google. com/recaptcha/api/fallback? k=6Lfx0SoUAAAAABsmxQrh74rGRzw56n0foAz43LTc\" frameborder=\"0\" scrolling=\"no\" style=\"width: 302px; height:422px; border-style: none;\"> </iframe> </div> </div> <div style=\"width: 300px; height: 60px; border-style: none; bottom: 12px; left: 25px; margin: 0px; padding: 0px; right: 25px; background: #f9f9f9; border: 1px solid #c1c1c1; border-radius: 3px;\"> <textarea id=\"g-recaptcha-response\" name=\"g-recaptcha-response\" class=\"g-recaptcha-response\" style=\"width: 250px; height: 40px; border: 1px solid #c1c1c1; margin: 10px 25px; padding: 0px; resize: none;\" value=\"\"> </textarea> </div> </div> { \"@context\" : \"http://schema. org/\", \"@type\" : \"JobPosting\", \"url\" : \"https://qsstechnosoft. freshteam. com/jobs/47XFcwHtE3U4/Trainee%20-%20Software%20Engineer\", \"title\" : \"Trainee - Software Engineer\", \"description\" : \"&lt;div&gt;Are you a software engineering fresher raring to go and looking to kick-start your career in a growth oriented startup culture with a hunger to learn and build awesome software products? &lt;/div&gt;&lt;div&gt;&lt;br style=&quot;padding:0px;margin:0px;&quot;&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;We could be a match! &lt;/p&gt;&lt;br style=&quot;padding:0px;margin:0px;&quot;&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;QSS Technosoft is looking for logically strong and dedicated B. E. /B. Tech. (CS/ IT/ECE) Freshers of 2021/2022 Batch for software developer position. &lt;/p&gt;&lt;br style=&quot;padding:0px;margin:0px;&quot;&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;&lt;strong style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;Skills or basic Knowledge preferred:&lt;/strong&gt; Basic knowledge of Javascript / NodeJS / Java, databases and frontend technologies. &lt;/p&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;&lt;strong style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;Eligibility Criteria:&lt;/strong&gt; B. E. / B. Tech. (CS/ IT/ECE), 60% of aggregate throughout academics i. e. 10th class, 12th class, Graduation (2021/2022 batch only)&lt;/p&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;&lt;strong style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;Service Agreement and other terms:&lt;/strong&gt; 2 years service agreement. &lt;/p&gt;&lt;br style=&quot;padding:0px;margin:0px;&quot;&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;&lt;strong style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;Interview Process:&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;- First Round: System test of 120 minutes (Aptitude and Programming Round)&lt;/p&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;- Second Round: Technical Interview face to face&lt;/p&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;- Third Round: HR Interview&lt;/p&gt;&lt;br style=&quot;padding:0px;margin:0px;&quot;&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;&lt;strong style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;Required Technical skills:&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;1. Good experience with backend technologies&lt;/p&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;2. Knowledge on HTML, CSS, JavaScript, JQuery &amp;amp; AJAX. &lt;/p&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;3. Knowledge on any relational database e. g. MySQL&lt;/p&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;4. Knowledge on Restful API development will be helpful&lt;/p&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;5. Self-starter with good communication &amp;amp; problem solving skills. &lt;/p&gt;&lt;p style=&quot;padding:0px;margin:0px;border:0px;vertical-align:baseline;box-sizing:border-box;&quot;&gt;6. Freshers who are trained, have interned somewhere, or certified on Java technologies will have an added advantage. &lt;/p&gt;&lt;div style=&quot;padding:0px;margin:20px 0px 0px;border:0px;font-size:16px;vertical-align:baseline;box-sizing:border-box;line-height:20px;color:rgb(0,0,0);font-family:Roboto,sans-serif;&quot;&gt;&lt;br&gt;&lt;/div&gt;&lt;/div&gt;\", \"datePosted\" : \"2025-07-04 06:28:28 UTC\", \"employmentType\" : \"FULL_TIME\", \"remote\" : \"false\", \"hiringOrganization\" : { \"@type\":\"Organization\", \"name\":\"QSS Technosoft Pvt. Ltd. \" }, \"jobLocation\" : { \"@type\": \"Place\", \"address\": { \"@type\": \"PostalAddress\", \"streetAddress\": \"\", \"addressRegion\": \"Noida\", \"postalCode\": \"\", \"addressLocality\": \"\", \"addressCountry\": \"India\" } } } var translation = { 'employer_title': `Designation`, 'employer_company': `Company/Business name`, 'employer_start_date': `Start date`, 'employer_end_date': `End date`, 'employer_is_current': `I currently work here`, 'employer_summary': `Summary`, 'employer_remove': `Remove this employer`, 'education_degree': `Degree`, 'education_field_of_study': `Field of study/major`, 'education_school_name': `Institution/school name`, 'education_start_date': `Start date`, 'education_end_date': `End date`, 'education_is_current': `Currently pursuing`, 'education_grade': `Grade`, 'education_remove': `Remove this degree` };", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://qsstechnosoft.freshteam.com/jobs/gFu3uMg3z0Qm/powerbi-developer", "company_id": 3376, "source": 3, "skills": "", "title": "PowerBI Developer", "location": "Noida\n                \n              \n              \n                Work Type: \n                Full Time", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://qsstechnosoft.freshteam.com/jobs/gFu3uMg3z0Qm/powerbi-developer#applicant-form", "description": "Job Description: Power BI DeveloperKey Responsibilities:Develop and maintain Power BI dashboards and reports. Collaborate with business stakeholders to understand their data needs and deliver insightful solutions. Create and optimize data models to support reporting needs. Ensure data accuracy and integrity by performing regular data validation and quality checks. Design and implement data visualizations that are easy to understand and actionable. Integrate Power BI reports into other applications using embedded analytics like Power BI service (SaaS), or by API automation. Provide training and support to end-users on Power BI tools and reports. Stay updated with the latest Power BI features and updates. Required Skills and Qualifications:Proven experience as a Power BI Developer or similar role. Strong experience with Power BI, including DAX, Power Query, and M language. Proficiency in data modeling, data warehousing, and ETL processes. Experience with SQL and other database technologies. Ability to analyze and visualize complex data sets. Excellent problem-solving skills and attention to detail. Strong communication skills with the ability to convey complex data insights to non-technical stakeholders. Preferred Skills:Experience with other BI tools such as Tableau, Qlik, or similar. Knowledge of Azure data services like Azure Data Factory, Azure SQL Database, etc. Understanding of data security and governance. What We Offer:Competitive salary and benefits package. Flexible working hours and remote work options. Opportunities for professional growth and development. A supportive and collaborative team environment. The chance to work on exciting and innovative projects. Submit Your Application You have successfully applied You have errors in applying Apply With Resume * First Name* Middle Name Last Name* Email* Mobile Phone Social Network and Web LinksProvide us with links to see some of your work (Git/ Dribble/ Behance/ Pinterest/ Blog/ Medium) + Employer + Add Employer Education + Add Education <div> <div style=\"width: 302px; height: 422px; position: relative;\"> <div style=\"width: 302px; height: 422px; position: absolute;\"> <iframe src=\"https://www. google. com/recaptcha/api/fallback? k=6Lfx0SoUAAAAABsmxQrh74rGRzw56n0foAz43LTc\" frameborder=\"0\" scrolling=\"no\" style=\"width: 302px; height:422px; border-style: none;\"> </iframe> </div> </div> <div style=\"width: 300px; height: 60px; border-style: none; bottom: 12px; left: 25px; margin: 0px; padding: 0px; right: 25px; background: #f9f9f9; border: 1px solid #c1c1c1; border-radius: 3px;\"> <textarea id=\"g-recaptcha-response\" name=\"g-recaptcha-response\" class=\"g-recaptcha-response\" style=\"width: 250px; height: 40px; border: 1px solid #c1c1c1; margin: 10px 25px; padding: 0px; resize: none;\" value=\"\"> </textarea> </div> </div> { \"@context\" : \"http://schema. org/\", \"@type\" : \"JobPosting\", \"url\" : \"https://qsstechnosoft. freshteam. com/jobs/gFu3uMg3z0Qm/PowerBI%20Developer\", \"title\" : \"PowerBI Developer\", \"description\" : \"&lt;div style=&quot;border: 0px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-variant-position: inherit; font-weight: 400; font-stretch: inherit; font-size: 12pt; line-height: inherit; font-family: Calibri, Helvetica, sans-serif; font-kerning: inherit; font-feature-settings: inherit; margin: 0px; padding: 0px; vertical-align: baseline; color: rgb(0, 0, 0); letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; white-space: normal; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;&lt;strong&gt;Job Description: Power BI Developer&lt;/strong&gt;&lt;/div&gt;&lt;div style=&quot;border: 0px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-variant-position: inherit; font-weight: 400; font-stretch: inherit; font-size: 12pt; line-height: inherit; font-family: Calibri, Helvetica, sans-serif; font-kerning: inherit; font-feature-settings: inherit; margin: 1em 0px; padding: 0px; vertical-align: baseline; color: rgb(0, 0, 0); letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; white-space: normal; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;&lt;strong&gt;Key Responsibilities:&lt;/strong&gt;&lt;/div&gt;&lt;ul style=&quot;color: rgb(0, 0, 0); font-family: &quot;&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Develop and maintain Power BI dashboards and reports. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Collaborate with business stakeholders to understand their data needs and deliver insightful solutions. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Create and optimize data models to support reporting needs. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Ensure data accuracy and integrity by performing regular data validation and quality checks. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Design and implement data visualizations that are easy to understand and actionable. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Integrate Power BI reports into other applications using embedded analytics like Power BI service (SaaS), or by API automation. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Provide training and support to end-users on Power BI tools and reports. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Stay updated with the latest Power BI features and updates. &lt;/li&gt;&lt;/ul&gt;&lt;div style=&quot;border: 0px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-variant-position: inherit; font-weight: 400; font-stretch: inherit; font-size: 12pt; line-height: inherit; font-family: Calibri, Helvetica, sans-serif; font-kerning: inherit; font-feature-settings: inherit; margin: 1em 0px; padding: 0px; vertical-align: baseline; color: rgb(0, 0, 0); letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; white-space: normal; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;&lt;strong&gt;Required Skills and Qualifications:&lt;/strong&gt;&lt;/div&gt;&lt;ul style=&quot;color: rgb(0, 0, 0); font-family: &quot;&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Proven experience as a Power BI Developer or similar role. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Strong experience with Power BI, including DAX, Power Query, and M language. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Proficiency in data modeling, data warehousing, and ETL processes. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Experience with SQL and other database technologies. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Ability to analyze and visualize complex data sets. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Excellent problem-solving skills and attention to detail. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Strong communication skills with the ability to convey complex data insights to non-technical stakeholders. &lt;/li&gt;&lt;/ul&gt;&lt;div style=&quot;border: 0px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-variant-position: inherit; font-weight: 400; font-stretch: inherit; font-size: 12pt; line-height: inherit; font-family: Calibri, Helvetica, sans-serif; font-kerning: inherit; font-feature-settings: inherit; margin: 1em 0px; padding: 0px; vertical-align: baseline; color: rgb(0, 0, 0); letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; white-space: normal; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;&lt;strong&gt;Preferred Skills:&lt;/strong&gt;&lt;/div&gt;&lt;ul style=&quot;color: rgb(0, 0, 0); font-family: &quot;&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Experience with other BI tools such as Tableau, Qlik, or similar. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Knowledge of Azure data services like Azure Data Factory, Azure SQL Database, etc. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Understanding of data security and governance. &lt;/li&gt;&lt;/ul&gt;&lt;div style=&quot;border: 0px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-variant-position: inherit; font-weight: 400; font-stretch: inherit; font-size: 12pt; line-height: inherit; font-family: Calibri, Helvetica, sans-serif; font-kerning: inherit; font-feature-settings: inherit; margin: 1em 0px; padding: 0px; vertical-align: baseline; color: rgb(0, 0, 0); letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; white-space: normal; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;&lt;strong&gt;What We Offer:&lt;/strong&gt;&lt;/div&gt;&lt;ul style=&quot;color: rgb(0, 0, 0); font-family: &quot;&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Competitive salary and benefits package. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Flexible working hours and remote work options. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;Opportunities for professional growth and development. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;A supportive and collaborative team environment. &lt;/li&gt;&lt;li style=&quot;font-family: Calibri, Helvetica, sans-serif; font-size: 12pt; color: rgb(0, 0, 0);&quot;&gt;The chance to work on exciting and innovative projects. &lt;/li&gt;&lt;/ul&gt;&lt;div style=&quot;border: 0px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-variant-position: inherit; font-weight: 400; font-stretch: inherit; font-size: 12pt; line-height: inherit; font-family: Calibri, Helvetica, sans-serif; font-kerning: inherit; font-feature-settings: inherit; margin: 1em 0px; padding: 0px; vertical-align: baseline; color: rgb(0, 0, 0); letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; white-space: normal; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;&lt;br&gt;&lt;/div&gt;\", \"datePosted\" : \"2025-06-29 08:32:16 UTC\", \"employmentType\" : \"FULL_TIME\", \"remote\" : \"false\", \"hiringOrganization\" : { \"@type\":\"Organization\", \"name\":\"QSS Technosoft Pvt. Ltd. \" }, \"jobLocation\" : { \"@type\": \"Place\", \"address\": { \"@type\": \"PostalAddress\", \"streetAddress\": \"\", \"addressRegion\": \"Noida\", \"postalCode\": \"\", \"addressLocality\": \"\", \"addressCountry\": \"India\" } } } var translation = { 'employer_title': `Designation`, 'employer_company': `Company/Business name`, 'employer_start_date': `Start date`, 'employer_end_date': `End date`, 'employer_is_current': `I currently work here`, 'employer_summary': `Summary`, 'employer_remove': `Remove this employer`, 'education_degree': `Degree`, 'education_field_of_study': `Field of study/major`, 'education_school_name': `Institution/school name`, 'education_start_date': `Start date`, 'education_end_date': `End date`, 'education_is_current': `Currently pursuing`, 'education_grade': `Grade`, 'education_remove': `Remove this degree` };", "ctc": null, "currency": null, "meta": {}}]