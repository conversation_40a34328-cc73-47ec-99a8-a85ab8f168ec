[{"jd_link": "https://ae.studio/jobs/4011417004/data-scientist", "company_id": 3378, "source": 3, "skills": "", "title": "Data Scientist", "location": "Florianopolis office/ Brazil remote", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://ae.studio/jobs/4011417004/data-scientist", "description": "Data ScientistAPPLYAE Studio is a mid-sized startup from California and we are looking for data scientists/developers/designers to work remotely and/or from our office in Florianópolis. We have a highly skilled team building products to increase human agency using the most recent technologies for startups as well as large companies like Samsung and Berkshire Hathaway. We have an awesome work environment and a highly productive process. We deploy the proceeds from our consulting work in development and data science and our skunkworks proceeds to support research into cause areas we think are of paramount interest to human beings. We have made a name for ourselves in cutting-edge brain computer interface (BCI) R&D, and now we want to replicate past successes and solve the problem of AI alignment (aligning future powerful systems with human best interests). We want to optimize for human agency, if you feel similarly, please apply to support our research efforts. Data scientists will have the opportunity between client projects to work on important alignment research, with the opportunity to potentially have your research project prioritized if particularly promi We are looking for someone with strong problem-solving skills and self-management skills. ABOUT YOU Fluency in Python - have used it on a number of projects Proven data science experience (primary or equal contributor to either a single large or multiple smaller data science analyses or products, working with a team / stakeholders) Genuine comfort with the sorts of statistics and methods used in classification problems Familiarity with NLP/NLU Experience calling machine learning APIs via REST or client libraries Real agile experience (worked with <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Extreme Programming) Growth mindset Willingness to learn our systems Full-time availability Demonstrated ability to work independently/autonomously Ability to communicate effectively and work exclusively in English. (This is a must) BONUS POINTS FOR Experience with self-managed data science projects Software development background Experience working with / building / training deep learning-based computer vision or NLP models or LLMs JavaScript / Node. js experience Clean Code, Unit Tests Experience managing clients and client relationships a big plus Experience in Startups RECRUITING NOTE There are an increasing number of scammers out there that are taking advantage of our company's name to scam people with fake offers that result in you ultimately giving the scammers money. Please make sure anyone who contacts you is contacting you from the domains ae. studio, agencyenterprise. com, agencyenterprisestudio. com (please make sure that the sender really is from those domains). If you have any questions about any interviews or offers you might have received and seem bogus, please contact recruiting-offers@ae. studio. Equity Working at AE means receiving equity in some of our client projects and in the Skunkworks projects we incubate. The equity granted is priced at a value of $0 when you receive it. If it were otherwise, you’d be forced to pay taxes. But to be clear, there are no free lunches, only the choice of who pays for your sandwich. At AE, the equity you receive is not free to AE. We invest millions annually in Skunkworks projects - paying our employees to build technology for which no client pays a bill. We receive equity from certain clients, which means we accept less in terms of our billable rate. We then pass that equity on to our employees. Sure it’s priced at $0 on day 1 - a call option on a stock trading at $100 with a strike price of $100 is priced at $0 today for tax purposes, but you’d want to receive that option. If you received that option on a dozen stocks, you’d be fairly confident that some of them would be worth a lot more than $0. Offering this equity (by investing in Skunkworks or offering clients a lower rate) comes out of AE’s margins. Theoretically, we should be lowering salaries to compensate. But we’re competitive on that front as well. Over the long-term, we believe AE employees who stick around are likely to fare extremely well monetarily (no legal promises). We think long-term. You should too when you choose your employer. We are looking to hire people who think long term. People create exponentially more value here in their 3rd or 4th year rather than their 1st or 2nd, and that only increases. The value you create leads to more equity issued every year, and you retain it while you remain at AE or at a Skunkworks co that spins out, in order to free up equity for others, which is how we afford to be generous with the equity. * Disclaimer: If you leave AE on good terms, you can retain 20% of your equity if you continue to send leads our way, share our core values, etc. If you leave AE for your own AE-incubated Skunkworks project (or someone else’s), you retain 100% of your equity. Folks create exponentially more value here in their 3rd and 4th years than their 1st or 2nd - we want folks who think long-term like we do. LocationFlorianopolis office/ Brazil remoteBecome an agent! We only hire the world's most effective people. Full name *Email *Website *Who are you? Resume/CV(File types: pdf, doc, docx, txt, rtf)Cover Letter(File types: pdf, doc, docx, txt, rtf) I want a challenge (optional)⚠️ there’s a pretty high standard for performance so if you’re looking to coast it’s probably not the best fit⚠️ we only hire people who share our core valuesLET’S WORK TOGETHER Data ScientistAPPLY Data Scientist APPLY AE Studio is a mid-sized startup from California and we are looking for data scientists/developers/designers to work remotely and/or from our office in Florianópolis. We have a highly skilled team building products to increase human agency using the most recent technologies for startups as well as large companies like Samsung and Berkshire Hathaway. We have an awesome work environment and a highly productive process. We deploy the proceeds from our consulting work in development and data science and our skunkworks proceeds to support research into cause areas we think are of paramount interest to human beings. We have made a name for ourselves in cutting-edge brain computer interface (BCI) R&D, and now we want to replicate past successes and solve the problem of AI alignment (aligning future powerful systems with human best interests). We want to optimize for human agency, if you feel similarly, please apply to support our research efforts. Data scientists will have the opportunity between client projects to work on important alignment research, with the opportunity to potentially have your research project prioritized if particularly promi We are looking for someone with strong problem-solving skills and self-management skills. ABOUT YOU Fluency in Python - have used it on a number of projects Proven data science experience (primary or equal contributor to either a single large or multiple smaller data science analyses or products, working with a team / stakeholders) Genuine comfort with the sorts of statistics and methods used in classification problems Familiarity with NLP/NLU Experience calling machine learning APIs via REST or client libraries Real agile experience (worked with Kanban, Scrum, Extreme Programming) Growth mindset Willingness to learn our systems Full-time availability Demonstrated ability to work independently/autonomously Ability to communicate effectively and work exclusively in English. (This is a must) BONUS POINTS FOR Experience with self-managed data science projects Software development background Experience working with / building / training deep learning-based computer vision or NLP models or LLMs JavaScript / Node. js experience Clean Code, Unit Tests Experience managing clients and client relationships a big plus Experience in Startups RECRUITING NOTE There are an increasing number of scammers out there that are taking advantage of our company's name to scam people with fake offers that result in you ultimately giving the scammers money. Please make sure anyone who contacts you is contacting you from the domains ae. studio, agencyenterprise. com, agencyenterprisestudio. com (please make sure that the sender really is from those domains). If you have any questions about any interviews or offers you might have received and seem bogus, please contact recruiting-offers@ae. studio. Equity Working at AE means receiving equity in some of our client projects and in the Skunkworks projects we incubate. The equity granted is priced at a value of $0 when you receive it. If it were otherwise, you’d be forced to pay taxes. But to be clear, there are no free lunches, only the choice of who pays for your sandwich. At AE, the equity you receive is not free to AE. We invest millions annually in Skunkworks projects - paying our employees to build technology for which no client pays a bill. We receive equity from certain clients, which means we accept less in terms of our billable rate. We then pass that equity on to our employees. Sure it’s priced at $0 on day 1 - a call option on a stock trading at $100 with a strike price of $100 is priced at $0 today for tax purposes, but you’d want to receive that option. If you received that option on a dozen stocks, you’d be fairly confident that some of them would be worth a lot more than $0. Offering this equity (by investing in Skunkworks or offering clients a lower rate) comes out of AE’s margins. Theoretically, we should be lowering salaries to compensate. But we’re competitive on that front as well. Over the long-term, we believe AE employees who stick around are likely to fare extremely well monetarily (no legal promises). We think long-term. You should too when you choose your employer. We are looking to hire people who think long term. People create exponentially more value here in their 3rd or 4th year rather than their 1st or 2nd, and that only increases. The value you create leads to more equity issued every year, and you retain it while you remain at AE or at a Skunkworks co that spins out, in order to free up equity for others, which is how we afford to be generous with the equity. * Disclaimer: If you leave AE on good terms, you can retain 20% of your equity if you continue to send leads our way, share our core values, etc. If you leave AE for your own AE-incubated Skunkworks project (or someone else’s), you retain 100% of your equity. Folks create exponentially more value here in their 3rd and 4th years than their 1st or 2nd - we want folks who think long-term like we do.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://ae.studio/jobs/4011507004/tech-lead-and-pm-(tpm)", "company_id": 3378, "source": 3, "skills": "", "title": "Tech Lead & PM (TPM)", "location": "LA office / US remote", "location_type": "remote", "job_type": "full_time", "min_experience": 4, "max_experience": null, "apply_link": "https://ae.studio/jobs/4011507004/tech-lead-and-pm-(tpm)", "description": "Tech Lead & PM (TPM)APPLYAgency Enterprise Studio is a company based in Los Angeles and we're looking for the world's most effective developers that are also a cultural fit to work with us on projects that increase Human Agency. We are looking for the best of the best. People who love what they do and are legitimately excellent at it. Is this you? If you answered this with an unequivocal yes, then it might not be the best fit. We also want people who understand that no one knows everything, don't have egos, understand their own shortcomings and are excited to grow. We want people who are badass but also humble and care about helping their team. We want people who get shit done and give a shit. We want people who don't care about titles but care about results. We are asking for a lot. What we can offer you? A legitimate meritocracy. We don't care if you fancy huh, we care if you are actually good at your job. Been passed over for the guy who's great at talking to the EVP about how you just need to move to microservices but just pushed several critical bugs to production? That will not happen here. The ability to work with other people like you. And the joy of not having to work with people who aren't - no shitty devs, assholes, or devs who spend 2 hours a day working and 6 hours on TikTok and spreading office gossip. An intensive onboarding bootcamp where you will be challenged, tested, and learn a ton. While we don't like firing people or having them quit, we care more about guaranteeing #2 and this process is meant to weed people out who aren't a fit. A highly supportive atmosphere. We have a high standard for performance, but we will do everything we can to help each individual attain it. Everyone at all levels of the company is willing to pitch in and provide advice, mentorship, or even jump in to get our hands dirty coding, organizing the backlog, or talking to clients when a team needs extra support. We're in this together. The ability to work on some of the most exciting technical and product challenges that exist today. We work with clients, who bring us in to work on their most critical and important tasks because they know we can deliver and on our own internal projects which solve real needs. We are leaders in the BCI space where we are constantly pushing towards our long-term goal of developing the BCI OS in a way that is agency-increasing. We also work on complex blockchain projects with legitimate use cases. We also build best-in-class web and mobile applications and have some of the world's best data scientists. The ability to work on a range of projects in different fields, industries, and stacks for companies ranging from early-stage startups to enterprise. A diversified employee equity program that includes equity in client and skunkworks projects. And the opportunity to potentially have AE support you doing your own startup as part of our skunkworks program. Competitive pay with regular performance-based raises and other fun stuff including company retreats, a really nice office, health and pet insurance, a cake to embarass you on your birthday, etc. STACK You will be working mostly with JavaScript and libraries/frameworks in its ecosystem. JavaScript / TypeScript React / Angular Node / Next. js / Apollo Everything else in the JavaScript ecosystem YOU WILL Work Full-time. Constantly communicate with clients. Create user stories and plan next steps. Lead software projects. Mentor team members. Tackle product problems and set the technology strategy for our clients. Work with 10x devs, designers, data scientists. Work in internal projects and client projects. Build projects with a product focused mentality while keeping your code clean. Build modern, efficient apps and test that they work correctly. Review pull requests from other members of the team. Have a founder mentality and proactively solve problems. Wear any hats needed to get the job done. ABOUT YOU A minimum of 4+ years of experience with managing projects, teams and clients. A minimum of 5+ years of programming experience with our stack. realizing in the middle of the night you forgot a major edge case that you can’t believe hasn’t burned you yet and need to fix asap git blaming some shitty code and realizing it was you, deciding to never git blame again having some super strong preference about the way something “needs to be done” that you read in a book, forum, conference or fancy colleague, being super militant about enforcing it and judgemental about people who don’t already do it. and then realizing this was wrong you had a stack you thought was universally superior but now you think people should just pick the right tool for the job being such an expert at something that you forget why anyone would think this is hard, then having to learn something new and sucking at it at first and remembering that programming actually is a skill, then getting really great at that Enjoy being hands-on in the codebase, but are also great at managing and mentoring other developers & designers and making sure projects stay on track. You have a strong command of fluent/business English speaking and writing skills. Experience working with Agile. Extreme Programming, Scrum or Kanban. Growth mindset. Like your work and accomplish many things. Ownership mentality. Problem-solving skills. Self-management skills. Can work independently/autonomously. Ability to communicate effectively and work exclusively in English. (This is a must) Willing to work with new technologies, different environments. Willing to work with everything and anything, Full Stack. OTHER STUFF ABOUT US Remote work. Equity. 401K Platinum health insurance. Opportunity to be part of a fast-growing team with a happy bunch of former entrepreneurs, data scientists, full stack developers, UX/UI designers and product managers. Opportunity to grow and advance in your career or flex your entrepreneurial side by participating in our internal skunkworks projects (some projects spin out with team members as the C-suite) We provide resources for you to grow and learn on the job (this includes Udemy classes, books, participation in events, mentoring and - of course - the latest generation hardware. ) Offices right on Abbot Kinney, in the heart of Venice, CA. Salt & Straw is steps from our office, and we get discounts in some Abbot Kinney stores. Weekly lunches with the US team. Monthly happy hours with friends and families. Occasional VR and game nights, if you are into that. Yearly retreat bringing together the whole team. THE INTERVIEW PROCESS Initial conversation, we get to know each other and see if it makes sense for both sides. Live coding test. Final interview. Note: There are an increasing number of scammers out there that are taking advantage of our company's name to scam people with fake offers that result in you ultimately giving the scammers money. Please make sure anyone who contacts you is contacting you from the domains ae. studio, agencyenterprise. com, agencyenterprisestudio. com (please make sure that the sender really is from those domains). If you have any questions about any interviews or offers you might have received and seem bogus, please contact recruiting-offers@ae. studio. Equity Working at AE means receiving equity in some of our client projects and in the Skunkworks projects we incubate. The equity granted is priced at a value of $0 when you receive it. If it were otherwise, you'd be forced to pay taxes. But to be clear, there are no free lunches, only the choice of who pays for your sandwich. At AE, the equity you receive is not free to AE. We invest millions annually in Skunkworks projects - paying our employees to build technology for which no client pays a bill. We receive equity from certain clients, which means we accept less in terms of our billable rate. We then pass that equity on to our employees. Sure it's priced at $0 on day 1 - a call option on a stock trading at $100 with a strike price of $100 is priced at $0 today for tax purposes, but you'd want to receive that option. If you received that option on a dozen stocks, you'd be fairly confident that some of them would be worth a lot more than $0. Offering this equity (by investing in Skunkworks or offering clients a lower rate) comes out of AE's margins. Theoretically, we should be lowering salaries to compensate. But we're competitive on that front as well. Over the long-term, we believe AE employees who stick around are likely to fare extremely well monetarily (no legal promises). We think long-term. You should too when you choose your employer. We are looking to hire people who think long term. People create exponentially more value here in their 3rd or 4th year rather than their 1st or 2nd, and that only increases. The value you create leads to more equity issued every year, and you retain it while you remain at AE or at a Skunkworks co that spins out, in order to free up equity for others, which is how we afford to be generous with the equity. * Disclaimer: If you leave AE on good terms, you can retain 20% of your equity if you continue to send leads our way, share our core values, etc. If you leave AE for your own AE-incubated Skunkworks project (or someone else's), you retain 100% of your equity. Folks create exponentially more value here in their 3rd and 4th years than their 1st or 2nd - we want folks who think long-term like we do. Salary range from $120k/year to $220k/year. Not in the range? That's fine, you can still apply and our team may fit you in a more senior position that we're not actively hiring right now, but we are growing fast and may have other openings soon. LocationLA office / US remoteBecome an agent! We only hire the world's most effective people. Full name *Email *Website *Who are you? Resume/CV(File types: pdf, doc, docx, txt, rtf)Cover Letter(File types: pdf, doc, docx, txt, rtf) I want a challenge (optional)⚠️ there’s a pretty high standard for performance so if you’re looking to coast it’s probably not the best fit⚠️ we only hire people who share our core valuesLET’S WORK TOGETHER Tech Lead & PM (TPM)APPLY Tech Lead & PM (TPM) APPLY Agency Enterprise Studio is a company based in Los Angeles and we're looking for the world's most effective developers that are also a cultural fit to work with us on projects that increase Human Agency. We are looking for the best of the best. People who love what they do and are legitimately excellent at it. Is this you? If you answered this with an unequivocal yes, then it might not be the best fit. We also want people who understand that no one knows everything, don't have egos, understand their own shortcomings and are excited to grow. We want people who are badass but also humble and care about helping their team. We want people who get shit done and give a shit. We want people who don't care about titles but care about results. We are asking for a lot. What we can offer you? A legitimate meritocracy. We don't care if you fancy huh, we care if you are actually good at your job. Been passed over for the guy who's great at talking to the EVP about how you just need to move to microservices but just pushed several critical bugs to production? That will not happen here. The ability to work with other people like you. And the joy of not having to work with people who aren't - no shitty devs, assholes, or devs who spend 2 hours a day working and 6 hours on TikTok and spreading office gossip. An intensive onboarding bootcamp where you will be challenged, tested, and learn a ton. While we don't like firing people or having them quit, we care more about guaranteeing #2 and this process is meant to weed people out who aren't a fit. A highly supportive atmosphere. We have a high standard for performance, but we will do everything we can to help each individual attain it. Everyone at all levels of the company is willing to pitch in and provide advice, mentorship, or even jump in to get our hands dirty coding, organizing the backlog, or talking to clients when a team needs extra support. We're in this together. The ability to work on some of the most exciting technical and product challenges that exist today. We work with clients, who bring us in to work on their most critical and important tasks because they know we can deliver and on our own internal projects which solve real needs. We are leaders in the BCI space where we are constantly pushing towards our long-term goal of developing the BCI OS in a way that is agency-increasing. We also work on complex blockchain projects with legitimate use cases. We also build best-in-class web and mobile applications and have some of the world's best data scientists. The ability to work on a range of projects in different fields, industries, and stacks for companies ranging from early-stage startups to enterprise. A diversified employee equity program that includes equity in client and skunkworks projects. And the opportunity to potentially have AE support you doing your own startup as part of our skunkworks program. Competitive pay with regular performance-based raises and other fun stuff including company retreats, a really nice office, health and pet insurance, a cake to embarass you on your birthday, etc. STACK You will be working mostly with JavaScript and libraries/frameworks in its ecosystem. JavaScript / TypeScript React / Angular Node / Next. js / Apollo Everything else in the JavaScript ecosystem YOU WILL Work Full-time. Constantly communicate with clients. Create user stories and plan next steps. Lead software projects. Mentor team members. Tackle product problems and set the technology strategy for our clients. Work with 10x devs, designers, data scientists. Work in internal projects and client projects. Build projects with a product focused mentality while keeping your code clean. Build modern, efficient apps and test that they work correctly. Review pull requests from other members of the team. Have a founder mentality and proactively solve problems. Wear any hats needed to get the job done. ABOUT YOU A minimum of 4+ years of experience with managing projects, teams and clients. A minimum of 5+ years of programming experience with our stack. realizing in the middle of the night you forgot a major edge case that you can’t believe hasn’t burned you yet and need to fix asap git blaming some shitty code and realizing it was you, deciding to never git blame again having some super strong preference about the way something “needs to be done” that you read in a book, forum, conference or fancy colleague, being super militant about enforcing it and judgemental about people who don’t already do it. and then realizing this was wrong you had a stack you thought was universally superior but now you think people should just pick the right tool for the job being such an expert at something that you forget why anyone would think this is hard, then having to learn something new and sucking at it at first and remembering that programming actually is a skill, then getting really great at that Enjoy being hands-on in the codebase, but are also great at managing and mentoring other developers & designers and making sure projects stay on track. You have a strong command of fluent/business English speaking and writing skills. Experience working with Agile. Extreme Programming, Scrum or Kanban. Growth mindset. Like your work and accomplish many things. Ownership mentality. Problem-solving skills. Self-management skills. Can work independently/autonomously. Ability to communicate effectively and work exclusively in English. (This is a must) Willing to work with new technologies, different environments. Willing to work with everything and anything, Full Stack. OTHER STUFF ABOUT US Remote work. Equity. 401K Platinum health insurance. Opportunity to be part of a fast-growing team with a happy bunch of former entrepreneurs, data scientists, full stack developers, UX/UI designers and product managers. Opportunity to grow and advance in your career or flex your entrepreneurial side by participating in our internal skunkworks projects (some projects spin out with team members as the C-suite) We provide resources for you to grow and learn on the job (this includes Udemy classes, books, participation in events, mentoring and - of course - the latest generation hardware. ) Offices right on Abbot Kinney, in the heart of Venice, CA. Salt & Straw is steps from our office, and we get discounts in some Abbot Kinney stores. Weekly lunches with the US team. Monthly happy hours with friends and families. Occasional VR and game nights, if you are into that. Yearly retreat bringing together the whole team. THE INTERVIEW PROCESS Initial conversation, we get to know each other and see if it makes sense for both sides. Live coding test. Final interview. Note: There are an increasing number of scammers out there that are taking advantage of our company's name to scam people with fake offers that result in you ultimately giving the scammers money. Please make sure anyone who contacts you is contacting you from the domains ae. studio, agencyenterprise. com, agencyenterprisestudio. com (please make sure that the sender really is from those domains). If you have any questions about any interviews or offers you might have received and seem bogus, please contact recruiting-offers@ae. studio. Equity Working at AE means receiving equity in some of our client projects and in the Skunkworks projects we incubate. The equity granted is priced at a value of $0 when you receive it. If it were otherwise, you'd be forced to pay taxes. But to be clear, there are no free lunches, only the choice of who pays for your sandwich. At AE, the equity you receive is not free to AE. We invest millions annually in Skunkworks projects - paying our employees to build technology for which no client pays a bill. We receive equity from certain clients, which means we accept less in terms of our billable rate. We then pass that equity on to our employees. Sure it's priced at $0 on day 1 - a call option on a stock trading at $100 with a strike price of $100 is priced at $0 today for tax purposes, but you'd want to receive that option. If you received that option on a dozen stocks, you'd be fairly confident that some of them would be worth a lot more than $0. Offering this equity (by investing in Skunkworks or offering clients a lower rate) comes out of AE's margins. Theoretically, we should be lowering salaries to compensate. But we're competitive on that front as well. Over the long-term, we believe AE employees who stick around are likely to fare extremely well monetarily (no legal promises). We think long-term. You should too when you choose your employer. We are looking to hire people who think long term. People create exponentially more value here in their 3rd or 4th year rather than their 1st or 2nd, and that only increases. The value you create leads to more equity issued every year, and you retain it while you remain at AE or at a Skunkworks co that spins out, in order to free up equity for others, which is how we afford to be generous with the equity. * Disclaimer: If you leave AE on good terms, you can retain 20% of your equity if you continue to send leads our way, share our core values, etc. If you leave AE for your own AE-incubated Skunkworks project (or someone else's), you retain 100% of your equity. Folks create exponentially more value here in their 3rd and 4th years than their 1st or 2nd - we want folks who think long-term like we do. Salary range from $120k/year to $220k/year. Not in the range? That's fine, you can still apply and our team may fit you in a more senior position that we're not actively hiring right now, but we are growing fast and may have other openings soon.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://ae.studio/jobs/4011748004/product-designer-and-manager", "company_id": 3378, "source": 3, "skills": "", "title": "Product Designer & Manager", "location": "LA office / US remote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": 5, "apply_link": "https://ae.studio/jobs/4011748004/product-designer-and-manager", "description": "Product Designer & ManagerAPPLY Do you love building world-class, cutting-edge software? Do you believe that humans should be their most focused, efficient, intentional selves? Do you believe technology should always help human beings do just that? Then we’d love you to help us increase human agency. We think building software to increase human agency is good. We also think it’s good business. Agency Enterprise is a software development, data science, and design studio based in Los Angeles. We’ve bootstrapped our way to 100+ without VC, PE, or outside shareholders. It’s a simple business model - our amazing people do amazing work, we turn a profit, and we hire more amazing people. We take the long view and develop technology to increase human agency for our clients, our employees, and humanity. We incubate agency-increasing technology startups, building MVPs and founders. We are currently winning international competitions in neuroscience machine learning and researching the cutting edge of brain-computer interface (BCI) technology. We’ve built incredible MVPs for incredible startups, worked with large enterprises like Walmart, Samsung, and Berkshire Hathaway, and everything in-between. ABOUT THE ROLE You will work closely with our tech leads and full-stack developers to design and plant the product development process in a cross-functional, Agile environment. You will also interact directly with clients and educate them about AE’s methodology. You will help them work through user experience mapping, building their product roadmap and feature prioritization. OTHER RESPONSIBILITIES Approach projects with an ownership mentality so that both client and internal products will succeed, and you will inspire the best possible performance from your teams. Co-lead kick-off meetings. Collaborate with stakeholders to create and manage product roadmaps. Manage the Agile product development process Facilitate sprint planning, estimation, stand-up, demo, and retrospective meetings. Identify business needs and translate those into product requirements. Create sitemaps, user flow diagrams, low, medium, and high fidelity mock-ups. Facilitate user testing and user feedback sessions. Create user stories and manage the product backlog. Manage the design/developer hand-off process. Oversee and manage product timelines and budgets. Manage product releases. Contribute to product growth by identifying critical areas for potential improvement, then leading experimentation efforts. Communicate effectively with clients and stakeholders verbally and in writing. ABOUT YOU You have a minimum of 5 years of professional experience as a designer and have a penchant for product management and Agile. Or maybe you’re a product manager with 5+ years on the job and an eye for UX/UI design? You love working in a collaborative, high-energy environment, using agile methodologies and tools like Figma, Sketch, Linear, Pivotal Tracker (or equivalents). You value direct and honest feedback, but you know how to communicate it gracefully so it betters your partners' (coworkers and clients) work and happiness. Bonus points if you have experience in HTML/CSS or product development, you have a background in entrepreneurship or consulting, or you like VR and/or ice cream. OTHER COOL STUFF Remote work. Diversified, multi-company equity. Platinum health insurance. A fast-growing team with a happy bunch of former entrepreneurs, data scientists, full stack developers, UX/UI designers and product managers. Advance in your career or flex your entrepreneurial side by participating in our internal skunkworks projects (some projects spin out with team members as the C-suite) Resources for you to grow and learn on the job (this includes Udemy classes, books, participation in events, mentoring and - of course - the latest generation hardware. Offices right on Abbot Kinney, in the heart of Venice, CA. (Salt & Straw is steps from our office, and we get discounts in some Abbot Kinney stores. ) Weekly lunches with the US team. Monthly happy hours with friends and families. Occasional VR and game nights, if you are into that. Yearly retreat bringing together the whole team (with tropical fun and hackathons). RECRUITING NOTE There are an increasing number of scammers out there that are taking advantage of our company's name to scam people with fake offers that result in you ultimately giving the scammers money. Please make sure anyone who contacts you is contacting you from the domains ae. studio, agencyenterprise. com, agencyenterprisestudio. com (please make sure that the sender really is from those domains). If you have any questions about any interviews or offers you might have received and seem bogus, please contact recruiting-offers@ae. studio. Equity Working at AE means receiving equity in some of our client projects and in the Skunkworks projects we incubate. The equity granted is priced at a value of $0 when you receive it. If it were otherwise, you’d be forced to pay taxes. But to be clear, there are no free lunches, only the choice of who pays for your sandwich. At AE, the equity you receive is not free to AE. We invest millions annually in Skunkworks projects - paying our employees to build technology for which no client pays a bill. We receive equity from certain clients, which means we accept less in terms of our billable rate. We then pass that equity on to our employees. Sure it’s priced at $0 on day 1 - a call option on a stock trading at $100 with a strike price of $100 is priced at $0 today for tax purposes, but you’d want to receive that option. If you received that option on a dozen stocks, you’d be fairly confident that some of them would be worth a lot more than $0. Offering this equity (by investing in Skunkworks or offering clients a lower rate) comes out of AE’s margins. Theoretically, we should be lowering salaries to compensate. But we’re competitive on that front as well. Over the long-term, we believe AE employees who stick around are likely to fare extremely well monetarily (no legal promises). We think long-term. You should too when you choose your employer. We are looking to hire people who think long term. People create exponentially more value here in their 3rd or 4th year rather than their 1st or 2nd, and that only increases. The value you create leads to more equity issued every year, and you retain it while you remain at AE or at a Skunkworks co that spins out, in order to free up equity for others, which is how we afford to be generous with the equity. * Disclaimer: If you leave AE on good terms, you can retain 20% of your equity if you continue to send leads our way, share our core values, etc. If you leave AE for your own AE-incubated Skunkworks project (or someone else’s), you retain 100% of your equity. Folks create exponentially more value here in their 3rd and 4th years than their 1st or 2nd - we want folks who think long-term like we do. Salary range from $100k/year to $200k/year. Not in the range? That's fine, you can still apply and our team may fit you in a more senior position that we're not actively hiring right now, but we are growing fast and may have other openings soon. LocationLA office / US remoteBecome an agent! We only hire the world's most effective people. Full name *Email *Website *Who are you? Resume/CV(File types: pdf, doc, docx, txt, rtf)Cover Letter(File types: pdf, doc, docx, txt, rtf) I want a challenge (optional)⚠️ there’s a pretty high standard for performance so if you’re looking to coast it’s probably not the best fit⚠️ we only hire people who share our core valuesLET’S WORK TOGETHER Product Designer & ManagerAPPLY Product Designer & Manager APPLY Do you love building world-class, cutting-edge software? Do you believe that humans should be their most focused, efficient, intentional selves? Do you believe technology should always help human beings do just that? Then we’d love you to help us increase human agency. We think building software to increase human agency is good. We also think it’s good business. Agency Enterprise is a software development, data science, and design studio based in Los Angeles. We’ve bootstrapped our way to 100+ without VC, PE, or outside shareholders. It’s a simple business model - our amazing people do amazing work, we turn a profit, and we hire more amazing people. We take the long view and develop technology to increase human agency for our clients, our employees, and humanity. We incubate agency-increasing technology startups, building MVPs and founders. We are currently winning international competitions in neuroscience machine learning and researching the cutting edge of brain-computer interface (BCI) technology. We’ve built incredible MVPs for incredible startups, worked with large enterprises like Walmart, Samsung, and Berkshire Hathaway, and everything in-between. ABOUT THE ROLE You will work closely with our tech leads and full-stack developers to design and plant the product development process in a cross-functional, Agile environment. You will also interact directly with clients and educate them about AE’s methodology. You will help them work through user experience mapping, building their product roadmap and feature prioritization. OTHER RESPONSIBILITIES Approach projects with an ownership mentality so that both client and internal products will succeed, and you will inspire the best possible performance from your teams. Co-lead kick-off meetings. Collaborate with stakeholders to create and manage product roadmaps. Manage the Agile product development process Facilitate sprint planning, estimation, stand-up, demo, and retrospective meetings. Identify business needs and translate those into product requirements. Create sitemaps, user flow diagrams, low, medium, and high fidelity mock-ups. Facilitate user testing and user feedback sessions. Create user stories and manage the product backlog. Manage the design/developer hand-off process. Oversee and manage product timelines and budgets. Manage product releases. Contribute to product growth by identifying critical areas for potential improvement, then leading experimentation efforts. Communicate effectively with clients and stakeholders verbally and in writing. ABOUT YOU You have a minimum of 5 years of professional experience as a designer and have a penchant for product management and Agile. Or maybe you’re a product manager with 5+ years on the job and an eye for UX/UI design? You love working in a collaborative, high-energy environment, using agile methodologies and tools like Figma, Sketch, Linear, Pivotal Tracker (or equivalents). You value direct and honest feedback, but you know how to communicate it gracefully so it betters your partners' (coworkers and clients) work and happiness. Bonus points if you have experience in HTML/CSS or product development, you have a background in entrepreneurship or consulting, or you like VR and/or ice cream. OTHER COOL STUFF Remote work. Diversified, multi-company equity. Platinum health insurance. A fast-growing team with a happy bunch of former entrepreneurs, data scientists, full stack developers, UX/UI designers and product managers. Advance in your career or flex your entrepreneurial side by participating in our internal skunkworks projects (some projects spin out with team members as the C-suite) Resources for you to grow and learn on the job (this includes Udemy classes, books, participation in events, mentoring and - of course - the latest generation hardware. Offices right on Abbot Kinney, in the heart of Venice, CA. (Salt & Straw is steps from our office, and we get discounts in some Abbot Kinney stores. ) Weekly lunches with the US team. Monthly happy hours with friends and families. Occasional VR and game nights, if you are into that. Yearly retreat bringing together the whole team (with tropical fun and hackathons). RECRUITING NOTE There are an increasing number of scammers out there that are taking advantage of our company's name to scam people with fake offers that result in you ultimately giving the scammers money. Please make sure anyone who contacts you is contacting you from the domains ae. studio, agencyenterprise. com, agencyenterprisestudio. com (please make sure that the sender really is from those domains). If you have any questions about any interviews or offers you might have received and seem bogus, please contact recruiting-offers@ae. studio. Equity Working at AE means receiving equity in some of our client projects and in the Skunkworks projects we incubate. The equity granted is priced at a value of $0 when you receive it. If it were otherwise, you’d be forced to pay taxes. But to be clear, there are no free lunches, only the choice of who pays for your sandwich. At AE, the equity you receive is not free to AE. We invest millions annually in Skunkworks projects - paying our employees to build technology for which no client pays a bill. We receive equity from certain clients, which means we accept less in terms of our billable rate. We then pass that equity on to our employees. Sure it’s priced at $0 on day 1 - a call option on a stock trading at $100 with a strike price of $100 is priced at $0 today for tax purposes, but you’d want to receive that option. If you received that option on a dozen stocks, you’d be fairly confident that some of them would be worth a lot more than $0. Offering this equity (by investing in Skunkworks or offering clients a lower rate) comes out of AE’s margins. Theoretically, we should be lowering salaries to compensate. But we’re competitive on that front as well. Over the long-term, we believe AE employees who stick around are likely to fare extremely well monetarily (no legal promises). We think long-term. You should too when you choose your employer. We are looking to hire people who think long term. People create exponentially more value here in their 3rd or 4th year rather than their 1st or 2nd, and that only increases. The value you create leads to more equity issued every year, and you retain it while you remain at AE or at a Skunkworks co that spins out, in order to free up equity for others, which is how we afford to be generous with the equity. * Disclaimer: If you leave AE on good terms, you can retain 20% of your equity if you continue to send leads our way, share our core values, etc. If you leave AE for your own AE-incubated Skunkworks project (or someone else’s), you retain 100% of your equity. Folks create exponentially more value here in their 3rd and 4th years than their 1st or 2nd - we want folks who think long-term like we do. Salary range from $100k/year to $200k/year. Not in the range? That's fine, you can still apply and our team may fit you in a more senior position that we're not actively hiring right now, but we are growing fast and may have other openings soon.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://ae.studio/jobs/4011756004/data-scientist", "company_id": 3378, "source": 3, "skills": "", "title": "Data Scientist", "location": "LA office / US remote", "location_type": null, "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://ae.studio/jobs/4011756004/data-scientist", "description": "Data ScientistAPPLYAE Studio is an LA-based company focused on increasing human agency while maintaining our ineffable culture and whimsy. If the work diminishes agency, we may simply decide to “eff” it after all. In our Venice CA office and our respective domiciles live a team of highly-skilled developers, data scientists, and dubiously-lucid humorists building agency-increasing products you may recall from such sentences as the one in this paragraph. We’ve worked with Samsung, Walmart, Berkshire Hathaway, and other large, important entities you’ve probably heard of. Our work environment is fantastic for aspiring coders, calculators, and entrepreneurs. Also, we have neither VC, nor PE, nor other acronyms funding us. We have a weird business model where we sell our work for a profit and use those profits to pay people (I told you it was weird! ). More specifically, we deploy the proceeds from our consulting work in development and data science and our skunkworks proceeds to support research into cause areas we think are of paramount interest to human beings. We have made a name for ourselves in cutting-edge brain computer interface (BCI) R&D, and now we want to replicate past successes and solve the problem of AI alignment (aligning future powerful systems with human best interests). We want to optimize for human agency, if you feel similarly, please apply to support our research efforts. Data scientists will have the opportunity between client projects to work on important alignment research, with the opportunity to potentially have your research project prioritized if particularly promising. Do you have strong problem-solving skills? Do you have a disposition suited for self-management? Have you ever composed a Gilbert and Sullivan parody that would offend Victorian sensibilities? If you can say “yes” to two of those three questions, please apply! ABOUT YOU Fluency in Python–used often, derided occasionally, and considered its eponymous reference to the best of British sketch comedy Proven data science experience–primary or equal contributor, multiple smaller projects, management of steak-holders (so projects come out ‘well-done’) Comfort with the statistics and methods deployed in classification problems–that is, the ability to see the random forest for the trees Familiarity with NLP/NLU–even if this posting’s language is a bit unnatural, we suspect you’ll understand our process Experience calling machine learning APIs via REST or client libraries - and determining that their responses lack wit, guile, and tact Agile experience (worked with Kanban, Scrum, Extreme Programming)–or a sound literary understanding of Steve Martin’s “Picasso at the Lapin Agile,” even if you don’t speak French Growth mindset – You had better be funnier after the first 90 days. . . Willingness to learn our systems (wait, we have systems? ) Full-time availability–but if you have cool side projects that increase human agency, we totally want you to keep working on it. . . we’ll even help! Demonstrated ability to work independently/autonomously Ability to communicate effectively and work exclusively in English. This is a must, our ability to crack jokes in other languages is decidedly de rigeur. . . BONUS POINTS FOR Experience with self-managed data science projects–show us your cool side hustles, especially if they increase human agency or include obscure references! Software development background - We need help with syntax in English too. . . Experience working with / building / training deep learning-based computer vision or NLP models or LLMs – extra double-secret bonus points for identifying Schroedinger’s cat in a video JavaScript / Node. js experience–require(‘sense of humor’) Clean Code, Unit Tests, Lather, Rinse, Repeat Experience managing clients and client relationships–because, y’know, they pay us. . . Experience in Startups–because, y’know, we kinda are one, or actually more like several startups all rolled into one! You care about AI alignment, and are optimistic in humanity’s capacity to problem solve and safely navigate accelerating AI capabilities and have an awesome future RECRUITING NOTE There are an increasing number of scammers out there that are taking advantage of our company's name to scam people with fake offers that result in you ultimately giving the scammers money. Please make sure anyone who contacts you is contacting you from the domains ae. studio, agencyenterprise. com, agencyenterprisestudio. com (please make sure that the sender really is from those domains). If you have any questions about any interviews or offers you might have received and seem bogus, please contact recruiting-offers@ae. studio. Equity Working at AE means receiving equity in some of our client projects and in the Skunkworks projects we incubate. The equity granted is priced at a value of $0 when you receive it. If it were otherwise, you’d be forced to pay taxes. But to be clear, there are no free lunches, only the choice of who pays for your sandwich. At AE, the equity you receive is not free to AE. We invest millions annually in Skunkworks projects - paying our employees to build technology for which no client pays a bill. We receive equity from certain clients, which means we accept less in terms of our billable rate. We then pass that equity on to our employees. Sure it’s priced at $0 on day 1 - a call option on a stock trading at $100 with a strike price of $100 is priced at $0 today for tax purposes, but you’d want to receive that option. If you received that option on a dozen stocks, you’d be fairly confident that some of them would be worth a lot more than $0. Offering this equity (by investing in Skunkworks or offering clients a lower rate) comes out of AE’s margins. Theoretically, we should be lowering salaries to compensate. But we’re competitive on that front as well. Over the long-term, we believe AE employees who stick around are likely to fare extremely well monetarily (no legal promises). We think long-term. You should too when you choose your employer. We are looking to hire people who think long term. People create exponentially more value here in their 3rd or 4th year rather than their 1st or 2nd, and that only increases. The value you create leads to more equity issued every year, and you retain it while you remain at AE or at a Skunkworks co that spins out, in order to free up equity for others, which is how we afford to be generous with the equity. * Disclaimer: If you leave AE on good terms, you can retain 20% of your equity if you continue to send leads our way, share our core values, etc. If you leave AE for your own AE-incubated Skunkworks project (or someone else’s), you retain 100% of your equity. Folks create exponentially more value here in their 3rd and 4th years than their 1st or 2nd - we want folks who think long-term like we do. Salary range from $120k/year to $220k/year. Not in the range? That's fine, you can still apply and our team may fit you in a more senior position that we're not actively hiring right now, but we are growing fast and may have other openings soon. LocationLA office / US remoteBecome an agent! We only hire the world's most effective people. Full name *Email *Website *Who are you? Resume/CV(File types: pdf, doc, docx, txt, rtf)Cover Letter(File types: pdf, doc, docx, txt, rtf) I want a challenge (optional)⚠️ there’s a pretty high standard for performance so if you’re looking to coast it’s probably not the best fit⚠️ we only hire people who share our core valuesLET’S WORK TOGETHER Data ScientistAPPLY Data Scientist APPLY AE Studio is an LA-based company focused on increasing human agency while maintaining our ineffable culture and whimsy. If the work diminishes agency, we may simply decide to “eff” it after all. In our Venice CA office and our respective domiciles live a team of highly-skilled developers, data scientists, and dubiously-lucid humorists building agency-increasing products you may recall from such sentences as the one in this paragraph. We’ve worked with Samsung, Walmart, Berkshire Hathaway, and other large, important entities you’ve probably heard of. Our work environment is fantastic for aspiring coders, calculators, and entrepreneurs. Also, we have neither VC, nor PE, nor other acronyms funding us. We have a weird business model where we sell our work for a profit and use those profits to pay people (I told you it was weird! ). More specifically, we deploy the proceeds from our consulting work in development and data science and our skunkworks proceeds to support research into cause areas we think are of paramount interest to human beings. We have made a name for ourselves in cutting-edge brain computer interface (BCI) R&D, and now we want to replicate past successes and solve the problem of AI alignment (aligning future powerful systems with human best interests). We want to optimize for human agency, if you feel similarly, please apply to support our research efforts. Data scientists will have the opportunity between client projects to work on important alignment research, with the opportunity to potentially have your research project prioritized if particularly promising. Do you have strong problem-solving skills? Do you have a disposition suited for self-management? Have you ever composed a Gilbert and Sullivan parody that would offend Victorian sensibilities? If you can say “yes” to two of those three questions, please apply! ABOUT YOU Fluency in Python–used often, derided occasionally, and considered its eponymous reference to the best of British sketch comedy Proven data science experience–primary or equal contributor, multiple smaller projects, management of steak-holders (so projects come out ‘well-done’) Comfort with the statistics and methods deployed in classification problems–that is, the ability to see the random forest for the trees Familiarity with NLP/NLU–even if this posting’s language is a bit unnatural, we suspect you’ll understand our process Experience calling machine learning APIs via REST or client libraries - and determining that their responses lack wit, guile, and tact Agile experience (worked with Kanban, Scrum, Extreme Programming)–or a sound literary understanding of Steve Martin’s “Picasso at the Lapin Agile,” even if you don’t speak French Growth mindset – You had better be funnier after the first 90 days. . . Willingness to learn our systems (wait, we have systems? ) Full-time availability–but if you have cool side projects that increase human agency, we totally want you to keep working on it. . . we’ll even help! Demonstrated ability to work independently/autonomously Ability to communicate effectively and work exclusively in English. This is a must, our ability to crack jokes in other languages is decidedly de rigeur. . . BONUS POINTS FOR Experience with self-managed data science projects–show us your cool side hustles, especially if they increase human agency or include obscure references! Software development background - We need help with syntax in English too. . . Experience working with / building / training deep learning-based computer vision or NLP models or LLMs – extra double-secret bonus points for identifying Schroedinger’s cat in a video JavaScript / Node. js experience–require(‘sense of humor’) Clean Code, Unit Tests, Lather, Rinse, Repeat Experience managing clients and client relationships–because, y’know, they pay us. . . Experience in Startups–because, y’know, we kinda are one, or actually more like several startups all rolled into one! You care about AI alignment, and are optimistic in humanity’s capacity to problem solve and safely navigate accelerating AI capabilities and have an awesome future RECRUITING NOTE There are an increasing number of scammers out there that are taking advantage of our company's name to scam people with fake offers that result in you ultimately giving the scammers money. Please make sure anyone who contacts you is contacting you from the domains ae. studio, agencyenterprise. com, agencyenterprisestudio. com (please make sure that the sender really is from those domains). If you have any questions about any interviews or offers you might have received and seem bogus, please contact recruiting-offers@ae. studio. Equity Working at AE means receiving equity in some of our client projects and in the Skunkworks projects we incubate. The equity granted is priced at a value of $0 when you receive it. If it were otherwise, you’d be forced to pay taxes. But to be clear, there are no free lunches, only the choice of who pays for your sandwich. At AE, the equity you receive is not free to AE. We invest millions annually in Skunkworks projects - paying our employees to build technology for which no client pays a bill. We receive equity from certain clients, which means we accept less in terms of our billable rate. We then pass that equity on to our employees. Sure it’s priced at $0 on day 1 - a call option on a stock trading at $100 with a strike price of $100 is priced at $0 today for tax purposes, but you’d want to receive that option. If you received that option on a dozen stocks, you’d be fairly confident that some of them would be worth a lot more than $0. Offering this equity (by investing in Skunkworks or offering clients a lower rate) comes out of AE’s margins. Theoretically, we should be lowering salaries to compensate. But we’re competitive on that front as well. Over the long-term, we believe AE employees who stick around are likely to fare extremely well monetarily (no legal promises). We think long-term. You should too when you choose your employer. We are looking to hire people who think long term. People create exponentially more value here in their 3rd or 4th year rather than their 1st or 2nd, and that only increases. The value you create leads to more equity issued every year, and you retain it while you remain at AE or at a Skunkworks co that spins out, in order to free up equity for others, which is how we afford to be generous with the equity. * Disclaimer: If you leave AE on good terms, you can retain 20% of your equity if you continue to send leads our way, share our core values, etc. If you leave AE for your own AE-incubated Skunkworks project (or someone else’s), you retain 100% of your equity. Folks create exponentially more value here in their 3rd and 4th years than their 1st or 2nd - we want folks who think long-term like we do. Salary range from $120k/year to $220k/year. Not in the range? That's fine, you can still apply and our team may fit you in a more senior position that we're not actively hiring right now, but we are growing fast and may have other openings soon.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://ae.studio/jobs/4569401004/senior-full-stack-developer-javascript", "company_id": 3378, "source": 3, "skills": "", "title": "Senior Full Stack Developer JavaScript", "location": "Florianopolis office/ Brazil remote", "location_type": "remote", "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://ae.studio/jobs/4569401004/senior-full-stack-developer-javascript", "description": "Senior Full Stack Developer JavaScriptAPPLYAgency Enterprise Studio is a company based in Los Angeles and we're looking for the world's most effective developers that are also a cultural fit to work with us on projects that increase Human Agency. We are looking for the best of the best. People who love what they do and are legitimately excellent at it. Is this you? If you answered this with an unequivocal yes, then it might not be the best fit. We also want people who understand that no one knows everything, don't have egos, understand their own shortcomings and are excited to grow. We want people who are badass but also humble and care about helping their team. We want people who get shit done and give a shit. We want people who don't care about titles but care about results. We are asking for a lot. What we can offer you? A legitimate meritocracy. We don't care if you fancy huh, we care if you are actually good at your job. Been passed over for the guy who's great at talking to the EVP about how you just need to move to microservices but just pushed several critical bugs to production? That will not happen here. The ability to work with other people like you. And the joy of not having to work with people who aren't - no shitty devs, assholes, or devs who spend 2 hours a day working and 6 hours on TikTok and spreading office gossip. An intensive onboarding bootcamp where you will be challenged, tested, and learn a ton. While we don't like firing people or having them quit, we care more about guaranteeing #2 and this process is meant to weed people out who aren't a fit. A highly supportive atmosphere. We have a high standard for performance, but we will do everything we can to help each individual attain it. Everyone at all levels of the company is willing to pitch in and provide advice, mentorship, or even jump in to get our hands dirty coding, organizing the backlog, or talking to clients when a team needs extra support. We're in this together. The ability to work on some of the most exciting technical and product challenges that exist today. We work with clients, who bring us in to work on their most critical and important tasks because they know we can deliver and on our own internal projects which solve real needs. We are leaders in the BCI space where we are constantly pushing towards our long-term goal of developing the BCI OS in a way that is agency-increasing. We also work on complex blockchain projects with legitimate use cases. We also build best-in-class web and mobile applications and have some of the world's best data scientists. The ability to work on a range of projects in different fields, industries, and stacks for companies ranging from early-stage startups to enterprise. A diversified employee equity program that includes equity in client and skunkworks projects. And the opportunity to potentially have AE support you doing your own startup as part of our skunkworks program. Competitive pay with regular performance-based raises and other fun stuff including company retreats, a really nice office, health and pet insurance, a cake to embarass you on your birthday, etc. STACK You will be working mostly with JavaScript and libraries/frameworks in its ecosystem. JavaScript / TypeScript React / Angular Node / Next. js / Apollo Everything else in the JavaScript ecosystem YOU WILL Work Full-time. Work with a 10x dev team. Work in internal projects and client projects. Build projects with a product focused mentality while keeping your code clean. Build modern, efficient apps and test that they work correctly. Constantly communicate with clients. Review pull requests from other members of the team. Have a founder mentality and proactively solve problems. Wear any hats needed to get the job done. ABOUT YOU Ability to communicate effectively and work exclusively in English. (This is a must) A minimum of 5+ years of programming experience with our stack. realizing in the middle of the night you forgot a major edge case that you can’t believe hasn’t burned you yet and need to fix asap git blaming some shitty code and realizing it was you, deciding to never git blame again having some super strong preference about the way something “needs to be done” that you read in a book, forum, conference or fancy colleague, being super militant about enforcing it and judgemental about people who don’t already do it. and then realizing this was wrong you had a stack you thought was universally superior but now you think people should just pick the right tool for the job being such an expert at something that you forget why anyone would think this is hard, then having to learn something new and sucking at it at first and remembering that programming actually is a skill, then getting really great at that Willing to work with new technologies, different environments. Willing to work with everything and anything, Full Stack. Experience working with Agile. Growth mindset. Like your work and accomplish many things. Ownership mentality. Problem-solving skills. Self-management skills. Can work independently/autonomously. OTHER STUFF ABOUT US Remote work. Flexible schedule. Monthly salary in USD. Multi-company Equity. Accountant that manages all the paperwork for you. Health Insurance after 3 months. English classes. Free lunch everyday at the office (if you're there)! Weekly talks, gather together with the team exchange knowledge. Yearly retreat bringing together the whole team. THE INTERVIEW PROCESS Initial conversation, we get to know each other and see if it makes sense for both sides. Live coding test. Final interview. Note: There are an increasing number of scammers out there that are taking advantage of our company's name to scam people with fake offers that result in you ultimately giving the scammers money. Please make sure anyone who contacts you is contacting you from the domains ae. studio, agencyenterprise. com, agencyenterprisestudio. com (please make sure that the sender really is from those domains). If you have any questions about any interviews or offers you might have received and seem bogus, please contact recruiting-offers@ae. studio. Equity Working at AE means receiving equity in some of our client projects and in the Skunkworks projects we incubate. The equity granted is priced at a value of $0 when you receive it. If it were otherwise, you'd be forced to pay taxes. But to be clear, there are no free lunches, only the choice of who pays for your sandwich. At AE, the equity you receive is not free to AE. We invest millions annually in Skunkworks projects - paying our employees to build technology for which no client pays a bill. We receive equity from certain clients, which means we accept less in terms of our billable rate. We then pass that equity on to our employees. Sure it's priced at $0 on day 1 - a call option on a stock trading at $100 with a strike price of $100 is priced at $0 today for tax purposes, but you'd want to receive that option. If you received that option on a dozen stocks, you'd be fairly confident that some of them would be worth a lot more than $0. Offering this equity (by investing in Skunkworks or offering clients a lower rate) comes out of AE's margins. Theoretically, we should be lowering salaries to compensate. But we're competitive on that front as well. Over the long-term, we believe AE employees who stick around are likely to fare extremely well monetarily (no legal promises). We think long-term. You should too when you choose your employer. We are looking to hire people who think long term. People create exponentially more value here in their 3rd or 4th year rather than their 1st or 2nd, and that only increases. The value you create leads to more equity issued every year, and you retain it while you remain at AE or at a Skunkworks co that spins out, in order to free up equity for others, which is how we afford to be generous with the equity. * Disclaimer: If you leave AE on good terms, you can retain 20% of your equity if you continue to send leads our way, share our core values, etc. If you leave AE for your own AE-incubated Skunkworks project (or someone else's), you retain 100% of your equity. Folks create exponentially more value here in their 3rd and 4th years than their 1st or 2nd - we want folks who think long-term like we do. LocationFlorianopolis office/ Brazil remoteBecome an agent! We only hire the world's most effective people. Full name *Email *Website *Who are you? Resume/CV(File types: pdf, doc, docx, txt, rtf)Cover Letter(File types: pdf, doc, docx, txt, rtf) I want a challenge (optional)⚠️ there’s a pretty high standard for performance so if you’re looking to coast it’s probably not the best fit⚠️ we only hire people who share our core valuesLET’S WORK TOGETHER Senior Full Stack Developer JavaScriptAPPLY Senior Full Stack Developer JavaScript APPLY Agency Enterprise Studio is a company based in Los Angeles and we're looking for the world's most effective developers that are also a cultural fit to work with us on projects that increase Human Agency. We are looking for the best of the best. People who love what they do and are legitimately excellent at it. Is this you? If you answered this with an unequivocal yes, then it might not be the best fit. We also want people who understand that no one knows everything, don't have egos, understand their own shortcomings and are excited to grow. We want people who are badass but also humble and care about helping their team. We want people who get shit done and give a shit. We want people who don't care about titles but care about results. We are asking for a lot. What we can offer you? A legitimate meritocracy. We don't care if you fancy huh, we care if you are actually good at your job. Been passed over for the guy who's great at talking to the EVP about how you just need to move to microservices but just pushed several critical bugs to production? That will not happen here. The ability to work with other people like you. And the joy of not having to work with people who aren't - no shitty devs, assholes, or devs who spend 2 hours a day working and 6 hours on TikTok and spreading office gossip. An intensive onboarding bootcamp where you will be challenged, tested, and learn a ton. While we don't like firing people or having them quit, we care more about guaranteeing #2 and this process is meant to weed people out who aren't a fit. A highly supportive atmosphere. We have a high standard for performance, but we will do everything we can to help each individual attain it. Everyone at all levels of the company is willing to pitch in and provide advice, mentorship, or even jump in to get our hands dirty coding, organizing the backlog, or talking to clients when a team needs extra support. We're in this together. The ability to work on some of the most exciting technical and product challenges that exist today. We work with clients, who bring us in to work on their most critical and important tasks because they know we can deliver and on our own internal projects which solve real needs. We are leaders in the BCI space where we are constantly pushing towards our long-term goal of developing the BCI OS in a way that is agency-increasing. We also work on complex blockchain projects with legitimate use cases. We also build best-in-class web and mobile applications and have some of the world's best data scientists. The ability to work on a range of projects in different fields, industries, and stacks for companies ranging from early-stage startups to enterprise. A diversified employee equity program that includes equity in client and skunkworks projects. And the opportunity to potentially have AE support you doing your own startup as part of our skunkworks program. Competitive pay with regular performance-based raises and other fun stuff including company retreats, a really nice office, health and pet insurance, a cake to embarass you on your birthday, etc. STACK You will be working mostly with JavaScript and libraries/frameworks in its ecosystem. JavaScript / TypeScript React / Angular Node / Next. js / Apollo Everything else in the JavaScript ecosystem YOU WILL Work Full-time. Work with a 10x dev team. Work in internal projects and client projects. Build projects with a product focused mentality while keeping your code clean. Build modern, efficient apps and test that they work correctly. Constantly communicate with clients. Review pull requests from other members of the team. Have a founder mentality and proactively solve problems. Wear any hats needed to get the job done. ABOUT YOU Ability to communicate effectively and work exclusively in English. (This is a must) A minimum of 5+ years of programming experience with our stack. realizing in the middle of the night you forgot a major edge case that you can’t believe hasn’t burned you yet and need to fix asap git blaming some shitty code and realizing it was you, deciding to never git blame again having some super strong preference about the way something “needs to be done” that you read in a book, forum, conference or fancy colleague, being super militant about enforcing it and judgemental about people who don’t already do it. and then realizing this was wrong you had a stack you thought was universally superior but now you think people should just pick the right tool for the job being such an expert at something that you forget why anyone would think this is hard, then having to learn something new and sucking at it at first and remembering that programming actually is a skill, then getting really great at that Willing to work with new technologies, different environments. Willing to work with everything and anything, Full Stack. Experience working with Agile. Growth mindset. Like your work and accomplish many things. Ownership mentality. Problem-solving skills. Self-management skills. Can work independently/autonomously. OTHER STUFF ABOUT US Remote work. Flexible schedule. Monthly salary in USD. Multi-company Equity. Accountant that manages all the paperwork for you. Health Insurance after 3 months. English classes. Free lunch everyday at the office (if you're there)! Weekly talks, gather together with the team exchange knowledge. Yearly retreat bringing together the whole team. THE INTERVIEW PROCESS Initial conversation, we get to know each other and see if it makes sense for both sides. Live coding test. Final interview. Note: There are an increasing number of scammers out there that are taking advantage of our company's name to scam people with fake offers that result in you ultimately giving the scammers money. Please make sure anyone who contacts you is contacting you from the domains ae. studio, agencyenterprise. com, agencyenterprisestudio. com (please make sure that the sender really is from those domains). If you have any questions about any interviews or offers you might have received and seem bogus, please contact recruiting-offers@ae. studio. Equity Working at AE means receiving equity in some of our client projects and in the Skunkworks projects we incubate. The equity granted is priced at a value of $0 when you receive it. If it were otherwise, you'd be forced to pay taxes. But to be clear, there are no free lunches, only the choice of who pays for your sandwich. At AE, the equity you receive is not free to AE. We invest millions annually in Skunkworks projects - paying our employees to build technology for which no client pays a bill. We receive equity from certain clients, which means we accept less in terms of our billable rate. We then pass that equity on to our employees. Sure it's priced at $0 on day 1 - a call option on a stock trading at $100 with a strike price of $100 is priced at $0 today for tax purposes, but you'd want to receive that option. If you received that option on a dozen stocks, you'd be fairly confident that some of them would be worth a lot more than $0. Offering this equity (by investing in Skunkworks or offering clients a lower rate) comes out of AE's margins. Theoretically, we should be lowering salaries to compensate. But we're competitive on that front as well. Over the long-term, we believe AE employees who stick around are likely to fare extremely well monetarily (no legal promises). We think long-term. You should too when you choose your employer. We are looking to hire people who think long term. People create exponentially more value here in their 3rd or 4th year rather than their 1st or 2nd, and that only increases. The value you create leads to more equity issued every year, and you retain it while you remain at AE or at a Skunkworks co that spins out, in order to free up equity for others, which is how we afford to be generous with the equity. * Disclaimer: If you leave AE on good terms, you can retain 20% of your equity if you continue to send leads our way, share our core values, etc. If you leave AE for your own AE-incubated Skunkworks project (or someone else's), you retain 100% of your equity. Folks create exponentially more value here in their 3rd and 4th years than their 1st or 2nd - we want folks who think long-term like we do.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://ae.studio/jobs/5476285004/alignment-data-scientist", "company_id": 3378, "source": 3, "skills": "", "title": "Alignment Data Scientist", "location": "Florianopolis office/ Brazil remote", "location_type": null, "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://ae.studio/jobs/5476285004/alignment-data-scientist", "description": "Alignment Data ScientistAPPLYAE Studio is an LA-based company focused on increasing human agency, primarily by making the imminent AGI future go well. Our team consists of the best developers, data scientists, researchers, and founders. We do all sorts of projects, always of the quality that makes our clients sing our praises. We’ve worked with Samsung, Walmart, Berkshire Hathaway, and other large, important entities you’ve probably heard of. Our work environment is fantastic for aspiring coders, calculators, and entrepreneurs. Also, we have neither VC, nor PE, nor other acronyms funding us. We have a weird business model where we sell our work for a profit and use those profits to pay people (I told you it was weird! ). We then reinvest those profits into our promising research on AI alignment and our ambitious internal skunkworks projects. We previously sold one of our skunkworks for some number of millions of dollars. We have made a name for ourselves in cutting-edge brain computer interface (BCI) R&D, and we are making a name for ourselves in our research and policy efforts on AI alignment. We want to optimize for human agency, if you feel similarly, please apply to support our efforts. Data scientists will have the opportunity between client projects to work on important alignment research, with the opportunity to potentially have your research project prioritized if particularly promising. ABOUT YOU Experience with ML or alignment research You care about AI alignment, and are optimistic in humanity’s capacity to problem solve and safely navigate accelerating AI capabilities and have an awesome future Eager to use AI and automate everything you do Comfortable working in a fast-paced, startup-like environment Proven data science experience–primary or equal contributor, multiple smaller projects, management of stake-holders. Experience working with / building / training deep learning-based computer vision or NLP models or LLMs Lots of experience with Python Experience calling AI models’ APIs via REST or client libraries Growth mindset. Excited about learning new things and solving difficult problems Demonstrated ability to work independently/autonomously Track record for breaking down complex problems into manageable chunks for individual or team execution Full-time availability–but if you have cool side projects that increase human agency, we totally want you to keep working on it. . . we’ll even help! Ability to communicate effectively and work exclusively in English and communicate complex technical concepts clearly to a non-technical audience BONUS POINTS FOR Experience with self-managed data science projects–show us your cool side hustles, especially if they increase human agency! Experienced with software engineering best practices / software development background Agile experience (worked with Kanban, Scrum, Extreme Programming) JavaScript / Node. js experience Experience managing clients and client relationships Experience in startups RECRUITING NOTE There are an increasing number of scammers out there that are taking advantage of our company's name to scam people with fake offers that result in you ultimately giving the scammers money. Please make sure anyone who contacts you is contacting you from the domains ae. studio, agencyenterprise. com, agencyenterprisestudio. com (please make sure that the sender really is from those domains). If you have any questions about any interviews or offers you might have received and seem bogus, please contact recruiting-offers@ae. studio. Equity Working at AE means receiving equity in some of our client projects and in the Skunkworks projects we incubate. The equity granted is priced at a value of $0 when you receive it. If it were otherwise, you’d be forced to pay taxes. But to be clear, there are no free lunches, only the choice of who pays for your sandwich. At AE, the equity you receive is not free to AE. We invest millions annually in Skunkworks projects - paying our employees to build technology for which no client pays a bill. We receive equity from certain clients, which means we accept less in terms of our billable rate. We then pass that equity on to our employees. Sure it’s priced at $0 on day 1 - a call option on a stock trading at $100 with a strike price of $100 is priced at $0 today for tax purposes, but you’d want to receive that option. If you received that option on a dozen stocks, you’d be fairly confident that some of them would be worth a lot more than $0. Offering this equity (by investing in Skunkworks or offering clients a lower rate) comes out of AE’s margins. Theoretically, we should be lowering salaries to compensate. But we’re competitive on that front as well. Over the long-term, we believe AE employees who stick around are likely to fare extremely well monetarily (no legal promises). We think long-term. You should too when you choose your employer. We are looking to hire people who think long term. People create exponentially more value here in their 3rd or 4th year rather than their 1st or 2nd, and that only increases. The value you create leads to more equity issued every year, and you retain it while you remain at AE or at a Skunkworks co that spins out, in order to free up equity for others, which is how we afford to be generous with the equity. * Disclaimer: If you leave AE on good terms, you can retain 20% of your equity if you continue to send leads our way, share our core values, etc. If you leave AE for your own AE-incubated Skunkworks project (or someone else’s), you retain 100% of your equity. Folks create exponentially more value here in their 3rd and 4th years than their 1st or 2nd - we want folks who think long-term like we do. LocationFlorianopolis office/ Brazil remoteBecome an agent! We only hire the world's most effective people. Full name *Email *Website *Who are you? Resume/CV(File types: pdf, doc, docx, txt, rtf)Cover Letter(File types: pdf, doc, docx, txt, rtf) I want a challenge (optional)⚠️ there’s a pretty high standard for performance so if you’re looking to coast it’s probably not the best fit⚠️ we only hire people who share our core valuesLET’S WORK TOGETHER Alignment Data ScientistAPPLY Alignment Data Scientist APPLY AE Studio is an LA-based company focused on increasing human agency, primarily by making the imminent AGI future go well. Our team consists of the best developers, data scientists, researchers, and founders. We do all sorts of projects, always of the quality that makes our clients sing our praises. We’ve worked with Samsung, Walmart, Berkshire Hathaway, and other large, important entities you’ve probably heard of. Our work environment is fantastic for aspiring coders, calculators, and entrepreneurs. Also, we have neither VC, nor PE, nor other acronyms funding us. We have a weird business model where we sell our work for a profit and use those profits to pay people (I told you it was weird! ). We then reinvest those profits into our promising research on AI alignment and our ambitious internal skunkworks projects. We previously sold one of our skunkworks for some number of millions of dollars. We have made a name for ourselves in cutting-edge brain computer interface (BCI) R&D, and we are making a name for ourselves in our research and policy efforts on AI alignment. We want to optimize for human agency, if you feel similarly, please apply to support our efforts. Data scientists will have the opportunity between client projects to work on important alignment research, with the opportunity to potentially have your research project prioritized if particularly promising. ABOUT YOU Experience with ML or alignment research You care about AI alignment, and are optimistic in humanity’s capacity to problem solve and safely navigate accelerating AI capabilities and have an awesome future Eager to use AI and automate everything you do Comfortable working in a fast-paced, startup-like environment Proven data science experience–primary or equal contributor, multiple smaller projects, management of stake-holders. Experience working with / building / training deep learning-based computer vision or NLP models or LLMs Lots of experience with Python Experience calling AI models’ APIs via REST or client libraries Growth mindset. Excited about learning new things and solving difficult problems Demonstrated ability to work independently/autonomously Track record for breaking down complex problems into manageable chunks for individual or team execution Full-time availability–but if you have cool side projects that increase human agency, we totally want you to keep working on it. . . we’ll even help! Ability to communicate effectively and work exclusively in English and communicate complex technical concepts clearly to a non-technical audience BONUS POINTS FOR Experience with self-managed data science projects–show us your cool side hustles, especially if they increase human agency! Experienced with software engineering best practices / software development background Agile experience (worked with Kanban, Scrum, Extreme Programming) JavaScript / Node. js experience Experience managing clients and client relationships Experience in startups RECRUITING NOTE There are an increasing number of scammers out there that are taking advantage of our company's name to scam people with fake offers that result in you ultimately giving the scammers money. Please make sure anyone who contacts you is contacting you from the domains ae. studio, agencyenterprise. com, agencyenterprisestudio. com (please make sure that the sender really is from those domains). If you have any questions about any interviews or offers you might have received and seem bogus, please contact recruiting-offers@ae. studio. Equity Working at AE means receiving equity in some of our client projects and in the Skunkworks projects we incubate. The equity granted is priced at a value of $0 when you receive it. If it were otherwise, you’d be forced to pay taxes. But to be clear, there are no free lunches, only the choice of who pays for your sandwich. At AE, the equity you receive is not free to AE. We invest millions annually in Skunkworks projects - paying our employees to build technology for which no client pays a bill. We receive equity from certain clients, which means we accept less in terms of our billable rate. We then pass that equity on to our employees. Sure it’s priced at $0 on day 1 - a call option on a stock trading at $100 with a strike price of $100 is priced at $0 today for tax purposes, but you’d want to receive that option. If you received that option on a dozen stocks, you’d be fairly confident that some of them would be worth a lot more than $0. Offering this equity (by investing in Skunkworks or offering clients a lower rate) comes out of AE’s margins. Theoretically, we should be lowering salaries to compensate. But we’re competitive on that front as well. Over the long-term, we believe AE employees who stick around are likely to fare extremely well monetarily (no legal promises). We think long-term. You should too when you choose your employer. We are looking to hire people who think long term. People create exponentially more value here in their 3rd or 4th year rather than their 1st or 2nd, and that only increases. The value you create leads to more equity issued every year, and you retain it while you remain at AE or at a Skunkworks co that spins out, in order to free up equity for others, which is how we afford to be generous with the equity. * Disclaimer: If you leave AE on good terms, you can retain 20% of your equity if you continue to send leads our way, share our core values, etc. If you leave AE for your own AE-incubated Skunkworks project (or someone else’s), you retain 100% of your equity. Folks create exponentially more value here in their 3rd and 4th years than their 1st or 2nd - we want folks who think long-term like we do.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://ae.studio/jobs/5476042004/business-development-manager", "company_id": 3378, "source": 3, "skills": "", "title": "Business Development Manager", "location": "LA office / US remote", "location_type": "remote", "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://ae.studio/jobs/5476042004/business-development-manager", "description": "Business Development ManagerAPPLYABOUT AE STUDIO AE Studio is a Los Angeles-based product design, development, and data science studio. We are on a mission to increase Human Agency—for our team, clients, users, and ultimately, humanity. Our work combines innovative design, cutting-edge AI technologies, and a founder-level dedication to create meaningful, impactful solutions. We are looking for the world’s most effective professionals who share our values and cultural alignment to work with us on projects that truly make a difference. At AE Studio, we guarantee to our clients: \"We’ll treat your project or product like it’s our own startup and do whatever it takes, with a founder-level mentality, to make it spectacularly successful. \" ABOUT THE ROLE As a Business Development Manager, you will play a pivotal role in driving new business growth, managing inbound leads, and expanding our client pipeline. You’ll collaborate closely with the Vice President of Sales to filter and qualify initial calls, manage pipeline intake, schedule follow-up meetings, and source new business opportunities. Your role will require a mastery of sales strategy, strong business acumen, and proficiency in modern sales tools like Slack, GSuite, and HubSpot. You will be empowered to develop strategic lead-generation initiatives, nurture relationships, and identify opportunities that align with AE Studio’s mission. ABOUT YOU AE Studio team members embody a growth mindset, an ownership mentality, and a commitment to innovation. You will thrive in this role if you are:Experienced and Skilled: 5+ years of experience in technology or software sales with a track record of success. A proven closer—skilled at qualifying leads, handling objections, and guiding prospects through the sales process. Highly organized with the ability to manage multiple deals at various stages in the pipeline. Proficient in Slack, GSuite, and HubSpot (or similar CRM tools). Tech-Savvy and Adaptable: Capable of quickly understanding complex software and technology solutions. Comfortable using sales automation tools to streamline processes and improve efficiency. Able to adapt to changing priorities and new sales strategies. Impact-Focused: Exceptional at managing and growing a sales pipeline, ensuring that every prospect receives high-value engagement. A strategic thinker who balances short-term wins with long-term business development objectives. Adept at sourcing and developing new leads, ensuring a steady flow of opportunities into the pipeline. A Strong Communicator and Collaborator: Excellent verbal and written communication skills, with the ability to build rapport and trust quickly. A natural relationship-builder, skilled at engaging C-level decision-makers and stakeholders. Open to receiving and providing direct and honest feedback to foster continuous growth. Mission-Driven: Passionate about building meaningful business relationships that align with AE Studio’s mission to increase human agency. A self-starter with a founder-level mentality, eager to take ownership of business development initiatives. Experienced in fast-paced, high-growth environments, whether in a startup, agency, or enterprise sales setting. WHAT YOU’LL GET A full-time position with a fast-growing, mission-driven team of designers, developers, and data scientists. The opportunity to work on exciting, high-impact projects that leverage AI and cutting-edge technology. A remote-first culture with flexible schedules and the option to work from one of our offices. Platinum health insurance and equity opportunities. Weekly knowledge-sharing talks and an annual team retreat. A supportive culture that values direct feedback, growth, and collaboration. Free lunch every day at the office (if you’re there! ). LocationLA office / US remoteBecome an agent! We only hire the world's most effective people. Full name *Email *Website *Who are you? Resume/CV(File types: pdf, doc, docx, txt, rtf)Cover Letter(File types: pdf, doc, docx, txt, rtf) I want a challenge (optional)⚠️ there’s a pretty high standard for performance so if you’re looking to coast it’s probably not the best fit⚠️ we only hire people who share our core valuesLET’S WORK TOGETHER Business Development ManagerAPPLY Business Development Manager APPLY ABOUT AE STUDIO AE Studio is a Los Angeles-based product design, development, and data science studio. We are on a mission to increase Human Agency—for our team, clients, users, and ultimately, humanity. Our work combines innovative design, cutting-edge AI technologies, and a founder-level dedication to create meaningful, impactful solutions. We are looking for the world’s most effective professionals who share our values and cultural alignment to work with us on projects that truly make a difference. At AE Studio, we guarantee to our clients: \"We’ll treat your project or product like it’s our own startup and do whatever it takes, with a founder-level mentality, to make it spectacularly successful. \" ABOUT THE ROLE As a Business Development Manager, you will play a pivotal role in driving new business growth, managing inbound leads, and expanding our client pipeline. You’ll collaborate closely with the Vice President of Sales to filter and qualify initial calls, manage pipeline intake, schedule follow-up meetings, and source new business opportunities. Your role will require a mastery of sales strategy, strong business acumen, and proficiency in modern sales tools like Slack, GSuite, and HubSpot. You will be empowered to develop strategic lead-generation initiatives, nurture relationships, and identify opportunities that align with AE Studio’s mission. ABOUT YOU AE Studio team members embody a growth mindset, an ownership mentality, and a commitment to innovation. You will thrive in this role if you are:Experienced and Skilled: 5+ years of experience in technology or software sales with a track record of success. A proven closer—skilled at qualifying leads, handling objections, and guiding prospects through the sales process. Highly organized with the ability to manage multiple deals at various stages in the pipeline. Proficient in Slack, GSuite, and HubSpot (or similar CRM tools). Tech-Savvy and Adaptable: Capable of quickly understanding complex software and technology solutions. Comfortable using sales automation tools to streamline processes and improve efficiency. Able to adapt to changing priorities and new sales strategies. Impact-Focused: Exceptional at managing and growing a sales pipeline, ensuring that every prospect receives high-value engagement. A strategic thinker who balances short-term wins with long-term business development objectives. Adept at sourcing and developing new leads, ensuring a steady flow of opportunities into the pipeline. A Strong Communicator and Collaborator: Excellent verbal and written communication skills, with the ability to build rapport and trust quickly. A natural relationship-builder, skilled at engaging C-level decision-makers and stakeholders. Open to receiving and providing direct and honest feedback to foster continuous growth. Mission-Driven: Passionate about building meaningful business relationships that align with AE Studio’s mission to increase human agency. A self-starter with a founder-level mentality, eager to take ownership of business development initiatives. Experienced in fast-paced, high-growth environments, whether in a startup, agency, or enterprise sales setting. WHAT YOU’LL GET A full-time position with a fast-growing, mission-driven team of designers, developers, and data scientists. The opportunity to work on exciting, high-impact projects that leverage AI and cutting-edge technology. A remote-first culture with flexible schedules and the option to work from one of our offices. Platinum health insurance and equity opportunities. Weekly knowledge-sharing talks and an annual team retreat. A supportive culture that values direct feedback, growth, and collaboration. Free lunch every day at the office (if you’re there! ).", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://ae.studio/jobs/**********/alignment-data-scientist", "company_id": 3378, "source": 3, "skills": "", "title": "Alignment Data Scientist", "location": "LA office / US remote", "location_type": "onsite", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://ae.studio/jobs/**********/alignment-data-scientist", "description": "Alignment Data ScientistAPPLYAE Studio is an LA-based company focused on increasing human agency, primarily by making the imminent AGI future go well. Our team consists of the best developers, data scientists, researchers, and founders. We do all sorts of projects, always of the quality that makes our clients sing our praises. We’ve worked with Samsung, Walmart, Berkshire Hathaway, and other large, important entities you’ve probably heard of. Our work environment is fantastic for aspiring coders, calculators, and entrepreneurs. Also, we have neither VC, nor PE, nor other acronyms funding us. We have a weird business model where we sell our work for a profit and use those profits to pay people (I told you it was weird! ). We then reinvest those profits into our promising research on AI alignment and our ambitious internal skunkworks projects. We previously sold one of our skunkworks for some number of millions of dollars. We have made a name for ourselves in cutting-edge brain computer interface (BCI) R&D, and we are making a name for ourselves in our research and policy efforts on AI alignment. We want to optimize for human agency, if you feel similarly, please apply to support our efforts. Data scientists will have the opportunity between client projects to work on important alignment research, with the opportunity to potentially have your research project prioritized if particularly promising. ABOUT YOU Experience with ML or alignment research You care about AI alignment, and are optimistic in humanity’s capacity to problem solve and safely navigate accelerating AI capabilities and have an awesome future Eager to use AI and automate everything you do Comfortable working in a fast-paced, startup-like environment Proven data science experience–primary or equal contributor, multiple smaller projects, management of stake-holders. Experience working with / building / training deep learning-based computer vision or NLP models or LLMs Lots of experience with Python Experience calling AI models’ APIs via REST or client libraries Growth mindset. Excited about learning new things and solving difficult problems Demonstrated ability to work independently/autonomously Track record for breaking down complex problems into manageable chunks for individual or team execution Full-time availability–but if you have cool side projects that increase human agency, we totally want you to keep working on it. . . we’ll even help! Ability to communicate effectively and work exclusively in English and communicate complex technical concepts clearly to a non-technical audience BONUS POINTS FOR Experience with self-managed data science projects–show us your cool side hustles, especially if they increase human agency! Experienced with software engineering best practices / software development background Agile experience (worked with Kanban, Scrum, Extreme Programming) JavaScript / Node. js experience Experience managing clients and client relationships Experience in startups Being in LA to work in person from our amazing office RECRUITING NOTE There are an increasing number of scammers out there that are taking advantage of our company's name to scam people with fake offers that result in you ultimately giving the scammers money. Please make sure anyone who contacts you is contacting you from the domains ae. studio, agencyenterprise. com, agencyenterprisestudio. com (please make sure that the sender really is from those domains). If you have any questions about any interviews or offers you might have received and seem bogus, please contact recruiting-offers@ae. studio. Equity Working at AE means receiving equity in some of our client projects and in the Skunkworks projects we incubate. The equity granted is priced at a value of $0 when you receive it. If it were otherwise, you’d be forced to pay taxes. But to be clear, there are no free lunches, only the choice of who pays for your sandwich. At AE, the equity you receive is not free to AE. We invest millions annually in Skunkworks projects - paying our employees to build technology for which no client pays a bill. We receive equity from certain clients, which means we accept less in terms of our billable rate. We then pass that equity on to our employees. Sure it’s priced at $0 on day 1 - a call option on a stock trading at $100 with a strike price of $100 is priced at $0 today for tax purposes, but you’d want to receive that option. If you received that option on a dozen stocks, you’d be fairly confident that some of them would be worth a lot more than $0. Offering this equity (by investing in Skunkworks or offering clients a lower rate) comes out of AE’s margins. Theoretically, we should be lowering salaries to compensate. But we’re competitive on that front as well. Over the long-term, we believe AE employees who stick around are likely to fare extremely well monetarily (no legal promises). We think long-term. You should too when you choose your employer. We are looking to hire people who think long term. People create exponentially more value here in their 3rd or 4th year rather than their 1st or 2nd, and that only increases. The value you create leads to more equity issued every year, and you retain it while you remain at AE or at a Skunkworks co that spins out, in order to free up equity for others, which is how we afford to be generous with the equity. * Disclaimer: If you leave AE on good terms, you can retain 20% of your equity if you continue to send leads our way, share our core values, etc. If you leave AE for your own AE-incubated Skunkworks project (or someone else’s), you retain 100% of your equity. Folks create exponentially more value here in their 3rd and 4th years than their 1st or 2nd - we want folks who think long-term like we do. LocationLA office / US remoteBecome an agent! We only hire the world's most effective people. Full name *Email *Website *Who are you? Resume/CV(File types: pdf, doc, docx, txt, rtf)Cover Letter(File types: pdf, doc, docx, txt, rtf) I want a challenge (optional)⚠️ there’s a pretty high standard for performance so if you’re looking to coast it’s probably not the best fit⚠️ we only hire people who share our core valuesLET’S WORK TOGETHER Alignment Data ScientistAPPLY Alignment Data Scientist APPLY AE Studio is an LA-based company focused on increasing human agency, primarily by making the imminent AGI future go well. Our team consists of the best developers, data scientists, researchers, and founders. We do all sorts of projects, always of the quality that makes our clients sing our praises. We’ve worked with Samsung, Walmart, Berkshire Hathaway, and other large, important entities you’ve probably heard of. Our work environment is fantastic for aspiring coders, calculators, and entrepreneurs. Also, we have neither VC, nor PE, nor other acronyms funding us. We have a weird business model where we sell our work for a profit and use those profits to pay people (I told you it was weird! ). We then reinvest those profits into our promising research on AI alignment and our ambitious internal skunkworks projects. We previously sold one of our skunkworks for some number of millions of dollars. We have made a name for ourselves in cutting-edge brain computer interface (BCI) R&D, and we are making a name for ourselves in our research and policy efforts on AI alignment. We want to optimize for human agency, if you feel similarly, please apply to support our efforts. Data scientists will have the opportunity between client projects to work on important alignment research, with the opportunity to potentially have your research project prioritized if particularly promising. ABOUT YOU Experience with ML or alignment research You care about AI alignment, and are optimistic in humanity’s capacity to problem solve and safely navigate accelerating AI capabilities and have an awesome future Eager to use AI and automate everything you do Comfortable working in a fast-paced, startup-like environment Proven data science experience–primary or equal contributor, multiple smaller projects, management of stake-holders. Experience working with / building / training deep learning-based computer vision or NLP models or LLMs Lots of experience with Python Experience calling AI models’ APIs via REST or client libraries Growth mindset. Excited about learning new things and solving difficult problems Demonstrated ability to work independently/autonomously Track record for breaking down complex problems into manageable chunks for individual or team execution Full-time availability–but if you have cool side projects that increase human agency, we totally want you to keep working on it. . . we’ll even help! Ability to communicate effectively and work exclusively in English and communicate complex technical concepts clearly to a non-technical audience BONUS POINTS FOR Experience with self-managed data science projects–show us your cool side hustles, especially if they increase human agency! Experienced with software engineering best practices / software development background Agile experience (worked with Kanban, Scrum, Extreme Programming) JavaScript / Node. js experience Experience managing clients and client relationships Experience in startups Being in LA to work in person from our amazing office RECRUITING NOTE There are an increasing number of scammers out there that are taking advantage of our company's name to scam people with fake offers that result in you ultimately giving the scammers money. Please make sure anyone who contacts you is contacting you from the domains ae. studio, agencyenterprise. com, agencyenterprisestudio. com (please make sure that the sender really is from those domains). If you have any questions about any interviews or offers you might have received and seem bogus, please contact recruiting-offers@ae. studio. Equity Working at AE means receiving equity in some of our client projects and in the Skunkworks projects we incubate. The equity granted is priced at a value of $0 when you receive it. If it were otherwise, you’d be forced to pay taxes. But to be clear, there are no free lunches, only the choice of who pays for your sandwich. At AE, the equity you receive is not free to AE. We invest millions annually in Skunkworks projects - paying our employees to build technology for which no client pays a bill. We receive equity from certain clients, which means we accept less in terms of our billable rate. We then pass that equity on to our employees. Sure it’s priced at $0 on day 1 - a call option on a stock trading at $100 with a strike price of $100 is priced at $0 today for tax purposes, but you’d want to receive that option. If you received that option on a dozen stocks, you’d be fairly confident that some of them would be worth a lot more than $0. Offering this equity (by investing in Skunkworks or offering clients a lower rate) comes out of AE’s margins. Theoretically, we should be lowering salaries to compensate. But we’re competitive on that front as well. Over the long-term, we believe AE employees who stick around are likely to fare extremely well monetarily (no legal promises). We think long-term. You should too when you choose your employer. We are looking to hire people who think long term. People create exponentially more value here in their 3rd or 4th year rather than their 1st or 2nd, and that only increases. The value you create leads to more equity issued every year, and you retain it while you remain at AE or at a Skunkworks co that spins out, in order to free up equity for others, which is how we afford to be generous with the equity. * Disclaimer: If you leave AE on good terms, you can retain 20% of your equity if you continue to send leads our way, share our core values, etc. If you leave AE for your own AE-incubated Skunkworks project (or someone else’s), you retain 100% of your equity. Folks create exponentially more value here in their 3rd and 4th years than their 1st or 2nd - we want folks who think long-term like we do.", "ctc": null, "currency": null, "meta": {}}]