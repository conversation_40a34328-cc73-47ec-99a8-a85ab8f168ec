[{"jd_link": "https://www.altexsoft.com/vacancy/associate-director-of-travel-technology", "company_id": 3379, "source": 3, "skills": "", "title": "Associate Director of Travel Technology", "location": "Remote, Europe, North America", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://www.altexsoft.com/vacancy/associate-director-of-travel-technology", "description": "ShareAbout AltexSoftAltexSoft is a fast-growing software engineering and consulting company that specializes in providing innovative solutions to our clients. We are seeking an experienced Associate Director of Travel Technology to work closely with the Managing Director of Travel Technology, playing a critical role in advancing AltexSoft's growth and presence within the Travel IT industry market. About ProjectThis role is pivotal in enhancing our industry impact and delivering strategic, client-focused solutions across the travel technology sector. This position offers a dynamic opportunity for a driven professional to play a significant role in AltexSoft's growth and to shape our success and market leadership, especially in the U. S. travel technology sector. Location - USA or Western/Central EuropeYou Have 5+ years of experience in the Travel IT industry, with extensive knowledge of the U. S. and European markets. An established network within the U. S. travel technology community and demonstrated ability to cultivate industry connections. Proven success in business development, client relations, and strategic consulting within the travel sector. Strong operational and financial acumen with a track record of driving project profitability. Exceptional leadership, communication, and interpersonal skills. You Are Going To Actively drive new client acquisition and lead presales activities to align solutions with client needs, fostering successful project initiations. Manage and lead upselling initiatives with existing clients, identifying opportunities to expand service offerings and strengthen AltexSoft’s market footprint. Bring deep expertise in the travel technology market to shape AltexSoft’s strategy and expand opportunities for growth. Develop and strengthen AltexSoft’s connections within the travel technology community, expanding our influence through valuable partnerships and insights. Collaborate in building and maintaining strong client relationships within travel industry, aligning solutions with client needs for sustainable growth. Support strategic initiatives to increase client satisfaction and market reach, advocating AltexSoft’s consulting and technology services. Partner with the Managing Director to ensure the operational and financial success of travel technology projects, overseeing efficient project execution and adherence to financial targets. Play an active role in financial planning, forecasting, and reporting to drive the travel technology portfolio’s profitability and success. Act as an active member of AltexSoft’s Center of Travel Technology Excellence, facilitating knowledge sharing, supporting team development, and embedding best practices across teams. Serve as a mentor and guide for emerging leaders within the travel technology group, nurturing expertise and promoting an innovative culture. We offerWork-life Balance Possibility to work remotely Health Care Reimbursement of medical expenses Online morning exercise Education Compensation for trainings, seminars, conferences Free access to the Pluralsight and ACloudGuru knowledge base Access to the AltexSoft library with top-notch materials A mentor for a probation period Engagement in our Mentorship Hub program as a mentor or a mentee to foster professional growth and development Development Horizontally — master new technologies at internal courses Vertically — choose your own career path through Competency trees Recognition Program All your activities are marked by points that can be exchanged for gifts to fit any taste.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.altexsoft.com/vacancy/engineering-manager", "company_id": 3379, "source": 3, "skills": "", "title": "Engineering Manager (Poland)", "location": "Poland", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.altexsoft.com/vacancy/engineering-manager", "description": "ShareAbout AltexSoftAs a hands-on Engineering Manager, you will lead and motivate a team of skilled engineers to develop cutting-edge SaaS and cloud-based solutions for the airline and travel industry. Drawing on your expertise in software development and leadership, you will oversee technical projects, drive innovation, and ensure the successful delivery of high-quality products that adhere to industry standards such as IATA’s New Distribution Capability (NDC). Your role will be instrumental in advancing modern airline retailing, delivering solutions that maximize efficiency and flexibility, free from the limitations of legacy systems. About ProjectThe client is an IATA-certified, global, travel content aggregation and distribution technology company. You HaveIndustry Knowledge Prior experience in the travel domain: the airline or hospitality industry, with knowledge of distribution, pricing, sales, and servicing. Understanding of IATA’s New Distribution Capability (NDC) and other relevant standards. Technical Expertise Strong experience in software development, with proficiency in languages such as Java, Python, or similar backend languages. Deep knowledge of SaaS and cloud technologies, with hands-on experience in AWS, Azure, or Google Cloud. Familiarity with modern software development methodologies, including Agile and Scrum. Expertise in microservices architecture, APIs, and web services. Bachelor’s or Master’s degree in Computer Science, Engineering, or a related field. Leadership & Management Proven track record of leading and managing engineering teams. Strong ability to mentor, coach, and develop talent. Experience in hiring, performance evaluation, and career development. Communication & Collaboration Excellent verbal and written communication skills. Ability to work effectively with cross-functional teams and stakeholders. Fluent in English (spoken and written) Would be a plusIndustry Knowledge Prior experience in the airline or travel industry, with knowledge of distribution, pricing, sales, and servicing. Understanding of IATA’s New Distribution Capability (NDC) and other relevant standards. You Are Going To Define and execute the technical vision, architecture, and implementation of projects aligned with IATA standards such as NDC, ARM, One-Order, and IFG. Collaborate with product management, design, and sales teams to shape and execute product roadmaps. Mentor and support the professional growth of team members, fostering an environment of innovation, teamwork, and continuous learning. Oversee the entire software development lifecycle, ensuring timely delivery and adherence to high-quality standards. Implement best practices in coding, testing, and deployment to ensure scalability, security, and reliability. Identify and solve complex technical challenges, offering innovative solutions. Stay updated on industry trends and emerging technologies, leveraging them to drive innovation within the team. Effectively manage project timelines, deliverables, and resource allocation. Promote a culture of high performance, accountability, and continuous improvement. We offerHealth Care Reimbursement of medical expenses Online morning exercise Education Compensation for trainings, seminars, conferences Free access to the Pluralsight and ACloudGuru knowledge base Access to the AltexSoft library with top-notch materials A mentor for a probation period Engagement in our Mentorship Hub program as a mentor or a mentee to foster professional growth and development Development Horizontally — master new technologies at internal courses Vertically — choose your own career path through Competency trees Recognition Program All your activities are marked by points that can be exchanged for gifts to fit any taste.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.altexsoft.com/vacancy/account-manager", "company_id": 3379, "source": 3, "skills": "", "title": "Account Manager", "location": "Remote, Europe", "location_type": "remote", "job_type": "full_time", "min_experience": 2, "max_experience": null, "apply_link": "https://www.altexsoft.com/vacancy/account-manager", "description": "ShareAbout AltexSoftWe are looking for a skilled Account Manager with a proven track record of successful business developmentAbout ProjectMission: Own and grow a portfolio of mid-size and enterprise clients, acting as the single point of accountability for relationship health, revenue expansion, and strategic alignment between the client and AltexSoft. You Have 5+ years in account management/client success for software outsourcing or technology consulting Solid understanding of different SDLC, delivery metrics, and typical outsourcing commercial models (T&M, managed team, fixed-scope) Proven upsell/cross-sell/etc. track record Practical experience in contract and deal negotiations, P&L ownership Deep knowledge and understanding of the pre-sales process for enterprise-level clients Excellent communication skills: executive-level presentation, status, and negotiation Ability to read delivery and business dashboards, spot trends, and turn insights into commercial actions Understanding of financial metrics and the ability to model business cases Would be a plus 2+ years handling enterprise-scale accounts Knowledge of the travel technology ecosystem Experience managing distributed, multi-vendor programs Additional languages besides fluent English You Are Going To1. Relationship Management:- Build and maintain C-level and stakeholder trust- Expand relations inside the client’s organization- Conduct regular QBRs, roadmap reviews, and satisfaction checks. Keep high level of satisfaction. 2. Growth & Revenue - Identify and close upsell/cross-sell opportunities (new teams, services, geographies):- Extend our presence in the client’s departments and units- Forecast and achieve account-level revenue and margin targets. 3. Delivery Alignment:- Stay continuously informed of project status, risks, and KPIs; intervene early if delivery jeopardizes business goals- Partner with Project/Program Managers to translate delivery insights into commercial actions (scope change, new phases, etc. ). - Support and drive the delivery team according to the business goals. 4. Commercial Governance:- Negotiate MSAs, SOWs, and change-orders- Manage invoicing accuracy, collections, and gross-margin health. 5. Account Planning & Reporting:- Maintain account plans, opportunity pipelines, revenue, and gross margin forecasts- Provide a concise status to the Delivery Director 6. Voice of the Customer:- Channel client feedback into product-/service-line leaders for continuous improvement. 7. Client Strategy & Business Consultancy: - Act as a strategic advisor to clients, aligning technology initiatives with their business goals. - Facilitate ideation and innovation workshops or contribute to clients' digital transformation agendas. - Collaborate with Solution Designers to proactively propose new initiatives. We offerWork-life Balance Possibility to work remotely Health Care Reimbursement of medical expenses Online morning exercise Education Compensation for trainings, seminars, conferences Free access to the Pluralsight and ACloudGuru knowledge base Access to the AltexSoft library with top-notch materials A mentor for a probation period Engagement in our Mentorship Hub program as a mentor or a mentee to foster professional growth and development Development Horizontally — master new technologies at internal courses Vertically — choose your own career path through Competency trees Recognition Program All your activities are marked by points that can be exchanged for gifts to fit any taste.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.altexsoft.com/vacancy/middle-devops", "company_id": 3379, "source": 3, "skills": "", "title": "Middle DevOps", "location": "Remote, Georgia, Poland, Portugal, Nigeria", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://www.altexsoft.com/vacancy/middle-devops", "description": "ShareAbout AltexSoftWe are looking for a Senior DevOps Engineer. You will be part of the team developing a new solution for the client - a big name in the travel industry. The role is focused around setting up project infrastructure in AWS environment using IaC, configuring necessary pipelines, setting up monitoring and everything else related to the product developed. About ProjectThe project is related to development of a new hotel-related module that needs to be integrated into the customer's existing pipeline of hotel distributionYou Have 5+ years working in Software Development/Information Technology Familiarity with Agile development methodologies and the DevOps cycle. Experience working with Linux-based infrastructure. Good fundamental understanding of networking. Hands-on experience with Terraform/Terragrunt or an alternative IaC. Experience using observability tooling such as Datadog. Experience working with microservice architectures. Excellent knowledge of AWS services, with practical experience. Proven record of troubleshooting complex technical issues. Excellent verbal and written communication skills, with the ability to explain complexity using diagrams and models. Would be a plus Azure and GCP practical experience and knowledge. AWS DevOps/Solution Architect certification. GDPR, PCI DSS, PII Data Protection experience. Experience developing software in Java or other high-level object-oriented languages. You Are Going To Work alongside Solution Architects and Developers to deliver complex and exciting solutions. Update and deploy solutions through IaC tooling (Terraform and Terragrunt) Maintenance and extension of CI/CD platform (GitHub Actions, ArgoCD) Design solutions that provide high availability, resiliency, and compliance with regulatory standards. Provide specialised advice and support to team members and developers around cloud infrastructure and container/K8s best practise. Provide operational support and investigate system issues to ensure uptime of solutions. Conduct root-cause analysis on system issues to ensure we can avoid similar incidents in future. Proactively alert stakeholders when an outage occurs. Track performance of systems against KPIs and metrics. Perform code reviews on pull requests raised by other team members, offering guidance and support to develop their skillsets and the quality of the output. Keep learning by staying up to date with technology and industry developments. Mentor junior members of the team. Share your knowledge through mentoring & contribute to continuous improvement of our methodology, knowledge base, and tools We offerWork-life Balance Possibility to work remotely Health Care Reimbursement of medical expenses Online morning exercise Education Compensation for trainings, seminars, conferences Free access to the Pluralsight and ACloudGuru knowledge base Access to the AltexSoft library with top-notch materials A mentor for a probation period Engagement in our Mentorship Hub program as a mentor or a mentee to foster professional growth and development Development Horizontally — master new technologies at internal courses Vertically — choose your own career path through Competency trees Recognition Program All your activities are marked by points that can be exchanged for gifts to fit any taste.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.altexsoft.com/vacancy/middlesenior-java-software-engineer", "company_id": 3379, "source": 3, "skills": "", "title": "Middle/Senior Java Software Engineer", "location": "Nigeria", "location_type": "remote", "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://www.altexsoft.com/vacancy/middlesenior-java-software-engineer", "description": "ShareAbout AltexSoftWe are looking for a Senior Java Developer to join our team. In this position, you’ll collaborate with a team of passionate experts, working within a fully functional development team within our company. Together, you’ll contribute to creating an innovative solution, leveraging each other’s strengths and expertise to achieve shared goals. About ProjectThe project is related to the development of a new hotel-related module that needs to be integrated into the customer's existing pipeline of hotel distribution. You Have 5+ years of Java development experience, including expertise with Spring Boot, JPA (Hibernate/JPA2), JSON, and REST API development and integration Strong knowledge of microservices architecture and cloud-based environments Solid experience with unit testing using JUnit and mocking frameworks like Mockito Strong understanding of relational databases (MySQL, PostgreSQL) and working knowledge of NoSQL solutions (e. g. , DynamoDB) Familiarity with Agile development methodologies (Scrum, Kanban) Experience with containerization tools such as Docker Excellent communication skills, with the ability to convey complex concepts clearly using diagrams and models Proven ability to collaborate effectively in a cross-functional team and engage with stakeholders Ability to design and develop reusable components and services for scalable, maintainable solutions Upper-Intermediate English (spoken and written) Would be a plus AWS Developer Certification Prior experience in the Travel Tech industry Solid grasp of clean code principles Experience with message brokers like RabbitMQ or Kafka You Are Going To Participate in full-cycle software development: architecture design, implementation, testing, and deployment Write clean, maintainable, and efficient code that meets performance and quality standards Collaborate with developers, architects, project managers, and BAs to align development efforts with project goals Provide technical leadership within the team: guide architecture decisions, ensure coding best practices, and support delivery standards Mentor and support Middle-level developers, helping them grow technically and ensuring consistent development quality Conduct code reviews, share feedback constructively, and promote a culture of continuous improvement Engage in regular team discussions to share ideas, address challenges, and propose improvements Apply strong analytical and debugging skills to identify, troubleshoot, and resolve complex issues Maintain comprehensive documentation for application changes and enhancements We offerWork-life Balance Possibility to work remotely Health Care Reimbursement of medical expenses Online morning exercise Education Compensation for trainings, seminars, conferences Free access to the Pluralsight and ACloudGuru knowledge base Access to the AltexSoft library with top-notch materials A mentor for a probation period Engagement in our Mentorship Hub program as a mentor or a mentee to foster professional growth and development Development Horizontally — master new technologies at internal courses Vertically — choose your own career path through Competency trees Recognition Program All your activities are marked by points that can be exchanged for gifts to fit any taste.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.altexsoft.com/vacancy/senior-devops-on-duty-shift-on-weekends-1100---2300-utc-22062", "company_id": 3379, "source": 3, "skills": "", "title": "Senior DevOps (on-duty shift on weekends 11:00 - 23:00 UTC)", "location": "Latin America", "location_type": "hybrid", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://www.altexsoft.com/vacancy/senior-devops-on-duty-shift-on-weekends-1100---2300-utc-22062", "description": "ShareAbout AltexSoftWe are looking for a senior DevOps Engineer to join our diverse weekend support team on a live product. Our team is actively supporting the product on 24/7 basis and we are looking for a staff extension to make the process even more reliable. As a part of our team you will be responsible for ensuring system stable operations during the weekends under the EST time zone. About ProjectThe project is centered on the development of a service that allows for the management and processing of orders and changes, giving customers and agents an efficient, responsive, integrated experience, like booking, holding, and issuing instant tickets, selling ancillary products before or after ticketing, re-issuing individual or multi-flight tickets and processing airline-initiated OCNsYou Have Experience support live production systems, including leveraging problem-solving skills for troubleshooting. Experience with monitoring solutions (Sentry, Graylag, Data Dog, or Splunk). 3+ years of experience provisioning, operating, and managing AWS environments and services such as EC2, VPC, ELB, Lambda, ECS, EKS, and security services. Knowledge on Infra Management with Hybrid environment and cloud security. Would be a plus AWS DevOps/SysOps/developer certifications, as well as those with experience supporting developers, so knowledge and understanding of privacy and HIPAA regulations. Strong experience working with microservice architecture and API knowledge is a nice plus. Travel tech background. You Are Going To Support and monitor infrastructure Participate in on-call duty (important) Operate and maintain production systems Work on infrastructure development Handle and resolve issues Configure and manage monitoring systems We offerRecognition Program All your activities are marked by points that can be exchanged for gifts to fit any taste.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.altexsoft.com/vacancy/senior-devops-engineer-76107", "company_id": 3379, "source": 3, "skills": "", "title": "Senior DevOps Engineer (Poland)", "location": "Poland", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://www.altexsoft.com/vacancy/senior-devops-engineer-76107", "description": "ShareAbout AltexSoftWe are currently seeking for a Senior DevOps Engineer. The successful candidate will be part of the platform and operations team, currently undertaking a migration to Kubernetes. The role is focused around ensuring that system performance is optimised and service availability objectives are met, knowledge is socialised with the wider team, and a code-first approach is taken to deployment of infrastructure. The ideal candidate will have in-depth hands-on experience working within platform and operations teams. About ProjectOur client is a global, travel content aggregation and distribution technology company. You Have Excellent verbal and written communication skills, with the ability to explain complexity using diagrams and models. 5+ years working in Software Development/Information Technology. Familiarity with Agile development methodologies and the DevOps cycle. Experience working with Linux-based infrastructure. Good fundamental understanding of networking. Hands-on experience with Terraform/Terragrunt. Experience using observability tooling such as Datadog. Experience working with microservice architectures. Excellent knowledge of AWS services, with practical experience. Proven record of troubleshooting complex technical issues. Would be a plus AWS DevOps/Solution Architect certification. GDPR, PCI DSS, PII Data Protection experience. Experience developing software in Java or other high-level object-oriented languages. You Are Going To Work alongside Solution Architects and Developers to deliver complex and exciting solutions. Update and deploy solutions through IaC tooling (Terraform and Terragrunt) Maintenance and extension of CI/CD platform (GitHub Actions, ArgoCD) Design solutions that provide high availability, resiliency, and compliance with regulatory standards. Provide specialised advice and support to team members and developers around cloud infrastructure and container/K8s best practise. Provide operational support and investigate system issues to ensure uptime of solutions. Conduct root-cause analysis on system issues to ensure we can avoid similar incidents in future. Proactively alert stakeholders when an outage occurs. Track performance of systems against KPIs and metrics. Perform code reviews on pull requests raised by other team members, offering guidance and support to develop their skillsets and the quality of the output. Keep learning by staying up to date with technology and industry developments. Mentor junior members of the team. Share your knowledge through mentoring & contribute to continuous improvement of our methodology, knowledge base, and tools. We offerWork-life Balance Possibility to work remotely Health Care Reimbursement of medical expenses Online morning exercise Education Compensation for trainings, seminars, conferences Free access to the Pluralsight and ACloudGuru knowledge base Access to the AltexSoft library with top-notch materials A mentor for a probation period Engagement in our Mentorship Hub program as a mentor or a mentee to foster professional growth and development Development Horizontally — master new technologies at internal courses Vertically — choose your own career path through Competency trees Recognition Program All your activities are marked by points that can be exchanged for gifts to fit any taste.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.altexsoft.com/vacancy/middlesenior-python-developer-ai-experience", "company_id": 3379, "source": 3, "skills": "", "title": "Middle/Senior <PERSON> (AI experience)", "location": "Georgia", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://www.altexsoft.com/vacancy/middlesenior-python-developer-ai-experience", "description": "ShareAbout AltexSoftAltexSoft is a fast-growing software engineering and consulting company that specializes in providing innovative solutions to our clients. We are looking for a Middle/Senior Python Developer (AI experience) to join our team. About ProjectWe are developing an AI-powered solution that leverages the latest advancements in machine learning and intelligent automation. You will collaborate with a team of skilled and experienced professionals to implement key components of the system. This role offers the opportunity to contribute to a strategically planned AI product, influence technical decisions, and work with modern, high-impact technologies. You Have 3+ years of experience in software development using Python Experience with RAG models and familiarity with LlamaIndex, Haystack Understanding of AI/ML principles and practical experience with AI tools like LangChain, AutoGen or CrewAI Experience with SQL and NoSQL databases Familiarity with MCP, the ability to structure and manage contextual data for LLM interactions Experience with writing and implementing APIs (e. g. REST API) DevOps experience: FastAPI, Docker, CI/CD, AWS, Azure, etc. You Are Going To Design and develop RAG architectures for AI-driven applications using Python Build and maintain AI Agents capable of autonomous reasoning and task execution Integrate models into applications and optimize performance Collaborate with team members in an agile environment, participating in planning, code reviews, and stand-ups. We offerWork-life Balance Possibility to work remotely Health Care Reimbursement of medical expenses Online morning exercise Education Compensation for trainings, seminars, conferences Free access to the Pluralsight and ACloudGuru knowledge base Access to the AltexSoft library with top-notch materials A mentor for a probation period Engagement in our Mentorship Hub program as a mentor or a mentee to foster professional growth and development Development Horizontally — master new technologies at internal courses Vertically — choose your own career path through Competency trees Recognition Program All your activities are marked by points that can be exchanged for gifts to fit any taste.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.altexsoft.com/vacancy/senior-fullstack-engineer-phpvue3-vilt", "company_id": 3379, "source": 3, "skills": "", "title": "Senior Fullstack Engineer PHP/Vue3 (VILT)", "location": "Georgia, Poland, Portugal, Remote", "location_type": "remote", "job_type": null, "min_experience": 25, "max_experience": 25, "apply_link": "https://www.altexsoft.com/vacancy/senior-fullstack-engineer-phpvue3-vilt", "description": "ShareAbout AltexSoftWe are seeking an experienced Senior Full Stack Engineer to join our team and play a pivotal role in development of travel project using Vue 3, PHP 8 and Laravel (VILT stack). About ProjectThe product has been in continuous development for over 25 years and includes a well-established codebase. While the focus of the role will be on developing new features, occasional interaction with legacy components may be required. These interactions are expected to be minimal and will be supported by the Client’s experienced development team to ensure a smooth integration process. You Have 5+ Solid experience with PHP 8 and Laravel 10, including building and maintaining robust, scalable back-end systems. Extensive experience with Vue 3, including a deep understanding of its ecosystem and best practices for building responsive and dynamic user interfaces. Strong skills in using Tailwind CSS and Tailwind UI for building efficient, maintainable, and visually appealing designs. In-depth knowledge of the VILT stack (Vue, Inertia, Laravel, Tailwind) is critical for building full-stack applications with seamless integration between front-end and back-end. Proficiency in Inertia. js to create single-page applications (SPAs) that integrate efficiently with Laravel. Experience working with Typescript for type-safe JavaScript development, ensuring maintainable and scalable front-end applications. Strong understanding of Docker for containerizing applications, ensuring consistent development environments and smooth deployment workflows. Database Management: Proficiency in MariaDB/MySQL, including the design, management, and optimization of databases for high performance and reliability. Extensive experience with GitLab for version control, managing repositories, and integrating CI/CD pipelines. Proven skills in designing, debugging, and optimising applications to ensure high reliability and performance across the entire stack Would be a plus AG Grid Inertia. js Jira/Confluence You Are Going To Develop an updated UI and integrate it with the backend using Tailwind UI library, PHP8, tables based on AG Grid and Design System components Thrive in an Agile / Scrum / Kanban environment, while actively participating in brainstorming sessions, contributing valuable insights to technology, algorithm, and product discussions, and enriching team dynamics through proactive involvement in scoping , grooming, and daily standups. Collaborate with UI/UX designers in transforming their visions from Figma blueprints into sleek Vue 3 component-based user-facing applications. Redesign existing code to improve quality, meet new standards, and implement fresh features while improving existing features and patterns. Employ your deep knowledge of Vue 3, Tailwind CSS, Tailwind UI, TypeScript and Design System to boost our platform’s development, scalability, and maintenance. Utilise and develop your expertise in the travel industry to enhance and expand customers' current operations. Adhere to top-tier software quality standards by creating high-quality, well-tested code and conducting comprehensive code reviews. We offerWork-life Balance Possibility to work remotely Health Care Reimbursement of medical expenses Online morning exercise Education Compensation for trainings, seminars, conferences Free access to the Pluralsight and ACloudGuru knowledge base Access to the AltexSoft library with top-notch materials A mentor for a probation period Engagement in our Mentorship Hub program as a mentor or a mentee to foster professional growth and development Development Horizontally — master new technologies at internal courses Vertically — choose your own career path through Competency trees Recognition Program All your activities are marked by points that can be exchanged for gifts to fit any taste.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.altexsoft.com/vacancy/senior-project-manager-33999", "company_id": 3379, "source": 3, "skills": "", "title": "Senior Project Manager", "location": "Europe", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": 5, "apply_link": "https://www.altexsoft.com/vacancy/senior-project-manager-33999", "description": "ShareAbout AltexSoftWe are looking for a Senior Project Manager willing to drive new challenging projects from scratch. As a Senior PM, you will be fully accountable for one or several projects. You will work closely with the Delivery Managers, PMO, and Pre-Sale team. The ideal candidate should have excellent leadership, communication, and organizational abilities to manage complex technical projects from initiation through delivery. You will be responsible for assembling and leading cross-functional teams, defining project roadmaps, tracking progress, managing risks, and ensuring projects are delivered on time and within scope. About ProjectThe client operates on a highly complex, legacy software system originally designed over two decades ago. The platform consists of approximately 48 microservices and has not undergone re-platforming since its inception. As a result, numerous workarounds and ad-hoc fixes have accumulated over the years, leading to performance degradation and measurable impact on business operations. You Have• Up to 5 years of commercial experience in a PM role • Strong knowledge of project management theory • Experience in managing distributed teams • Experience in effective planning, execution, and delivery • Ability to convert product features into business value and vice versa • Ability to prioritize tasks and understand stakeholder needs • Eagerness to learn technical aspects of the project • Great problem-solving and diplomacy skills • Excellent communication, negotiation, and client relationship management skills • Ability to build strong relationships with members of development teams • Ability to build trusted relationships with the client • Ability to manage by KPI (develop and cascade, bring it to your team) • English level Upper-Intermediate or higher. Would be a plus• Ability to coach and mentor PMs • Technical background • Experience in Pre-SalesYou Are Going To• Start and manage projects according to AltexSoft and industry best practices • Identify client’s needs and continuously improve the level of provided services • Establish SDLC on new projects from scratch • Build effective and friendly relations inside the team and with other departments • Build a trustful and strong relationship with the client • Acting as the first point of escalation for clients • Leveraging between client and team to ensure business value and expectations are clear and the delivered solution meets them • Develop new business opportunities with existing clients by promoting company products and services (Up-sale/cross-sale) • Ensure the delivery happens on time and within budget • Final decision about team composition acceptance • Increase client and team satisfaction • Collaborate with top management to improve project processesWe offerWork-life Balance Possibility to work remotely Health Care Reimbursement of medical expenses Online morning exercise Education Compensation for trainings, seminars, conferences Free access to the Pluralsight and ACloudGuru knowledge base Access to the AltexSoft library with top-notch materials A mentor for a probation period Engagement in our Mentorship Hub program as a mentor or a mentee to foster professional growth and development Development Horizontally — master new technologies at internal courses Vertically — choose your own career path through Competency trees Recognition Program All your activities are marked by points that can be exchanged for gifts to fit any taste.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.altexsoft.com/vacancy/solution-architect", "company_id": 3379, "source": 3, "skills": "", "title": "Solution Architect", "location": "Poland", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://www.altexsoft.com/vacancy/solution-architect", "description": "ShareAbout AltexSoftWe are currently seeking a Solution Architect. The role will involve and lead the architecture and technical design of the whole product range. With a global presence and a rapidly growing team, our client is now expanding operations to Warsaw, Poland. Join them as they build a new office from the ground up and bring together a team of top talent to drive their mission forward! About ProjectOur client is a leading travel technology company revolutionizing airline retailing and distribution. As an IATA-certified technology provider, they empower airlines and travel agencies with cutting-edge New Distribution Capability (NDC), enabling seamless booking experiences and dynamic personalization. Their innovative platform bridges the gap between airlines, travel sellers, and customers, ensuring efficiency, transparency, and enhanced profitability for all stakeholders. You Have Excellent verbal and written communication skills, with the ability to explain complexity using diagrams and models. Exceptional interpersonal skills with both internal stakeholders and within the team. Familiarity with Agile methodologies, and how architecture can fit with them. Experience with various architectural approaches including microservices, service-oriented architectures, and event-driven systems. Experience with cloud technologies, especially AWS. Excellent knowledge of both Relational Databases (MySQL), and non-relational / NoSQL datastores and the trade-offs between them. Ability to work with engineers and guide them on how to correctly implement designs. Experience in the travel domain. You Are Going To Participate in and lead meetings with internal technical and product staff, vendors, and customers to design solutions to fit requirements. Develop recommendations for technical choices and implementation approaches. Embrace an open-minded, team-player approach, being receptive to feedback and offering suggestions. Collaborate with technical leads and senior engineers, DevOps/platform engineers, and others to help them produce lower-level technical designs. Participate in regular planning sessions for technology and each product vertical to help ensure work is sufficiently understood to begin. Work with other architects and leadership to ensure alignment on technical vision and business objectives We offerWork-life Balance Possibility to work remotely Health Care Reimbursement of medical expenses Online morning exercise Education Compensation for trainings, seminars, conferences Free access to the Pluralsight and ACloudGuru knowledge base Access to the AltexSoft library with top-notch materials A mentor for a probation period Engagement in our Mentorship Hub program as a mentor or a mentee to foster professional growth and development Development Horizontally — master new technologies at internal courses Vertically — choose your own career path through Competency trees Recognition Program All your activities are marked by points that can be exchanged for gifts to fit any taste.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.altexsoft.com/vacancy/solution-architect-83344", "company_id": 3379, "source": 3, "skills": "", "title": "Solution Architect (.Net)", "location": "Georgia, Poland, Portugal", "location_type": "remote", "job_type": "full_time", "min_experience": 3, "max_experience": null, "apply_link": "https://www.altexsoft.com/vacancy/solution-architect-83344", "description": "ShareAbout AltexSoftWe are looking for a Solution Architect with deep expertise in software architecture and strong hands-on technical capabilities to lead the design and delivery of secure, scalable, and high-performing systems. In this role, you will define architectural blueprints, provide technical leadership to development teams, and ensure that solutions align with both business objectives and modern technology standards. You will be responsible for setting the technical direction, overseeing implementation, and driving architectural consistency across projects. A high level of proficiency with the Microsoft technology stack is essential, along with extensive experience designing cloud solutions on both Azure and AWS. You will work closely with engineering teams and engage directly with executive stakeholders, including the CTO and other C-level leaders, to shape technology strategy and guide key architectural decisions. About ProjectA cutting-edge internet filtering platform engineered to deliver highly customizable and secure browsing experiences across Windows and Android devices. The solution serves individuals, families, businesses, and communities seeking fine-grained content control and robust online safety. The system comprises multiple integrated components that work together to provide high-quality filtering capabilities while offering exceptional flexibility in configuration. At the core of the platform is a suite of web-based portals that support user management, filtering settings, and administrative controls. These portals are continuously enhanced and extended to ensure a unique, user-centric filtering experience without compromising browsing security or performance. You Have 10+ years of experience in software development, including 3+ years in a senior solution architecture or enterprise architecture role. Strong expertise in the Microsoft technology stack (. NET Core, ASP. NET (MVC/Web API), C#, MSSQL, Azure SQL, ServiceBus). Extensive hands-on experience with both Azure and AWS, including key services (Azure App Services, Azure Functions, Service Bus, Azure SQL, AWS Lambda, API Gateway, EC2, S3, RDS, IAM). Vast experience designing distributed systems, microservices, and secure APIs (OAuth2, OpenID Connect). High proficiency in Infrastructure as Code (Terraform, Bicep, etc. ) and CI/CD pipelines (Azure DevOps, GitHub Actions, etc. ). Strong knowledge of Docker and Kubernetes in production environments. Deep knowledge of event-driven architecture, DDD, and messaging patterns. Solid experience in architectural modeling and design methodologies (UML, C4, etc. ). Wide-ranging experience with performance tuning and observability (logging, metrics, tracing). Exposure to regulated environments and security compliance. Well-rounded knowledge of Agile, Scrum, and DevOps delivery models. Excellent written and verbal communication skills, including the ability to engage effectively with C-level executives, business stakeholders, and technical teams. Bachelor’s or Master’s degree in Computer Science, Software Engineering, or a related field. Ukrainian language - native Would be a plus Understanding of enterprise architecture frameworks (e. g. , TOGAF). Knowledge of Angular (v12+), TypeScript. Familiarity with data architecture, including data lakes, pipelines, and real-time data processing. Deep knowledge of PostgreSQL and Windows Services. You Are Going To Drive technical discussions and provide leadership to development teams throughout the project lifecycle, focusing on system architecture. Design and document enterprise-grade solution architectures using Azure, AWS, . NET Core, ASP. NET, and related Microsoft technologies. Architect systems for hybrid, cloud-native, or cloud-migration scenarios using both Azure and AWS services. Ensure solutions meet business and technical goals while adhering to security, scalability, maintainability, and performance best practices. Define and govern architectural patterns, coding standards, and technical processes for engineering teams. Act as a technical liaison between business stakeholders, developers, QA, and DevOps. Engage in early-stage solution discovery, translating complex business problems into clear technical strategies. Provide mentorship to technical leads and engineers, and lead architectural reviews and design sessions. Drive proof-of-concepts (PoCs) to validate technologies and architectural approaches before implementation. Support security, compliance, and risk management activities related to application and infrastructure architecture. Complete estimation and scope decomposition. Cooperate with other stakeholders and actively contribute to design, decision-making, and implementation. Collaborate with a project manager to define project scope, timelines, and deliverables. Engage with business stakeholders to gather requirements and provide regular updates on project status. Communicate complex technical concepts to non-technical stakeholders in an accessible manner. We offerWork-life Balance Possibility to work remotely Health Care Reimbursement of medical expenses Online morning exercise Education Compensation for trainings, seminars, conferences Free access to the Pluralsight and ACloudGuru knowledge base Access to the AltexSoft library with top-notch materials A mentor for a probation period Engagement in our Mentorship Hub program as a mentor or a mentee to foster professional growth and development Development Horizontally — master new technologies at internal courses Vertically — choose your own career path through Competency trees Recognition Program All your activities are marked by points that can be exchanged for gifts to fit any taste.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.altexsoft.com/vacancy/solutions-architect-82980", "company_id": 3379, "source": 3, "skills": "", "title": "Solutions Architect", "location": "Remote, Nigeria", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": 3, "apply_link": "https://www.altexsoft.com/vacancy/solutions-architect-82980", "description": "ShareAbout AltexSoftWe’re looking for a Solutions Architect who will join our Architecture Competence Center and help drive technical excellence across presales, discovery, and solution design. This is a highly strategic role for a senior engineer who thrives in fast-paced environments, can communicate technical ideas clearly, and can architect scalable, maintainable systems across domains. We’re especially interested in individuals who embrace modern AI tools and patterns as part of their daily workflows. If you have experience within the Travel Industry, this would be a big advantage. About ProjectWe are a consulting company. Every day we create solutions that solve our customers’ problems. Thanks to this practice, we have real knowledge and proven expertise in Travel Tech, Data Science, UI/UX, Business Analysis, and Software Engineering. Our portfolio includes more than 300 cases that we are proud of. Day by day, we improve to be more productive and help our customers implement business development strategies through technology. If you are focused on working with content, you want to create live products to see the results of your work in action, welcome to our team. You Have Proven experience as a Solutions Architect in software development projects 8+ years in software development, including at least 3 years in an architecture or tech lead role Solid expertise in one of the following backend technologies: Python, Java, . NET, Node. js Practical experience in designing cloud-native architectures (preferably AWS, but Azure/GCP also acceptable) Strong understanding of software architecture patterns: microservices, event-driven architecture, API-first, serverless Hands-on experience working with containers and orchestration tools (Docker, Kubernetes) Deep knowledge of system integration, API design (REST/gRPC), and security best practices Proven ability to participate in and lead presales and discovery phases Strong verbal and written communication skills in English (Upper-Intermediate or higher) Experience mentoring developers and conducting architecture reviews Would be a plus Experience working in or for the Travel Tech industry Familiarity with modern AI implementation patterns and infrastructure (ML pipelines, vector DBs, LLM APIs, etc. ) Ability to create solution PoCs and validate technical feasibility Understanding of DevOps best practices and CI/CD pipelines Knowledge of front-end technologies (React, Vue, Angular) AI-native mindset – using AI tools in daily workflows to improve productivity Solid expertise in two or more of the following backend technologies: Python, Java, . NET, Node. js You Are Going To Participate in presales and discovery phases to define technical solutions and estimate project scope Design scalable and maintainable system architectures across various domains (primarily Travel Tech) Evaluate and choose appropriate tech stacks based on project needs and constraints Collaborate with business analysts, designers, and client stakeholders Communicate architecture decisions clearly to technical and non-technical audiences Define non-functional requirements (scalability, security, performance, etc. ) Guide development teams through implementation and provide technical leadership Ensure architectural consistency and best practices across multiple projects Continuously evaluate emerging technologies, especially in the AI/ML space We offerWork-life Balance Possibility to work remotely Health Care Reimbursement of medical expenses Online morning exercise Education Compensation for trainings, seminars, conferences Free access to the Pluralsight and ACloudGuru knowledge base Access to the AltexSoft library with top-notch materials A mentor for a probation period Engagement in our Mentorship Hub program as a mentor or a mentee to foster professional growth and development Development Horizontally — master new technologies at internal courses Vertically — choose your own career path through Competency trees Recognition Program All your activities are marked by points that can be exchanged for gifts to fit any taste.", "ctc": null, "currency": null, "meta": {}}]