[{"jd_link": "https://www.mobiloitte.com/careers/independent-technology-sales-consultant-london-greater-uk/", "company_id": 3380, "source": 3, "skills": "", "title": "Independent Technology Sales Consultant – London (Greater UK)", "location": "Job Location: Greater UK Remote (WFH)", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://www.mobiloitte.com/careers/independent-technology-sales-consultant-london-greater-uk/", "description": "Mobiloitte is a global digital-engineering house with delivery centers on four continents and a client portfolio spanning enterprise, government, and scale-ups. Our multidisciplinary teams design, build, and manage solutions across AI & GenAI, Blockchain, Web & Mobile Apps, Gaming, IoT, Metaverse, IT Support, and Staff Augmentation. To accelerate our expansion in the UK, we are inviting experienced, London-based professionals to join us as independent consultants/partners (not employees) and champion Mobiloitte’s full suite of services across the region. Core Responsibilities • Map and develop new B2B opportunities across London’s tech, finance, public-sector, and high-growth verticals. • Own the sales cycle complete prospecting, qualification, proposal management, negotiation, and close. • Maintain a robust, transparent pipeline in collaboration with Mobiloitte pre-sales and delivery leads. • Cultivate senior-level relationships and ensure smooth project handoffs and on-going client satisfaction. • Meet or exceed quarterly revenue targets and provide concise funnel and activity reports. Engagement Model • Performance-based commission in GBP, with progressive tiers that increase as volume milestones are hit. • Initial 12-month consultancy agreement, renewable by mutual consent; consultants operate on a self-employed basis and remain responsible for their own IR35 and HMRC compliance. • Structured onboarding, solution playbooks, case studies, demo environments, and direct access to global technical SMEs. • Marketing collateral, local events support, and co-branded lead-gen campaigns supplied. Ideal Profile • A proven hunter: 3–5+ years closing complex technology deals in the UK (average £250k+). • Established network with CIOs, CTOs, and procurement teams in sectors such as fintech, healthtech, govtech, retail, or logistics. • Demonstrated success working as an independent rep, boutique agency principal, or channel partner. • Familiarity with UK data-protection and procurement standards (UK GDPR, CCS frameworks, G-Cloud helpful). • Confident communicator able to navigate multicultural teams and fast-paced deal cycles. Please share a concise overview of your recent wins (deal size, sector), local market connections, and two references. Why Partner with Mobiloitte • End-to-end service breadth lets you tailor truly holistic solutions for clients. • 500+ engineers, ISO-certified processes, and a “follow-the-sun” delivery model ensure execution certainty. • Transparent, uncapped commission plan and rapid payout schedule. • Opportunity to carve out regional leadership as we scale UK operations. Note: (This is a consultancy partnership, not an employment offer. All consultants operate under their own business entity and manage their own tax and regulatory obligations).", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.mobiloitte.com/careers/job-for-mass-email-marketing-executive-5-openings-apply-now/", "company_id": 3380, "source": 3, "skills": "", "title": "Job For Mass Email Marketing Executive (5 Openings) Apply Now", "location": "Job Location: New Delhi", "location_type": "hybrid", "job_type": null, "min_experience": 1, "max_experience": 3, "apply_link": "https://www.mobiloitte.com/careers/job-for-mass-email-marketing-executive-5-openings-apply-now/", "description": "Location: New Delhi (Onsite) / Hybrid Eligible Experience: 1–3 Years | Open Positions: 5 About Mobiloitte: Mobiloitte is a leading full-stack digital transformation company, delivering high-impact solutions across domains such as Mobility, Web, Blockchain, IoT, and AI. With a presence in India and globally, we believe in data-driven performance marketing backed by cutting-edge technology and agile strategies. Role Overview: We are expanding our digital marketing team and seeking 5 driven and detail-oriented Mass Email Marketing Executives to execute targeted email campaigns across industries and regions. This role is pivotal in building brand awareness, generating leads, and optimizing outreach funnels through structured email automation. Key Responsibilities: Design, build, and send high-volume mass email campaigns using platforms like Mailchimp, Sendinblue, or Zoho Campaigns. Manage and segment contact lists for better targeting and performance. Ensure deliverability, track performance metrics (open/click rates), and optimize subject lines and content for engagement. Conduct A/B testing of emails and landing page links. Coordinate with sales and content teams to align campaigns with strategic goals. Stay updated on email marketing trends, tools, and compliance (CAN-SPAM, GDPR). To Apply: Fill Registration Form NOW Required Skills & Experience: 1–3 years of proven experience in mass email campaign execution. The role requires proficiency with tools such as Mailchimp, SendGrid, Zoho Campaigns, or similar platforms. The role requires familiarity with basic HTML/CSS for email formatting. You must possess a robust analytical approach to decipher campaign data and formulate practical suggestions. A proactive attitude, attention to detail, and creativity in messaging are needed. Good to Have: Experience in B2B IT services email marketing. Understanding of CRM integrations and lead generation workflows. Prior work with international client campaigns (USA, UK, UAE, etc. ). What We Offer: The role operates in a collaborative and high-growth environment, where KPIs are structured. The role provides the opportunity to work on global campaigns with enterprise clients. The role offers a competitive salary, along with incentives based on performance metrics. Opportunities for career advancement exist in the digital and performance marketing sector.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.mobiloitte.com/careers/job-for-independent-technology-sales-consultant-singapore/", "company_id": 3380, "source": 3, "skills": "", "title": "Job For Independent Technology Sales Consultant – Singapore", "location": "Job Location: Remote (WFH) Singapore", "location_type": null, "job_type": "full_time", "min_experience": 3, "max_experience": null, "apply_link": "https://www.mobiloitte.com/careers/job-for-independent-technology-sales-consultant-singapore/", "description": "Mobiloitte, a global digital-engineering powerhouse active across India, the USA, the UK, the UAE, South Africa, and Singapore, is expanding its consultant network to accelerate growth in Southeast Asia. We build and manage advanced solutions in AI & GenAI, Blockchain, Web & Mobile Apps, Gaming, IoT, Metaverse, IT Support, and Staff Augmentation for enterprises and government agencies alike. We are inviting seasoned, Singapore-based independent consultants to represent Mobiloitte’s full suite of offerings on a partnership (non-employment) basis. Key Responsibilities Identify and cultivate new B2B opportunities in Singapore and wider ASEAN markets. Maintain a healthy sales pipeline: prospecting, qualification, proposal coordination, and closure. Build long-term client relationships, aligning Mobiloitte’s solutions to business goals and local compliance requirements (e. g. , PDPA, IMDA guidelines). Meet agreed quarterly revenue targets and provide regular funnel reports. Liaise with Mobiloitte’s global delivery teams for seamless onboarding and project hand-over. Engagement Model Commission-only structure with tiered percentages starting at a competitive base and rising with volume; payouts in SGD. 12-month renewable consultancy agreement with clear milestones. Comprehensive onboarding: solution briefings, pitch decks, case studies, and dedicated pre-sales support. Marketing collateral, demo environments, and access to senior technical experts supplied. What We’re Looking For Proven track record selling complex technology solutions (3+ years preferred) in Singapore or ASEAN. Strong C-level network across sectors such as fintech, healthcare, logistics, public-sector, or emerging tech. Demonstrated success closing mid- to large-ticket deals (SGD 250k +). Comfort navigating multicultural environments and compliance-driven procurement processes (GovTech, MAS, ACRA-registered entities). Self-starter mindset with excellent communication and negotiation skills.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.mobiloitte.com/careers/job-for-senior-ios-consultant-swift-multiple-projects-work-from-home/", "company_id": 3380, "source": 3, "skills": "", "title": "Job for Senior iOS Consultant – <PERSON> (Multiple Projects | Work From Home)", "location": "Job Location: Remote (WFH)", "location_type": "remote", "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://www.mobiloitte.com/careers/job-for-senior-ios-consultant-swift-multiple-projects-work-from-home/", "description": "Experience: 5+ Years (iOS-Swift) About Mobiloitte Mobiloitte is a global technology and innovation partner operating across India, UAE, Singapore, UK, USA, and South Africa. Known for delivering world-class apps, AI-powered systems, and smart automation, we are scaling high-impact mobile projects and looking for seasoned iOS experts to strengthen our consultancy pool. Role Overview We are hiring a Senior iOS Consultant (Swift) to work across multiple fast-paced, enterprise-grade mobile projects. This is a consulting role that demands end-to-end ownership, quick prototyping skills, and real-world deployment experience. Ideal for a technically mature iOS developer who thrives in dynamic environments. Key Responsibilities Architect, develop, and optimize iOS applications using Swift. Work collaboratively with cross-functional teams including UI/UX, API, QA, and DevOps. Translate product requirements into clean, maintainable, and scalable code. Own delivery across multiple projects simultaneously — ensuring deadlines, quality, and performance. Conduct regular code reviews and mentoring for in-house junior developers (if engaged). Troubleshoot and debug production issues, ensuring compliance with App Store guidelines. Must-Have Skills 5+ years of experience in native iOS development using Swift. Strong grasp of MVC, MVVM, and Clean Architecture patterns. Proficient with UIKit, CoreData, CoreAnimation, Push Notifications, and RESTful APIs. Hands-on experience with App Store deployment, provisioning profiles, certificates. Familiarity with third-party libraries (e. g. , Alamofire, Realm, Firebase). Clear understanding of mobile performance tuning and UI responsiveness. Excellent problem-solving and independent decision-making abilities. Bonus (Preferred but Not Mandatory) Experience with SwiftUI, Combine framework, and real-time data handling. Familiarity with TestFlight, CI/CD tools like Bitrise, Jenkins, or GitHub Actions. Past work with modular codebases, white-label apps, or multi-client app structures. Exposure to Figma-to-code handoff and accessibility standards. Engagement & Benefits 100% Remote | Flexible Hours (Project-based milestone delivery) Multi-project pipeline ensures continuous opportunities Competitive consulting compensation (Hourly/Retainer Model) Opportunity for longer-term strategic tech consulting roles Exposure to enterprise-grade and global product environments How to Apply Are you prepared to apply your expertise in iOS to numerous active global projects? Complete Registration Form: Share your resume and project portfolio to [careers@mobiloitte. com] Apply directly at www. mobiloitte. com/careers", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.mobiloitte.com/careers/job-for-seo-aeo-voice-search-expert-manager/", "company_id": 3380, "source": 3, "skills": "", "title": "Job For SEO & AEO / Voice Search Expert Manager", "location": "Job Location: New Delhi Remote (WFH)", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": 5, "apply_link": "https://www.mobiloitte.com/careers/job-for-seo-aeo-voice-search-expert-manager/", "description": "Experience: 5+ Years About Mobiloitte Mobiloitte is a full-service digital solutions company focused on innovation, agility, and precision. With operations across India, UAE, Singapore, UK, USA, and South Africa, we build future-ready tech solutions for a global clientele. We’re now seeking an SEO & Answer Engine Optimization/Voice Search Expert Manager to elevate our digital visibility, drive qualified leads, and stay ahead of evolving search behavior trends. Key Responsibilities Develop and execute advanced SEO strategies including technical, on-page, off-page, and local SEO tailored for lead generation. Lead initiatives for Answer Engine Optimization (AEO) to capture visibility in rich snippets, People Also Ask (PAA), and zero-click searches. Design and implement Voice Search Optimization strategies (Google Assistant, Alexa, Siri) aligned with conversational keyword targeting. Conduct in-depth keyword research, competitive analysis, and content audits using tools such as SEMrush, Ahrefs, Moz, and Google Search Console. Monitor, analyze, and report KPIs: organic traffic, keyword rankings, bounce rate, dwell time, and conversions. Integrate SEO with marketing automation platforms and CRM systems to drive quality lead nurturing and funnel optimization. Collaborate with content, development, and UI/UX teams to ensure SEO best practices are integrated across all channels. Stay updated with Google Algorithm updates, Core Web Vitals, and AI-powered search behaviors like SGE (Search Generative Experience). <PERSON><PERSON> and lead a small team of SEO executives to achieve weekly/monthly lead generation targets. Required Skills & Tools Expertise Proven expertise in SEO tools: SEMrush, Ahrefs, Moz Pro, Screaming Frog, Google Analytics, Google Data Studio, Search Console, Surfer SEO. Experience with schema markup, featured snippets optimization, and structured data implementation. Familiarity with platforms like WordPress, Shopify, and HTML/CSS basics for technical audits. Voice Search Optimization techniques using NLP-based keyword mapping. Integration of CRM and marketing platforms includes HubSpot, Zoho CRM, Salesforce, and Mailchimp (optional but preferred). Lead Generation Focus Strong understanding of SEO as a lead generation engine— from TOFU content to CRO on landing pages. Demonstrated ability to generate and track organic marketing-qualified leads (MQLs). Exposure to B2B/B2C funnel analytics and campaign ROI measurement through SEO. What We’re Looking For We are seeking a minimum of 5 years of specialized experience in SEO, AEO, and voice search strategies. The ideal candidate should possess strong project management and stakeholder communication skills. The candidate should possess the ability to function independently and take a high level of strategic ownership. Compensation & Perks The salary is competitive and depends on your experience. WFH flexibility or a dynamic workspace at our New Delhi campus. We offer bonuses and incentives based on performance. Work with a tech-driven, globally distributed team.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.mobiloitte.com/careers/job-for-senior-wordpress-developer-wfh-consultant-mode/", "company_id": 3380, "source": 3, "skills": "", "title": "Job For Senior WordPress Developer (WFH/Consultant Mode)", "location": "Job Location: WFH (India)", "location_type": "remote", "job_type": "contract", "min_experience": 5, "max_experience": null, "apply_link": "https://www.mobiloitte.com/careers/job-for-senior-wordpress-developer-wfh-consultant-mode/", "description": "Experience: 5+ Years Industry: IT Services & Digital Solutions About Mobiloitte: The full-service software development company Mobiloitte specializes in advanced web, mobile, and cloud solutions. With clients in India, USA, Singapore, UAE, UK, and South Africa, we offer high-performance digital engineering and full-stack innovation. We need a Senior WordPress Developer (Consultant Mode) with hands-on experience and creative problem-solving for mission-critical CMS and eCommerce projects. Key Responsibilities: Lead custom projects for WordPress development, including theme, plugin, and API integrations. Develop pixel-perfect front-end interfaces using HTML5, CSS3, JavaScript, and modern UI frameworks. Optimize WordPress websites for speed, scalability, and SEO best practices. Integrate payment gateways, CRM tools, and marketing automation platforms. Manage WordPress security, backups, and technical documentation. Work closely with UI/UX designers, QA teams, and backend developers in an agile environment. Provide technical consulting and solution architecture for client projects. To Apply: Fill Registration Form NOW Required Skills & Qualifications: 5+ years of hands-on WordPress development experience (custom themes, plugins, hooks, filters). Strong command of PHP, MySQL, HTML5, CSS3, and JavaScript (jQuery/React preferred). Experience with WooCommerce, Elementor, WP Bakery, ACF Pro, CPT UI, and REST API. Familiarity with Git-based workflows, deployment tools, and remote collaboration. Understanding of SEO principles, Core Web Vitals, and Google Lighthouse. Proven track record of delivering high-performance, responsive websites. Engagement Model: Mode: Consultant / Work from Home Hours: Flexible with expected availability for daily scrum/check-ins Compensation: Project-based / Hourly / Monthly Retainer (based on experience & availability) Start Date: Immediate Why Join Mobiloitte? Exposure to global clients & enterprise-grade projects Remote flexibility with result-oriented engagement Dynamic, innovation-led tech ecosystem Opportunity for long-term consulting assignments", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.mobiloitte.com/careers/job-for-solution-architect-hybrid-human-ai-coding-mobiloitte-group/", "company_id": 3380, "source": 3, "skills": "", "title": "Job for Solution Architect – Hybrid Human + AI Coding", "location": "Job Location: Remote (WFH)", "location_type": "hybrid", "job_type": null, "min_experience": 8, "max_experience": 8, "apply_link": "https://www.mobiloitte.com/careers/job-for-solution-architect-hybrid-human-ai-coding-mobiloitte-group/", "description": "8+ years in software architecture & development About the Role Mobiloitte is on the forefront of the AI revolution in software engineering. We’re seeking a Solution Architect to lead the charge in scaling hybrid human + Codex/LLM-powered development workflows across our engineering verticals. You will architect and operationalize an end-to-end AI-integrated DevOps ecosystem combining the power of LLMs, agentic microtools, custom orchestrators, and your architectural foresight. This role isn’t about theoretical AI tinkering. It’s about practical, secure, scalable deployment of AI pair programming, from ticket-to-prompt automation to self-healing CI/CD loops. You’ll be the cornerstone of Mobiloitte’s shift toward AI-augmented software delivery—working with elite engineers and AI strategists across India, UAE, Singapore, UK, USA, and South Africa. Your Responsibilities Architect the Hybrid AI Coding Stack: Define and deploy workflows integrating GPT-4, Claude, CodeWhisperer, and open-source LLMs via IDE plugins, LangChain/LlamaIndex pipelines, and CI triggers. Drive Agentic Orchestration: Spin up model “roles” (coder, test writer, doc polisher) chained with tool use, function calling, and real-time validations. Establish AI Governance & Security Controls: Lead initiatives to enforce safe prompting, credential sanitization, and IP protection using differential access, logging policies, and audit trails. Automate Developer Workflows: Implement auto ticket-to-prompt pipelines from Jira/GitLab and initiate “AI Ready” labeling frameworks with pre-validated contexts. Integrate Quality & Compliance Gates: Orchestrate LLM-driven PRs with ESLint, Semgrep, and OWASP checks, and enforce human approval and diff-based commits. Analyze and Improve AI ROI: Track model accuracy, regression rates, LOC throughput, and DORA metrics to measure and enhance hybrid team productivity. Must-Have Qualifications I have over 8 years of experience in software development and architecture, working on cloud-native, web, and enterprise applications. Deep hands-on experience with LLM tooling: GPT-4 API, Code Interpreter, LangChain, LlamaIndex, or agentic frameworks. Proven capability in building developer platform pipelines—CI/CD (GitHub Actions, Jenkins), code quality gates, container orchestration (Docker/K8s). Strong grasp of secure development practices: secrets handling, code sanitization, and licensing risk controls. You possess the ability to connect AI and engineering, thinking not only in terms of model prompts but also in terms of user stories, pipelines, and microservices. You are proficient in working with Node. js, Python, or Java, as well as one frontend stack, such as React or Angular. Bonus Points For Experience deploying self-healing CI workflows triggered by test failures or diff-based re-prompts. Hands-on with prompt engineering for deterministic output, test-first coding, and few-shot design patterns. You have experience in hosting open-source models such as Ollama, Hugging Face, and vLLM. Experience working in cross-border teams and with multi-regional compliance frameworks (GDPR, India IT Act, etc. ). What We Offer Elite tech ecosystem where you can experiment with the latest AI models and developer tooling at scale. Strategic influence at the intersection of AI and software engineering transformation. Opportunities to collaborate globally with our tech hubs in India, UAE, Singapore, UK, USA, and South Africa. Competitive compensation, rapid career growth, and a direct path to leadership in AI-augmented engineering. Ready to Architect the Future of AI-Powered Development? Send your resume and a short write-up on your experience with LLMs or AI coding automation to careers@mobiloitte. com Subject: “Solution Architect – Hybrid AI Coding” Complete Registration Form:", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.mobiloitte.com/careers/job-for-tech-lead-react-native-work-from-home-available/", "company_id": 3380, "source": 3, "skills": "", "title": "Job for Tech Lead – React Native (Work From Home Available)", "location": "Job Location: Remote (WFH)", "location_type": "remote", "job_type": "full_time", "min_experience": 3, "max_experience": null, "apply_link": "https://www.mobiloitte.com/careers/job-for-tech-lead-react-native-work-from-home-available/", "description": "Experience: 5+ Years About Mobiloitte Mobiloitte is a full-service digital transformation company with a strong presence in Blockchain, AI/ML, IoT, Mobile & Web App Development. With operations across India, UAE, Singapore, UK, USA, and South Africa, we empower clients with scalable, future-ready technology solutions. Our people-first culture fosters innovation, collaboration, and performance excellence. Role Overview We are seeking a React Native Tech Lead with a solid background in mobile application architecture and development. The ideal candidate will be responsible for leading a team of developers, setting the technical vision, and delivering high-quality, scalable mobile applications for global clients — all while working remotely. Key Responsibilities Lead and manage end-to-end mobile app development using React Native. Collaborate with UI/UX, QA, backend, and DevOps teams to ensure smooth project execution. Architect modular, maintainable, and scalable codebases aligned with industry best practices. Mentor junior developers and conduct regular code reviews and performance appraisals. Drive agile development processes and sprint planning. Ensure cross-platform optimization and device compatibility. Stay updated with the latest trends in mobile technologies. Must-Have Skills 5+ years of experience in mobile development; 3+ years in React Native. Proven experience leading technical teams. Deep understanding of JavaScript (ES6+), TypeScript, Redux, RESTful APIs, Firebase/GraphQL. Familiarity with native build tools like Xcode, Android Studio. Experience integrating third-party APIs and SDKs. Excellent communication and stakeholder management skills. Preferred Qualifications Experience working in a remote or distributed team. Exposure to CI/CD pipelines and app deployment on App Store/Play Store. Knowledge of Agile methodologies (Scrum, Kanban). Experience with analytics and crash-reporting tools like Firebase, Sentry, etc. Perks & Benefits 100% Remote Work – Flexible and performance-driven environment. Upskilling programs & global project exposure. Health & wellness benefits (as applicable). Performance bonuses and rewards. Dynamic and inclusive work culture. Apply Now Ready to lead high-impact mobile projects from the comfort of your home? Complete Registration Form: Submit your resume to careers@mobiloitte. com or apply directly at www. mobiloitte. com/careers", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.mobiloitte.com/careers/job-for-tech-lead-ai-agent-development-work-from-home/", "company_id": 3380, "source": 3, "skills": "", "title": "Job for Tech Lead – AI Agent Development (Work From Home)", "location": "Job Location: Remote (WFH)", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://www.mobiloitte.com/careers/job-for-tech-lead-ai-agent-development-work-from-home/", "description": "Experience: 7+ Years (AI/ML/NLP), 3+ Years in GenAI/LLM Applications About Mobiloitte Mobiloitte is a full-stack digital transformation company with global operations across India, UAE, Singapore, UK, USA, and South Africa. We are rapidly expanding our AI division with a focus on intelligent agent systems, LLM integrations, and context-aware automation. We seek a seasoned AI Tech Lead to spearhead a dynamic and growing team shaping the future of multi-agent AI products. Role Overview We are looking for a Tech Lead – AI Agent Development with deep hands-on expertise in building, scaling, and managing LLM-based agent architectures. This role requires both strong leadership and deep technical mastery in modern NLP paradigms, embedding techniques, and Retrieval-Augmented Generation (RAG) systems. The role offers the opportunity to build end-to-end AI solutions that continuously learn and adapt – powering intelligent agents deployed across enterprise and consumer domains. Key Responsibilities Lead and architect intelligent agent systems using LangChain, LlamaIndex, or similar LLM frameworks. Drive the design and implementation of advanced RAG pipelines, from chunking strategy to prompt templating. Optimize embedding generation, storage, and retrieval strategies using vector databases (e. g. , FAISS, Pinecone, Weaviate, Chroma). Oversee modular agent frameworks for task automation, memory handling, tool orchestration, and reasoning workflows. Set best practices for chunking logic, token optimization, prompt compression, and data formatting for optimal contextualization. Lead experimentation in agent self-refinement, feedback loops, and continuous learning paradigms. Collaborate with product owners, backend, and DevOps teams to integrate AI solutions into scalable architectures. Manage and mentor a growing team of AI developers, data scientists, and NLP engineers. Stay abreast of the latest in open-source models, agentic workflows, and GenAI frameworks. Must-Have Technical Skills Strong command over Python, with hands-on experience in frameworks like LangChain, LlamaIndex, Transformers (HuggingFace). Deep understanding of text embeddings (OpenAI, Cohere, Sentence-BERT), vector math, and high-dimensional similarity search. Proficient in vector database integration (Pinecone, FAISS, Weaviate, Qdrant, or Chroma). Experience building scalable RAG pipelines, including custom chunkers, retrievers, and context optimizers. Solid grasp of agent memory architectures, dynamic tool calling, and planning/action chaining. Knowledge of prompt engineering, structured prompting, few-shot/coT techniques. Comfortable deploying LLM-based solutions in real-world production environments, including prompt monitoring, latency tuning, and fail-safes. Preferred/Bonus Skills Familiarity with self-hosted LLMs (Mistral, LLaMA2/3, Claude, Mixtral, Phi-2). Experience in real-time AI applications, low-latency APIs, and streaming responses (LangChain Streaming). Knowledge of AutoGPT, BabyAGI, Open Agents, or similar experimental frameworks. Exposure to data annotation, knowledge distillation, or custom embedding training. Prior team management experience (5+ members) with sprint planning, code reviews, and mentorship. Soft Skills & Leadership Traits Ability to drive experimentation while maintaining architectural discipline. Strategic mindset: balance between rapid PoCs and long-term scalability. Excellent communication – technical, cross-functional, and client-facing. Bias for action – a builder and problem-solver with high ownership. Engagement & Benefits 100% Remote | Flexi-Hours | Global Project Exposure High-impact role at the forefront of GenAI evolution Opportunity to build core IP and AI product lines Performance bonuses and long-term leadership track Supportive leadership + global engineering collaboration How to Apply If you’re ready to build the next generation of autonomous, intelligent AI agents, let’s connect. Complete Registration Form: Send your updated resume and GitHub/portfolio to [careers@mobiloitte. com] Apply directly at www. mobiloitte. com/careers", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.mobiloitte.com/careers/part-time-remote-flexible-job-for-senior-net-consultant-kendo-ui-expert/", "company_id": 3380, "source": 3, "skills": "", "title": "Part-Time Job For Senior .NET Consultant (Kendo UI Expert)", "location": "Job Location: Remote (WFH)", "location_type": "remote", "job_type": "part_time", "min_experience": 7, "max_experience": null, "apply_link": "https://www.mobiloitte.com/careers/part-time-remote-flexible-job-for-senior-net-consultant-kendo-ui-expert/", "description": "Location: Remote | Commitment: 3–4 Hours Daily (Part-Time) Experience Level: Senior | Projects: Multiple (Ongoing) About Mobiloitte: Mobiloitte is a full-service software development company working with leading-edge technologies and global clients across domains. We’re agile, innovation-driven, and compliance-focused — building secure, scalable, and business-aligned digital solutions. Position Overview: We are actively seeking an experienced Senior . NET Consultant with a strong command over Kendo UI for multiple ongoing client projects. This is a part-time consulting role (3–4 hours/day), ideal for seasoned professionals who are hands-on and can deliver clean, efficient, and scalable solutions regularly while collaborating with our internal tech teams. Key Responsibilities: Architect, develop, and maintain applications using . NET (C#/ASP. NET/MVC/Web API) and Kendo UI components. Work collaboratively with cross-functional teams to analyze requirements and ensure timely technical delivery. Handle integrations, customizations, performance tuning, and bug fixes for various live and upcoming projects. Provide mentorship and code-level guidance to junior developers (where required). Maintain clear and timely documentation as per internal standards and client requirements. To Apply: Fill Registration Form NOW Required Skills & Experience: Minimum 7+ years of experience in . NET development. Proven hands-on expertise in Kendo UI (widgets, grid, charting, etc. ) – MANDATORY. Strong understanding of JavaScript, jQuery, AJAX, and front-end integration with . NET applications. Experience with SQL Server, REST APIs, and enterprise-grade system design. Ability to dedicate at least 3–4 hours daily, consistently and reliably. Excellent problem-solving skills and client-handling capabilities. Preferred Attributes: The candidate should have previous experience working in a consulting or multi-project environment. The candidate should have familiarity with agile methodologies and collaborative development tools such as Git and JIRA. Strong communication skills, both written and verbal. What We Offer: The work environment is remote and flexible. You will have the chance to contribute to projects at the enterprise level, spanning various domains. The work environment is fast-paced and collaborative, requiring regular interaction with tech leads and architects. We offer competitive compensation that aligns with your expertise and commitment.", "ctc": null, "currency": null, "meta": {}}]