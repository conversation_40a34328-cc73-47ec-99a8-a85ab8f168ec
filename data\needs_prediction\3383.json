[{"jd_link": "https://www.cleverdevsoftware.com/careers/business-analyst", "company_id": 3383, "source": 3, "skills": "", "title": "Middle Business Analyst", "location": "", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://www.cleverdevsoftware.com/careers/business-analyst", "description": "home/careers/Middle Business AnalystMiddle Business AnalystWe are looking for a Middle Business Analyst to join our team and contribute to the success of industry-leading solutions. ‍In this role, you will work on projects with strong market positions and significant growth potential. Your expertise in analyzing requirements, optimizing processes, and collaborating with stakeholders will be essential in driving efficiency and innovation. Project Work Format: Remote work from home (if you wish, it is possible to organize your work in the office)Project description in ConfluenceTask setting in JiraMain Tasks:Intake: Conduct sessions (interviews, workshops and other) with clients to identify, understand, challenge, and agree upon requirements. Discovery: Recommend optimal methods for achieving desired business outcomes using a variety of analytical tools and techniques. Decomposition: Produce clear and comprehensive documentation to communicate requirements, business solution options, and feasibility assessments to both the team and various stakeholder audiences. Support: Help both the team and stakeholders to keep clear and syncronized vision regarding the documented requirements. What We Expect from a Candidate:3+ years of experience in business analysis within software engineering. Experience in developing systems such as CRMs, ERPs, or integrations with third-party systems. Proficiency in English (С1 level), both verbal and written. Ability to handle all levels of requirements – business, user and solution. Demonstrated experience in all phases of the requirements lifecycle: elicitation, analysis, documentation, and validation. Familiarity with agile methodologies and approaches (Scrum, Kanban). A systematic, disciplined, and analytical approach to problem-solving. Strong negotiation and influencing skills with stakeholders at all levels. Experience in the Healthcare or Fintech domain (banking) is a plus. Experience in fixed-budget projects is a plus. Will Be a Plus:Broad understanding of various business domainsTermsFormal employment or B2B Friendly Atmosphere of Communication and CooperationOpen Free Control StylePaid Leave of 25 Calendar Days6 Sick DaysCorporate English CoursesCool Office Parties Every Month", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.cleverdevsoftware.com/careers/angular-developer", "company_id": 3383, "source": 3, "skills": "", "title": "Angular Developer", "location": "", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.cleverdevsoftware.com/careers/angular-developer", "description": "home/careers/Angular DeveloperAngular DeveloperWe are looking for an Angular Developer to join our outstanding development team in one of our advanced projects. Project Work Format:Remote work from home (if you wish, it is possible to organize your work in the office) Project description in Confluence Task setting in Jira Main Tasks:Develop Web applications (based on Angular) Interact with backend developers and business analysts Analyze emerging problems and correct detected errorsWhat We Expect from a Candidate:Experience in TypeScript JavaScript commercial experience (modern syntax and ES6 + functions)Strong command of AngularExperience in HTML5 and CSS3, including SASS / SCSS Ability to write efficient, safe, well-documented and pure JavaScript code Experience in refactoring and existing functions improvementWill Be a Plus:Technical English, at document reading level REST API interfaceLibraries: Material, RxJS, Nx, NgRxTermsFormal employment or B2B Friendly Atmosphere of Communication and CooperationOpen Free Control StylePaid Leave of 25 Calendar Days6 Sick DaysCorporate English CoursesCool Office Parties Every Month", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.cleverdevsoftware.com/careers/mid-level-ux-ui-designer", "company_id": 3383, "source": 3, "skills": "", "title": "Mid-Level UX/UI Designer", "location": "", "location_type": "remote", "job_type": "internship", "min_experience": null, "max_experience": null, "apply_link": "https://www.cleverdevsoftware.com/careers/mid-level-ux-ui-designer", "description": "home/careers/Mid-Level UX/UI Designer Mid-Level UX/UI Designer We’re looking for a mid-level UX/UI Designer to join our team and contribute to the design of complex B2B interfaces — including dashboards, admin panels, CRM systems, and other business tools. You’ll play a key role in shaping intuitive, functional, and visually polished experiences that solve real user problems. Project Work Format:Remote work from home (if you wish, it is possible to organize your work in the office)Project description in ConfluenceTask setting in JiraMain Tasks:Design new product features from scratch and improve existing user interfaces. Define and optimize UX flows, page structure, and interaction logic. Create interactive prototypes and finalize UI using established design systems and reusable components. Design dashboards, forms, lists, and other data-driven UI elements within a structured product logic. Conduct UX analysis, ask the right questions, and propose well-reasoned solutions that balance user needs and business goals. What We Expect from a Candidate:Strong proficiency in Figma: components, auto-layouts, variants, and design systems. Ability to map UX flows, user scenarios, and interface logic. Experience with both low- and high-fidelity prototyping. A solid portfolio of work designing admin panels, dashboards, or other B2B interfaces. Understanding of responsive design and web layout principles. Experience collaborating with cross-functional teams (e. g. , business analysts, developers). Strong logical thinking — not just visual styling, but meaningful UX decisions. Experience working independently or in small, cross-functional teams is a plus. English level: B1 or higher — enough to communicate with colleagues and understand documentation. Will Be a Plus Familiarity with analytics tools or data-heavy interfaces. Experience conducting user research or usability testing. Basic understanding of HTML/CSS (for communicating with developers). This Role Is Not a Fit If You. . . Have no UX experience or come only from a graphic/marketing design background. Rely solely on UI kits or templates without understanding UX principles. Are at an intern or junior level — this is a mid-level position requiring confidence and independence. TermsFormal employment or B2B Friendly Atmosphere of Communication and CooperationOpen Free Control StylePaid Leave of 25 Calendar Days6 Sick DaysCorporate English CoursesCool Office Parties Every Month", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.cleverdevsoftware.com/careers/qa-lead-manual-testing", "company_id": 3383, "source": 3, "skills": "", "title": "QA Lead (Manual Testing)", "location": "", "location_type": "remote", "job_type": null, "min_experience": 2, "max_experience": null, "apply_link": "https://www.cleverdevsoftware.com/careers/qa-lead-manual-testing", "description": "home/careers/QA Lead (Manual Testing) QA Lead (Manual Testing) We are seeking a highly skilled and experienced QA Lead with a strong background in manual testing to join our remote team. The ideal candidate will possess leadership qualities, be detail-oriented, and have a passion for ensuring software quality. As a QA Lead, you will be responsible for leading a team of QA engineers, designing test plans, and ensuring the delivery of high-quality software solutions. Project Work Format:Remote work from home (if you wish, it is possible to organize your work in the office)Project description in ConfluenceTask setting in JiraMain Tasks:Lead a team of QA engineers in manual testing efforts across multiple projects. Define, develop, and maintain comprehensive test plans and test cases. Coordinate testing activities and ensure adherence to QA processes and timelines. Identify, document, and track defects through resolution. Participate in sprint planning and product release activities. Continuously improve QA processes and practices. What We Expect from a Candidate:5+ years of experience in software testing, primarily manual testing. 2+ years of experience in a QA leadership or team lead role. Strong understanding of QA methodologies, tools, and processes. Excellent analytical and problem-solving skills. Good communication and interpersonal skills. Fluent in English (spoken and written). Ability to work independently and manage a remote QA team. TermsFormal employment or B2B Friendly Atmosphere of Communication and CooperationOpen Free Control StylePaid Leave of 25 Calendar Days6 Sick DaysCorporate English CoursesCool Office Parties Every Month", "ctc": null, "currency": null, "meta": {}}]