[{"jd_link": "https://www.streamssolutions.com/jobs/microsoft-dynamics-365-fo-developer/", "company_id": 3385, "source": 3, "skills": "", "title": "Microsoft Dynamics 365 F&O Developer", "location": "Gurugram india", "location_type": null, "job_type": "full_time", "min_experience": 2, "max_experience": 4, "apply_link": "https://www.streamssolutions.com/jobs/microsoft-dynamics-365-fo-developer/", "description": "Job Category: Technical Job Type: Full Time Job Location: Gurugram india Experience: 2 to 4 Years Job Overview We are seeking a skilled Microsoft Dynamics 365 Finance and Operations (F&O) Developer to design, develop, and customize solutions on the Dynamics 365 F&O platform. The ideal candidate will have expertise in X++, strong problem-solving skills, and experience with ERP system integration. Roles and Responsibilities Develop and customize Dynamics 365 F&O solutions using X++. Integrate Dynamics 365 F&O with other systems via APIs and data entities. Configure modules to align with business processes. Perform testing, debugging, and provide technical support. Collaborate with stakeholders to gather requirements and deliver solutions. Requirements Minimum 2 years of experience in developing and customizing Microsoft Dynamics 365 for Finance and Operations (F&O). Can collaborate with stakeholders to define requirements, estimate deliverables, and set expectations for development tasks. Bachelor’s degree in computer science or related field. Proven experience with Dynamics 365 F&O and X++ development. Strong understanding of ERP systems and integration technologies. Excellent problem-solving and communication skills. Microsoft Dynamics 365 certification is a plus. ", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.streamssolutions.com/jobs/netsuite-developer-2/", "company_id": 3385, "source": 3, "skills": "", "title": "NetSuite Developer", "location": "Gurugram india", "location_type": null, "job_type": "full_time", "min_experience": 2, "max_experience": 4, "apply_link": "https://www.streamssolutions.com/jobs/netsuite-developer-2/", "description": "Job Category: Technical Job Type: Full Time Job Location: Gurugram india Experience: 2 to 4 Years Roles and responsibilities Translate business requirements into acceptance criteria and well-architected solutions thatbest leverage the NetSuite platform. Actively contribute in technical design sessions, effort estimations, and document technicalsolutions aligned with business objectives; identify gaps between current and desired endstates. Perform high-impact development (configuration or code) to solve complex problems usingNetSuite development platform (SuiteScript 1. 0, SuiteScript2. 0, SuiteFlow etc), reducetechnical debt, and implement best practices that sustainably achieve strategic businessobjectives. Maintain and refine development standards, branching strategies, environment strategies,deployment processes and development workflows. Resolve production issues in timely manner. Qualifications 2+ years of NetSuite platform developer experience. Expert knowledge of the NetSuite platform and development concepts. Can collaborate with stakeholders to define requirements, estimate deliverables, and setexpectations for development tasks. Advanced knowledge of DevOps functionality and technology. Experience with the Atlassianstack (JIRA, Confluence) General knowledge of integration platforms (Boomi, Celigo). Proven ability to design, build, optimize, and release effective NetSuite solutions, bothdeclarative and programmatic, that sustainably achieve desired business outcomes. General understanding of branching strategy, environment management, and deploymentmethodologies. SQL database or relational database skills Nice To Have: NetSuite Developer Certification ", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.streamssolutions.com/jobs/microsoft-dynamics-365-fo-solution-architect/", "company_id": 3385, "source": 3, "skills": "", "title": "Microsoft Dynamics 365 F&O Solution Architect", "location": "Gurugram india", "location_type": null, "job_type": "full_time", "min_experience": 12, "max_experience": 12, "apply_link": "https://www.streamssolutions.com/jobs/microsoft-dynamics-365-fo-solution-architect/", "description": "Job Category: Technical Job Type: Full Time Job Location: Gurugram india Experience: 8 to 12 Years Job Summary: We are seeking a highly skilled and experienced Microsoft Dynamics 365 Finance & Operations (F&O) Solution Architect to join our team. The ideal candidate will have a strong background in Dynamics 365 F&O and be able to design and implement comprehensive solutions that meet our business needs. The Solution Architect will work closely with stakeholders, including business leaders and IT professionals, to ensure the successful delivery of Dynamics 365 F&O projects. Roles and Responsibilities Architect and design comprehensive Dynamics 365 F&O solutions that meet business requirements and align with best practices. Work with stakeholders to understand business needs and translate them into functional and technical requirements. Oversee the implementation of Dynamics 365 F&O solutions, including configuration, customization, and integration with other systems. Provide technical guidance and leadership to development teams, ensuring the solution is implemented effectively and efficiently. Collaborate with project managers to define project scope, timelines, and deliverables; track progress and address issues as they arise. Ensure solutions are tested thoroughly and meet quality standards before deployment. Create and maintain comprehensive documentation for solutions, including design specifications, configuration guides, and user manuals. Develop and deliver training sessions for end-users; provide ongoing support and troubleshoot issues as needed. Continuous Improvement: Stay updated with the latest Dynamics 365 F&O features and industry trends; recommend and implement improvements to enhance system performance and functionality. Required Skills and Qualifications: Technical Expertise: Extensive experience with Microsoft Dynamics 365 Finance and Operations, including deep knowledge of modules such as Financials, Supply Chain Management, and Retail. Architectural Skills: Proven experience designing and implementing technical architectures for Dynamics 365 F&O solutions. Familiarity with enterprise architecture frameworks is a plus. Programming Knowledge: Proficiency in X++, C#, SQL, and other relevant technologies. Experience with Azure services and cloud-based solutions is beneficial. Integration Experience: Solid experience with integrating Dynamics 365 F&O with other systems using tools such as Data Management Framework (DMF), Application Integration Framework (AIF), and custom integrations. Analytical Skills: Strong problem-solving skills with the ability to analyze complex technical issues and develop effective solutions. Project Management: Experience managing or participating in Dynamics 365 F&O projects from initiation through deployment. Familiarity with Agile methodologies is a plus. Communication: Excellent verbal and written communication skills. Ability to articulate complex technical concepts to non-technical stakeholders. Certification: Microsoft Dynamics 365 F&O certifications are highly preferred (e. g. , Dynamics 365 Finance and Operations Core, Dynamics 365 Finance, Dynamics 365 Supply Chain Management). ", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.streamssolutions.com/jobs/technical-lead-product-development/", "company_id": 3385, "source": 3, "skills": "", "title": "Technical Lead – Product Development", "location": "", "location_type": null, "job_type": null, "min_experience": 2, "max_experience": 4, "apply_link": "https://www.streamssolutions.com/jobs/technical-lead-product-development/", "description": "Roles and Responsibilities Hands-on role responsible for leading a team in developing, implementing, and enhancingweb application products. Evaluate functional/business and technical requirements and identify gaps/risks and provideestimates. Implements the best practices of Agile to deliver quality products predictably andconsistently. Provides product support, product troubleshooting support and resolves product issues anddefects. Promotes knowledge sharing activities within and across different product teams byengaging in training, and mentoring. Keep skills up to date through ongoing self-directed training Required Experience 6+ years of experience developing Web Applications using HTML5, CSS, JavaScript. Minimum 4 years of experience with Node. js. Minimum 4 years of experience with JavaScript libraries/frameworks such as jQuery, React,VueJS, AngularJS. 2+ experience configuration and development in WordPress. Minimum 2 years of experience leading a team of developers. Knowledge of CSS Frameworks such as Bootstrap. Knowledge of REST Web APIs, databases such as MySQL, Mongo, and Postgres. Experience in Agile software development. Knowledge of AWS Code deployment/pipeline is must. Desired Exposure to cloud platforms such as AWS, Azure ", "ctc": null, "currency": null, "meta": {}}]