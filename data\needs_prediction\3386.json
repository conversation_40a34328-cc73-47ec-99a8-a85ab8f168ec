[{"jd_link": "https://corpsoft.io/career/back-end-engineer-php-laravel%f0%9f%a6%be/", "company_id": 3386, "source": 3, "skills": "", "title": "Back End Engineer (<PERSON><PERSON>, <PERSON><PERSON>)🦾", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 2, "max_experience": null, "apply_link": "https://corpsoft.io/contacts/", "description": "Requirements: – 2+ years of Laravel development experience (on commercial projects); – Experience with Vue. js or React. js would be a plus; – Strong understanding of OOP fundamentals and adherence to a coding standard; – Experience with SQL or NoSQL databases; – Practice experience with building REST API services; – Practice experience with <PERSON>er; – Experience in working in a product-focused team; – English (Intermediate and higher); – Scrum process & Agile approach understanding; – Proactive position and desire to suggest new ideas. Responsibilities: – Create products logic following the best practices of the domain development, and also taking into account user needs and product domain context; – Study user needs and suggest implementation ways that will work the best; – Deepen and evaluate product increment requirements in collaboration with a cross-functional squad including tech and proxy product owners; – Come up with new initiatives within your area to improve performance; – Take part in implementing, maintaining, and improving existing products. Expectations: – High level of efficiency and productivity, proactive approach; – On-time delivery of projects/tasks and positive feedback from clients/squad; – Technical and personal growth. We offer: – Regular result-based salary reviews; – Comfortable working hours (10-19 Kyiv time zone); – Bonus system tied to the product’s success – Established product-focused environment; – Range of tasks, from quick and simple to challenging investigation to run; – Cheerful & dynamic environment; – Friendly and open-minded team; – Virtual workspace with perspective to move into one of the offices – Mentorship; – Gym and English classes discounts. Hiring steps: – First interview with the Recruiter; – Technical interview with the Squad Lead; – Job Offer.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://corpsoft.io/career/full-stack-engineer-%f0%9f%9b%a0%ef%b8%8f/", "company_id": 3386, "source": 3, "skills": "", "title": "Full Stack Engineer 🛠️", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 1, "max_experience": null, "apply_link": "https://corpsoft.io/contacts/", "description": "We are looking for a Full Stack Engineer to join our team. Requirements: – 2+ years of Laravel development experience (on commercial projects); – 1+ years experience with Vue. js (React. js, JS, jQuery acceptable); – Strong understanding of OOP fundamentals and adherence to a coding standard; – Strong knowledge of Design Patterns; – Experience with SQL or NoSQL databases; – Practice experience with building REST API services; – Practice experience with Docker; – Experience in working in a product-focused team; – English (Intermediate and higher); – Scrum process & Agile approach understanding; – Proactive position and desire to suggest new ideas. Responsibilities: – Create products logic following the best practices of the domain development, and also taking into account user needs and product domain context; – Study user needs and suggest implementation ways that will work the best; – Deepen and evaluate product increment requirements in collaboration with a cross-functional squad including tech and proxy product owners; – Come up with new initiatives within your area to improve performance; – Take part in implementing, maintaining, and improving existing products. Expectations: – High level of efficiency and productivity, proactive approach; – On-time delivery of projects/tasks and positive feedback from clients/squad; – Technical and personal growth. We offer: – Regular result-based salary reviews; – Comfortable working hours (10-19 Kyiv time zone); – Bonus system tied to the product’s success; – Established product-focused environment; – Range of tasks, from quick and simple to challenging investigation to run; – Cheerful & dynamic environment; – Friendly and open-minded team; – Remote job location with perspective to move into one of the offices; – Mentorship; – Gym and English classes discounts. Hiring steps: – First interview with the Recruiter; – Technical interview with the Squad Lead; – Job Offer.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://corpsoft.io/career/full-stack-engineer-squad-lead-%f0%9f%9a%80/", "company_id": 3386, "source": 3, "skills": "", "title": "Full Stack Engineer/ Squad Lead 🚀", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 2, "max_experience": null, "apply_link": "https://corpsoft.io/contacts/", "description": "We are looking for a candidate with strong leadership skills for a corresponding position in a team of 2 to 5 people. You will be expected to work directly on products as well as manage a team. Responsibilities: – Managing a team of 3 to 5 people, assigning tasks, and assessing risks, both technical on the project and the team side; – Eliminating blockers at any stage – both technical and resource-related; – Create products logic following the best practices of the domain development, and also taking into account user needs and product domain context; – Deepen and evaluate product increment requirements in collaboration with a cross-functional squad including tech and proxy product owners; – Come up with new initiatives within your area to improve performance; – Take part in implementing, maintaining, and improving existing products. Requirements: – Experience in working in a product-focused team (as a Squad Lead would be a plus); – English (Upper-intermediate and higher); – Scrum process & Agile approach understanding; – Proactive position and analytical skills; – Problem-solving and strong leadership skills; – 2+ years of Laravel development experience (on commercial projects. ); – Commercial experience with Vue. js (React. js, JS, jQuery acceptable); – Knowledge of HTML5, CSS3; – Experience with AWS, Google, and Microsoft services would be a plus; – Strong understanding of OOP fundamentals and adherence to a coding standard; – Strong knowledge of Design Patterns and LAMP; – Experience with SQL or NoSQL databases; – Practice experience with building REST API services; – Practice experience with Docker. Expectations: – Cohesive team, high level of efficiency and productivity; – On-time delivery of projects/tasks and positive feedback from clients; – Technical growth, both personal and team. We offer: – Regular result-based salary reviews; – Comfortable working hours (10-19 Kyiv time zone); – Bonus system tied to the product’s success – Established product-focused environment; – Range of tasks, from quick and simple to challenging investigation to run; – Cheerful & dynamic environment; – Friendly and open-minded team; – Remote job location with perspective to move into one of the offices; – Mentorship; – Gym and English classes discounts. Hiring steps: – First interview with the Recruiter; – Technical interview with the CTO; – Job Offer.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://corpsoft.io/career/product-delivery-manager-%f0%9f%92%bc/", "company_id": 3386, "source": 3, "skills": "", "title": "Product Delivery Manager 💼", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 2, "max_experience": 2, "apply_link": "https://corpsoft.io/contacts/", "description": "Responsibilities: – Driving regular communication (written and calls) with clients; – Scope planning based on provided context and product owner’s vision in close collaboration with the client (PO), and the team; – Ensuring smooth delivery per the altered Scrum process established within the team; – Overseeing the quality of the deliverables; – Doing reports and analysis in order to incorporate improvements into the process flow. Requirements: – At least 2 years of commercial experience in project management; – В2-С1 English; – Excellent profound knowledge of project management processes, Scrum framework, and Agile approach; – Outstanding communication and presentation skills; – Strong risk management skills; – Strong leadership skills; – Excellent negotiation and problem-solving skills; – Good understanding of the corporate environment and objectives; ability to propose solutions for their achievement. We offer: – Comfortable working hours (10-19 Kyiv time zone); – Regular result-based salary reviews, benefits and bonus system; – Product-oriented specialization and projects in line with it; – Cheerful & dynamic environment; – Open-minded skillful team; – Remote job location with perspective to move into one of the offices; – Mentorship. Hiring steps: – First interview with the Recruiter; – Practical Assignment; – Second interview with the Squad Lead; – Job Offer.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://corpsoft.io/career/we-are-looking-for-a-php-wordpress-engineer-to-join-our-team/", "company_id": 3386, "source": 3, "skills": "", "title": "ML/AI Engineer 🧠", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://corpsoft.io/contacts/", "description": "We are looking for a Machine Learning Engineer to help us deploy, configure, and optimize Mistral 7B, integrate it into our services, and implement Retrieval-Augmented Generation (RAG) for database interactions. Key Responsibilities: Configure and optimize Mistral 7B for business tasks (fine-tuning, quantization, performance optimization). Assess required computational resources, select the optimal infrastructure for model deployment (on-premise or cloud), and analyze cost efficiency. Implement RAG to integrate models with vector databases. Orchestrate interactions between multiple ML services (e. g. , one model generates tags, and another validates task descriptions). Develop a service for interacting with the model (API for predictions, model management, integration with our application). Optimize model performance for real-world usage. Requirements: Experience with LLM models (Mistral 7B, GPT-3/4, LLaMA, Claude, Falcon, Bloom, etc. ). Understanding of Retrieval-Augmented Generation (RAG) and model integration with databases. Hands-on experience with fine-tuning and dataset preparation/annotation. Experience with vector databases (Pinecone, Weaviate, FAISS). Strong proficiency in Python and libraries like PyTorch, TensorFlow, Hugging Face, and LangChain. Experience in evaluating and optimizing infrastructure for AI deployments. Experience in developing APIs for integrating AI models into business processes. We offer: Comfortable working hours (10-19 Kyiv time zone); Regular result-based salary reviews; Bonus system tied to the product’s success; Established product-focused environment; Range of tasks, from quick and simple to challenging investigation to run; Cheerful & dynamic environment; Friendly and open-minded team; Virtual workspace with perspective to move into one of the offices; Mentorship; Gym and English classes discounts. Hiring steps: 1st interview with the Recruiter; Technical interview with the Squad Lead; Job offer.", "ctc": null, "currency": null, "meta": {}}]