[{"jd_link": "https://themindstudios.com/careers/42-middle-ruby-developer/", "company_id": 3388, "source": 3, "skills": "", "title": "Middle Ruby Developer", "location": "Dnipro, remote\nWEB\n3+", "location_type": "remote", "job_type": "full_time", "min_experience": 3, "max_experience": 12, "apply_link": "https://themindstudios.com/careers/42-middle-ruby-developer/", "description": "Middle Ruby Developer Location: Dnipro, remote Team: WEB Minimum Experience: 3+ Hello and good vibes to everyone reading this! We’re currently looking for a Middle Ruby Developer to join our team in a full-time collaboration format. About us: Mind Studios specializes in mobile and web app development, helping businesses unlock their potential and turn ideas into profitable products. Our key domains include: Real Estate, Logistics, Healthcare, Fitness, and Mental Health. Our achievements: — 12 years in the IT market; — Offices in Dnipro, Kyiv, and Vienna; — Over 90 professionals on our team. What we offer: — Work in a professional team building innovative products; — Support for your growth: regular performance reviews, assessments & personal development plans; — Education budget tailored to your skills and experience; — Flexibility: work remotely or from one of our offices; — Paid vacations and sick leaves; — Transparency: monthly company updates. In this role, you will: — Design, development and further support of web-applications created with Ruby and using the Rails framework; — Testing applications using unit-tests and integration testing, according to company standards and best practices; — Development and support of high-load systems architecture; — Analysis of the code and architecture of applications in order to identify and eliminate technical problems, including reviewing and providing high-quality feedback on code and architectural solutions created by other developers; — Work according to Gitflow adopted by the company, using Git and Gitlab to control and manage source code; — Development of documentation for web application APIs. What is important to join us: — 3+ years of experience with Ruby; — Strong knowledge and practice: — Experience with Postman, Swagger or other tools for API documentation; — Ruby on Rails, Network Protocols; — REST API, Popular Gems (Sidekiq, Rpush, CarrierWave, RABL etc. ) , PostgreSQL; — HTML/CSS (HAML/SASS), Javascript, GIT, Linux; — Basic knowledge of AWS — English Intermediate is required. It’s great if you additionally have: — Docker, NoSQL (Redis), Payment Gateways (Stripe, Paypal); — Design application architecture; — Excellent debugging and troubleshooting skills. About stages: 1st stage: Pre-interview with a recruiter; 2nd stage: Interview with Team Lead and People Ops; 3rd stage: English test; 4th stage: offer. What makes us different from others? We are focused on results, not on processes, although we make sure there is no mess. You are going to have a solid influence on the product and its future strategy. We build products for users, not for clients, and sometimes it is OK to say a reasoned “No” to the customer.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://themindstudios.com/careers/73-junior-project-manager-gamedev/", "company_id": 3388, "source": 3, "skills": "", "title": "Junior Project Manager (Gamedev)", "location": "Dnipro, remote\nGAMEDEV\n0.5+", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://themindstudios.com/careers/73-junior-project-manager-gamedev/", "description": "Junior Project Manager (Gamedev) Location: Dnipro, remote Team: GAMEDEV Minimum Experience: 0. 5+ Приві<PERSON>, гарного настрою усім, хто читає ці рядки! Наразі ми у пошуку Junior Project Manager-а на full-time формат співпраці у команду ігрової розробки. У Mind Studios Games наша основна мета — створювати ігри, які виглядають і відчуваються сучасно, зручно та естетично. Тезисно про нас: — ми на ринку вже 12 років; — наші офіси знаходяться в Дніпрі, Києві та Відні; — ми зростаюча компанія, наразі в нас працює понад 90 крутих спеціалістів. Основні обов’язки: — вести щоденну комунікацію з клієнтами (дзвінки, чати, фідбек); — планувати й координувати завдання в Jira; — слідкувати за дедлайнами та завантаженням команди; — вести внутрішню документацію (Confluence, Google Docs); — проводити стендапи, демо, ретроспективи; — допомагати команді вирішувати дрібні блокери та не втрачати фокус. У нас буде повний match, якщо у тебе: — досвід роботи PM до 1 року або управління в інших сферах; — пройдені курси з проектного менеджменту; — ігровий досвід (ви ж полюбляєте ігри, так? ); — англійська B2/C1 (дзвінки, листування з клієнтами); — проактивність, емпатія, почуття гумору. Як ми взаємодіємо з кандидатами: 1-й етап: заповнення анкети; 2-й етап: онлайн спілкування з Talent Acquisition Specialist (до 45 хвилин); 3-й етап: test task; 4-й етап: онлайн спілкування з PM Lead (до 60 хвилин); 5-й етап: english test (до 20 хвилин); 6-й етап: offer. Якщо стався match — ми будемо раді поспілкуватись детальніше 😈", "ctc": null, "currency": null, "meta": {}}]