[{"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009028240/IT-Recruiter_South-Asia?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "IT Recruiter_South Asia", "location": "Hyderabad, India   | Posted on 11/11/2024", "location_type": null, "job_type": null, "min_experience": 4, "max_experience": null, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009028240/IT-Recruiter_South-Asia?source=CareerSite", "description": "We are seeking an experienced IT Recruiter to join our dynamic team. The ideal candidate will have a strong background in IT hiring, with a key focus on bulk hiring and staff augmentation. The role involves handling recruitment not only for India but also for Canada and the USA. Proficiency in using Zoho Recruit is essential. Candidates from Hyderabad are preferred, as face-to-face interviews will be part of the selection process. Manage the full-cycle recruitment process for IT roles, including Software Engineers. Drive bulk hiring and staff augmentation efforts to meet high-volume hiring needs. Source, screen, and shortlist candidates for various technical positions across India, Canada, and the USA. Collaborate with hiring managers to understand staffing requirements and develop sourcing strategies. Utilize Zoho Recruit for candidate management, job postings, and tracking recruitment progress. Build a pipeline of qualified candidates through job boards, social media, and networking events. Ensure a seamless candidate experience from application to onboarding. Stay updated on market trends and hiring practices in India, Canada, and the USA. RequirementsProven experience in IT recruitment, specifically for software engineering roles. Expertise in bulk hiring and staff augmentation. Familiarity with international hiring practices, particularly for Canada and the USA. Hands-on experience with Zoho Recruit or similar Applicant Tracking Systems (ATS). Strong communication and interpersonal skills. Ability to work in a fast-paced, target-driven environment. Based in Hyderabad or willing to attend face-to-face interviews in Hyderabad. Preferred Qualifications:4+ years of experience in IT recruitment. Experience working in a similar industry or consulting firm.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009030096/SDR_IT-Staff-Augmentation-Only?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "SDR_IT Staff Augmentation Only", "location": "Remote Job   | Posted on 11/04/2024", "location_type": "remote", "job_type": null, "min_experience": 1, "max_experience": 3, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009030096/SDR_IT-Staff-Augmentation-Only?source=CareerSite", "description": "This is a remote position. Lead Generation & Prospecting: Identify, research, and engage potential clients in international markets (primarily the USA, Canada, Middle East and APAC Regions) through cold calling, LinkedIn outreach, and email prospecting. Qualifying Leads: Engage with prospects to determine staffing needs, qualifying leads based on factors like company size, staffing requirements, and budget. Sales Tools Utilization: Use LinkedIn Sales Navigator, Apollo, ZoomInfo, and HubSpot to research, track, and manage leads for optimal outreach and conversion. Pipeline & CRM Management: Maintain accurate and detailed records of lead interactions in HubSpot, ensuring streamlined communication across teams. Relationship Building: Foster initial relationships with prospective clients, providing a positive and professional brand impression and facilitating a smooth handover to the sales team. Collaboration: Partner with sales and marketing teams to refine outreach strategies, share insights from international markets, and contribute to continuous improvement in targeting and messaging. Key Performance Indicators (KPIs): Number of qualified leads generated per month Conversion rate from outreach to scheduled meetings Timeliness and consistency of follow-ups CRM data accuracy and management Requirements Bachelor’s Degree in Business, Marketing, or a related field (or equivalent experience). 1-3 years of experience in sales, lead generation, or business development, with an emphasis on international or IT staffing. Fluent in English, with strong written and verbal communication skills to engage with international clients. Experience with cold calling as a primary outreach method, with proven success in lead generation. Familiarity with CRM systems, specifically HubSpot, as well as LinkedIn Sales Navigator, Apollo, or ZoomInfo. Organized and detail-oriented, with the ability to manage multiple leads and follow-up activities. Self-driven and target-oriented individual, with a record of meeting or exceeding KPIs. Experience working with clients in the USA or Canada is highly desirable.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009278307/Senior-Recruiter_North-America?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Senior Recruiter_North America", "location": "India   | Posted on 11/29/2024", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009278307/Senior-Recruiter_North-America?source=CareerSite", "description": "Work with the top management, provide innovative solutions to solve complex problems and be proactive in your approach. Have knowledge on various US hiring terms such as 1099, C2C and W2. Knowledge on Boolean search and keyword search using recruitment tools. Create an executive pipeline and maintain relationship with potential candidates. Source, screen, and shortlist candidates for various technical and non-technical positions across Canada, and the USA. Collaborate with hiring managers to understand staffing requirements and develop sourcing strategies. Utilize Zoho Recruit for candidate management, job postings, and tracking recruitment progress. Build a pipeline of qualified candidates through job boards, social media, and networking events. Ensure a seamless candidate experience from application to onboarding. Stay updated on market trends and hiring practices in Canada, and the USA. RequirementsProven experience in IT and Non IT recruitment. Expertise in bulk hiring and staff augmentation. Familiarity with international hiring practices, particularly for Canada and the USA. Hands-on experience with Zoho Recruit or similar Applicant Tracking Systems (ATS) is desirableStrong communication and interpersonal skills. Ability to work in a fast-paced, target-driven environment.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009030138/Marketing-Manager_Outbound-Lead-Generation-IT-Staff-Augmentation-?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Marketing Manager_Outbound Lead Generation(IT Staff Augmentation)", "location": "Remote Job   | Posted on 10/28/2024", "location_type": "remote", "job_type": "full_time", "min_experience": 3, "max_experience": null, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009030138/Marketing-Manager_Outbound-Lead-Generation-IT-Staff-Augmentation-?source=CareerSite", "description": "This is a remote position. Lead Generation: Utilize outbound strategies, including cold calling, email campaigns, LinkedIn outreach, and social media engagement to generate high-quality leads. Prospecting: Identify potential clients in the IT sector who require staff augmentation services, focusing on mid to large enterprises. Market Research: Conduct research to understand market trends, client needs, and competitive landscape for targeted outreach. Qualification: Assess potential clients’ needs and qualify leads to ensure they align with our staffing solutions and pricing structures. Sales Outreach: Develop and maintain a database of potential clients and manage communication strategies, ensuring a regular follow-up cadence. Target Setting: Meet or exceed monthly and quarterly lead generation targets to drive revenue growth. Collaboration: Work closely with the Business Development and Sales teams to transition qualified leads and provide insights on client needs and market dynamics. Reporting: Track lead generation activities and results, providing reports and updates to management on a regular basis. Requirements Experience: 3+ years of outbound lead generation experience, ideally in IT services or IT staff augmentation. Knowledge of IT Staff Augmentation: Familiarity with the IT staff augmentation model, including typical client needs, staffing solutions, and the Time & Materials (T&M) pricing model. Skills: Strong communication skills, persuasive sales abilities, and the ability to build relationships with key stakeholders. Tools: Proficiency in CRM software (e. g. , Salesforce, HubSpot), LinkedIn Sales Navigator, and email marketing tools. Target Regions: Prior experience targeting regions such as North America, Europe, or the Middle East is preferred. Results-Oriented: Ability to work independently, meet lead generation targets, and adapt strategies as needed to maximize outreach effectiveness. Availability: Flexibility to work across different time zones if required to target specific regions", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009340549/IT-Lead?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "IT Lead", "location": "India   | Posted on 12/06/2024", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009340549/IT-Lead?source=CareerSite", "description": "Must be very strong in Azure cloud administration, Intune, Office 365 administration, ITSM tools, and advanced windows server troubleshooting. Hands-on experience with installation and troubleshooting of server-client issues. Administration experience with Atlassian products (like JIRA, Confluence, etc. ,). Have an excellent understanding of protocols and core networking concepts and ability to use diagnostic tools such as traceroute, ping, and nslookup etc. , Strong knowledge of implementing and effectively developing best practices including but not limited to IT operations, security practices, backup mechanisms and disaster recovery strategies. Must be aware, and willing to own all IT-related policies and compliance. Ability to drive the company infrastructure independently Ability to be a good listener, and to really understand a customer problem or question and help them solve it. Excellent writing skills. Most of your work will be written (email, documentation, etc. ). Other requirements: Should have at least two of the following certification: Any windows server certification + MS-102, AZ-104, AZ-800.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009405504/Senior-Apple-Vision-Pro-Mixed-Reality-Developer?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Senior Apple Vision Pro / Mixed Reality Developer", "location": "India   | Posted on 12/09/2024", "location_type": null, "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009405504/Senior-Apple-Vision-Pro-Mixed-Reality-Developer?source=CareerSite", "description": "Job Description: Senior Apple Vision Pro / Mixed Reality DeveloperAbout the Role: We are seeking an experienced Senior Apple Vision Pro / Mixed Reality Developer to lead the development of a cutting-edge medical application. This application will leverage Mixed Reality (MR) for surgical planning and intraoperative assistance, specifically targeting pediatric cardiology. As a senior technical lead, you will be responsible for defining the overall technical vision, driving innovation, managing complex 3D systems, and ensuring that the app is scalable, secure, and integrated into healthcare workflows & ensure that the app adheres to regulatory standards. Key Responsibilities: Technical Leadership: Lead the development of a mixed reality application using Apple Vision Pro, ensuring high-performance 3D visualization, interactive cutting planes, and real-time overlays for surgical planning. Architect Complex Systems: Design and implement the architecture for rendering 3D models, handling interactive MR features, and managing real-time medical data, with a focus on performance optimization and scalability. Innovate with MR: Drive the integration of the latest Mixed Reality technologies (e. g. , ARKit, RealityKit, Metal) and medical-specific features into the app, pushing the boundaries of what is possible in surgical visualization. Ensure Medical Compliance: Oversee the integration of HIPAA-compliant features for data security and ensure that the application aligns with necessary medical regulations (FDA, CE). Collaborate with Stakeholders: Work closely with medical professionals, UX/UI designers, backend developers, and regulatory teams to define requirements and ensure that the app aligns with real-world needs and clinical workflows. Data Integration: Oversee the integration of 3D heart models and medical imaging data (e. g. , DICOM), ensuring that the app works seamlessly with hospital systems (e. g. , EHR, PACS). Optimization and Real-Time Rendering: Lead the effort to optimize 3D rendering for high-quality visuals and low latency, ensuring smooth interactions and performance even with complex models and large datasets. Mentor & Manage Team: Provide guidance and mentorship to junior developers and foster a collaborative environment. Lead code reviews, ensure adherence to best development practices, and support team development through regular feedback and training. Testing and Quality Assurance: Define and implement robust testing strategies for MR environments, ensuring that the app functions reliably in clinical settings with minimal downtime and performance issues. RequirementsSkills & Qualifications: Technical Skills: Expert in Mixed Reality Development with deep knowledge of Apple Vision Pro, ARKit, RealityKit, and Metal for high-performance 3D rendering and interactions. 8+ years of experience in software development, including at least 5+ years in MR/AR development with a focus on real-time applications and 3D model rendering. Extensive experience in developing 3D visualization applications, with a strong understanding of geometry handling, cutting planes, real-time overlays, and interactive medical data integration. Proficiency in Swift and experience with SwiftUI for iOS and Vision Pro applications. Expertise in high-performance graphics rendering, particularly using Metal for complex 3D visualizations and optimization in MR applications.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009414592/Full-Cycle-Sales-Business-Development-Representative-Custom-Software-Development-?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Full Cycle Sales & Business Development Representative (Custom Software Development)", "location": "India   | Posted on 12/09/2024", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009414592/Full-Cycle-Sales-Business-Development-Representative-Custom-Software-Development-?source=CareerSite", "description": "About Us: We at Zazz are a dynamic custom software development company specializing in creating innovative, tailored solutions for our clients. Our goal is to redefine the way businesses operate through technology, and we’re looking for a passionate Sales & Business Development Representative to join our growing team. Key Responsibilities: Opportunity Identification & Lead Generation: Actively seek out and identify new business opportunities in the Canadian and US markets. Utilize outbound strategies such as cold emailing, LinkedIn outreach, and professional networking to generate high-quality leads. Full-Cycle Sales Ownership: Manage the entire sales process, from lead generation to deal closure. Conduct discovery calls and consultations to understand client needs and propose tailored software development solutions. Create and deliver compelling pitches, proposals, and presentations that address client requirements. Negotiate and finalize contracts, ensuring a win-win outcome for both parties. Client Relationship Management: Serve as the primary point of contact for clients throughout the sales cycle, building trust and ensuring client satisfaction. Work closely with internal teams to align on client expectations and ensure smooth project transitions post-sale. Sales Reporting & Pipeline Management: Maintain and update leads, opportunities, and sales progress in a CRM system. Provide accurate sales forecasts and reports to the management team. Track and analyze sales metrics to identify areas of improvement and optimize sales strategies. Qualifications: Experience: Minimum 3+ years of experience in full-cycle sales or business development, specifically in the custom software development industry, targeting Canadian and US clients. Sales Skills: Demonstrated ability to independently manage the entire sales cycle, from lead generation to closing deals. Communication: Exceptional verbal and written communication skills, with a persuasive and client-focused approach. Time Zone Alignment: Ability to work and communicate effectively during Eastern Standard Time (EST). Global Readiness: Self-motivated and disciplined to succeed in a remote, globally distributed work environment. Tech Savviness: Proficiency with sales tools, CRM platforms, and business communication platforms (e. g. , LinkedIn, email marketing tools). Why Join Us? Global Impact: Be part of a team that delivers cutting-edge software solutions to clients across North America. Flexibility: Enjoy a fully remote position with the ability to work from anywhere in the world. Growth Opportunities: Work in a fast-paced, innovative environment with significant career advancement potential. Inclusive Culture: Join a diverse and supportive team dedicated to professional and personal growth. How to Apply:", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009577461/Full-Cycle-Sales-Specialist-IT-Staff-Augmentation?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Full Cycle Sales Specialist IT Staff Augmentation", "location": "Remote Job   | Posted on 01/21/2025", "location_type": "remote", "job_type": null, "min_experience": 2, "max_experience": 3, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009577461/Full-Cycle-Sales-Specialist-IT-Staff-Augmentation?source=CareerSite", "description": "This is a remote position. Position Overview: We are seeking a dynamic and results-driven Full Cycle Sales Specialist with a strong focus on lead generation and deal closure within the IT Staff Augmentation domain. The ideal candidate will be a creative and persuasive communicator who thrives on building relationships and driving business growth. This role involves significant outreach on platforms such as LinkedIn, as well as cold outreach through other mediums, to identify and secure opportunities in IT Staff Augmentation. Key Responsibilities: 1. Lead Generation: Strategically identify and engage with potential clients using platforms like LinkedIn, email campaigns, and cold calls. Develop and maintain a robust pipeline of qualified leads. Conduct market research to understand client needs and industry trends. 2. Client Engagement: Build strong, trust-based relationships with potential clients. Understand client requirements and present tailored IT staff augmentation solutions. 3. Deal Closure: Negotiate contracts and close deals with a focus on building long-term client relationships. Collaborate with internal teams to ensure seamless onboarding and delivery of services. 4. Reporting & Strategy: Regularly report on lead generation metrics, sales progress, and market feedback. Contribute to the development of sales strategies and identify areas for improvement. Requirements Bachelor's degree in business administration, Marketing, or a related field. 2-3 years of proven experience in IT Staff Augmentation, with a strong focus on lead generation and sales. Exceptional written and verbal communication skills. Proficiency in using LinkedIn and other outreach tools for business development. Self-motivated, goal-oriented, and able to work independently. Knowledge of the IT industry, recruitment processes, and staff augmentation services.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009488539/Data-Engineer-6%E2%80%938-Years---LA?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Data Engineer (6–8 Years) - LA", "location": "Remote Job   | Posted on 01/17/2025", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009488539/Data-Engineer-6%E2%80%938-Years---LA?source=CareerSite", "description": "This is a remote position. Required Qualifications Bachelor’s or Master’s degree in Computer Science, Engineering, or a related field. 5+ years of professional experience as a Data Engineer or in a related role. Proficiency in Python for data processing and pipeline development. Strong knowledge of SQL and experience working with relational databases (e. g. , PostgreSQL, MySQL, or Snowflake). Hands-on experience with data pipeline tools and frameworks (e. g. , Apache Spark, Kafka, or Flink). Familiarity with cloud data solutions such as AWS S3, Redshift, Google BigQuery, or Azure Data Lake. Experience with CI/CD tools and version control systems like Git. Strong problem-solving skills and attention to detail. Excellent communication and collaboration skills. Preferred Qualifications Familiarity with NoSQL databases (e. g. , MongoDB, Cassandra). Experience with containerization technologies (Docker, Kubernetes). Knowledge of big data processing tools and frameworks (e. g. , Hadoop). Certifications in cloud platforms (AWS Certified Data Analytics, Azure Data Engineer, etc. ). Familiarity with data visualization tools and BI platforms (e. g. , Tableau, Power BI)", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009589254/Low-Code-No-Code-Technical-Consultant?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Low-Code/No-Code Technical Consultant", "location": "Remote Job   | Posted on 02/06/2025", "location_type": "remote", "job_type": "contract", "min_experience": 2, "max_experience": 2, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009589254/Low-Code-No-Code-Technical-Consultant?source=CareerSite", "description": "This is a remote position. We are seeking a seasoned Low-Code/No-Code Technical Consultant to lead our efforts in deploying and managing enterprise-grade applications using leading low-code/no-code platforms. This role is ideal for a professional who can leverage these platforms to deliver rapid, cost-effective, and scalable solutions while ensuring alignment with business needs and technical governance. Key Responsibilities: Develop and deploy enterprise applications using platforms such as Power Platform, Appian, and Mendix, ensuring they meet high standards of functionality and design. Implement advanced integrations with ERP, CRM, and third-party APIs to enhance application capabilities and automate business processes. Establish and maintain governance frameworks to manage and scale applications effectively in low-code/no-code environments. Utilize rapid prototyping to create cost-saving solutions, significantly reducing development time and expense. Deliver applications 30%-50% faster than traditional development methods, demonstrating clear time-to-market advantages. Translate complex business requirements into functional enterprise solutions, focusing on delivering measurable outcomes for clients. Understand and communicate the commercial implications of licensing and total cost of ownership (TCO) for low-code/no-code platforms. Requirements Skills and Qualifications Mastery of enterprise-grade low-code/no-code platforms such as Power Platform, Appian, and Mendix. Proven expertise in integrating these platforms with ERP, CRM systems, and external APIs. Proficiency in developing governance frameworks for managing scaling applications in low-code/no-code environments. Demonstrated ability to deliver enterprise applications significantly faster than traditional coding methods. Strong commercial acumen, with a clear understanding of the financial aspects of low-code/no-code implementations. Excellent problem-solving skills and the ability to translate business needs into technical solutions. Education and Experience Bachelor’s degree in Computer Science, Information Technology, or a related field. At least 8+ years of experience in IT development, with a strong focus on low-code/no-code technologies and at least 2 years in a consulting or strategic role.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009589163/Solutions-Architect---Mobile-App-Development?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Solutions Architect - Mobile App Development", "location": "India   | Posted on 12/23/2024", "location_type": null, "job_type": null, "min_experience": 2, "max_experience": 10, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009589163/Solutions-Architect---Mobile-App-Development?source=CareerSite", "description": "We are seeking a Solutions Architect with extensive experience in mobile app development to join our dynamic team. This role involves leading the design and development of innovative mobile applications using cross-platform and native technologies. The ideal candidate will have a strong background in developing scalable architectures and implementing effective monetization strategies. Key Responsibilities: Lead the architecture and design of new mobile applications and enhance existing apps using React Native, Flutter, Swift, and Kotlin. Develop and execute a mobile product roadmap focused on achieving key business KPIs. Implement scalable and maintainable mobile backend solutions using modern microservices architecture. Optimize app functionality with offline-first strategies to enhance user experience under various network conditions. Utilize app analytics tools like Firebase and Mixpanel to gather insights and drive data-informed decisions for user engagement strategies. Design and implement robust monetization models, including in-app purchases, advertisements, and subscription services. Ensure the highest level of security for all mobile applications by incorporating SSL pinning, anti-debugging techniques, and code obfuscation. Provide strategic recommendations on cost-effective solutions while balancing technical feasibility and market demand. RequirementsSkills and Qualifications: Proven expertise in cross-platform frameworks (React Native, Flutter) and native app development (Swift for iOS, Kotlin for Android). Strong experience in designing scalable architectures and implementing offline-first strategies. Proficiency in mobile app analytics tools (Firebase, Mixpanel) and advanced knowledge of monetization strategies. Demonstrable experience with mobile security practices, including SSL pinning, anti-debugging, and code obfuscation. Proven ability to define and execute mobile product roadmaps aligned with business objectives. Experience developing mobile-first strategies for both B2C and B2B markets. Excellent commercial acumen with a focus on delivering cost-effective and market-driven solutions. Education and Experience: Bachelor's or Master's degree in computer science, Information Technology, or a related field. Minimum of 8-10 years of experience in mobile app development with at least 2 years in a solutions architect or similar leadership role.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009589193/Solutions-Architect---Backend-Development?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Solutions Architect - Backend Development", "location": "India   | Posted on 12/23/2024", "location_type": null, "job_type": null, "min_experience": 2, "max_experience": 5, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009589193/Solutions-Architect---Backend-Development?source=CareerSite", "description": "We are seeking a highly skilled Solutions Architect specializing in backend development to join our technology team. This role is crucial for driving the development of robust, scalable, and secure backend services for our business-critical applications. The ideal candidate will bring a deep understanding of distributed systems, cloud-native principles, and financial-grade security protocols. Key Responsibilities: Design and implement scalable and high-availability backend systems using event-driven architectures and real-time processing. Optimize backend services for high-stakes applications such as e-commerce platforms and payment systems. Ensure compliance with financial-grade security protocols like PCI-DSS and GDPR. Develop cost-effective strategies for backend scaling using cloud services, tailored to the specific needs of the business. Map backend scalability plans to business growth forecasts, ensuring the technological infrastructure supports long-term objectives. Conduct technical audits and present back findings and performance improvements to key stakeholders. Manage and optimize total cost of ownership (TCO) for backend ecosystems. RequirementsSkills and Qualifications: Advanced understanding of distributed systems, event-driven architectures, and real-time data processing. Expertise in designing high-availability systems with robust load balancing and failover capabilities. Proficiency in optimizing backend services for critical business operations, with a focus on scalability and security. Familiarity with compliance protocols such as PCI-DSS and GDPR. Demonstrable experience in cost-effective backend scaling strategies and cloud service integrations. Ability to effectively map technical capabilities to business growth and scalability requirements. Experience in conducting technical audits and implementing strategic improvements. Education and Experience: Bachelor’s or Master’s degree in Computer Science, Information Technology, or a related field. Minimum of 8+ years of experience in backend development, with at least 2-5 years in a solutions architect or senior development role.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009702184/Growth-Marketing-Analyst?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Growth Marketing Analyst", "location": "Remote Job   | Posted on 01/07/2025", "location_type": "remote", "job_type": null, "min_experience": 2, "max_experience": 4, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009702184/Growth-Marketing-Analyst?source=CareerSite", "description": "This is a remote position. Growth Strategy & Execution: Develop and implement data-driven strategies to drive customer acquisition, engagement, and retention. Collaborate with product, design and engineering teams to align growth initiatives with overall business goals. Email Marketing Optimization: Launch, manage, and optimize multi-channel marketing campaigns, with a focus on customer experience, conversion and retention. Run A/B tests, analyze campaign performance, and continuously refine to improve key metrics such as open rates, click-through rates (CTR), and conversion rates. Data Analysis & Reporting: Analyze customer behavior, product performance, and market trends to provide insights on how to maximize user conversion and identify growth opportunities. Track and report on key growth metrics, including CAC (Customer Acquisition Cost), CLV, email campaign performance, and upsell funnel success. Present data insights to key stakeholders and provide actionable recommendations for future growth initiatives. Required Skills:We're looking for someone who is organized, self-motivated, and takes ownership of their projects and tasks. The required skills are Education: Bachelor’s degree in Marketing, Business, Economics, Data Science, or a related field. Experience: 2-4 years of experience in a growth marketing, product marketing, or marketing analyst role, preferably in a startup or high-growth company. Marketing Channels: Proven experience in email marketing, and familiarity with other digital marketing channels such as paid media, SEO, and social media. Data Analysis: Strong analytical skills with the ability to interpret data, identify trends, and provide actionable insights. Tools & Platforms: Experience with marketing and analytics tools such as Google Analytics, Tableau, ActiveCampaign and other CRM/email marketing tools would be preferred. Communication: Excellent communication skills, strong analytical and problem solving skills, be able to find good solutions to simple and complex problems, and known for getting things done. Team Collaboration: Strong collaboration skills to work effectively across teams, including product, sales, and engineering.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009589391/IoT-Solutions-Architect?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "IoT Solutions Architect", "location": "Remote Job   | Posted on 02/04/2025", "location_type": "remote", "job_type": null, "min_experience": 8, "max_experience": 8, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009589391/IoT-Solutions-Architect?source=CareerSite", "description": "This is a remote position. We are seeking a skilled IoT Solutions Architect to develop and deploy cutting-edge IoT ecosystems for our industrial applications. The ideal candidate will have comprehensive expertise in IoT, particularly in predictive maintenance, edge computing, and regulatory compliance. This role involves designing IoT solutions that optimize operational efficiency, ensure compliance, and create substantial business value. Key Responsibilities Design and implement industrial IoT solutions with advanced predictive maintenance capabilities to enhance asset management and reduce downtime. Utilize edge computing frameworks such as AWS Greengrass and Azure IoT Edge to process data locally and reduce latency. Ensure IoT solutions comply with global regulatory standards, including GDPR and HIPAA, addressing data security and privacy concerns effectively. Develop strategies to monetize IoT data analytics, transforming data into actionable insights that drive business value. Deliver cost-efficient IoT ecosystems that meet predefined KPIs, demonstrating tangible benefits and ROI. Align IoT solution proposals with current market trends to ensure relevance and competitiveness. Evaluate and implement scalable IoT architectures that support business growth and adapt to evolving technological landscapes. Requirements Skills and Qualifications Advanced expertise in designing and implementing industrial IoT solutions, especially for predictive maintenance. Proficiency in edge computing technologies, including AWS Greengrass and Azure IoT Edge. Strong knowledge of IoT regulatory compliance globally, ensuring solutions meet stringent standards. Experience in monetizing IoT data for enhanced business decision-making and operational improvements. Proven track record of developing cost-efficient IoT systems with measurable impacts on business operations. Strong commercial acumen in IoT, with a keen understanding of market trends and ROI metrics. Excellent problem-solving abilities and a strategic mindset for scaling IoT solutions. Education and Experience: Bachelor’s or Master’s degree in Computer Science, Information Technology, or a related field with a specialization in IoT. At least 8 years of experience in IoT architecture, with significant projects in industrial settings. Relevant certifications in IoT technologies and frameworks are preferred.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/*****************/Marketing-Data-Specialist?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Marketing Data Specialist", "location": "India   | Posted on 01/07/2025", "location_type": "flexible", "job_type": null, "min_experience": 1, "max_experience": 2, "apply_link": "https://careers.zazz.io/jobs/Careers/*****************/Marketing-Data-Specialist?source=CareerSite", "description": "Assist with data enrichment, acquisition, standardization, and segmentation efforts. Aid in the management of lead and contact creation and routing workflows, ensuring data flows smoothly between different CRMs and lead generation platformsEnsure lead creation and conversion processes across Sales and Marketing are optimized and adhere to internal and external data health and compliance requirements. Support Growth Marketing Analyst in implementing data governance policies and practices. Oversee intake, perform related actions, and ensure positive outcomes of lead generation programs with goal to automate process where appropriate. Collaborate with marketing, sales, and customer success teams to support data-driven initiatives and reporting. Requirements1-2 years of experience in data management, marketing operations, sales operations, or customer success operationsFamiliarity with CRM systems (e. g. , Salesforce) and data management tools like Apollo, Hubspot, Zoominfo etc. Strong attention to detail. Ability to work independently and collaboratively with a global team. Analytical mindset adept at problem finding and problem solvingAble to work flexible hours as required by business priorities", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009738574/Strategic-Resource-Allocation-Specialist?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Strategic Resource Allocation Specialist", "location": "Remote Job   | Posted on 01/10/2025", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009738574/Strategic-Resource-Allocation-Specialist?source=CareerSite", "description": "This is a remote position. We are looking for an experienced and strategic Resource Manager to optimize our workforce utilization, manage the bench effectively, and ensure the strategic allocation of resources to projects. The ideal candidate will work closely with Project Managers, HR, and Leadership to maximize productivity, reduce bench costs, and foster a high-performance culture. Responsibilities: 1. Bench Management: • Continuously monitor and maintain an updated record of employees on the bench, including their skills, experience, and availability. • Develop strategies to minimize bench time by proactively identifying opportunities for allocation. • Plan for cross-training and skill development for underutilized resources. 2. Strategic Resource Allocation: • Collaborate with Project Managers and Delivery Heads to forecast resource requirements and allocate resources effectively. • Prioritize high-performing employees (based on KPIs) for critical and high-value projects. • Identify opportunities for internal transfers or redeployment based on project pipeline and skills match. 3. Performance Monitoring and Management: • Identify low-performing individuals on the bench and work with HR to implement Performance Improvement Plans (PIPs). • Provide recommendations for terminations based on consistent underperformance or lack of alignment with organizational needs. • Develop plans to retain high-potential resources through meaningful engagements. 4. Cost Optimization: • Analyze and reduce bench costs by optimizing resource allocation and improving utilization rates. • Implement data-driven insights to align workforce planning with budget constraints. 5. Collaboration: • Act as the central point of contact between delivery teams, sales, and HR for resource allocation decisions. • Partner with HR to define upskilling programs for bench employees and identify key learning gaps. • Support leadership with workforce planning reports and analytics to aid strategic decision-making. 6. Forecasting and Reporting: • Provide regular reports on resource allocation, bench strength, utilization, and associated costs. • Use forecasting tools to anticipate future bench challenges and align resource supply with demand. 7. Tool and Process Optimization: • Utilize workforce management tools (e. g. , Hub Planner, Float, or Mavenlink) to track and plan resources. • Recommend process improvements and tools to streamline resource allocation and bench management. Skills: • Strong analytical and problem-solving skills to manage complex resource allocation scenarios. • Proficiency in workforce management tools, data analysis, and Excel. • Excellent communication and interpersonal skills to collaborate with cross-functional teams. • Ability to manage sensitive situations (e. g. , PIPs, terminations) with professionalism and empathy. ​", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000009767438/Enterprise-Solutions-Consultant?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Enterprise Solutions Consultant", "location": "Remote Job   | Posted on 01/13/2025", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": 7, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000009767438/Enterprise-Solutions-Consultant?source=CareerSite", "description": "This is a remote position. Position: Full-Cycle Sales Representative – Enterprise Technologies We are seeking a highly motivated and experienced Full-Cycle Sales Representative to join our team. This role is pivotal in driving revenue by managing the entire sales process—from lead generation to deal closure—with a focus on enterprise technologies. The ideal candidate will possess a strong background in sales, technical acumen in enterprise solutions, and the ability to align our offerings with client needs. Key Responsibilities Lead Generation and Prospecting Proactively identify and target potential clients through various channels, including networking, referrals, and digital outreach. Build and maintain a robust pipeline of qualified leads. Client Engagement and Discovery Develop a deep understanding of client needs, challenges, and goals. Effectively communicate complex technical concepts and present tailored solutions. Proposal Development and Solution Selling Craft compelling, customized proposals with accurate effort estimations. Address client pain points with value-driven solutions, positioning Zazz as the partner of choice. Deal Negotiation and Closure Navigate complex sales cycles with enterprise clients, ensuring alignment of expectations. Expertly manage negotiations to close deals, meeting or exceeding sales targets. Market Analysis and Strategy Stay abreast of industry trends, competitive landscape, and emerging technologies to identify new business opportunities. Provide strategic insights to optimize the sales process and expand market reach. Requirements Enterprise Technologies: Experience selling solutions in areas such as cloud services, security and compliance, data engineering platforms, and implementation services. Technical Proficiency: Strong understanding of enterprise technology stacks and their applications in solving business challenges. Sales Acumen: Proven track record in full-cycle sales roles, with the ability to manage all stages of the sales process independently. Qualifications Bachelor's degree in business, Marketing, Computer Science, or a related field (preferred). Minimum 5-7 years of experience in enterprise technology sales, with a focus on lead generation and solution selling. Exceptional communication and negotiation skills. Ability to perform effort estimations and craft comprehensive project proposals. Proactive, strategic thinker with a results-oriented mindset. Benefits Competitive salary and performance-based incentives. Opportunities for professional growth and career advancement. A collaborative and innovative work environment. Remote work flexibility and a focus on work-life balance.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/*****************/Sales-Development-Representative---Global?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Sales Development Representative - Global", "location": "Remote Job   | Posted on 01/15/2025", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": 5, "apply_link": "https://careers.zazz.io/jobs/Careers/*****************/Sales-Development-Representative---Global?source=CareerSite", "description": "This is a remote position. Roles and responsibilities Own a set of named accounts and execute campaigns to achieve associated pipeline- creation metrics. Create target prospects lists for key accounts. Research key contacts, leads and decision makers within the accounts and work on lead nurturing and building relationships that help you create opportunities. Responsible for generating initial meetings for the Sales Team by reaching out to prospective companies via cold emails, social outreach and/or phone calls. Ensure successful follow through of the outbound cadence cycle by maintaining accurate activity and lead qualification information within the CRM. Work on nurturing the leads generated from various Marketing campaigns and take them through the cycle of qualification to transfer to the Sales teams. Develop understanding of Zazz's staff augmentation offerings and articulate Zazz's business value proposition to decision makers in global companies to assess buying interest. RequirementsRequirements 3-5 years of Lead Generation and Business Development experience in Staff Augmentation services company. Comfortable with Cold calls and Emails, and engaging with a global target audience across time zones. Expertise with sales tools like Salesforce, LinkedIn Sales Navigator, Apollo, Zoominfo, Hubspot etc. Strong written and oral communication skills. Adaptable, professional, courteous, motivated with a strong work ethic. Work in a highly demanding and competitive environment. Has shown demonstrable ownership of accounts and over-achievement of pipeline creation targets in previous stints.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/*****************/Sr-UI-UX-Designer?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Sr. UI/UX Designer", "location": "Remote Job   | Posted on 01/20/2025", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://careers.zazz.io/jobs/Careers/*****************/Sr-UI-UX-Designer?source=CareerSite", "description": "This is a remote position. Responsibilities: Collaborate with cross-functional teams, including product managers, developers, and stakeholders, to understand project requirements and user needs. Design and deliver wireframes, prototypes, and high-fidelity visuals that effectively communicate the desired user experience and meet business objectives. Create intuitive, aesthetically appealing, and user-friendly interfaces that align with the company’s brand and product vision. Develop user flows, information architectures, and interaction models to ensure seamless and logical user experiences. Conduct usability testing, A/B testing, and heuristic evaluations to validate design decisions and identify areas for improvement. Gather and analyze user feedback through surveys, interviews, and other research methods to refine designs and address usability challenges. Lead client interactions during the design phase, acting as a point of contact to present concepts, gather feedback, and ensure alignment with client expectations. Collaborate with the development team to ensure the accurate implementation of designs and resolve any design-related queries during development. Stay updated on the latest UI/UX trends, tools, techniques, and industry best practices, sharing insights and knowledge within the team. Advocate for user-centered design principles, fostering a culture of empathy for users across the organization. Work closely with stakeholders to gather requirements, provide design recommendations, and address concerns regarding user experience. Manage project timelines and deliverables, ensuring that design milestones are met without compromising quality. Maintain and update design systems, ensuring consistency and scalability across all digital products. Provide mentorship and guidance to junior designers, helping them grow in their roles and improve their design skills. Proactively identify and address potential design challenges and risks, ensuring the delivery of optimal solutions. ​ Requirements Bachelor's degree in Design, or a related field (or equivalent practical experience). 3+ years of professional experience in UI/UX design, preferably in a software development or technology-driven environment. Strong portfolio showcasing a range of UI/UX design projects, with a focus on user-centered design principles and problem-solving Proficiency in design tools such as Sketch, Adobe XD, Figma, or similar. Solid understanding of user-centered design processes and methodologies. Experience conducting user research, usability testing, and gathering user feedback. Knowledge of responsive and mobile-first design principles. Excellent visual design skills, including typography, color theory, and layout composition. Excellent communication and collaboration skills to work effectively within a team and across departments. Ability to manage multiple projects and priorities in a fast-paced environment. Up-to-date knowledge of design trends, industry tools, and emerging UI/UX technologies.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000010616379/Cloud-Engineer-Azure-5-to-8-years---Latin-America?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Cloud Engineer (Azure) (5 to 8 years) - Latin America", "location": "Remote Job   | Posted on 02/21/2025", "location_type": "remote", "job_type": "full_time", "min_experience": 5, "max_experience": 8, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000010616379/Cloud-Engineer-Azure-5-to-8-years---Latin-America?source=CareerSite", "description": "This is a remote position. We are looking for a skilled Cloud Engineer with 5-8 years of experience specializing in Microsoft Azure, cloud infrastructure automation, and modern DevOps practices. The ideal candidate will have expertise in Infrastructure as Code (IaC) using Terraform, container orchestration with Kubernetes and Docker, and cloud security best practices. This role involves designing, deploying, and maintaining high-availability cloud solutions while ensuring seamless CI/CD automation and scalability. RequirementsKey ResponsibilitiesAzure Cloud Architecture: Design, implement, and maintain cloud solutions in Microsoft Azure, ensuring reliability, scalability, and security. Infrastructure as Code (IaC): Develop and manage infrastructure using Terraform and other automation tools. Containerization & Orchestration: Deploy, manage, and optimize Kubernetes clusters and Docker containers for microservices-based applications. CI/CD Automation: Build and optimize CI/CD pipelines for continuous integration and delivery, ensuring efficient and automated deployments. Cloud Security & Compliance: Implement best practices for cloud security, identity management, and compliance in Azure environments. Monitoring & Optimization: Use tools like Azure Monitor, Prometheus, Grafana, and Log Analytics to track system performance and troubleshoot issues proactively. Collaboration & Documentation: Work closely with DevOps, development, and security teams to streamline cloud infrastructure processes. Document best practices, architectures, and configurations. Cost Optimization: Analyze and optimize cloud usage to reduce costs while maintaining high performance. Required Skills & Qualifications5-8 years of experience in Azure cloud engineering and infrastructure management. Expertise in Azure services (e. g. , Azure Virtual Machines, Azure Kubernetes Service (AKS), Azure DevOps, Azure Functions, Azure Networking). Strong hands-on experience with Terraform for infrastructure automation. Proficiency in Kubernetes and Docker for containerized applications. Experience with CI/CD tools (Azure DevOps, Jenkins, GitHub Actions, GitLab CI/CD, or similar). Solid understanding of networking concepts, security best practices, and identity management in cloud environments. Knowledge of scripting/programming languages like Python, PowerShell, or Bash for automation. Familiarity with logging, monitoring, and observability tools (Azure Monitor, Prometheus, Grafana, ELK Stack). Experience with cloud cost optimization and governance in an enterprise environment. Excellent problem-solving skills and the ability to work in a fast-paced, collaborative environment. Preferred Certifications• Microsoft Certified: DevOps Engineer Expert• Certified Kubernetes Administrator (CKA)• HashiCorp Certified: Terraform AssociateAdditional Considerations• Experience in multi-cloud and hybrid cloud architectures. • Exposure to FinOps and cost-optimization strategies for cloud infrastructure. • Contributions to open-source DevOps tools and repositories are a plus. • Experience in serverless architecture and automation-driven deployments. BenefitsSalary & Benefits• Competitive Compensation (Market-leading pay based on skills & experience). • Remote Work Flexibility (Client-based work schedules). • Career Growth – Upskilling programs, certification support. • Paid Time Off (PTO) – Vacation, sick leave, and holidays (Full-time candidates)", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000010616426/Cloud-Engineer-AWS---Latin-America-5-to-8-years-?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Cloud Engineer (AWS) - Latin America (5 to 8 years)", "location": "Remote Job   | Posted on 02/21/2025", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000010616426/Cloud-Engineer-AWS---Latin-America-5-to-8-years-?source=CareerSite", "description": "This is a remote position. About the Role:We are seeking a highly skilled Cloud Engineer with expertise in AWS migration and Snowflake integrations to join our dynamic team. The ideal candidate will play a crucial role in designing, implementing, and optimizing cloud-based data solutions, with a strong focus on AWS services and data platform integrations rather than core infrastructure management. Role Description: We are looking for experienced AWS Engineers for Implement Data Ingestion and management pipelines for deploying AI models and data pipelines. Strong understanding of data architecture Design, build, and maintain scalable, secure, and resilient cloud-based infrastructure on AWS using Infrastructure as Code (IaC) tools such as CloudFormation and Terraform. Automate complex deployments, monitoring, and security tasks to ensure seamless operation of production systems. Lead initiatives to improve system performance, availability, scalability, and security across AWS services. Serve as the expert on AWS networking, security, and monitoring, implementing best practices for cloud governance, compliance, and security policies. Collaborate with product managers and development teams to ensure infrastructure aligns with the product vision and roadmaps. Leverage AWS-native and third-party observability tools to monitor system performance and ensure proper incident management and response. Act as a technical mentor for junior engineers, providing guidance on cloud infrastructure, Python scripting, automation strategies, and DevOps best practices. Troubleshoot and resolve complex production issues, performing root cause analysis and implementing long-term solutions. Stay up-to-date with the latest AWS services, features, and trends, continuously evolving the team’s cloud capabilities. RequirementsRequired Skills & Qualifications: 5+ years of experience working in cloud engineering, with a primary focus on AWS environments. Strong analytical skills to effectively analyze data requirements and troubleshoot data related issues. Good documentation and coaching practice. Deep understanding and hands-on experience with Infrastructure as Code (IaC) using CloudFormation / Terraform. Proven track record of automating complex cloud deployments and scaling services in production environments. Expertise in AWS networking (VPC, subnets, security groups, etc. ), cloud security (IAM, encryption, security compliance), and cloud-native monitoring tools (CloudWatch, Grafana, Datadog, Prometheus, etc. ). Strong Python programming skills for automation and infrastructure management. Experience with containerization technologies (e. g. , Docker, ECS, EKS) and orchestration. A collaborative and mentoring mindset, with the ability to guide and support junior engineers. Excellent communication skills, with the ability to work cross-functionally and influence technical direction. AWS Certified Solutions Architect or similar certification is preferred. Data modeling & warehousing Scalable and robust data pipeline construction and ETL tools. Strong understanding of data security, compliance and governance.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000010616468/Artificial-Intelligence-Engineer-5-to-8-years---Latin-America?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Artificial Intelligence Engineer (5 to 8 years) - Latin America", "location": "Remote Job   | Posted on 02/21/2025", "location_type": "remote", "job_type": null, "min_experience": 8, "max_experience": 8, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000010616468/Artificial-Intelligence-Engineer-5-to-8-years---Latin-America?source=CareerSite", "description": "This is a remote position. Job Summary:We are seeking a highly skilled Artificial Intelligence Engineer with 5 to 8 years of experience to join our innovative team. The ideal candidate will be responsible for designing, developing, and deploying AI and machine learning models to solve complex business challenges. This role requires expertise in deep learning, natural language processing (NLP), computer vision, and AI-driven automation solutions. ​ Key Responsibilities: Develop, implement, and optimize machine learning models, deep learning algorithms, and AI-driven solutions. Design and maintain scalable AI architectures to support real-time decision-making and automation. Work with structured and unstructured data sources to extract insights and improve AI models. Collaborate with data scientists, software engineers, and business stakeholders to integrate AI solutions into production environments. Utilize NLP techniques for text processing, entity recognition, sentiment analysis, and chatbot development. Apply computer vision techniques for image recognition, object detection, and video analytics. Optimize and fine-tune AI models for accuracy, efficiency, and scalability. Stay updated with the latest AI trends, frameworks, and best practices to drive innovation. Develop and maintain documentation for AI models, data pipelines, and deployment workflows. Ensure compliance with ethical AI practices, data privacy regulations, and security standards. ​ RequirementsRequired Qualifications: Bachelor’s or Master’s degree in Computer Science, Artificial Intelligence, Machine Learning, Data Science, or a related field. 5 to 8 years of hands-on experience in AI and machine learning development. Strong programming skills in Python (preferred), R, or Java. Proficiency in ML/DL frameworks such as TensorFlow, PyTorch, Keras, Scikit-learn, OpenCV, and Hugging Face. Experience with NLP, computer vision, reinforcement learning, or predictive analytics. Familiarity with cloud platforms (AWS, Azure, or Google Cloud) and AI model deployment using Docker, Kubernetes, or serverless architectures. Strong experience in handling large-scale datasets, feature engineering, and data preprocessing. Experience with MLOps, CI/CD pipelines for AI models, and version control using Git. Knowledge of big data technologies like Hadoop, Spark, or Kafka is a plus. Excellent problem-solving skills, analytical mindset, and ability to work in an agile development environment. Strong communication and collaboration skills to work with cross-functional teams. Preferred Qualifications: Experience in AI model explainability, ethical AI, and responsible AI practices. Understanding of edge AI, federated learning, or hybrid AI architectures. Certification in AI/ML (e. g. , TensorFlow Developer, AWS Certified Machine Learning) is a plus. Experience with LLMs (Large Language Models) like GPT, BERT, or T5 is advantageous. ​", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000010616451/DevOps-Engineer-Docker-and-Kubernetes-certified---Latin-America-5-8-years-?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "DevOps Engineer (Docker and Kubernetes certified) - Latin America (5-8 years)", "location": "Remote Job   | Posted on 02/21/2025", "location_type": "remote", "job_type": "full_time", "min_experience": 5, "max_experience": 8, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000010616451/DevOps-Engineer-Docker-and-Kubernetes-certified---Latin-America-5-8-years-?source=CareerSite", "description": "This is a remote position. Job Summary:We are seeking a skilled DevOps Engineer with 5-8 years of experience and Docker & Kubernetes certification to join our dynamic team. The ideal candidate will be responsible for automating, deploying, and managing cloud infrastructure and CI/CD pipelines to support high-availability applications. You will play a key role in enhancing system reliability, scalability, and security. Key Responsibilities: Design, implement, and maintain CI/CD pipelines using tools such as Jenkins, GitLab CI/CD, or CircleCI. Deploy, manage, and troubleshoot containerized applications using Docker and Kubernetes. Automate infrastructure provisioning using Terraform, Ansible, or CloudFormation. Monitor system performance and troubleshoot issues using Prometheus, Grafana, ELK Stack, or Datadog. Collaborate with development and operations teams to improve deployment efficiency and system reliability. Optimize cloud environments (AWS, Azure, or GCP) for performance, cost, and security. Implement security best practices including IAM policies, encryption, and vulnerability scanning. Manage and optimize Kubernetes clusters using Helm charts and Kubernetes Operators. Develop and maintain infrastructure as code (IaC) and configuration management scripts. Perform regular system updates, patching, and backups to ensure system integrity. Lead DevOps initiatives and mentor junior team members on best practices and new technologies. RequirementsRequired Qualifications: 5-8 years of hands-on experience in DevOps, Cloud Engineering, or Site Reliability Engineering (SRE). Docker Certified Associate (DCA) and/or Certified Kubernetes Administrator (CKA) required. Strong experience in Kubernetes (K8s) deployment, scaling, and monitoring. Proficiency in scripting languages such as Bash, Python, or Go. Experience with infrastructure-as-code (IaC) tools like Terraform, Ansible, or CloudFormation. Hands-on experience with AWS, Azure, or Google Cloud Platform (GCP). Knowledge of networking concepts, DNS, load balancing, and security best practices. Strong understanding of GitOps principles and tools such as ArgoCD or Flux. Experience working with logging and monitoring tools (Prometheus, ELK, Datadog, etc. ). Solid understanding of Linux system administration and troubleshooting. Preferred Qualifications: Certified Kubernetes Security Specialist (CKS) or additional cloud certifications (AWS, Azure, or GCP). Experience with service mesh technologies (Istio, Linkerd, Consul). Familiarity with serverless computing (AWS Lambda, Google Cloud Functions, etc. ). Exposure to GitOps workflows and Kubernetes operators. Prior experience working in a DevSecOps environment.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000010616555/Machine-Learning-Engineer-5-to-8-years---Latin-America?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Machine Learning Engineer (5 to 8 years) - Latin America", "location": "Remote Job   | Posted on 02/21/2025", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": 8, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000010616555/Machine-Learning-Engineer-5-to-8-years---Latin-America?source=CareerSite", "description": "This is a remote position. Job Summary:We are seeking an experienced Machine Learning Engineer to join our dynamic team. The ideal candidate will have 5-8 years of hands-on experience in designing, developing, and deploying machine learning models and AI-driven solutions. You will work closely with data scientists, software engineers, and product teams to build scalable ML solutions that drive business impact. Key Responsibilities: Develop and deploy machine learning models for various applications, including recommendation systems, NLP, and predictive analytics. Design and implement scalable data pipelines and model training workflows. Work with large-scale structured and unstructured data, ensuring high-quality data processing and feature engineering. Optimize and fine-tune ML models for performance, accuracy, and scalability. Collaborate with cross-functional teams, including data scientists, product managers, and software engineers, to integrate ML models into production systems. Stay updated with the latest advancements in machine learning, deep learning, and artificial intelligence. Conduct A/B testing and evaluate model performance using appropriate metrics. Ensure responsible AI practices, including model explainability, fairness, and security. ​ RequirementsRequired Qualifications: 5-8 years of experience in machine learning, deep learning, or AI engineering. Strong programming skills in Python, Scala, or Java. Proficiency in machine learning frameworks (TensorFlow, PyTorch, Scikit-learn, etc. ). Experience with cloud platforms (AWS, GCP, or Azure) for deploying ML models. Expertise in big data technologies (Spark, Hadoop, Kafka) is a plus. Experience with MLOps practices, including CI/CD pipelines for ML models. Solid understanding of data structures, algorithms, and software engineering principles. Strong problem-solving skills and ability to work independently. Preferred Qualifications: Experience with natural language processing (NLP), computer vision, or reinforcement learning. Familiarity with containerization technologies (Docker, Kubernetes) for model deployment. Experience in handling real-time streaming data and working with time-series forecasting. Background in statistics, optimization, or mathematical modeling. Education: Bachelor's or Master’s degree in Computer Science, Data Science, Artificial Intelligence, or a related field.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000010616509/Cybersecurity-Analyst-5-to-8-years---Latin-America?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Cybersecurity Analyst (5 to 8 years) - Latin America", "location": "Remote Job   | Posted on 02/21/2025", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": 8, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000010616509/Cybersecurity-Analyst-5-to-8-years---Latin-America?source=CareerSite", "description": "This is a remote position. Job Summary:We are seeking a highly skilled and experienced Cybersecurity Analyst to join our team. The ideal candidate will have 5-8 years of hands-on experience in cybersecurity, risk assessment, and threat mitigation. This role requires expertise in security operations, incident response, vulnerability management, and compliance with industry security standards. Key Responsibilities: Monitor, detect, and respond to security incidents using SIEM tools and other security monitoring solutions. Conduct vulnerability assessments, penetration testing, and risk analysis to identify potential threats. Implement and enforce security policies, procedures, and best practices. Investigate security breaches, analyze attack patterns, and recommend remediation measures. Perform forensic analysis to determine the root cause of security incidents. Manage security tools and technologies, such as firewalls, IDS/IPS, antivirus, and endpoint detection solutions. Ensure compliance with regulatory requirements such as NIST, ISO 27001, HIPAA, PCI-DSS, and GDPR. Collaborate with IT teams to develop security strategies and implement security enhancements. Conduct security awareness training and educate employees on cybersecurity best practices. Document security incidents, risk assessments, and policy updates. ​ RequirementsRequired Qualifications: 5-8 years of experience in cybersecurity, information security, or a related field. Bachelor's degree in Cybersecurity, Computer Science, Information Technology, or a related discipline (or equivalent experience). Proficiency in security tools such as Splunk, Wireshark, Nessus, Qualys, Palo Alto, CrowdStrike, or similar technologies. Hands-on experience with incident response, threat hunting, forensic analysis, and penetration testing. Strong understanding of network security, encryption, access control, and cloud security principles. Familiarity with regulatory and compliance frameworks such as NIST, ISO 27001, SOC 2, HIPAA, PCI-DSS, and GDPR. Experience with scripting and automation using Python, PowerShell, or Bash is a plus. Strong analytical, problem-solving, and communication skills. Industry certifications such as CISSP, CISM, CEH, GIAC, or Security+ are highly desirable. Preferred Qualifications: Experience in cloud security (AWS, Azure, or Google Cloud Platform). Knowledge of zero trust architecture and zero-day threat mitigation. Experience working in a Security Operations Center (SOC) environment. Familiarity with DevSecOps practices and secure coding methodologies. ​", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000010616532/Java-Developer-5-to-8-years---Latin-America?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Java Developer (5 to 8 years) - Latin America", "location": "Remote Job   | Posted on 02/21/2025", "location_type": "remote", "job_type": null, "min_experience": 8, "max_experience": 8, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000010616532/Java-Developer-5-to-8-years---Latin-America?source=CareerSite", "description": "This is a remote position. Job Summary:We are seeking an experienced Java Developer with 5 to 8 years of hands-on experience in software development. The ideal candidate will have strong expertise in Java, Spring Boot, Microservices, and Cloud technologies. They should be able to design, develop, and maintain scalable and high-performance applications while collaborating with cross-functional teams. ​Key Responsibilities: Develop, test, and maintain high-quality software applications using Java 8+, Spring Boot, and Microservices architecture. Design and implement RESTful APIs and integrate with third-party services. Work with relational and NoSQL databases such as MySQL, PostgreSQL, MongoDB, etc. Deploy and maintain applications on cloud platforms (AWS, Azure, GCP). Implement CI/CD pipelines and automate deployment processes. Collaborate with front-end developers, UX designers, and other stakeholders. Ensure application security, scalability, and performance optimization. Troubleshoot, debug, and resolve software defects and production issues. Participate in code reviews, design discussions, and agile development processes. Keep up-to-date with the latest Java technologies and industry trends. ​ RequirementsRequired Skills & Qualifications: 5 to 8 years of professional experience in Java development. Strong expertise in Spring Boot, Spring MVC, Spring Security, and Spring Data. Experience in designing and developing Microservices architecture. Proficiency in working with RESTful APIs and JSON/XML data formats. Hands-on experience with SQL databases (MySQL, PostgreSQL) and NoSQL databases (MongoDB, Redis, Cassandra). Familiarity with Cloud platforms such as AWS, Azure, or GCP (AWS preferred). Experience with Docker, Kubernetes, and containerization technologies. Knowledge of CI/CD tools like Jenkins, GitHub Actions, GitLab CI/CD. Strong understanding of multithreading, concurrency, and design patterns. Exposure to message brokers (Kafka, RabbitMQ) is a plus. Experience with Unit Testing (JUnit, Mockito) and integration testing. Excellent problem-solving skills and attention to detail. Strong communication skills and ability to work in an Agile environment. Preferred Qualifications: Experience with GraphQL, Reactive Programming (WebFlux), or Event-Driven Architecture. Familiarity with Terraform, Ansible, or other infrastructure-as-code tools. Prior experience working in FinTech, Healthcare, or E-commerce domains is a plus. Certifications in AWS, Java (Oracle Certified), or DevOps are advantageous.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.zazz.io/jobs/Careers/86541000010718290/Brand-Storytelling-Creative-Marketing-Specialist?source=CareerSite", "company_id": 3390, "source": 3, "skills": "", "title": "Brand Storytelling & Creative Marketing Specialist", "location": "Remote Job   | Posted on 02/24/2025", "location_type": "remote", "job_type": null, "min_experience": 2, "max_experience": 4, "apply_link": "https://careers.zazz.io/jobs/Careers/86541000010718290/Brand-Storytelling-Creative-Marketing-Specialist?source=CareerSite", "description": "This is a remote position. About the Role:We are looking for a dynamic Inbound Marketing Specialist who has a proven track record of driving high-volume user sign-ups in a freemium model or social-driven recruitment platform. The ideal candidate should have experience leveraging organic growth strategies, social media engagement, and performance marketing to attract a large audience of job seekers. Responsibilities:• Develop and implement high-impact inbound marketing campaigns to drive mass sign-ups for Zazz’s recruitment platform. • Leverage organic and paid growth strategies, including SEO, social media (TikTok, Instagram, LinkedIn, YouTube Shorts), and viral content creation. • Create engaging short-form content (videos, reels, social ads) that attracts and converts job seekers. • Optimize campaign performance through A/B testing, funnel analysis, and user engagement tracking. • Work closely with growth marketing and content teams to improve sign-up conversion rates. RequirementsQualifications:• 2-4 years of experience in inbound or performance marketing with a focus on high-volume user acquisition. • Strong expertise in social media marketing, content virality, and SEO optimization. • Ability to craft TikTok, Instagram, and YouTube Shorts content that drives engagement. • Data-driven mindset with experience in performance tracking, A/B testing, and audience segmentation. • Experience in recruitment marketing is a plus.", "ctc": null, "currency": null, "meta": {}}]