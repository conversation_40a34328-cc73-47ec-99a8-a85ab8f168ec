[{"jd_link": "https://makingsense.hiringroom.com/jobs/get_vacancy/68068debe1433a4005378ff6", "company_id": 3391, "source": 3, "skills": "", "title": "Product Analyst", "location": "Argentina", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://hiringroom.com/jobs/get_vacancy/68068debe1433a4005378ff6/candidates/new", "description": "Descripción del puesto Job Title: Product Analyst Location: Remote! Position main responsibilities Get an understanding of client business and processes, to contribute to the definition of products that better fit the client's needs Work collaboratively with Product Manager and Project Team to gather requirements and create product backlog Work close with UX team to create great experience for the user Prioritize the stories in the backlog to maximize the business value Take ownership of backlog, being the referent at a functional level. Facilitate team planning and meetings as required, bringing demos during Sprint review Bring support to Team during Sprint, completing and adjusting definitions if it is needed Elaborate project documentation Requisitos Knowledge and Skills Agile Methodologies, mainly Scrum and Kanban Experience in analysis and definition of mobile applications Understanding of Software Architecture Experience working with API integration and defining endpoints Experience working with back-end definition Excellent communication Skills Resilience Flexibility Negotiation Autonomy Proactivity Ability to work in several projects at the same time Ability to “fill the gaps” in definition and work under assumptions Upper-intermediate/Advanced English level. Nice to have BS Degree on System Engineering or Similar Experience in programming languages is a plus Experience as a Product Owner Experience working in data-driven projects Beneficios English lessons. Stretching classes. Prepaid health coverage. Maternity benefits. Postularse", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://makingsense.hiringroom.com/jobs/get_vacancy/67f5779c9c8d9ded98d6683a", "company_id": 3391, "source": 3, "skills": "", "title": "<PERSON> Developer", "location": "Argentina", "location_type": "remote", "job_type": null, "min_experience": 1, "max_experience": null, "apply_link": "https://hiringroom.com/jobs/get_vacancy/67f5779c9c8d9ded98d6683a/candidates/new", "description": "Descripción del puesto Job Title: Senior Python Developer Location: Remote position in LATAM! Responsibilities: Mission Perform tasks in all phases of the development cycle with little or none technical supervision. Appropriately assess problematic situations to gain adequate understanding of problems involved and assumes the responsibility of delivering complex tasks on time and in scope within the team’s plan. Training Learn technologies involved in the project. Coaching Take a leadership role when working with peers and coach junior and semi-senior developers/analysts. Assign tasks according to technical skills, potential and motivation. Communication Attend conference calls and exchange e-mails with clients Suggest changes in client environment to achieve project objectives. Development Perform difficult coding tasks Design and code complete small software modules. Perform code reviews. Reporting Report progress of tasks to team lead Document writing Write project documentation Requisitos Experience 5+ years of experience working with client/server 5+ years with Python 3+ years with Django 2+ years Pandas 2+ years Cloud Solutions 2+ years Redis / SQL DBs (desirable: postgresql) 1+ year containers (Docker, docker-compose) Experience working with git, git flows. Experience with CI (desirable: Jenkins) Experience working with distributed systems, microservices Exposition to Jupyter Notebooks Exposition to Kubernetes English level; Upper intermediate Background: Concepts SOLID, OOP (Object Oriented Programming), IoC (Inversion of Control), DI (Dependency Injection), CI (Continuous Integration) Strong Unit testing Advanced Knowleadge in HTTP: verbs, routes, headers, RESTful services. Beneficios English lessons. Stretching classes. Prepaid health coverage. Maternity benefits. Postularse", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://makingsense.hiringroom.com/jobs/get_vacancy/681904069cdca602c0ffa728", "company_id": 3391, "source": 3, "skills": "", "title": "React Developer", "location": "Argentina", "location_type": "remote", "job_type": null, "min_experience": 4, "max_experience": null, "apply_link": "https://hiringroom.com/jobs/get_vacancy/681904069cdca602c0ffa728/candidates/new", "description": "Descripción del puesto Job Title: Sr React Developer Location: Remote position in Argentina Mission Perform tasks in all phases of the development cycle with little or none technical supervision. Appropriately assess problematic situations to gain adequate understanding of problems involved and assume the responsibility of delivering complex tasks on time and in scope within the team’s plan. Training Learn technologies involved in the project Coaching Take a leadership role when working with peers and coach junior and semi-senior developers/analysts. Assign tasks according to technical skills, potential and motivation. Communications Attend conference calls and exchange e-mails with clients Suggest changes in client environment to achieve project objectives. Frequent contact with clients on system design and fully knowledgeable of the client’s business and information flows, as well as the tools and technologies needed to meet client information requirements. Understand client project domain Development Perform difficult coding tasks Design and code complete small software modules. Perform code reviews. Reporting Report progress of tasks to team lead Report blocker situations Documentation Write project documentation Requisitos Experience 5+ years of experience working with Javascript as a FE or Full Stack developer 4+ years working with React + Redux Enzyme / Jest React Router Redux Thunk Requirements English level: Upper Intermediate + Beneficios English lessons. Stretching classes. Postularse", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://makingsense.hiringroom.com/jobs/get_vacancy/681903412efe66123964351e", "company_id": 3391, "source": 3, "skills": "", "title": "Sales Account Executive", "location": "Argentina", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": 5, "apply_link": "https://hiringroom.com/jobs/get_vacancy/681903412efe66123964351e/candidates/new", "description": "Descripción del puesto As a Sales Account Executive, you will oversee and actively participate in lead generation and sales activities to drive business growth. This includes managing the lead generation team and taking a hands-on role in direct sales activities such as prospecting, research, and customer engagement. Conduct sales calls, develop presales strategies, and lead client presentations. Research and qualify new leads to expand the client pipeline. Design and implement strategies to optimize lead generation processes and maximize conversions. Collaborate with cross-functional teams to align sales efforts with broader business objectives. Monitor and analyze sales metrics to inform decision-making and continuous improvement. Requisitos Experience Minimum of 5 years in sales, with a focus on lead generation and direct sales. Prior experience in the software industry is highly preferred. Proven track record of working with U. S. -based clients and achieving sales targets. Knowledge Strong understanding of sales funnels, lead nurturing, and qualification processes. Familiarity with the software industry and relevant market trends. Technical skills Proficiency in CRM tools like Salesforce or HubSpot. Excellent research and data analysis skills to identify potential leads and market opportunities. Strong bilingual (English and Spanish) communication skills. AI Tools for prospecting/qualifying is a nice to have. Postularse", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://makingsense.hiringroom.com/jobs/get_vacancy/6824a8863fee81d42944ad2a", "company_id": 3391, "source": 3, "skills": "", "title": ".NET Developer", "location": "Argentina", "location_type": null, "job_type": null, "min_experience": 1, "max_experience": null, "apply_link": "https://hiringroom.com/jobs/get_vacancy/6824a8863fee81d42944ad2a/candidates/new", "description": "Descripción del puesto Job Title: Sr . NET Core Location: Argentina Position main responsibilities: Mission Perform tasks in all phases of the development cycle with little or none technical supervision. Appropriately assess problematic situations to gain adequate understanding of problems involved and assume the responsibility of delivering complex tasks on time and in scope within the team’s plan. Training Learn technologies involved in the project Coaching Take a leadership role when working with peers and coach junior and semi-senior developers/analysts. Assign tasks according to technical skills, potential and motivation. Communications Attend conference calls and exchange e-mails with clients Suggest changes in client environment to achieve project objectives. Frequent contact with clients on system design and fully knowledgeable of the client’s business and information flows, as well as the tools and technologies needed to meet client information requirements. Understand client project domain Development Perform difficult coding tasks Design and code complete small software modules. Perform code reviews. Reporting Report progress of tasks to team lead Report blocker situations Documentation Write project documentation Requisitos Experience 6+ years with . NET technologies 2+ years of experience with EntityFramework 3+ years with client-server applications 1+ year working with Cloud Solutions (AWS preferred) Experience with git, git flows. Experience with Agile Methodologies Requirements English Upper Intermediate + Concepts SOLID, OOP (Object Oriented Programming), IoC (Inversion of Control), DI (Dependency Injection), CI (Continuous Integration) Strong in the use of unit testing Intermediate knowledge of HTTP protocol: verbs, routes, headers, RESTful services. Abilities Excellent communication skills Beneficios English lessons. Stretching classes. Prepaid health coverage. Maternity benefits. Postularse", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://makingsense.hiringroom.com/jobs/get_vacancy/68347068fa6e7c5e97574f66", "company_id": 3391, "source": 3, "skills": "", "title": "DevOps JR", "location": "Argentina", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://hiringroom.com/jobs/get_vacancy/68347068fa6e7c5e97574f66/candidates/new", "description": "Descripción del puesto At MakingSense, we are looking for a Junior DevOps Engineer with basic knowledge of AWS, Terraform, Gitlab Runners or Github Actions, Docker, monitoring tools, and preferably Kubernetes. No previous experience is required, but a proactive attitude and commitment to learning and growing in the role are required. Responsibilities: Automation of infrastructure with Terraform. Management of cloud environments with AWS. Configuration and execution of CI/CD pipelines with Gitlab Runners or Github Actions. Container management with Docker. Collaboration on projects with Kubernetes. Use of monitoring tools to ensure system performance. Requisitos Minimum Requirements: Basic knowledge of AWS and Terraform. Familiarity with Gitlab Runners or Github Actions. Basic knowledge of Docker. Advanced English level. (not mandatory) Knowledge of monitoring tools. (not exclusive) Knowledge of Kubernetes. What we expect: We value your ability to apply what you've learned more than your previous experience. We want someone committed to their growth, willing to put their knowledge into practice and continue learning along the way. We recommend studying what you need before the interview if you feel it's necessary to be prepared. Beneficios Prepaid health coverage English Classes Wellness Program Postularse", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://makingsense.hiringroom.com/jobs/get_vacancy/682c803a81612dbe1db711ab", "company_id": 3391, "source": 3, "skills": "", "title": "Sr Node Js Developer", "location": "Colombia", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://hiringroom.com/jobs/get_vacancy/682c803a81612dbe1db711ab/candidates/new", "description": "Descripción del puesto Job Title: NodeJS Developer Location: Remote position! Mission Perform tasks in all phases of the development cycle with little or none technical supervision. Appropriately assess problematic situations to gain adequate understanding of problems involved and assume the responsibility of delivering complex tasks on time and in scope within the team’s plan. Training Learn technologies involved in the project Coaching Take a leadership role when working with peers and coach junior and semi-senior developers/analysts. Assign tasks according to technical skills, potential and motivation. Communications Attend conference calls and exchange e-mails with clients Suggest changes in client environment to achieve project objectives. Frequent contact with clients on system design and fully knowledgeable of the client’s business and information flows, as well as the tools and technologies needed to meet client information requirements. Understand client project domain Development Perform difficult coding tasks Design and code complete small software modules. Perform code reviews. Reporting Report progress of tasks to team lead Report blocker situations Documentation Write project documentation Requisitos xperience 5+ years of software development experience 3+ years of experience with NodeJS Experience with ORMs (Sequelize, TypeORM) Docker Typescript Experience with CI/CD Experience with Kubernetes (is a plus) English level: Upper Intermediate+ Beneficios English lessons. Stretching classes. Prepaid health coverage. Maternity benefits. Postularse", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://makingsense.hiringroom.com/jobs/get_vacancy/683f117b3bb1b7a29b79feac", "company_id": 3391, "source": 3, "skills": "", "title": "Sales Operations Specialist", "location": "Argentina", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://hiringroom.com/jobs/get_vacancy/683f117b3bb1b7a29b79feac/candidates/new", "description": "Descripción del puesto Principales responsabilidades: ● Analizar la performance del equipo comercial, identificando patrones, cuellos de botella y oportunidades de mejora en el funnel de ventas. ● Diseñar y mantener dashboards de reporting, con foco en KPIs clave de ventas, productividad del equipo, conversión por etapa, revenue generado, entre otros. ● Detectar oportunidades comerciales dentro de la base de leads y clientes activos, a partir del análisis de comportamiento, segmentos, historial de compra y campañas anteriores. ● Colaborar con el equipo de ventas en la planificación estratégica, aportando insights desde los datos para optimizar la asignación de leads, forecast y planes de acción. ● Auditar y optimizar el uso del CRM (Zoho CRM), asegurando la calidad de los datos, la trazabilidad del pipeline y la correcta implementación de procesos comerciales. ● Proponer y ejecutar mejoras en los procesos de ventas, automatizaciones simples, rutinas de seguimiento y metodologías de trabajo basadas en análisis. ● Construir modelos de proyección y forecast, que permitan anticipar resultados y facilitar la toma de decisiones del equipo de liderazgo comercial. ● Trabajar en conjunto con marketing y producto, para alinear tácticas de generación de demanda con las oportunidades detectadas desde el área comercial. Requisitos Requisitos: Formación y experiencia: ● Estudios universitarios completos o en curso. ● Entre 3 y 5 años de experiencia profesional, incluyendo al menos 1 año en análisis comercial, sales operations o roles similares. ● Experiencia trabajando con equipos comerciales o en entornos B2B/SaaS. Conocimientos técnicos: ● Dominio de Excel / Google Sheets avanzado (fórmulas, tablas dinámicas, segmentación de datos). ● Manejo de CRM (ideal Zoho): reportes, auditorías de pipeline, gestión de etapas de ventas. ● Conocimiento en herramientas de visualización de datos (Looker, Power BI, Google Data Studio, etc. ). ● Capacidad para construir y presentar reportes ejecutivos con insights comerciales. Habilidades clave: ● Pensamiento analítico y mirada estratégica del negocio. ● Excelente capacidad de síntesis y comunicación clara (escrita y oral). ● Proactividad para detectar oportunidades y generar propuestas de mejora. ● Organización y seguimiento de procesos. ● Trabajo colaborativo con áreas comerciales, marketing y liderazgo. Beneficios ● Dia de cumpleaños libre ● Clases de ingles ● Cobertura medica. ● Descuentos en plataformas ● Semana Doppler ● Dia flex Detalles Nivel mínimo de educación: Universitario (Indistinto) Postularse", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://makingsense.hiringroom.com/jobs/get_vacancy/6841905ffc8c11a77c079968", "company_id": 3391, "source": 3, "skills": "", "title": "Java Developer", "location": "Colombia", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://hiringroom.com/jobs/get_vacancy/6841905ffc8c11a77c079968/candidates/new", "description": "Descripción del puesto Job Title: Senior Java Developer Location: Remote position! Responsibilities: Mission Perform tasks in all phases of the development cycle with little or none technical supervision. Appropriately assess problematic situations to gain adequate understanding of problems involved and assumes the responsibility of delivering complex tasks on time and in scope within the team’s plan. Training Learn technologies involved in the project. Coaching Take a leadership role when working with peers and coach junior and semi-senior developers/analysts. Assign tasks according to technical skills, potential and motivation. Communication Attend conference calls and exchange e-mails with clients Suggest changes in client environment to achieve project objectives. Development Perform difficult coding tasks Design and code complete small software modules. Perform code reviews. Reporting Report progress of tasks to team lead Document writing Write project documentation Requisitos Experience 5+ years of experience with Java 3+ years of experience working with Spring Boot, Maven, Gradle, Hibernate GCP MySQL Github and gitflow English level: Upper Intermediate+ Postularse", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://makingsense.hiringroom.com/jobs/get_vacancy/6841914fcdb811e250f36190", "company_id": 3391, "source": 3, "skills": "", "title": "UX Designer", "location": "Argentina", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://hiringroom.com/jobs/get_vacancy/6841914fcdb811e250f36190/candidates/new", "description": "Descripción del puesto Responsibilities: Participate in the Product Creation Process from a UX perspective. The possibility to independently see a project’s overall as well as work together with other designers in a particular solution. Discuss and collaborate with multidisciplinary teams and Clients about product solutions from a UX perspective. Translate ideas and concepts into great user experiences. Create User flows, Wireframes, Interactions and Final Detailed Mockups. Collaborate with other designers and developers as needed. Work directly with our engineers, delivering them product specs and assets. Requisitos Requirements / Experience: You have 5+ years of UX Design Experience. You’ve designed consumer and/or commercial web and/or mobile Application Solutions You have experience designing for IOS and Native mobiles You have experience designing projects with data entry process requirements. You've deep knowledge about Research, Design and Execution. You’re passionate about designing great and unique user experiences. You have solid intuition and understanding of design principles. Excellent communication skills. You should be able to clearly communicate and discuss about your design decisions. You have basic knowledge of HTML & CSS (coding skills will be appreciated). You dream over the idea to be the best creating successful experiences. You understand and have experience working with AGILE methodology and Lean Processes. Good command of English is required. Beneficios English lessons. Stretching classes. Prepaid health coverage. Maternity benefits. <PERSON><PERSON><PERSON>", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://makingsense.hiringroom.com/jobs/get_vacancy/6841af514bfad7dda3b07356", "company_id": 3391, "source": 3, "skills": "", "title": "Java Developer", "location": "Argentina", "location_type": null, "job_type": null, "min_experience": 6, "max_experience": null, "apply_link": "https://hiringroom.com/jobs/get_vacancy/6841af514bfad7dda3b07356/candidates/new", "description": "Descripción del puesto Job Title: Senior Java Developer Location: Argentina Responsibilities: Mission Perform tasks in all phases of the development cycle with little or none technical supervision. Appropriately assess problematic situations to gain adequate understanding of problems involved and assumes the responsibility of delivering complex tasks on time and in scope within the team’s plan. Training Learn technologies involved in the project. Coaching Take a leadership role when working with peers and coach junior and semi-senior developers/analysts. Assign tasks according to technical skills, potential and motivation. Communication Attend conference calls and exchange e-mails with clients Suggest changes in client environment to achieve project objectives. Development Perform difficult coding tasks Design and code complete small software modules. Perform code reviews. Reporting Report progress of tasks to team lead Document writing Write project documentation Requisitos Experience 6+ years of working experience as a back-end developer with Java Need to feel comfortable maintaining and enhancing large production-grade applications. Activities may include performing triage, root cause analysis, debugging and bug-fixing in a tech stack with different frameworks, languages and versions. Good command of English is required. Knowledge: Java Docker Linux Git Maven / Gradle DB: MySQL, PostgreSQL, Oracle, MSSQL ActionScript (Adobe AIR) JavaScript JSP, XSLT REST & SOAP web services Kafka Zookeeper Testing frameworks (spock) Mule (MuleSoft) Spring framework Beneficios English Lessons. Stetching classes. Prepaid health coverage. Maternity benefits. Postularse", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://makingsense.hiringroom.com/jobs/get_vacancy/685c53e9b03b03deb856322f", "company_id": 3391, "source": 3, "skills": "", "title": "Pasantía Rentada - Analista de Marketing Creativo", "location": "Buenos Aires, Argentina", "location_type": null, "job_type": "part_time", "min_experience": null, "max_experience": null, "apply_link": "https://hiringroom.com/jobs/get_vacancy/685c53e9b03b03deb856322f/candidates/new", "description": "Descripción del puesto Responsabilidades: Colaboración con la vertical de PR & Events en las siguientes tareas: Soporte operativo en la gestión de nuestros eventos trimestrales: principalmente a la hora de hacer seguimiento y prospección de aliados/sponsors. Implica contactarlos por mail, asegurarse de su participación, prospectar nuevos sponsors, etc. Soporte operativo en la coordinación de acciones para nuevos cursos, webinars, eventos, etc. Generación de contenido para piezas comunicacionales en distintos formatos y respondiendo a objetivos diversos: banners, emails, placas de webinars, landing pages, etc. (se valora la creatividad). Requisitos Conocimientos: Capacidad de redacción creativa Manejo de Excel y/o Google Spreadsheet intermedio. Uso de ChatGPT Inglés intermedio. Se valorará: Experiencia en marketing online orientada a la generación de leads. Experiencia en generación de contenidos para formatos digitales Habilidades: Capacidad para trabajar en equipo y colaborar con diferentes áreas. Proactividad y disposición para aprender nuevas herramientas y estrategias. Creatividad para diseñar y ejecutar campañas efectivas. Organización y gestión del tiempo, con capacidad para priorizar tareas y cumplir con plazos ajustados en un entorno dinámico. Carreras afines: Licenciatura en Marketing Licenciatura en Comercialización Publicidad Comunicación Social (con orientación en publicidad o digital) Relaciones Públicas Jornada Part Time (4hs diarias) con disponibilidad para trabajar por la mañana. Beneficios - Semana Doppler - Día de cumpleaños libre -Descuentos varios Detalles Nivel mínimo de educación: Universitario (En Curso) Postularse", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://makingsense.hiringroom.com/jobs/get_vacancy/6846eb1d787a9db06fbc8211", "company_id": 3391, "source": 3, "skills": "", "title": "UX Developer", "location": "Argentina", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://hiringroom.com/jobs/get_vacancy/6846eb1d787a9db06fbc8211/candidates/new", "description": "Descripción del puesto Job Title UX Developer Location: Remote position in LATAM ● Translate design ideas and concepts into great web experiences. ● Discuss and collaborate with multidisciplinary teams and Clients about product solutions from a UX developer perspective. ● Create solid and modularized HTML structures, work with task runners (Gulp and Grunt), work with CSS pre-processors (sass and less) and post-processors as well. ● Work directly with UX Designers and FE developers, being the bridge between product design and production. ● Collaborate with other developers and roles as needed. ● Be passionate about transferring your knowledge to your teammates and help them grow. ● The possibility to independently see a project’s overall as well as work together with other designers or developers in a particular solution. Requisitos Basic knowledge of UX Design HTML Tags and Accessibility CSS, animations and transitions Javascript focused on interactions Experience in React or Angular Experience in Typescript preferred Hooks, States and Props CSS-in-JS / Styled Components Storybook UI Frameworks (Material UI, Ant, Tailwind) Preprocessors Post processors Task Runners AGILE methodologies AGILE UX Documentation Beneficios Daily lunch. English lessons. Stretching classes. Prepaid health coverage. Maternity benefits. Postularse", "ctc": null, "currency": null, "meta": {}}]