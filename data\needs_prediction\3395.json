[{"jd_link": "https://tateeda.com/careers/senior-middle-front-end-developer", "company_id": 3395, "source": 3, "skills": "", "title": "Senior JAVA Developer", "location": "Full time\nEurope\nLATAM\nRemote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://tateeda.com/careers/senior-middle-front-end-developer", "description": "Required skills 5+ years’ experience in Java-based programming. Significant coding skills in Java 8Exceptional problem-solving and analytical abilities. Knowledge of current frameworks (Spring, Spring Boot), SDKs, APIs, and libraries. Experience with Restful servicesKnowledge of MySQLGood organizational and time-management skills. Meticulous at both analysis and execution. Experience working with the Fintech domain is a plus Responsibilities Designing, creating, and implementing Java-based applications. Interpreting briefs to create high-quality coding that functions according to specifications. Determining application functions and building objectives with the team. Ensuring that written code falls in line with the project objectives. Problem solving with other team members in the project. Identifying and resolving immediate and potential issues with applications. Drafting detailed reports on the work performed and projects completed. Participating in group meetings to discuss projects and objectives. Assisting other developers with troubleshooting, debugging, and coding. Meeting deadlines on fast-paced deliverables. We offer Interesting projects and challenging tasks;Professional and personal growth;5-day work-week in a friendly and family work environment;Ability to work remotely;English training;Medical insurance;Active team building and corporate parties.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://tateeda.com/careers/senior-middle-back-end-developer", "company_id": 3395, "source": 3, "skills": "", "title": "Full stack .net + Angular Developer", "location": "Full time\nEurope\nLATAM\nRemote", "location_type": null, "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://tateeda.com/careers/senior-middle-back-end-developer", "description": "Required skills 5+ years’ experience developing with . NET and C# (including experience with . NET Core)Angular experience is required; Kendo UI experience is nice to haveExtensive experience with . NET, C# and technologies such as WEB APIEntity Framework CoreStrong SQL skillsFamiliar with TDD, be able to write unit tests for existing and new codeFlutter experience is a plus but not requiredMicrosoft Azure experience is nice to haveExperience with SOA, Web Services and large distributed systemsStrong understanding of design patternsAbility to quickly learn new technology and apply knowledge to solve technical and business problems Would be a plus Microsoft Azure. Azure Service Bus. Azure Functions (Serverless). Microservices. SSO. Responsibilities Provide senior-level programming for next generation application based in C# and . NET Core, relational databases and distributed n-tier application platformsMake recommendations as to feasibility of implementation of modified or new technologies and / or frameworksMaintain and apply strong understanding of business practices to effectively fulfill responsibilities while working on multiple high-priority tasksPlay an active role in the design and development of new codeParticipate in scrum agile development process We offer Interesting projects and plenty of challenging tasksProfessional and personal growth5-day workweek in a friendly work environmentCompetitive salaryFlexible full-time workAbility to work remotelyEnglish coursesActive team building and corporate parties", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://tateeda.com/careers/senior-middle-devops-engineer", "company_id": 3395, "source": 3, "skills": "", "title": "Senior IT recruiter", "location": "Full time\nLATAM\nRemote", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://tateeda.com/careers/senior-middle-devops-engineer", "description": "Required skills 3 + years of experience in the IT industry 5000 + connections in the LATAM regionProficient with <PERSON><PERSON><PERSON> Recruiter, ATS, Boolean SearchEnglish Upper-Intermediate is a MUST Responsibilities Active search for qualified candidates in line with vacancies requirements using diverse sourcing channels and techniques; searching for new and potential clients via different channelsFrom screening calls, through scheduling interviews until presenting an offer – taking care of the best candidate experiencePublish Vacancies on Job Boards and analysis of incoming candidate’s CVDevelop a talent pipeline for critical positions We offer Interesting projects and challenging tasks;Professional and personal growth;5-day work-week in a friendly and family work environment;Competitive salary;Flexible full-time work;Ability to work remotely;English training;Active team building and corporate parties.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://tateeda.com/careers/senior-python-software-developer", "company_id": 3395, "source": 3, "skills": "", "title": "Senior Python Software Developer", "location": "Full time\nEurope\nLATAM\nRemote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://tateeda.com/careers/senior-python-software-developer", "description": "Senior Software Developer (Python) Wanted! Come work with us, be part of the Tateeda family! ? Headquartered in San Diego, CA, TATEEDA is renowned for its dedication and extensive custom software development services and expertise. Fully committed to success, we expertly handle technical and workforce challenges on your behalf. TATEEDA offers a diverse pool of over 100 senior developers and specialists, strategically located in the U. S. A. , Ukraine, Poland, Brazil, Colombia and more. REQUIRED SKILLS AND QUALIFICATION: Bachelor’s degree in Computer Science, Engineering, or related field (or equivalent experience). English is a must. 5+ years of Python development experience in a professional environment. Strong knowledge of Python frameworks like Django, Flask, or FastAPI. Experience with relational and non-relational databases (e. g. , PostgreSQL, MySQL, MongoDB). Proficiency in RESTful API design and implementation. Familiarity with front-end technologies (e. g. , HTML, CSS, JavaScript) is a plus. Experience with cloud platforms (AWS, Azure, or Google Cloud) and containerization (Docker/Kubernetes). Strong understanding of version control systems (Git). Excellent problem-solving and debugging skills. Experience with Agile methodologies. NICE TO HAVE: Familiarity with machine learning libraries (e. g. , TensorFlow, PyTorch) or data analysis tools (e. g. , Pandas, NumPy). Hands-on experience with CI/CD pipelines and DevOps tools. Knowledge of microservices architecture. WHY JOIN TATEEDA? Fully remote company with flexible work hours. Competitive salary (U$) Opportunity to work on cutting-edge projects with a talented team. Supportive environment for professional growth and development. Continuous learning If you are passionate about leveraging your Python expertise to build impactful solutions, we’d love to hear from you!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://tateeda.com/careers/senior-middle-full-stack-net-core-developer", "company_id": 3395, "source": 3, "skills": "", "title": "Senior Automation Developer", "location": "Full time\nLATAM\nRemote", "location_type": null, "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://tateeda.com/careers/senior-middle-full-stack-net-core-developer", "description": "Qualifications BS/MS degree in Computer Science, Engineering or a related subject 5+ years experience in software quality assurance; automation experience is a must Professional experience with JavaScript/Typescript Professional experience with Cypress and Appium Professional experience with Mobile Testing (on all platforms, Android/iOS) Tosca experience is not required but is a plus Solid knowledge of SQL and scripting Microsoft stack, . Net and C# hands-on knowledge is a plus Experience in writing clear, concise and comprehensive test plans and test cases Hands-on experience with both white box and black box testing Experience working in an Agile/Scrum development process Experience with performance and/or security testing is a plus Responsibilities Review quality specifications and technical design documents to provide timely and meaningful feedback Create detailed, comprehensive and well-structured test plans and test cases Estimate, prioritize, plan and coordinate quality testing activities Maintain and apply strong understanding of business practices to effectively fulfill responsibilities while working on multiple high-priority tasks Design, develop and implement scenario testing for existing code base and for new functionality under development, using a strong understanding of security-based design patterns Identify, record, document thoroughly and track bugs Perform thorough regression testing when bugs are resolved Participate in scrum agile development process We offer Interesting projects and challenging tasksProfessional and personal growth5-day workweek in a friendly and family work environmentCompetitive salaryFlexible full-time workAbility to work remotelyEnglish trainingActive team building and corporate parties", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://tateeda.com/careers/software-engineering-manager", "company_id": 3395, "source": 3, "skills": "", "title": "Senior QA Engineer", "location": "Full time\nLATAM\nRemote", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://tateeda.com/careers/software-engineering-manager", "description": "Required Qualifications BS/MS degree in Computer Science, Engineering or a related subjectExperience in software quality assuranceHands-on experience with automated testing using CypressSolid knowledge of SQL and scriptingMicrosoft stack, . Net and C# hands on knowledge is a plusMobile application testing experience is a plusExperience in writing clear, concise and comprehensive test plans and test casesHands-on experience with both white box and black box testingExperience working in an Agile/Scrum development processExperience with performance and/or security testing is a plus Responsibilities Review quality specifications and technical design documents to provide timely and meaningful feedbackCreate detailed, comprehensive and well-structured test plans and test casesEstimate, prioritize, plan and coordinate quality testing activitiesMaintain and apply strong understanding of business practices to effectively fulfill responsibilities while working on multiple high-priority tasksDesign, develop and implement scenario testing for existing code base and for new functionality under development, using a strong understanding of security-based design patternsIdentify, record, document thoroughly and track bugsPerform thorough regression testing when bugs are resolvedParticipate in scrum agile development process We offer Interesting projects and challenging tasks;Professional and personal growth;5-day work-week in a friendly and family work environment;Competitive salary;Flexible full-time work;Ability to work remotely;English training;Active team building and corporate parties.", "ctc": null, "currency": null, "meta": {}}]