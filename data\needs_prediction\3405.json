[{"jd_link": "https://www.ideas2it.com/careers/business-head", "company_id": 3405, "source": 3, "skills": "", "title": "Business Head / Practice Head", "location": "Chennai", "location_type": "onsite", "job_type": null, "min_experience": 10, "max_experience": 20, "apply_link": "https://www.ideas2it.com/careers/business-head", "description": "Job Role :We are looking for a Business Head / Practice Head for the Ideas2IT Consulting practice. The ideal candidate would be a seasoned consulting professional from the Indian IT services industry with prior experience in areas like Digital Transformation, Solution Design & Consulting, Pre-Sales, Customer Advisory expertise and outstanding leadership skills. The role reports into the Ideas2IT executive team and requires the candidate to take end to end ownership of the consulting team. The candidate also needs to have significant prior experience with leading, running and executing Pre-Sales responsibilities in an IT services company. ‍Why Choose Ideas2IT:Ideas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you can work on various technologies. Gen AI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. What’s in it for you? End to end business ownership for the Ideas2IT Consulting Practice including strategy, execution, offerings, pre-sales, revenues, consulting project execution and people leadership. ‍Build, maintain and position the consulting offerings portfolio. Set up and lead the overall consulting practice for Ideas2ItThis will include a central Pre-Sales team to work with sales, practice and delivery teams for customer proposalsThought leadership based value creation through articles, blogs, lead magnets, and speaker sessions‍‍Execute consulting projects including workshops, assessments and advisory engagements based on the offerings portfolio‍Here’s what you’ll bring18-20 years of experience in the IT services industry10-12 years of prior experience in consulting, practice and pre-sales related rolesGood knowledge in areas like Digital Transformation, AI & ML, Data Science, Data Engineering & Analytics, BI, Cloud, and related technologiesHands on Pre-Sales experience including exposure to working with sales and practice teams for proposal development and presentationsMust have executed and led consulting projects such as workshops, assessments and advisory engagementsStrong knowledge of IT services project execution and delivery, especially with Global Delivery (Onsite-Nearshore-Offshore) in an IT services contextOutstanding leadership skills, excellent communication and collaboration skills with the ability to work effectively across team boundariesAbility to work as an Individual Contributor rather than just being a manager of a teamConsulting experience with US customers, especially in the healthcare sectorPrior experience with custom Application Development with latest technology stacksPrior experience in Product Management and EngineeringDomain experience in healthcare and/or financial servicesStrong knowledge of AI, ML, Gen AI and LLMs including business use cases across industry sectors", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/data-architect", "company_id": 3405, "source": 3, "skills": "", "title": "Data Architect", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.ideas2it.com/careers/data-architect", "description": "Job Role:We are seeking a highly skilled and experienced Data Architect with expertise in designing and building data platforms in cloud environments. The ideal candidate will have a strong background in either AWS Data Engineering or Azure Data Engineering, along with proficiency in distributed data processing systems like Spark. Additionally, proficiency in SQL, data modeling, building data warehouses, and knowledge of ingestion tools and data governance are essential for this role. The Data Architect will also need experience with orchestration tools such as Airflow or Dagster and proficiency in Python, with knowledge of Pandas being beneficial. ‍Why Choose Ideas2ITIdeas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you will have the opportunity to work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. ‍What’s in it for you? ‍You will get to work on impactful products instead of back-office applications for the likes of customers like Facebook, Siemens, Roche, and more You will get to work on interesting projects like the Cloud AI platform for personalized cancer treatment Opportunity to continuously learn newer technologies Freedom to bring your ideas to the table and make a difference, instead of being a small cog in a big wheel Showcase your talent in Shark Tanks and Hackathons conducted in the company‍‍Here’s what you’ll bring‍Experience in designing and building data platforms in any cloud. Strong expertise in either AWS Data Engineering or Azure Data Engineering Develop and optimize data processing pipelines using distributed systems like Spark. • Create and maintain data models to support efficient storage and retrieval. Build and optimize data warehouses for analytical and reporting purposes, utilizing technologies such as Postgres, Redshift, Snowflake, etc. Knowledge of ingestion tools such as Apache Kafka, Apache Nifi, AWS Glue, or Azure Data Factory. Establish and enforce data governance policies and procedures to ensure data quality and security. Utilize orchestration tools like Airflow or Dagster to schedule and manage data workflows. Develop scripts and applications in Python to automate tasks and processes. Collaborate with stakeholders to gather requirements and translate them into technical specifications. Communicate technical solutions effectively to clients and stakeholders. Familiarity with multiple cloud ecosystems such as AWS, Azure, and Google Cloud Platform (GCP). Experience with containerization and orchestration technologies like Docker and Kubernetes. Knowledge of machine learning and data science concepts. Experience with data visualization tools such as Tableau or Power BI. Understanding of DevOps principles and practices. ‍", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/backend-developer", "company_id": 3405, "source": 3, "skills": "", "title": "Backend Developer", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": 10, "apply_link": "https://www.ideas2it.com/careers/backend-developer", "description": "Job Role :The Java Developer role entails creating high-performance and reusable components like core application logic, databases, data and application integration, API, and other backend processes to drive our clients’ innovation-led applications. This is not yet another Java Developer role where you just create layers of interconnected classes with Java syntax. We need people with a deep understanding of building complex micro services on the cloud leveraging the power of Java and associated technologies. Why Choose Ideas2IT:Ideas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you will have the opportunity to work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. ‍What’s in it for you? You will work on diverse technology challenges like: some textA robust distributed platform to manage a self-healing swarm of bots on unreliable network / compute Large-scale Cloud-Native applications Document Comprehension Engine leveraging RNN and other latest OCR techniquesCompletely data-driven low-code platform You will leverage cutting-edge technologies like Blockchain, IoT, and Data Science as you work on projects for leading Silicon Valley startups. Your role does not start or end with just Java development; you will enjoy the freedom to share your suggestions on the choice of tech stacks across the length of the project If there is a certain technology you would like to explore, you can do your Technical PoCsWork in a culture that values capability over experience and continuous learning as a core tenetHere’s what you’ll bring5-10 years of strong Java development experience in Java, Spring MVC or Spring Boot(Microservices), hibernate any cloud platforms(AWS, Azure, etc), Core Java with a good understanding of concepts including but not limited to ORM, IOC, AOP, etc. Deep understanding of underlying core concepts like garbage collection, heap allocation, multithreadingAbility to write high-performance, reusable, and scalable backend servicesAbility to write high-quality code with test-driven developmentStrong familiarity with databases like MySQL, Postgres, Oracle, Mongo, etc. Good understanding of cloud services and cloud-native developmentKnowledge of application and database performance optimizationGood communication and presentation skillsFamiliarity with modern design principles like Microservices", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/data-engineer", "company_id": 3405, "source": 3, "skills": "", "title": "Data Engineer", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.ideas2it.com/careers/data-engineer", "description": "Job Role :As a Data Engineer, you'll build and maintain data pipelines and architectures. Responsibilities include optimizing databases and ETL processes, using Python or SQL,and collaborating with data teams for informed decision-making. Why Choose Ideas2IT:Ideas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you will have the opportunity to work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. As a Data Engineer, exclusively focus on engineering data pipelines for complex products‍What’s in it for you? You will work on diverse technology challenges like: A robust distributed platform to manage a self-healing swarm of bots onunreliable network / compute Large-scale Cloud-Native applications Document Comprehension Engine leveraging RNN and other latest OCR techniquesCompletely data-driven low-code platform You will leverage cutting-edge technologies like Blockchain, IoT, and Data Science as you work on projects for leading Silicon Valley startups. Your role does not start or end with just Java development; you will enjoy the freedom to share your suggestions on the choice of tech stacks across the length of the project If there is a certain technology you would like to explore, you can do your Technical PoCsWork in a culture that values capability over experience and continuous learning as a core tenetHere’s what you’ll bringProficiency in SQL and experience with database technologies (e. g. , MySQL, PostgreSQL, SQL Server). Experience in any one of the cloud environments – AWS, AzureExperience with data modeling, data warehousing, and building ETL pipelines. Experience building large-scale data pipelines and data-centric applications using any distributed storage platformExperience in data processing tools like Pandas, pyspark. Experience in cloud services like S3, Lambda, SQS, Redshift, Azure Data Factory, ADLS, Function Apps, etc. Expertise in one or more high-level languages (Python/Scala)Ability to handle large-scale structured and unstructured data from internal and third-party sources Ability to collaborate with analytics and business teams to improve data models that feed business intelligence tools, increase data accessibility, and foster data-driven decision-making across the organizationExperience with data visualization tools like PowerBI, TableauExperience in containerization technologies like Docker , Kubernetes", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/data-scientist", "company_id": 3405, "source": 3, "skills": "", "title": "Data Scientist", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": 3, "max_experience": 8, "apply_link": "https://www.ideas2it.com/careers/data-scientist", "description": "Job Role:‍You will work on cutting-edge machine learning projects, such as enhancing TorchServe for model serving and improving PyTorch training. Key projects include developing a Document Comprehension Engine using RNN and OCR techniques, creating AI-driven adaptive engineering systems to generate industrial designs from historical data, and building advanced Q&A systems that dynamically create knowledge bases from unstructured documents. You will be involved in numerous other innovative AI initiatives, contributing to the advancement of machine learning technologies. ‍‍Why Choose Ideas2IT:‍Ideas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you will have the opportunity to work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. One of our GenAI initiatives is LegacyLeap, which converts legacy applications to modern technologies. This is a complex AI problem, and you will have an opportunity to work on it. The market response to the LegacyLeap platform has been tremendous. What’s in it for you? Apply cutting-edge AI, ML, and GenAI to build our products and help customers with their AI initiatives. Opportunity to work with Fortune 500 customers like Facebook, Siemens, and Roche Our products allow you to bring your ideas to the table If you have a great idea, we will incubate it. Work in a culture that values capability over experience and continuous learning as a core tenant Working with the Facebook AI Research (FAIR) team on core ML projects like improving Torchserve model serving, Pytorch training, and Core LlamaAI-driven Adaptive engineering to semi-automagically build industrial designs based on historical data A variety of LLM-based projects (GPT and self-hosted) like visual storyboard creator, AI Tutor, AI Recruiter, Q&A with RAG, etc. AI-driven approaches to increase the enrollment rate of patients in underdeveloped countries in Africa for healthcare servicesHere’s what you’ll bring3-8 years of strong Data Science experience (statistics, Machine learning Deep learning) Software development skills in Python Has a good understanding of Deep learning algorithms – RNN, LSTM, CNN, RCNN Expert in at least one of DL frameworks like Pytorch, Tensorflow, Keras Experience using encoder-decoder architecture, statistical and Transformers models Semantic and syntactic analysis for pre-processing key phrase extraction, intent, and entity extraction Experience developing applications for NLP tasks like NLI, Q&A, and Sentiment analysis Good to have exposure to LLMs, Prompt Engineering, and Langchain Implementation of chatbots like Rasa, and Amazon Lex is a plus.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/devops-architect", "company_id": 3405, "source": 3, "skills": "", "title": "DevOps Architect", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://www.ideas2it.com/careers/devops-architect", "description": "Job Role :As a DevOps Architect, you are responsible for designing and implementing your organization's DevOps strategy. You'll work closely with development and operations teams to enhance collaboration, improve deployment frequency, and ensure the reliability and scalability of applications. Your key responsibilities include architecting CI/CD pipelines, managing cloud infrastructure, implementing monitoring and logging solutions, and fostering a culture of continuous improvement and agility within the organization. Why Choose Ideas2IT:Ideas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you will have the opportunity to work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. What’s in it for you? Perform capacity planning, automation, testing, performance tuning, and tools development. Develop and maintain the continuous integration and continuous delivery pipeline. Develop and deploy a control plane for all platform services to guarantee observability, monitoring, analytics, and alerting. Provide on-call support for the platform. Collaborate with the DevOps teams of the SaaS products built on the platform to resolve incidents and implement changes quickly and efficiently. Collaborate with the cyber-security team to integrate security measures into all aspects of the platform. Work with technical project managers, product managers, and operations managers to set priorities and track operational metrics. Participate in planning, system demos, and inspect & adapt events. Drive and coordinate platform adoption, actively engaging product development, quality, regulatory, and customer success teams. Experience in the diagnostics and pharmaceutical industry or other highly regulated industries like finance or insurance seen as highly advantageous. Here’s what you’ll bring8+ years of experience in a systems engineering/DevOps role3+ years of industry experience with Amazon Web Services, IAM, VPC peering, API Gateway, NLB, EC2, ECS, EKS, Lambda, S3, RDS, DynamoDB, SQS, etc. Strong knowledge of Linux systems and internals (Ubuntu/Alpine preferred) Experience in creating software to automate production systems with one of the following languages: Python, Ruby, Java, Go, etc. Strong experience with configuration management, monitoring, and systems tools (e. g. Ansible, SumoLogic, Prometheus, Grafana, etc. ). Proficiency with source control, continuous integration, and testing methods (Git, GitLab, Jenkins) Understanding of cloud provisioning tools, e. g. CloudFormation and Terraform. Strong knowledge of docker, Kubernetes Experience working with cloud-based technologies (Cloudflare CDN and Qualys WAS are highly desirable). Exposure to messaging pub/subsystems (e. g. RabbitMQ, Active-MQ, Kafka)Experience with Linux package management tools e. g. : rpm, apk, deb & fpm, etc.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/devops-engineer", "company_id": 3405, "source": 3, "skills": "", "title": "DevOps Engineer", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://www.ideas2it.com/careers/devops-engineer", "description": "Job Role :A DevOps Engineer is responsible for overseeing the deployment and operational aspects of software applications, bridging the gap between development and operations teams. This role focuses on automating and streamlining processes to enhance the efficiency, reliability, and scalability of software development and deployment. Key responsibilities include continuous integration and continuous deployment (CI/CD), infrastructure as code (IaC), monitoring and logging, and collaboration with cross-functional teams. Why Choose Ideas2IT:Ideas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you will have the opportunity to work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. What’s in it for you? Perform capacity planning, automation, testing, performance tuning, and tools development. Develop and maintain the continuous integration and continuous delivery pipeline. Develop and deploy a control plane for all platform services to guarantee observability, monitoring, analytics, and alerting. Provide on-call support for the platform. Collaborate with the DevOps teams of the SaaS products built on the platform to resolve incidents and implement changes quickly and efficiently. Collaborate with the cyber-security team to integrate security measures into all aspects of the platform. Work with technical project managers, product managers, and operations managers to set priorities and track operational metrics. Participate in planning, system demos, and inspect & adapt events. Drive and coordinate platform adoption, actively engaging product development, quality, regulatory, and customer success teams. Experience in the diagnostics and pharmaceutical industry or other highly regulated industries like finance or insurance is seen as highly advantageous. Here’s what you’ll bring8+ years of experience in a systems engineering/DevOps role 3+ years of industry experience with Amazon Web Services, IAM, VPC peering, API Gateway, NLB, EC2, ECS, EKS, Lambda, S3, RDS, DynamoDB, SQS, etc. Strong knowledge of Linux systems and internals (Ubuntu/Alpine preferred) Experience in creating software to automate production systems with one of the following languages: Python, Ruby, Java, Go, etc. Strong experience with configuration management, monitoring, and systems tools (e. g. Ansible, SumoLogic, Prometheus, Grafana, etc. ). Proficiency with source control, continuous integration, and testing methods (Git, GitLab, Jenkins) Understanding of cloud provisioning tools, e. g. CloudFormation and Terraform. Strong knowledge of docker, Kubernetes Experience working with cloud-based technologies (Cloudflare CDN and Qualys WAS are highly desirable). Exposure to messaging pub/subsystems (e. g. RabbitMQ, Active-MQ, Kafka) Experience with Linux package management tools e. g. : rpm, apk, deb & fpm, etc.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/dot-net-developer-angular", "company_id": 3405, "source": 3, "skills": "", "title": "Dot Net Developer Angular", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://www.ideas2it.com/careers/dot-net-developer-angular", "description": "Job Role:‍This role involves collaborating with project managers, business analysts, and other developers to create efficient, scalable, and robust solutions. Key responsibilities include writing clean, scalable code, debugging applications, and collaborating with project teams to create efficient solutions. Proficiency in C#, ASP. NET, and SQL Server is essential, along with strong problem-solving skills and attention to detail. This role requires the ability to work effectively in a dynamic, collaborative environment. ‍‍Why Choose Ideas2IT:‍Ideas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you will have the opportunity to work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. ‍What’s in it for you? You will get to work on impactful products instead of back-office applications for the likes of customers like Facebook, Siemens, Roche, and moreYou will get to work on interesting projects like the Cloud AI platform for personalized cancer treatmentOpportunity to continuously learn newer technologiesFreedom to bring your ideas to the table and make a difference, instead of being a small cog in a big wheel Showcase your talent in Shark Tanks and Hackathons conducted in the company‍Here’s what you’ll bring5+ years of . net development experienceA deep understanding of the . NET platform. Experience with the ASP. NET MVC. C#, JavaScript, Microservices, Web API, WPF Exposure to an ORM such as Entity Framework or NHibernate or equivalent implementation. Database development in SQL Server. Experience with Enterprise Service Bus/Azure Service Bus preferredFamiliarity with object serialization to one of the following: XML, JSON, BSON. Development experience using HTML5/CSS/JavaScript frameworks such as AngularJS or Ember or equivalent is a plus. Preferred work experience working on large enterprise application integrations. Deep understanding and delivering solutions using common messaging patternsAuthoring APIs compliant with REST standards Development using HTML5/CSS/JavaScript frameworks such as AngularJS or Ember or equivalentDevelopment using Silverlight or WPF JQuery ‍", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/dot-net-developer", "company_id": 3405, "source": 3, "skills": "", "title": "Dot Net Developer VueJS", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://www.ideas2it.com/careers/dot-net-developer", "description": "Job Role:‍This role involves collaborating with project managers, business analysts, and other developers to create efficient, scalable, and robust solutions. Key responsibilities include writing clean, scalable code, debugging applications, and collaborating with project teams to create efficient solutions. Proficiency in C#, ASP. NET, and SQL Server is essential, along with strong problem-solving skills and attention to detail. This role requires the ability to work effectively in a dynamic, collaborative environment. ‍‍Why Choose Ideas2IT:‍Ideas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you will have the opportunity to work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. ‍What’s in it for you? You will get to work on impactful products instead of back-office applications for the likes of customers like Facebook, Siemens, Roche, and moreYou will get to work on interesting projects like the Cloud AI platform for personalized cancer treatmentOpportunity to continuously learn newer technologiesFreedom to bring your ideas to the table and make a difference, instead of being a small cog in a big wheel Showcase your talent in Shark Tanks and Hackathons conducted in the company‍Here’s what you’ll bring5+ years of . net development experienceA deep understanding of the . NET platform. Experience with the ASP. NET MVC. C#, JavaScript, Microservices, Web API, WPF Exposure to an ORM such as Entity Framework or NHibernate or equivalent implementation. Database development in SQL Server. Experience with Enterprise Service Bus/Azure Service Bus preferredFamiliarity with object serialization to one of the following: XML, JSON, BSON. Development experience using HTML5/CSS/JavaScript frameworks such as AngularJS or Ember or equivalent is a plus. Preferred work experience working on large enterprise application integrations. Deep understanding and delivering solutions using common messaging patternsAuthoring APIs compliant with REST standards Development using HTML5/CSS/JavaScript frameworks such as AngularJS or Ember or equivalentDevelopment using Silverlight or WPF JQuery ‍", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/frontend-developer", "company_id": 3405, "source": 3, "skills": "", "title": "Frontend Developer (React)", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.ideas2it.com/careers/frontend-developer", "description": "Job Role:As a Frontend Developer at Ideas2IT, you'll have the opportunity to lead the charge into the future, leveraging your expertise to drive innovation and technical advancement in frontend development. You'll create and maintain user-facing features on websites and web applicationsYour tasks include coding in HTML, CSS, and JavaScript, collaborating with backend developers and designers, and ensuring a seamless user experience. Strong proficiency in front-end technologies and an eye for design are crucial. You'll tackle challenges in a dynamic team environment, striving for optimal performance and user satisfaction. ‍Why Choose Ideas2ITIdeas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you will have the opportunity to work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. ‍What’s in it for you? ‍You will get to work on impactful products instead of back-office applications for the likes of customers like Facebook, Siemens, Roche, and moreYou will get to work on interesting projects like the Cloud AI platform for personalized cancer treatmentOpportunity to continuously learn newer technologiesFreedom to bring your ideas to the table and make a difference, instead of being a small cog in a big wheelShowcase your talent in Shark Tanks and Hackathons conducted in the company‍‍‍Here’s what you’ll bring‍Strong MVCC frontend development experience in frameworks like React, Angular (plus the NGRX Library), and Vue. Strong foundation skills in HTML5, CSS3, SASS, LESS, SCSS, JavaScript, and OO JavaScript (OOJS). Experience in building reusable components using modern techniques. Experience in TDD (Testing Driven Development). Knowledge of responsive design techniques, and high proficiency with security and performance optimization techniques. Experience in Source Control and Continuous Integration (Git, GitLab, BitBucket, Jenkins). Strong problem-solving, debugging, and troubleshooting skills. Good interpersonal relationship skills. Experience in AWS, Azure, and other cloud-based tools. Experience in building PWA (Progressive Web App) applications. Micro frontend experience across frameworks, e. g. , Federation modules in Angular. ‍", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/fullstack-developer", "company_id": 3405, "source": 3, "skills": "", "title": "Fullstack Developer", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.ideas2it.com/careers/fullstack-developer", "description": "Job Role:As a Full-Stack Developer, you’ll work with stakeholders to create key product features, using JavaScript (React, Angular, or Vue) and backend languages like Java, Node, Python, or GoLang. Skilled in Cloud platforms (AWS, Azure, GCP) and Microservices, you’ll develop scalable, high-performance applications, employing both relational and NoSQL databases. This role is perfect for a proactive developer passionate about high-quality code and continuous learning. Why Choose Ideas2ITIdeas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you will have the opportunity to work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. ‍What’s in it for you? ‍Collaborate with multiple stakeholders to understand the business context Implement development best practices Take responsibility for developing product features ‍‍Here’s what you’ll bring‍Rich experience in Javascript and front-end frameworks like React, Angular, or Vue Build a backend API using Java, Node, Python, or GoLang Proficiency in leveraging Cloud Native components in AWS, Azure, or GCPExperience in building scalable applications using Microservices principles is a plus Experience in designing for performance is a big plus Ability to write high-quality code Experience in polyglot persistence using databases like relational (MySQL, Postgres) and NoSQL (MongoDB, Cassandra, DynamoDB, Redis, etc) Familiarity with DevOps tools and technologies is a plus Passion for continuous learning of new technologies‍", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/lead-data-scientist", "company_id": 3405, "source": 3, "skills": "", "title": "Lead Data Scientist", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.ideas2it.com/careers/lead-data-scientist", "description": "Job Role:As a Lead Data Scientist, you’ll drive data-driven innovation, transforming complex datasets into actionable insights that guide strategic decisions. Collaborating with cross-functional teams, you’ll develop predictive models, analyze trends, and deploy scalable solutions that maximize impact. This role combines technical expertise with leadership, offering opportunities to shape data strategies, mentor junior scientists, and influence high-impact projects across the organization. ‍Why Choose Ideas2IT:‍Ideas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you will have the opportunity to work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. One of our GenAI initiatives is LegacyLeap, which converts legacy applications to modern technologies. This is a complex AI problem, and you will have an opportunity to work on it. The market response to the LegacyLeap platform has been tremendous. What’s in it for you? Experience in identifying enterprise-level challenges and transforming them into impactful product ideas. A knack for turning abstract concepts into structured strategies that align with market needs. A sharp analytical mindset that can evaluate market trends, customer insights, and competitor analysis to ensure a spot-on product-market fit. Skilled at building actionable roadmaps that are informed by real customer needs, market demand, and strategic objectives. An ability to guide product development from ideation to execution. Proven ability to engage with stakeholders, extract meaningful insights, and align diverse teams on shared goals, ensuring a seamless journey from product concept to launch. A team-oriented approach with a history of fostering innovation through close collaboration with engineering, marketing, sales, and design teams, always with a focus on the customer. Here’s what you’ll bringJoin a dynamic environment that values creativity and strategic thinking, where you can truly shape the future of products and solve real-world challenges. Leverage a wealth of market data and industry insights to sharpen your strategic skills and stay ahead of the competition. Opportunities for continuous learning and advancement as you work on cutting-edge products with a talented, cross-functional team. Play a key role in guiding product direction, collaborating with key decision-makers, and influencing the development of solutions that make a difference in the industry.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/power-bi", "company_id": 3405, "source": 3, "skills": "", "title": "Power BI Developer", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.ideas2it.com/careers/power-bi", "description": "Job Role:As a Power BI Developer at Ideas2IT, you'll design and develop Power BI reports and dashboards to turn raw data into valuable insights. Collaborating with data engineers and analysts, you'll ensure data security and provide user training. Proficiency in Power BI, SQL, data modeling, and excellent communication skills are essential. A bachelor's degree in a relevant field and Power BI certifications are preferred. ‍Why Choose Ideas2IT:Ideas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you can work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. What’s in it for you? You will get to work on impactful products instead of back-office applications for the likes of customers like Facebook, Siemens, Roche, and moreYou will get to work on interesting projects like the Cloud AI platform for personalized cancer treatmentOpportunity to continuously learn newer technologiesFreedom to bring your ideas to the table and make a difference, instead of being a small cog in a big wheelShowcase your talent in Shark Tanks and Hackathons conducted in the companyHere’s what you’ll bringCollaborating with stakeholders to understand business requirements and translate them into technical specifications. Designing, developing, and deploying BI solutions in reporting tools. Identifying key performance indicators and designing multi-dimensional data models. Ability to present large amounts of information in an easy-to-understand format. Developing dynamic and attractive dashboards and reports using toolsConducting unit testing, troubleshooting, and optimizing existing BI systems. Knowledge of programming languages like Python or R for advanced data analysis and automation. Good Understanding of data visualization principles and techniques. Proficiency in BI tools such as Power BI, Tableau, and Quicksight. Experience in database management, SQL, and data warehousing. Strong analytical and problem-solving skills. Crafting and executing queries for data analysis. Soft skills like communication, collaboration, and project management.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/practice-head-ai-data-analytics", "company_id": 3405, "source": 3, "skills": "", "title": "Practice Head – AI, Data & Analytics", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": 8, "max_experience": 20, "apply_link": "https://www.ideas2it.com/careers/practice-head-ai-data-analytics", "description": "Job Role:This role includes defining and developing practice offerings, driving pre-sales and delivery support, showcasing thought leadership, and fostering team growth through competency development. Essential skills include expertise in AI/ML, Data Science, BI, and Data Engineering, along with leadership and pre-sales experience. ‍Why Choose Ideas2IT:‍Ideas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you can work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. As a Technical Architect, exclusively focus on engineering complex products. What’s in it for you? You will define, develop, and maintain the AI, Data, and Analytics practice offerings portfolio. Opportunity to work on pre-sales activities, including proposals, estimations, PoCs, presentations, and demos. Lead project execution, deploying team members, and overseeing projects to ensure high-quality delivery. Engage in thought leadership by contributing to blogs, whitepapers, and social media. Drive people leadership by hiring, mentoring, and upskilling team members through structured programs. Here’s what you’ll bringPrior experience in a Practice Manager / Practice Head role in relevant areas (AI, Data & Analytics): 8-10 years. Overall (tech) industry experience: 15-20 years. Expertise in AI, Data, Analytics, BI, DWH, ML, and related technologies. Strong leadership skills with the ability to mentor, coach, and motivate team members. Pre-sales and practice management expertise. Customer interfacing, presentation, and consulting skills. Technology expertise in Cloud, Web, and Mobile Applications, and Automation. Consulting and sales experience with domain knowledge in verticals like Healthcare or BFSI. Experience in developing thought leadership collateral such as IPs, solutions, and frameworks. Ability to design competency development and certification programs for team members. Proven track record of scaling practices and achieving high revenue growth. Graduate / Post Graduate Degree & Any qualifications (e. g. , certifications) in leadership/people management will be a plus.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/project-manager", "company_id": 3405, "source": 3, "skills": "", "title": "Project Manager", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://www.ideas2it.com/careers/project-manager", "description": "Job Role:‍This Project Management role entails abridging forward-thinking clients and a highly talented engineering team to ensure the seamless delivery of cutting-edge products. ‍‍Why Choose Ideas2IT:Ideas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you will have the opportunity to work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. As a Project Manager focus on leading complex projects and products. ‍‍What’s in it for you? ‍Take ownership of delivering innovative products for cutting-edge Silicon Valley startups and enterprises alikeHandle projects in varied and exciting/niche technologies like Data Science, Cloud Engineering, Blockchain, Mobile, etc. Opportunity to learn product management to add to your project management skillsBring your disruptive ideas to the table and enjoy the freedom to experiment with them Critically influence the outcome of a startup rather than being a small cog in a big machine Support and inspire incredibly talented delivery teams, while making autonomous decisions on resource planning, the team’s appraisals, promotions, and more Work with the Senior Management on RFPs for innovation-led projects Work in an informal, collaborative, transparent, non-hierarchical, ego-free culture with meritocracy being the sole true North Get trained and certified - PSM / Prince II / PMP Certification, SAFe Product Owner / Product Manager‍Here’s what you’ll bring‍A deep understanding of the client’s business context3+ years of experience in managing software delivery projects in medium to large IT services9+ years of overall experienceTechnology savvy to guide the development team The ability to play a techno-functional roleExperience in Agile Delivery Methodology and helping teams explore, adapt, and optimize Agile-Lean processes, principles, and practicesNurture and mentor a passionate teamClient relationship management ‍", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/python-developer", "company_id": 3405, "source": 3, "skills": "", "title": "Python Developer", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": 4, "max_experience": 8, "apply_link": "https://www.ideas2it.com/careers/python-developer", "description": "Job Role:The Python Developer role involves developing high-performance, reusable components such as core application logic, data processing, integration, APIs, and other backend functionalities to empower our clients' innovative applications. This isn't your typical Python Developer position where you merely write code; we're seeking individuals with a profound grasp of building intricate microservices in the cloud, harnessing the capabilities of Python and its ecosystem of tools and libraries. ‍Why Choose Ideas2IT:‍Ideas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you will have the opportunity to work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. What’s in it for you? You will get to work on impactful products instead of back-office applications for the likes of customers like Facebook, Siemens, Roche, and moreYou will get to work on interesting projects like the Cloud AI platform for personalized cancer treatmentOpportunity to continuously learn newer technologiesFreedom to bring your ideas to the table and make a difference, instead of being a small cog in a big wheelShowcase your talent in Shark Tanks and Hackathons conducted in the companyHere’s what you’ll bring‍Strong programming experience in core Python, with a minimum of 4-8 years in Python application development‍Good design knowledge with OOPSProficiency in Python 3. 6 or higher, with the know-how of features in the latest versionsFlask, Django, or equivalent to create backend APIsExpert in SQL and comfortable with traditional RDBsExperience with enterprise architecture and developing highly scalable servicesGood debugging skills with any Python IDE, adhering to PEP standards and unit testingExperience in API Development LifecycleExperience in AWS or any other cloud platformsExperience in Rabbitmq or similar queuing tools, monolithic/microservice architecture, Design patternsAn understanding of containerization services like Docker, KubernetesWork experience in Source control, Code standard (PEP), AgileExperience in Zato or similar ESB tools", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/technical-architect", "company_id": 3405, "source": 3, "skills": "", "title": "Technical Architect (Backend)", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": 7, "max_experience": null, "apply_link": "https://www.ideas2it.com/careers/technical-architect", "description": "Job Role :The Technical Architect role entails architecting high-performance, scalable, robust, and secure enterprise-grade applications for enterprises and innovative Silicon Valley startups. Why Choose Ideas2IT:Ideas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you can work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. As a Technical Architect, exclusively focus on engineering complex products. What’s in it for you? Work on greenfield development projects and other niche projects for high-profile enterprises and Silicon Valley startupsLeverage cutting-edge technologies like Blockchain, IoT, and Data Science as you work on projects and use the latest tech stacksArchitect projects right from the start - from effort estimation to delivery and beyondThe freedom to recommend emerging technologies for client projects and our product initiativesSpeaking of our product ideas, we run hackathons frequently and conceptualize homegrown ideas - you could propose your ideas or be the one transforming a winning idea into an actual productIf there is a specific technology you would like to explore, you have the freedom to do so, and the company will provide the necessary resourcesWe believe in constant upskilling. We will provide you with the opportunities to upskill and broaden your capabilities as the technology landscape evolves and new exciting technologies emergeY<PERSON> will work with talented colleagues who would be happy to engage you in deep technical discussionsAuthor and publish technical blogs, deliver tech talksWork in a culture that values capability over experience and continuous learning as a core tenetHere’s what you’ll bring7+ years of total experienceProven experience in designing and delivering highly scalable applicationsEvaluate and recommend the right frameworks and tools for a given problemExpertise in multiple technology stacks is a plusEnsure code quality by continuous evangelization, code reviews, and adopt the right toolsMentor and build a strong engineering teamBackend Architects with a strong foundation in Java must have solid experience in architecting and developing high-performance, scalable server-side solutions that are secure, bug-free, and reusable. Experience in Java 8 or aboveShould have strong experience in decision-making and stakeholder management about Design/Technology Choices, Platform StandardsShould have experience in providing direction and high-level architectural blueprints for Platform DevelopmentStrong design knowledge for APIs (Restful / GraphQL API implementations), microservices and event-driven architecture expertise, containerization, service discovery, service mesh, etc. Should have a broad knowledge of the Java/J2EE platform, Spring, Maven, Jira, Code repository (Bitbucket or any tools), Jenkins, GIT, etc. Core Java with a good understanding of concepts including but not limited to ORM, IOC, AOP, etc. Lambda Expressions and Multi-threadingExperience with RDBMS, NoSQLKnowledge of cloud services like ECS, EC2, Migration to the Cloud, SQS / SNS, Lambda, etc. DevOps knowledge and experience using Jenkins, Docker Kubernetes Experience in monitoring tools like Dynatrace and AppDynamicsExperience/Certification in any of the cloud platforms like AWS, Azure", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ideas2it.com/careers/technical-architect-frontend", "company_id": 3405, "source": 3, "skills": "", "title": "Technical Architect (Frontend)", "location": "Chennai", "location_type": null, "job_type": null, "min_experience": 7, "max_experience": null, "apply_link": "https://www.ideas2it.com/careers/technical-architect-frontend", "description": "Job Role :The Technical Architect role entails architecting high-performance, scalable, robust, and secure enterprise-grade applications for enterprises and innovative Silicon Valley startups. Why Choose Ideas2IT:Ideas2IT has all the good attributes of a product startup and a services company. Since we launch our products, you will have ample opportunities to learn and contribute. However, single-product companies stagnate in the technologies they use. In our multiple product initiatives and customer-facing projects, you will have the opportunity to work on various technologies. AGI is going to change the world. Big companies like Microsoft are betting heavily on this (see here and here). We are following suit. As a Technical Architect, exclusively focus on engineering complex products. What’s in it for you? Work on greenfield development projects and other niche projects for high-profile enterprises and Silicon Valley startupsLeverage cutting-edge technologies like Blockchain, IoT, and Data Science as you work on projects and use the latest tech stacksArchitect projects right from the start - from effort estimation to delivery and beyondThe freedom to recommend emerging technologies for client projects and our product initiativesSpeaking of our product ideas, we run hackathons frequently and conceptualize homegrown ideas - you could propose your ideas or be the one transforming a winning idea into an actual productIf there is a specific technology you would like to explore, you have the freedom to do so, and the company will provide the necessary resources We believe in constant upskilling. We will provide you with the opportunities to upskill and broaden your capabilities as the technology landscape evolves and new exciting technologies emergeY<PERSON> will work with talented colleagues who would be happy to engage you in deep technical discussions Author and publish technical blogs, deliver tech talks Work in a culture that values capability over experience and continuous learning as a core tenetHere’s what you’ll bring7+ years of total experienceProven experience in designing and delivering highly scalable applications Evaluate and recommend the right frameworks and tools for a given problemExpertise in multiple technology stacks is a plusEnsure code quality by continuous evangelization, code reviews, and adopt the right tools Mentor and build a strong engineering team‍Front End Architects must have expertise in modern platforms like Angular, React, etc. , and must have architected highly responsive web apps with good UX and performance ‍", "ctc": null, "currency": null, "meta": {}}]