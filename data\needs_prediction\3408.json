[{"jd_link": "https://www.girikon.com/blog/jobopenings/aws-developer-noidabangalore-girik1060/", "company_id": 3408, "source": 3, "skills": "", "title": "AWS Developer – Noida/Bangalore (#Girik1060)", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 4, "max_experience": 6, "apply_link": "https://www.girikon.com/blog/jobopenings/aws-developer-noidabangalore-girik1060/", "description": "Share this post on: AWS Developer – Noida/Bangalore (#Girik1060) Girikon is ranked 335 by Inc. 5000 in 2019 as one of the fastest growing private company in USA. Girikon is a Salesforce Gold Partner and has expertise in the various Salesforce Platforms, including Service Cloud, Community Cloud, Sales Cloud, Marketing Cloud, Pardot, Salesforce CPQ, Commerce Cloud, Manufacturing Cloud, Education Cloud, Finance Cloud, Billing, 3rd Party integrations, Force. com development, Lightning Development, Einstein Analytics, MuleSoft and Data Migration. Girikon’s strength lies in Salesforce implementation, customization, development, integration and support. Girikon is a Salesforce Gold Partner, HIPAA compliant, ISO 9001 & ISO 27001 certified. Along with Salesforce, Girikon also supports its customers on technology platforms such as Java, Microsoft, Oracle, Data Management services etc. Girikon is looking for Full time “AWS Developer” with the following job description: Years of Experience – 4 to 6 years Location – Noida/Bangalore Shift Timing – UK Shift (1:00 pm to 10:00 pm) IST Roles and Responsibilities: Create, develop, and deploy serverless applications utilizing AWS technologies Work with cross-functional teams to comprehend project needs and choose the best technical answers Implement monitoring and logging tools to guarantee the applications’ high availability, dependability, and scalability Improve application performance to enable quick responses and cost savings Continually update our AWS infrastructure with the newest AWS technologies and best practices Skills & Qualification: 4+ years of experience working with AWS development and serverless tools like Glue. Demonstrable experience working with programming languages like Python/Scripting language Extensive knowledge of Integration services like App flow. Experience with AWS including, S3, DynamoDB, API Gateway, Lambda, CloudFormation, IAM, Athena, Glue, EC2, SQS, SNS, CloudFront, Route53, SES. In-depth knowledge of the AWS architecture and the most effective methods for creating and delivering cloud-based apps Ability to work cooperatively in a fast-paced atmosphere and offer prompt fixes for technical issues. Bachelor’s/master’s degree in engineering, Computer Science (or equivalent experience Excellent problem-solving and communication abilities Fluent in conversational and written English communication. Competencies/Values: Ability to work in a start-up-like culture and conform to shifting priorities, demands and timelines through analytical and problem-solving capabilities. Ability to complete projects successfully while maintaining sensitivity to political issues. Persuasive, encouraging, and motivating. Excellent negotiation and conflict resolution skills. Flexible during times of change. Ability to understand the different communication styles of team members and contractors who come from a broad spectrum of disciplines. Ability to elicit cooperation from a wide variety of sources, including upper management, clients, and other departments. Apply Interested candidates can submit their resume at “careers@girikon. com” Share this post on: ← Previous postData Analyst – Remote (#Girik1056) Next post →Full Stack Developer – Bangalore (#Girik1061)", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.girikon.com/blog/jobopenings/data-analyst-remote-girik1056/", "company_id": 3408, "source": 3, "skills": "", "title": "Data Analyst – Remote (#Girik1056)", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 4, "max_experience": 4, "apply_link": "https://www.girikon.com/blog/jobopenings/data-analyst-remote-girik1056/", "description": "Share this post on: Data Analyst – Remote (#Girik1056) Girikon is ranked 335 by Inc. 5000 in 2019 as one of the fastest growing private company in USA. Girikon is a Salesforce Gold Partner and has expertise in the various Salesforce Platforms, including Service Cloud, Community Cloud, Sales Cloud, Marketing Cloud, Pardot, Salesforce CPQ, Commerce Cloud, Manufacturing Cloud, Education Cloud, Finance Cloud, Billing, 3rd Party integrations, Force. com development, Lightning Development, Einstein Analytics, MuleSoft and Data Migration. Girikon’s strength lies in Salesforce implementation, customization, development, integration and support. Girikon is a Salesforce Gold Partner, HIPAA compliant, ISO 9001 & ISO 27001 certified. Along with Salesforce, Girikon also supports its customers on technology platforms such as Java, Microsoft, Oracle, Data Management services etc. Girikon is looking for Full time “Data Analyst” with the following job description: Years of Experience – 3 to 4 years Location – Remote Shift Timing – PST (9:30 pm to 6:30 am) (Night Shift) Roles and Responsibilities: Business Process Understanding and Mapping – Develop deep understanding of the client’s business processes and document interactions between different actors and applications and the data flow. Operational Support and Engagement – Our consultants are pivotal in linking clients’ requirements with data-driven insights. They will work alongside the client team on a day-to-day basis, to ensure that the data flow through the different application is happening properly, triage Data Issues and resolve these by making appropriate updates to the application data. Client Centric Data System Oversight – Girikon team continuously monitors the different data systems to ensure data integrity and relevance. By understanding the operational workflows and leveraging our expertise, we not only respond to immediate data requests but also anticipate future needs, preparing the clients to face upcoming challenges effectively. These include Qualitative and Quantitative validation of the data, thus ensuring that the Reports & Dashboards – Girikon consultants leverage their experience with Excel, SQL and different analytics tools like Power BI, Salesforce Tableau, Google Analytics etc. to create meaningful Reports and Dashboards that provide the needed insights for the business teams. AI Engine Data Model Refinement – Girikon, with its offering in the Artificial Intelligence, enables its Data Management consultant to be able to refine and train the AI models. This is a relatively new area in the industry, but Girikon provides an edge with a string AI/ML practice. Needed Skillset: Excel, Smartsheet SQL Power BI, Tableau Salesforce Einstein Strong Communication Business Analysis and Process Documentation Education BCA/ MCA/B. Tech/MBA Competencies/Values: Ability to work in a start-up-like culture and conform to shifting priorities, demands and timelines through analytical and problem-solving capabilities. Ability to complete projects successfully while maintaining sensitivity to political issues. Persuasive, encouraging, and motivating. Excellent negotiation and conflict resolution skills. Flexible during times of change. Ability to understand the different communication styles of team members and contractors who come from a broad spectrum of disciplines. Ability to elicit cooperation from a wide variety of sources, including upper management, clients, and other departments. Apply Interested candidates can submit their resume at “careers@girikon. com” Share this post on: ← Previous postData Engineer – Bangalore/Noida, India (#Girik1055) Next post →AWS Developer – Noida/Bangalore (#Girik1060)", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.girikon.com/blog/jobopenings/data-engineer-bangalorenoida-india-girik1055/", "company_id": 3408, "source": 3, "skills": "", "title": "Data Engineer – Bangalore/Noida, India (#Girik1055)", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 7, "max_experience": 10, "apply_link": "https://www.girikon.com/blog/jobopenings/data-engineer-bangalorenoida-india-girik1055/", "description": "Share this post on: Data Engineer – Bangalore/Noida, India (#Girik1055) Girikon is ranked 335 by Inc. 5000 in 2019 as one of the fastest growing private company in USA. Girikon is a Salesforce Gold Partner and has expertise in the various Salesforce Platforms, including Service Cloud, Community Cloud, Sales Cloud, Marketing Cloud, Pardot, Salesforce CPQ, Commerce Cloud, Manufacturing Cloud, Education Cloud, Finance Cloud, Billing, 3rd Party integrations, Force. com development, Lightning Development, Einstein Analytics, MuleSoft and Data Migration. Girikon’s strength lies in Salesforce implementation, customization, development, integration and support. Girikon is a Salesforce Gold Partner, HIPAA compliant, ISO 9001 & ISO 27001 certified. Along with Salesforce, Girikon also supports its customers on technology platforms such as Java, Microsoft, Oracle, Data Management services etc. Girikon is looking for Full time “Data Engineer” with the following job description: Years of Experience – 7+ years Working Days – (Five days working)/PST Location – Bangalore/Noida, India Shift Timing – UK Shift (1:00 pm to 10:00 pm) Roles and Responsibilities: Must Have 7 to 10 years of Hands-on experience with Python, SQL, and Talend ETL. Strong experience with ETL processes using Talend. Experience using PySpark & Pandas Libraries for Data transformation activities. Expert in SQL – Experience with SQL database programming, and SQL performance. tuning, relational model analysis, queries, stored procedures, views, functions, and triggers. Strong knowledge of multiple databases (PostgreSQL, SQL Server, Oracle, Snowflake DB, Redshift). Experience working in Cloud Environment (AWS/AZURE/GCP). Knowledge & Understanding of Data Warehousing concepts. Good to have experience/knowledge in BI tools (Tableau/Power BI). Desired Candidate Profile: 7+ years of hands-on experience with Python, SQL Development, and Talend ETL Bachelor’s/master’s degree in engineering, Computer Science or equivalent experience Excellent problem-solving and communication abilities Fluent in verbal and written English communication. Ability to work cooperatively in a fast-paced atmosphere and offer prompt fixes for technical issues. Competencies/Values: Ability to work in a start-up-like culture and conform to shifting priorities, demands and timelines through analytical and problem-solving capabilities. Ability to complete projects successfully while maintaining sensitivity to political issues. Persuasive, encouraging, and motivating. Excellent negotiation and conflict resolution skills. Flexible during times of change. Ability to understand the different communication styles of team members and contractors who come from a broad spectrum of disciplines. Ability to elicit cooperation from a wide variety of sources, including upper management, clients, and other departments. Apply Interested candidates can submit their resume at “careers@girikon. com” Share this post on: ← Previous postHubSpot Strategist – Noida India (#Girik1054) Next post →Data Analyst – Remote (#Girik1056)", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.girikon.com/blog/jobopenings/email-marketing-specialist-it-product-sales-ai-solutions-noida-girik1063/", "company_id": 3408, "source": 3, "skills": "", "title": "Email Marketing Specialist – IT Product Sales (AI Solutions) – Noida (#Girik1063)", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 3, "max_experience": 5, "apply_link": "https://www.girikon.com/blog/jobopenings/email-marketing-specialist-it-product-sales-ai-solutions-noida-girik1063/", "description": "Share this post on: Email Marketing Specialist – IT Product Sales (AI Solutions) – Noida (#Girik1063) Girikon is ranked 335 by Inc. 5000 in 2019 as one of the fastest growing private company in USA. Girikon is a Salesforce Gold Partner and has expertise in the various Salesforce Platforms, including Service Cloud, Community Cloud, Sales Cloud, Marketing Cloud, Pardot, Salesforce CPQ, Commerce Cloud, Manufacturing Cloud, Education Cloud, Finance Cloud, Billing, 3rd Party integrations, Force. com development, Lightning Development, Einstein Analytics, MuleSoft and Data Migration. Girikon’s strength lies in Salesforce implementation, customization, development, integration and support. Girikon is a Salesforce Gold Partner, HIPAA compliant, ISO 9001 & ISO 27001 certified. Along with Salesforce, Girikon also supports its customers on technology platforms such as Java, Microsoft, Oracle, Data Management services etc. Girikon is looking for Full time “Email Marketing Specialist” with the following job description: Location – Noida Job Type – Full-time Years of Experience – 3-5 years in email marketing with a focus on IT product sales, especially AI-based solution Key Responsibilities: Strategic Campaign Development: Design and execute email marketing strategies tailored to promote AI-based IT products and services. Content Creation: Craft compelling email content, subject lines, and CTAs to increase open rates and conversions. Lead Nurturing: Build and manage automated email workflows to engage prospects throughout the buyer journey. Performance Analysis: Monitor and analyze campaign performance metrics (open rates, click through rates, conversion rates) and optimize for success. A/B Testing: Conduct experiments to determine the most effective email marketing practices. Integration & Collaboration: Collaborate with the sales, product, and design teams to align email campaigns with business goals and product updates. Compliance: Ensure adherence to email marketing best practices, including CAN-SPAM Act and GDPR compliance. Required Qualifications: Bachelor’s degree in marketing, Business, or a related field. Proven experience in email marketing with a focus on IT product sales, preferably AI-based solutions. Proficiency in email marketing tools (e. g. , Mailchimp, HubSpot, Pardot). Strong understanding of customer segmentation, personalization, and lifecycle marketing. Excellent writing and editing skills with attention to detail. Analytical mindset with experience in data-driven decision-making. Knowledge of email deliverability and troubleshooting. Preferred Skills: Familiarity with CRM platforms like Salesforce. Basic understanding of AI technologies and their application in business. Experience in integrating email campaigns with broader digital marketing efforts. Certification in email marketing or digital marketing (e. g. , HubSpot Email Marketing Certification). Competencies/Values: Ability to work in a start-up-like culture and conform to shifting priorities, demands and timelines through analytical and problem-solving capabilities. Ability to complete projects successfully while maintaining sensitivity to political issues. Persuasive, encouraging, and motivating. Excellent negotiation and conflict resolution skills. Flexible during times of change. Ability to understand the different communication styles of team members and contractors who come from a broad spectrum of disciplines. Ability to elicit cooperation from a wide variety of sources, including upper management, clients, and other departments. Apply Interested candidates can submit their resume at “careers@girikon. com” Share this post on: ← Previous postSenior Salesforce Developer – Noida (#Girik1062) Next post →Salesforce Consultant – Phoenix, AZ / Dallas, TX(#Girik1064)", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.girikon.com/blog/jobopenings/full-stack-developer-bangalore-girik1061/", "company_id": 3408, "source": 3, "skills": "", "title": "Full Stack Developer – Bangalore (#Girik1061)", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 5, "max_experience": 5, "apply_link": "https://www.girikon.com/blog/jobopenings/full-stack-developer-bangalore-girik1061/", "description": "Share this post on: Full Stack Developer – Bangalore (#Girik1061) Girikon is ranked 335 by Inc. 5000 in 2019 as one of the fastest growing private company in USA. Girikon is a Salesforce Gold Partner and has expertise in the various Salesforce Platforms, including Service Cloud, Community Cloud, Sales Cloud, Marketing Cloud, Pardot, Salesforce CPQ, Commerce Cloud, Manufacturing Cloud, Education Cloud, Finance Cloud, Billing, 3rd Party integrations, Force. com development, Lightning Development, Einstein Analytics, MuleSoft and Data Migration. Girikon’s strength lies in Salesforce implementation, customization, development, integration and support. Girikon is a Salesforce Gold Partner, HIPAA compliant, ISO 9001 & ISO 27001 certified. Along with Salesforce, Girikon also supports its customers on technology platforms such as Java, Microsoft, Oracle, Data Management services etc. Girikon is looking for Full time “Full Stack Developer” with the following job description: Years of Experience – 5+ years Location – Bangalore What you’ll be doing: Contribute to all aspects of the services under the team’s ownership, which includes design, implementation, refactoring, automated testing, deployment, and uptime of the services. Help other team members grow as engineers through code review, pairing, and mentoring. Improve internal team processes by keeping what’s working, throwing away what’s not. Help set the vision of the services under the team’s ownership. Help align the roadmap based on customer and company desires. Build the technology the right way: for us, this means simple, well-tested services that gradually grow over time, and that provide plenty of insight into production performance. What we’re looking for: 5+ years work experience as a full stack. Enjoy working with any and all technologies like Java, JavaScript, MySQL, React. js/Angular. js, Node. js, MongoDB and love playing around with AWS. Must have a very strong knowledge of how the web works and how to make it efficient (HTTP, caching, page rendering, sockets, etc). Must possess solid software development fundamentals (data structures, algorithms, problem-solving, OO design, and system architecture). Must have exposure to Microservice Architecture, Domain Driven Design, REST APIs Care about great design, not only in the product and visual sense but also in your code and technical architecture Have experience managing Git and release workflow Have a BE/BTech/M. Sc. – Computer Science/ IT Have a minimum 3 to 5 years in Java/JavaScript programming experience. Being passionate about your craft and want to be surrounded by like-minded individuals Ability to clearly communicate to technical and non-technical audiences. Ability to multitask in a dynamic environment Possess a good understanding of continuous delivery, writing unit tests and automated testing. Prior experience delivering using Agile on at least 2 key projects. Comfortable working at all tiers of modern applications, from the frontend all the way to the database. Do TDD/BDD all day. Competencies/Values: Ability to work in a start-up-like culture and conform to shifting priorities, demands and timelines through analytical and problem-solving capabilities. Ability to complete projects successfully while maintaining sensitivity to political issues. Persuasive, encouraging, and motivating. Excellent negotiation and conflict resolution skills. Flexible during times of change. Ability to understand the different communication styles of team members and contractors who come from a broad spectrum of disciplines. Ability to elicit cooperation from a wide variety of sources, including upper management, clients, and other departments. Apply Interested candidates can submit their resume at “careers@girikon. com” Share this post on: ← Previous postAWS Developer – Noida/Bangalore (#Girik1060) Next post →Senior Salesforce Developer – Noida (#Girik1062)", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.girikon.com/blog/jobopenings/graduate-marketing-analyst-australia-girik925/", "company_id": 3408, "source": 3, "skills": "", "title": "Graduate Marketing Analyst – Australia (#Girik951)", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://www.girikon.com/blog/jobopenings/graduate-marketing-analyst-australia-girik925/", "description": "Share this post on: Graduate Marketing Analyst – Australia (#Girik951) Girikon is an IT consulting & development company, based out of Phoenix, Arizona with development centres in Phoenix, USA & Noida / Bangalore, India and Melbourne, Australia Girikon is a Salesforce Gold Consulting Partner, HIPAA compliant, ISO 9001 certified & ISO 27001 certified. Along with Salesforce, Girikon also supports its customers on technology platforms such as Java, Microsoft, Oracle, Data Management services etc. Girikon has expertise in the majority of Salesforce Platforms, including Sales Cloud, Service Cloud, Community Cloud, Marketing Cloud, Pardot, Salesforce CPQ, Billing, 3rd Party integrations, Force. com development, Lightning Development and Data Migration. Our network of offices in USA, India, and Australia, allows Girikon to quickly respond customer’s requirements with a balanced resource loading. Girikon has a team of experienced and certified Salesforce Architects, Developers, Consultants, and Administrators. Girikon’s strength lies in Salesforce implementation, customization, development, integration, and support. Girikon is looking for Full time “Graduate Marketing Analyst” with the following job description: Location – Australia Roles : We are looking for bright and driven graduate from the Business and Law discipline to work on exciting Marketing projects for our Girikon Australian team. You will work with the Australia team to gain an understanding of current client base and a range of industries to equip you with knowledge to collaborate with the Global Marketing team to develop a marketing strategy that is relevant to the Australian branch of Girikon. There are a variety of business requirements that need to be gathered and investigated for incorporating into a marketing strategy, plus the generation of key objectives and action plans. This is a great opportunity to gain invaluable industry experience and exposure whilst leveraging your theoretical knowledge and turning it into tangible products and outcomes. Girikon will require the successful candidate to be a self-starter, have a passion for making a difference and a growth mindset with desire to learn during an increased growth phase of the company. Responsibilities : Analysis and requirements gathering / investigation for input into strategic planning Assist with the development and planning of marketing strategy for Australia. That includes: Social media – enhance presence Web site – review and provide suggested improvements / roadmap Events – showcasing benefits of having Girikon as a consulting partner Promotion of the Girikon consulting services Provide input to engagement plans Distilling key objectives Creation of action plans Engage with business experts, users, and leaders to understand how changes to the Marketing processes, product and service branding can improve Girikon’s position in different industry segments Articulate ideas but also balance them against what is technically and financially feasible plus functionally reasonable. Depending on experience you might work with data sets, various products, tools, software, services, or process. Ability to work from home and flexible working hours if required. Education & Experience : Undergraduate Degree Business – majoring in Marketing desirable however not a prerequisite Work Experience desirable however not a prerequisite Apply If interested, please submit your resume to “careers@girikon. com” Note: Successful candidates will usually be contacted within two weeks. Please be aware that applications will be kept on file for up to 12 months. Share this post on: ← Previous postSalesforce Developer/ Senior Developer – Pan India (Preference Bangalore / Noida) (#Girik940) Next post →Tech Lead – Salesforce – Noida/ Gurgaon (#Girik995)", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.girikon.com/blog/jobopenings/hubspot-strategist-noida-india-girik1054/", "company_id": 3408, "source": 3, "skills": "", "title": "HubSpot Strategist – Noida India (#Girik1054)", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://www.girikon.com/blog/jobopenings/hubspot-strategist-noida-india-girik1054/", "description": "Share this post on: HubSpot Strategist – Noida India (#Girik1054) Girikon is ranked 335 by Inc. 5000 in 2019 as one of the fastest growing private company in USA. Girikon is a Salesforce Gold Partner and has expertise in the various Salesforce Platforms, including Service Cloud, Community Cloud, Sales Cloud, Marketing Cloud, Pardot, Salesforce CPQ, Commerce Cloud, Manufacturing Cloud, Education Cloud, Finance Cloud, Billing, 3rd Party integrations, Force. com development, Lightning Development, Einstein Analytics, Mulesoft and Data Migration. Girikon’s strength lies in Salesforce implementation, customization, development, integration and support. Girikon is a Salesforce Gold Partner, HIPAA compliant, ISO 9001 & ISO 27001 certified. Along with Salesforce, Girikon also supports its customers on technology platforms such as Java, Microsoft, Oracle, Data Management services etc. Girikon is looking for Full time “HubSpot Strategist” with the following job description: Years of Experience – 5+ years Location – Noida, India Shift Timing – UK Shift (1:00 pm to 10:00 pm) Roles and Responsibilities: Consulting with clients to understand their RevOps goals and recommending the best strategies to achieve them using HubSpot. Consulting clients in defining their Content strategies, Content marketing, Sales enablement, Social media marketing, Web designing and Demand generation. Training clients and internal teams on HubSpot’s features, functionalities, and best practices. Keeping up to date with new features and updates in the HubSpot platform. Collaborating with Project Managers to develop RevOps approaches for clients. Providing strategic direction to Implementation Specialists to ensure the effective use of HubSpot in client projects. Serving as a resource for the rest of the team to stay informed about HubSpot’s capabilities and potential applications. Works with the Sales, Marketing and Servicing teams to review, recommend and implement best practice CRM processes and procedures within HubSpot. Collaborates with cross-functional teams to understand marketing, sales, and professional services objectives and supporting the appropriate systems, processes, and tools to help teams accomplish their goals. Drive and lead all aspects of sales and marketing operations from GTM strategy, process optimization, technology stack, territory planning, commission and incentive plans, lead-to-deal processes, proposals, and sales enablement. Desired Candidate Profile: Degree in computer science. Strong knowledge in any CRM configuration and customization of Sales, Services, Marketing modules and System integration with marketplace apps. Expertise in CRM customization like Workflow, Object Creation, Field creation, Tab Creation to improve business processes. Has knowledge of common Marketing Automation system functionality. Is comfortable with HTML, CSS, and has familiarity with JavaScript/jQuery. Has the proven ability to learn & communicate technical concepts. Possesses strong written & verbal communication skills to effectively maintain relationships with clients. The ideal candidate would possess deep technical expertise in HubSpot, strong consulting and training skills, and the ability to effectively communicate and collaborate with clients and internal teams. Competencies/Values: Ability to work in a start-up-like culture and conform to shifting priorities, demands and timelines through analytical and problem-solving capabilities. Ability to complete projects successfully while maintaining sensitivity to political issues. Persuasive, encouraging, and motivating. Excellent negotiation and conflict resolution skills. Flexible during times of change. Ability to understand the different communication styles of team members and contractors who come from a broad spectrum of disciplines. Ability to elicit cooperation from a wide variety of sources, including upper management, clients, and other departments. Apply Interested candidates can submit their resume at “careers@girikon. com” Share this post on: ← Previous postTech Lead – Salesforce – Noida/ Gurgaon (#Girik995) Next post →Data Engineer – Bangalore/Noida, India (#Girik1055)", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.girikon.com/blog/jobopenings/salesforce-consultant-phoenix-az-dallas-txgirik1064/", "company_id": 3408, "source": 3, "skills": "", "title": "Salesforce Consultant – Phoenix, AZ / Dallas, TX(#Girik1064)", "location": "", "location_type": null, "job_type": "contract", "min_experience": 1, "max_experience": 1, "apply_link": "https://www.girikon.com/blog/jobopenings/salesforce-consultant-phoenix-az-dallas-txgirik1064/", "description": "Share this post on: Salesforce Consultant – Phoenix, AZ / Dallas, TX(#Girik1064) Be part of a rapidly growing IT Services company in the Phoenix Valley, with a chance to work on challenging assignments and grow along with the growth of the company. “Girikon Inc” is a business consulting, technology & development company based in Phoenix, AZ. Girikon is a Salesforce Gold Consulting Partner, HubSpot Diamond Implementation Partner, Oracle Partner, Microsoft Silver Partner & Talend Partner. Girikon is looking to onboard an organized and detail-oriented “Salesforce Consultant” to assist with Testing activities on Salesforce Platform. Our ideal candidate will be a strong Salesforce Tester, outstanding listener, and an enthusiastic team-player with Scrum experience. Consultant will join our team only after the successful completion of Background check, Reference check, E-verify process & Drug Test. **** Applications coming through Agencies will not be considered. Location – Phoenix, AZ / Dallas, TX Responsibilities include but are not limited to: Should have hands-on experience on Salesforce setups such as Permission Sets, Sharing Model, Roles, OWD, Object relationships. Architect the solution. Customize fields, page layouts, record types, reports and dashboards. Design, configure, and develop custom code/objects. Use APEX and Visualforce to create new functionality and extend existing apps. Experience in SFDC customization, integration and administration. Proficiency in Apex, Visual force, Web Services, SOQL, AJAX, XML, Javascript and HTML. Understanding of the capabilities and constraints of the Salesforce. Assist in data import/export/updates, system customization. Experience on Salesforce CPQ or other CPQ products like Apptus, Cloudsense etc. will be a big plus. Ability to understand key business requirements and converting them into solution design. Excellent communication and presentation skills. Sound knowledge on Standard object, Sales process, Validations, Workflows, Process Builders, Reports and Dashboards. Knowledge on Delegated Admin capabilities, Communities, Partner portal, Sites is desirable. Qualifications: 7+ years of Experience as a Salesforce Developer in Sales cloud, Experience cloud & CPQ. Undergraduate Degree with good GPA in computer science or equivalent field. Salesforce Developer or Salesforce Admin Certification is must. Good to have both. Knowledge of at least one programming language (e. g. SQL, Java, HTML, C++, etc. ) is a plus. Excellent verbal communication skills. Experience working both independently and in a team-oriented, collaborative environment is essential. Proficiency with Agile development tools – Jira, Confluence, etc Why part of Girikon? Competitive salary commensurate with Industry Standards. Enrollment in group health plan (Medical, Dental, and Vision). 401(k) plan with employer match after completing 1 year of employment. Training and development of our employees Paid Personal Time Off and Sick days. Standard public holidays for all employees. Work/Life balance. Apply Interested candidates can submit their resume at “hrusa@girikon. com” Share this post on: ← Previous postEmail Marketing Specialist – IT Product Sales (AI Solutions) – Noida (#Girik1063) Next post →Sr Salesforce Developer – Dallas, TX(#Girik1065)", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.girikon.com/blog/jobopenings/salesforce-developer-senior-developer-pan-india-preference-bangalore-noida-girik940/", "company_id": 3408, "source": 3, "skills": "", "title": "Salesforce Developer/ Senior Developer – Pan India (Preference Bangalore / Noida) (#Girik940)", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 3, "max_experience": 6, "apply_link": "https://www.girikon.com/blog/jobopenings/salesforce-developer-senior-developer-pan-india-preference-bangalore-noida-girik940/", "description": "Share this post on: Salesforce Developer/ Senior Developer – Pan India (Preference Bangalore / Noida) (#Girik940) Girikon is ranked 335 by Inc. 5000 in 2019 as one of the fastest growing private company in USA. Girikon is a Salesforce Gold Partner and has expertise in the various Salesforce Platforms, including Service Cloud, Community Cloud, Sales Cloud, Marketing Cloud, Pardot, Salesforce CPQ, Commerce Cloud, Manufacturing Cloud, Education Cloud, Finance Cloud, Billing, 3rd Party integrations, Force. com development, Lightning Development, Einstein Analytics, Mulesoft and Data Migration. Girikon’s strength lies in Salesforce implementation, customization, development, integration and support. Girikon is a Salesforce Gold Partner, HIPAA compliant, ISO 9001 & ISO 27001 certified. Along with Salesforce, Girikon also supports its customers on technology platforms such as Java, Microsoft, Oracle, Data Management services etc. Girikon is looking for Full time “Salesforce Developer/ Senior Developer” with the following job description: Location – Pan India (Preference Bangalore / Noida) Total Experience – 3 – 6 Year Relevant Experience – 3+ Year Shift Timing – UK (1 PM – 10 PM) Qualification & Skills: Hands-on extensive Salesforce experience Ability to Perform configuration, customization, integration and support of Salesforce. com Hands-on Experience in customizing using APEX, Triggers, Batch Apex, Visualforce, etc Understanding of the Salesforce. com data model Hands-on Experience in designing of Custom Objects, Custom Fields, Page Layouts, Workflows, Record types, Validation Rules, Workflow Rules, Approval Process, Sandbox Refreshments, Apex Data Loader, etc. Must-Have hands-on experience on Deployments using Change set, , Managing release dependencies across multiple projects Understanding the release management and software development life cycle Should be able to review code of development team members Have experience to work in multiple Salesforce ORG/environment Strong communication skills to ask relevant questions in client/dev team meeting etc. & able to work around release related dependencies across projects Comfortable in client calls including executive sponsors and stakeholders Ability to convert customer requirement to technical specifications Knowledge of Salesforce recommended best practices around design and development. Good to Have: Experience in lightning implementation, LWC Eclipse IDE and ANT Deployment tool Experience in working with HTML, CSS, Ajax, JavaScript, and jQuery Experience with Subversion, Git or other SCM systems will be plus Experience on CPQ, Billing will be plus Salesforce certifications and Trailhead badges will be plus Good to have experience in Release Management tools like ANT, GitHub, Jenkins, Copado or similar Competencies/Values: Ability to work in a startup-like culture and conform to shifting priorities, demands and timelines through analytical and problem-solving capabilities. Ability to complete projects successfully while maintaining sensitivity to political issues. Persuasive, encouraging, and motivating. Excellent negotiation and conflict resolution skills. Flexible during times of change. Ability to understand the different communication styles of team members and contractors who come from a broad spectrum of disciplines. Ability to elicit cooperation from a wide variety of sources, including upper management, clients, and other departments. Apply If interested, please submit your resume to “careers@girikon. com” Share this post on: Next post →Graduate Marketing Analyst – Australia (#Girik951)", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.girikon.com/blog/jobopenings/senior-salesforce-developer-noida-girik1062/", "company_id": 3408, "source": 3, "skills": "", "title": "Senior Salesforce Developer – <PERSON><PERSON> (#Girik1062)", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 4, "max_experience": null, "apply_link": "https://www.girikon.com/blog/jobopenings/senior-salesforce-developer-noida-girik1062/", "description": "Share this post on: Senior Salesforce Developer – Noida (#Girik1062) Girikon is ranked 335 by Inc. 5000 in 2019 as one of the fastest growing private company in USA. Girikon is a Salesforce Gold Partner and has expertise in the various Salesforce Platforms, including Service Cloud, Community Cloud, Sales Cloud, Marketing Cloud, Pardot, Salesforce CPQ, Commerce Cloud, Manufacturing Cloud, Education Cloud, Finance Cloud, Billing, 3rd Party integrations, Force. com development, Lightning Development, Einstein Analytics, MuleSoft and Data Migration. Girikon’s strength lies in Salesforce implementation, customization, development, integration and support. Girikon is a Salesforce Gold Partner, HIPAA compliant, ISO 9001 & ISO 27001 certified. Along with Salesforce, Girikon also supports its customers on technology platforms such as Java, Microsoft, Oracle, Data Management services etc. Girikon is looking for Full time “Senior Salesforce Developer” with the following job description: Years of Experience – 4+ years Location – Noida Shift Timing – UK Shift (1 pm – 10 pm) Skills Needed: Hands-on extensive Salesforce experience Ability to Perform configuration, customization, integration and support of Salesforce. com Hands-on Experience in customizing using APEX, Triggers, Batch Apex, Visualforce, etc. Understanding of the Salesforce. com data model Hands-on Experience in designing of Custom Objects, Custom Fields, Page Layouts, Workflows, Record types, Validation Rules, Workflow Rules, Approval Process, Sandbox Refreshments, Apex Data Loader. Must-Have hands-on experience on Deployments using Change set, Managing release dependencies across multiple projects Understanding the release management and software development life cycle Should be able to review code of development team members Have experience to work in multiple Salesforce ORG/environment Strong communication skills to ask relevant questions in client/dev team meeting etc. & able to work around release related dependencies across projects Comfortable in client calls including executive sponsors and stakeholders Ability to convert customer requirement to technical specifications Knowledge of Salesforce recommended best practices around design and development. Good to Have: Experience in lightning implementation, LWC Eclipse IDE and ANT Deployment tool Experience in working with HTML, CSS, Ajax, JavaScript, and jQuery Experience with Subversion, Git or other SCM systems will be plus Experience on CPQ, Billing will be plus Salesforce certifications and Trailhead badges will be plus Good to have experience in Release Management tools like ANT, GitHub, Jenkins, Copado or similar Competencies/Values: Ability to work in a start-up-like culture and conform to shifting priorities, demands and timelines through analytical and problem-solving capabilities. Ability to complete projects successfully while maintaining sensitivity to political issues. Persuasive, encouraging, and motivating. Excellent negotiation and conflict resolution skills. Flexible during times of change. Ability to understand the different communication styles of team members and contractors who come from a broad spectrum of disciplines. Ability to elicit cooperation from a wide variety of sources, including upper management, clients, and other departments. Apply Interested candidates can submit their resume at “careers@girikon. com” Share this post on: ← Previous postFull Stack Developer – Bangalore (#Girik1061) Next post →Email Marketing Specialist – IT Product Sales (AI Solutions) – Noida (#Girik1063)", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.girikon.com/blog/jobopenings/tech-lead-salesforce-noida-gurgaon-girik995/", "company_id": 3408, "source": 3, "skills": "", "title": "Tech Lead – Salesforce – Noida/ Gurgaon (#Girik995)", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 2, "max_experience": 2, "apply_link": "https://www.girikon.com/blog/jobopenings/tech-lead-salesforce-noida-gurgaon-girik995/", "description": "Share this post on: Tech Lead – Salesforce – Noida/ Gurgaon (#Girik995) Girikon is ranked 335 by Inc. 5000 in 2019 as one of the fastest growing private company in USA. Girikon is a Salesforce Gold Partner and has expertise in the various Salesforce Platforms, including Service Cloud, Community Cloud, Sales Cloud, Marketing Cloud, Pardot, Salesforce CPQ, Commerce Cloud, Manufacturing Cloud, Education Cloud, Finance Cloud, Billing, 3rd Party integrations, Force. com development, Lightning Development, Einstein Analytics, Mulesoft and Data Migration. Girikon’s strength lies in Salesforce implementation, customization, development, integration and support. Girikon is a Salesforce Gold Partner, HIPAA compliant, ISO 9001 & ISO 27001 certified. Along with Salesforce, Girikon also supports its customers on technology platforms such as Java, Microsoft, Oracle, Data Management services etc. Girikon is looking for Full time “Tech Lead – Salesforce” with the following job description: Experience – 6+ Year Location – Noida/ Gurgaon Working Days – 5 Days Shift Timing – General (10 AM – 7 PM) Roles and Responsibilities: Hands-on development of solutions on the SFDC platform. Blueprint the technical design for the salesforce application considering all the functional, technical, non-functional and integration needs of the application Overall 7+ years of Experience and must have played role of Salesforce Architecture for minimum 2 years Candi<PERSON> will be responsible for the detailed design of Salesforce related projects from inception through production support through the use of UML diagrams, design documentation, and best-practice methodologies Experience in Salesforce (SFDC) CRM with end to end implementation experience and Salesforce. com integration experience including different business systems as well as integration tools. Candidate will be responsible for ensuring that the system accurately meets the defined expectations of the business unit, ensuring that proper testing is implemented and performance requirements are closely monitored by working with the development teams. Experience with environment management, release management, source control, Continuous development and Continuous Integration (ANT, GitHub, Jenkins, Copado or similar) Expertise in managing discoveries, requirement gathering & scope definition demonstrated through successful delivery of mid to level size & complex projects Collaborate with multiple stakeholders to understand the implementation requirements and deliver with quality Integration experience using both web-based technologies (Soap, Rest) and Integration/Middleware tools such as Informatica, Jitterbit and MuleSoft Extensive experience in end-to-end implementation experience with the SFDC platform and responsible for the application design, development, and support for Salesforce projects. Strong development and deployment knowledge of Lightning Components, Lightning Experience LWC, Lightning Design System, Triggers, Visual Force, Salesforce configurations, Apex classes, APEX Web services, API, AppExchange deployment, Workflow Alerts and Actions, and Approval Workflow. Lead a team of Salesforce developers, provide solutions, technical Mentoring, code review, performance tuning and coaching of the development team, Provide technical support to peers & juniors Excellent communication (written and oral) and interpersonal skills. Excellent leadership and management skills. Competencies/Values: Ability to work in a startup-like culture and conform to shifting priorities, demands and timelines through analytical and problem-solving capabilities. Ability to complete projects successfully while maintaining sensitivity to political issues. Persuasive, encouraging, and motivating. Excellent negotiation and conflict resolution skills. Flexible during times of change. Ability to understand the different communication styles of team members and contractors who come from a broad spectrum of disciplines. Ability to elicit cooperation from a wide variety of sources, including upper management, clients, and other departments. Apply Interested candidates can submit their resume at “careers@girikon. com” Share this post on: ← Previous postGraduate Marketing Analyst – Australia (#Girik951) Next post →HubSpot Strategist – Noida India (#Girik1054)", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.girikon.com/blog/jobopenings/sr-salesforce-developer-dallas-txgirik1065/", "company_id": 3408, "source": 3, "skills": "", "title": "Sr Salesforce Developer – Dallas, TX(#Girik1065)", "location": "", "location_type": "onsite", "job_type": "contract", "min_experience": 6, "max_experience": 6, "apply_link": "https://www.girikon.com/blog/jobopenings/sr-salesforce-developer-dallas-txgirik1065/", "description": "Share this post on: Sr Salesforce Developer – Dallas, TX(#Girik1065) Be part of a rapidly growing IT Services company in the Phoenix Valley, with a chance to work on challenging assignments and grow along with the growth of the company. “Girikon Inc” is a business consulting, technology & development company based in Phoenix, AZ. Girikon is a Salesforce Gold Consulting Partner, HubSpot Diamond Implementation Partner, Oracle Partner, Microsoft Silver Partner & Talend Partner. Girikon is looking to onboard an organized and detail-oriented “Sr Salesforce Developer” to assist with Testing activities on Salesforce Platform. Our ideal candidate will be a strong Salesforce Tester, outstanding listener, and an enthusiastic team-player with Scrum experience. Consultant will join our team only after the successful completion of Background check, Reference check, E-verify process & Drug Test. We are an Equal Opportunity Employer that values diversity in the workplace. Location – Phoenix, AZ / Dallas, TX Responsibilities include but are not limited to: Develop solutions within Salesforce Government Cloud using OmniStudio, Apex, Visualforce, Web Services, SOQL, AJAX, XML, JavaScript, and HTML. Configure Salesforce settings including Permission Sets, Sharing Rules, Roles, Organization-Wide Defaults (OWD), and object relationships. Integrate and work with Azure Active Directory for identity and access management. Customize Salesforce components such as fields, page layouts, record types, reports, and dashboards to meet business needs. Design, configure, and implement custom objects and code to support unique business processes. Perform end-to-end Salesforce customization, system integration, and platform administration. Manage data operations, including imports, exports, updates, and general system customization tasks. Qualifications: Over 6 years of experience in Salesforce development, with strong expertise in OmniStudio. Bachelor’s degree in computer science or a related field, with a solid academic record. Willingness and ability to work onsite as required. Proven development experience within public sector organizations. Salesforce Developer Certification is required. Strong verbal and written communication skills. Demonstrated ability to work independently as well as collaboratively in a team-oriented environment. Proficient in Agile development methodologies and tools, including Jira and Confluence. Why part of Girikon? Competitive salary commensurate with Industry Standards. Enrollment in group health plan (Medical, Dental, and Vision). Training and development of our employees Paid Personal Time Off and Sick days. Standard public holidays for all employees. Work/Life balance. Apply Interested candidates can submit their resume at “hrusa@girikon. com” Share this post on: ← Previous postSalesforce Consultant – Phoenix, AZ / Dallas, TX(#Girik1064)", "ctc": null, "currency": null, "meta": {}}]