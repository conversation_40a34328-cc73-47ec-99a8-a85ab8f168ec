[{"jd_link": "https://softjourn.com/careers/vacancies/senior-full-stack-engineer-react-node-js-wroclaw", "company_id": 3410, "source": 3, "skills": "", "title": "Senior Full-Stack Engineer (React+Node.js)", "location": "Location Wroclaw, Worldwide, Remote", "location_type": null, "job_type": "part_time", "min_experience": 4, "max_experience": 20, "apply_link": "https://softjourn.com/careers/vacancies/senior-full-stack-engineer-react-node-js-wroclaw", "description": "About the project: We are looking for part-time (with up to 100% potential availability) contractors with a technology stack that might be considered for any projects in SoftJourn that are looking for Senior Full-Stack (React+Node. js) Developers for temporary (or permanent) assignments. There is no specific project to which we've related this search. Still, we may suggest some typical projects and opportunities as examples where the entrant might be involved. Typical projects have the following traits:• It has a RESTful Application Programming Interface system under maintenance, a Web application for customers, a Website for guests, Dashboard, and complex interactive front-end interfaces. • It's using PostgreSQL, MongoDB or MySQL RDS as DBs. • Project hosted on AWS. Project technologies:• back-end: Node. js, PostgreSQL, Redis, MongoDB• front-end: React, React Router, Redux/Context API, TypeScript, ExpressJS, CSS-in-JS solutions. • tools: npm, grunt, webpack, ESLint, Jest, React Testing Library. The ideal candidate will be comfortable working on both client and server sides of applications, with a strong understanding of modern JavaScript principles and best practices, including SOLID design patterns. Experience with AI integration tools and services would be advantageous. Requirements:Strong experience with Node. js and Express. js and/or Nest. js (5+ years), demonstrating a deep understanding of event-driven architecture, async patterns, server optimization, and production-grade API development. Experience with database technologies including PostgreSQL, MongoDB, and MySQL RDS with proven ability to design complex schemas, optimize queries, implement efficient data access patterns, and leverage Redis caching. strategies(read-through, write-through) for high-traffic applications. Hands-on expertise with tools like OpenAPI/Swagger, Postman, and processes for API contract testing. Strong proficiency with React ecosystem (4+ years), including deep knowledge of React hooks, component lifecycle, advanced state management patterns (Redux/Context API), performance optimization, and complex UI system design. Solid experience with modern frontend build pipelines, bundlers (Webpack, Vite), module systems, and optimization techniques for production environments. Advanced skills with CSS/CSS-in-JS frameworks (e. g. Tailwind, Styled-Components) and design system tools (Figma or Storybook). Advanced TypeScript expertise across Javascript full stack, including custom type definitions, generics, conditional types, and ability to design type-safe APIs and components. Proven experience developing complex full-stack applications with microservices and/or serverless approaches, ensuring scalability and maintainability. Demonstrated experience implementing and designing RESTful APIs following best practices. Experience with integrating third-party services. Understanding of modern integration patterns. Thorough knowledge and practical application of OOP, SOLID principles, design patterns, and enterprise integration concepts (versioning strategy, naming convention standards, etc. ) in production environments. Following and optimizing coding standards, architecture patterns/styles, and development best practices across teams. Upper-intermediate English level sufficient for technical discussions, documentation writing, code reviews, and effective team collaboration in an international environment. Proactivity and continuous learning (demonstrated ability to research and come up with possible problem solutions, stay ahead of emerging trends, especially in AI and cloud technologies). Will be a plus:Experience with developing AI-powered features using APIs (OpenAI, Claude), and integrating GitHub Copilot workflows. Experience with AWS/Azure/GCP microservice-based architectures with Docker containers and Kubernetes/ECS orchestration, backed by IaC CI/CD pipelines. Experience with Serverless Framework or AWS SAM. Knowledge of GraphQL would be a big plus. Knowledge of service workers, offline caching strategies, and PWA best practices. Experience with code audit. Handling the audit process through stages like (static analysis, manual review, remediation tracking) and provide deliverables like risk map, a remediation plan. Conducting code audits for security vulnerabilities (static code analyzers like SonarQube, Snyk, dependency vulnerability scanning), performance bottlenecks, and technical debt assessment, with knowledge of industry security standards and compliance requirements. Responsibilities:Design, develop, and deploy scalable web applications including React, TypeScript, Redux/Context API for the frontend. Develop and maintain RESTful APIs using Node. js and Express. js. Implement data models with PostgreSQL, MongoDB, or MySQL RDS. Ensure optimal performance with Redis caching strategies for high-traffic applications. Maintain consistency in code quality, documentation, and development workflows. Conduct comprehensive code reviews. Facilitate knowledge transfer sessions. Implement and optimize development processes using a modern AI toolset. Implement automated testing strategies (unit, integration, e2e). On demand, participate in code audits and provide recommendations for codebase improvements and risk mitigation. Partner closely with PM, UX/UI designers, BAs, and Solution Architects throughout project implementation. About us:Softjourn is a full-cycle consulting and software development company, with expert product teams experienced in Fintech, Media & Entertainment, with a special emphasis on Ticketing. Headquartered in Silicon Valley, California, with R&D offices in Ukraine, Poland, and Brazil, Softjourn is a global software development company with over 20 years of experience. Softjourn has been honored as a veteran-friendly business by the Veteran Hub in Ukraine. We are committed to creating a supportive environment for veterans and implementing processes that address their needs. We highly value the unique skills and perspectives that military veterans bring to our company and are dedicated to assisting their transition to the workforce. Softjourn Inc. is an Equal Opportunity Employer. We celebrate diversity in all forms and are committed to maintaining a discrimination-free workplace that treats applicants and employees with dignity and respect. Our employment process is conducted without regard to race, color, religion, nationality or ethnic background, sex, pregnancy, sexual orientation, gender identity or expression, age, disability, protected veteran status, genetic information, or other attributes protected by state, federal, and local law.", "ctc": null, "currency": null, "meta": {}}]