[{"jd_link": "https://www.segurotechnologies.com/careers?id=16523913675db0e3a7bac65a72b2c8c7335729a7cf", "company_id": 3323, "source": 3, "skills": "ASP.NET ,C# , EFCore, .NetCore, MS SQL, TSQL, jQuery, WebAssembly, React/Angular, GIT, Azure, Unit Testing", "title": ".NET DEVELOPERS", "location": "Kochi\nFull Time\n#1022", "location_type": null, "job_type": null, "min_experience": 1, "max_experience": 2, "apply_link": "https://www.segurotechnologies.com/careers?id=16523913675db0e3a7bac65a72b2c8c7335729a7cf", "description": "Job Description Hands-on experience on at least 1 large project Knowledge of below mentioned skills in web based technology Must be familiar with Software Development Lifecycle Skills ASP. NET ,C# , EFCore, . NetCore, MS SQL, TSQL, jQuery, WebAssembly, React/Angular, GIT, Azure, Unit Testing Required Experience & Qualifications Experience: 1-2 Years (4 nos. )", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.segurotechnologies.com/careers?id=1652391418f7553282f791079702ee8c18e4c73c3e", "company_id": 3323, "source": 3, "skills": "ASP.NET ,C# , EFCore, .NetCore, MS SQL, TSQL, jQuery, WebAssembly, React/Angular, Agile, OOPS, GIT, Visio, Azure, Unit Testing, Design Principles", "title": ".NET SR. DEVELOPERS", "location": "Kochi\nFull Time\n#1021", "location_type": null, "job_type": null, "min_experience": 2, "max_experience": 4, "apply_link": "https://www.segurotechnologies.com/careers?id=1652391418f7553282f791079702ee8c18e4c73c3e", "description": "Job Description Hands-on experience on at least 2 large end to end implementation Possess deep knowledge of the skills mentioned below, information architecture and have a record of successful project delivery of web based applications Should have participated in all aspects of analysis, design, development, integration, and installation of the solution. Needs to be detail-oriented and understand change impact on existing code and systems. Anticipate and identify issues that may affect deliverables, manage project releases. Ability to develop and implement corrective actions. Ensure that all applications are developed to comply with architectural standards and established methodologies and techniques Must be familiar with Software Development Lifecycle. Skills ASP. NET ,C# , EFCore, . NetCore, MS SQL, TSQL, jQuery, WebAssembly, React/Angular, Agile, OOPS, GIT, Visio, Azure, Unit Testing, Design Principles Required Experience & Qualifications Experience: 2-4 years (4 nos. ) Mandatory: At least minimum of 2+ years of project experience not just IT experience", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.segurotechnologies.com/careers?id=16523914281bfd46f0328f548e2467f2f8affcfa0b", "company_id": 3323, "source": 3, "skills": "PHP , MySQL , WordPress, Laravel, Web Services, Agile, OOPS, GIT, Unit Testing, Design Principles\n\n*Added Advantage: HTML, CSS, jQuery, React/Angular", "title": "PHP DEVELOPERS", "location": "Kochi\nFull Time\n#1025", "location_type": null, "job_type": null, "min_experience": 1, "max_experience": 2, "apply_link": "https://www.segurotechnologies.com/careers?id=16523914281bfd46f0328f548e2467f2f8affcfa0b", "description": "Job Description Hands-on experience on at least 1 large project Good working knowledge of PHP MVC web frameworks and Object-oriented programming Must be familiar with Software Development Lifecycle Skills PHP , MySQL , WordPress, Laravel, Web Services, Agile, OOPS, GIT, Unit Testing, Design Principles *Added Advantage: HTML, CSS, jQuery, React/Angular Required Experience & Qualifications Experience: 1-2 Years (2 nos. ) Mandatory: At least minimum of 2+ years of project experience not just IT experience", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.segurotechnologies.com/careers?id=165239143825f6b3add468ba61c2bef1d3cdb8ce59", "company_id": 3323, "source": 3, "skills": "PHP , MySQL , WordPress, Laravel, Web Services, Agile, OOPS, GIT, Unit Testing, Design Principles\n\n*Added Advantage: HTML, CSS, jQuery, React/Angular", "title": "PHP SR. DEVELOPERS", "location": "Kochi\nFull Time\n#1026", "location_type": null, "job_type": null, "min_experience": 2, "max_experience": 4, "apply_link": "https://www.segurotechnologies.com/careers?id=165239143825f6b3add468ba61c2bef1d3cdb8ce59", "description": "Job Description Hands-on experience on at least 2 large end to end implementation Possess deep knowledge of the skills mentioned below, information architecture and have a record of successful project delivery of web based applications Good working knowledge of PHP MVC web frameworks and Object-oriented programming Experience with Rest API Development using PHP Good understanding of PHP Should be good with WordPress (Rest,Hooks,Custom post types and Taxonomies) Experience in creating/customizing WordPress themes/plugins Should have participated in all aspects of analysis, design, development, integration, and installation of the solution. Needs to be detail-oriented and understand change impact on existing code and systems. Anticipate and identify issues that may affect deliverables, manage project releases. Ability to develop and implement corrective actions. Ensure that all applications are developed to comply with architectural standards and established methodologies and techniques. Must be familiar with Software Development Lifecycle. Skills PHP , MySQL , WordPress, Laravel, Web Services, Agile, OOPS, GIT, Unit Testing, Design Principles *Added Advantage: HTML, CSS, jQuery, React/Angular Required Experience & Qualifications Experience: 2-4 years (2 nos. ) Mandatory: At least minimum of 2+ years of project experience not just IT experience", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.segurotechnologies.com/careers?id=165239144760710fe32051a4822a246bf45a718de4", "company_id": 3323, "source": 3, "skills": "Recruitment, Payroll, Good English (Written & Oral), Email Eti<PERSON>te, Microsoft Office, Basic Accounting, Ethics & Values", "title": "HR", "location": "Kochi\nFull Time\n#1030", "location_type": null, "job_type": null, "min_experience": 1, "max_experience": 2, "apply_link": "https://www.segurotechnologies.com/careers?id=165239144760710fe32051a4822a246bf45a718de4", "description": "Job Description Good understanding of recruitment process and Payroll management Good English communication skills (Written & Oral) Good interpersonal skills Knowledge of Microsoft Office tools like Word, Excel, PowerPoint Good email etiquette Basic accounting knowledge Should possess good ethics & values Skills Recruitment, Payroll, Good English (Written & Oral), Email Etiquette, Microsoft Office, Basic Accounting, Ethics & Values Required Experience & Qualifications Experience: 1-2 years (1 no. )", "ctc": null, "currency": null, "meta": {}}]