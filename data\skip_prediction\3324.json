[{"jd_link": "https://cynoteck.com/applyforjobs?id=Business%20Central%20Technical%20Consultant%20", "company_id": 3324, "source": 3, "skills": "●  4 - 6 years of hands-on experience in Microsoft Dynamics 365 Business Central or NAV development (optional).\n  ●  Strong knowledge of AL (Application Language) and C/AL coding.\n  ●  Experience with Visual Studio Code and extensions for Business Central development.\n  ●  Familiarity with SQL Server and database management.\n  ●  Experience working with Power Automate, ●  Experience with REST APIs, OData, and SOAP services.\n      ●  Understanding Azure integrations is a plus., ●  Ability to troubleshoot and debug Business Central solutions effectively.\n      ●  Strong problem-solving skills and attention to detail., ●  Excellent communication skills to work closely with functional consultants and clients.\n      ●  Ability to work in an agile and waterfall development environment., ●  Microsoft Certified: Dynamics 365 Business Central Functional Consultant Associate.", "title": "Business Central Technical Consultant", "location": "", "location_type": null, "job_type": "contract", "min_experience": 1, "max_experience": 6, "apply_link": "https://cynoteck.com/applyforjobs?id=Business%20Central%20Technical%20Consultant%20", "description": "Job Description We are looking for a passionate and skilled Business Central Technical Consultant to join our team. The ideal candidate will be responsible for the design, development, and implementation of solutions in Microsoft Dynamics 365 Business Central. They will collaborate with functional consultants, understand client requirements, and deliver robust and scalable solutions with the help of our Team. Location: Dehradun,Noida (On-Site) Requirement: 1 Years of Experience: 4-6 years Key Responsibilities: Develop, customize, and implement Microsoft Dynamics 365 Business Central solutions. Design and develop extensions and integrations using AL and other related technologies. Debug, troubleshoot, and optimize Business Central solutions for performance. Collaborate with functional consultants to understand client requirements and translate them into technical specifications. Build integration between Business Central and third-party applications using APIs and web services. Maintain technical documentation for customizations and processes. Participate in code reviews to ensure best practices and high-quality code standards. Stay updated with the latest features, updates, and best practices in Dynamics 365 Business Central. Proficiency in creating and customizing RDLC, Word Layout, and Excel-based reports. Take part in client calls wherever required along with team. Required Skills: Technical Expertise: ● 4 - 6 years of hands-on experience in Microsoft Dynamics 365 Business Central or NAV development (optional). ● Strong knowledge of AL (Application Language) and C/AL coding. ● Experience with Visual Studio Code and extensions for Business Central development. ● Familiarity with SQL Server and database management. ● Experience working with Power Automate Integration Skills: ● Experience with REST APIs, OData, and SOAP services. ● Understanding Azure integrations is a plus. Analytical and Problem-Solving Skills: ● Ability to troubleshoot and debug Business Central solutions effectively. ● Strong problem-solving skills and attention to detail. Team Collaboration: ● Excellent communication skills to work closely with functional consultants and clients. ● Ability to work in an agile and waterfall development environment. Certifications (Preferred): ● Microsoft Certified: Dynamics 365 Business Central Functional Consultant Associate. Optional Skills: Experience in Power Platform tools (Power BI, Power Apps, Power Automate). Knowledge of version control tools like Git. Exposure to Agile or Scrum project methodologies.", "ctc": null, "currency": null, "meta": {}}]