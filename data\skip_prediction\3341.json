[{"jd_link": "https://www.iblesoft.com/jobs/automation-software-engineer/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Responsible for working within a fast-paced environment working with team members to design, build, and support RPA applications for our clients.\nDesign and develop RPA solutions/ automation as per business requirements.\nWorking with clients and senior developers to execute RPA initiatives by analyzing and design process workflows, building, testing and implementing RPA solutions and ensuring quality of the system.\nResponsible for understanding the current process.\nConfigure new processes and objects using core workflow principles that are efficient, maintainable and readable.\nManage cross-functional projects to implement Business Performances and RPA efficient solutions.\nPreparing the SDD document after understanding the process thoroughly.\nParticipating in trainings to enhance skills and abilities in RPA, including tools and best practices.\nAct as Controls and Automation engineer with role of designing and developing software for drilling applications based on given requirements and specification.\nHave a good understanding of basic machine controls which include induction motor control using AC Drives, MCCs, hydraulic/pneumatics and valve controls.\nControl software development will also include developing machine control algorithms for PLC based systems.\nHMI design and implementation, instrumentation and sensor interface, data logging, and communication interfaces with other industrial processors like PLC s\nDuties will include generation of Design documentation, PLC Code, HMI applications, Bill of Materials, Network Architectures, One Line Drawing, Operations manuals, United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "Automation Software Engineer", "location": "", "location_type": null, "job_type": "full_time", "min_experience": 5, "max_experience": 9, "apply_link": "https://www.iblesoft.com/jobs/automation-software-engineer/", "description": "Full Time USA Job Posted Date: December 16, 2019 The scope of the project is to build an enterprise data warehouse to integrate property, lease, unit and other important data related to prologis globally and analyze it to understand the overall performance of the organization and to improve the same. It involves extracting data from various source systems and store it in a single data store after applying required business transformations. Responsible for working within a fast-paced environment working with team members to design, build, and support RPA applications for our clients. Design and develop RPA solutions/ automation as per business requirements. Working with clients and senior developers to execute RPA initiatives by analyzing and design process workflows, building, testing and implementing RPA solutions and ensuring quality of the system. Responsible for understanding the current process. Configure new processes and objects using core workflow principles that are efficient, maintainable and readable. Manage cross-functional projects to implement Business Performances and RPA efficient solutions. Preparing the SDD document after understanding the process thoroughly. Participating in trainings to enhance skills and abilities in RPA, including tools and best practices. Act as Controls and Automation engineer with role of designing and developing software for drilling applications based on given requirements and specification. Have a good understanding of basic machine controls which include induction motor control using AC Drives, MCCs, hydraulic/pneumatics and valve controls. Control software development will also include developing machine control algorithms for PLC based systems. HMI design and implementation, instrumentation and sensor interface, data logging, and communication interfaces with other industrial processors like PLC s Duties will include generation of Design documentation, PLC Code, HMI applications, Bill of Materials, Network Architectures, One Line Drawing, Operations manuals Qualification: Bachelor’s degree in Computer Science Engineering, Information Technology, Computer Applications, any other related field. Location: Doral, Florida. Send Resume to: HR Dept. , Iblesoft Inc. ,7801 NW 37TH Street, Suite LP-104, Doral, FL 33195. All employees of Iblesoft, Inc. are automatically enrolled in the employee referral program of the company. Referral fee of $1,000 will be paid if referred candidate is hired by the company. Job Features Job Category IT Experience 5-9 years No. of position Multiple Job Role Automation Software Engineer", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/backup-and-storage-engineer/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Designing and implementing TSM, Avamar and Networker servers across the sites and perform administrative activities like backup management, capacity management, etc. and leverage it to the operations team.\nImplemented the designed Networker data zone at multiple datacenters configured the libraries and data domains to be ready for backups and restores.\nPerformed migration of clients from old Networker data zones to the new Networker data zones.\nCustomizing the backup environment, managing number of groups, number of clients in each group, backup schedule times, and configuring policy directive resource.\nConfiguring Data Domain to Networker for de-duplication using boost protocol.\nSetting up network environments using TCP/IP, NIS, NFS, DNS, SNMP, VSFTP and DHCP.\nBuilds, Install, configure brand new virtual and physical servers, test, deploy RHEL 6 and 7, CentOS 6.4 servers to the network, OS installation and configuration – standard and advanced (net installation and jumpstart, kick start).\nInstallation, Configuration and maintenance of VMware in an Enterprise Environment and also configuring Virtual Machines on the VMware host.\nPrimarily coordinate with various third-party teams (IBM, EMC and Red hat) across the globe, Delivery and Implementation Managers, and Consultants in monitoring, identifying and resolving client issues/requests /Incidents/Change Requests (RFC) and Problem Tickets.\nExperience on supporting heterogeneous environments with NetBackup\nConfiguration & management of Virtual Tape Libraries, Enterprise to mid-level tape libraries.\nInstalling and configuring Avamar clients, SQL and oracle with DDBOOST\nMonitoring System Performance of Virtual memory, Management Swap Space, Disk utilization and CPU utilization.\nConfiguring and Maintaining LVM on Linux servers with flexibility of data storage and data migration.\nConsult with customers or other departments on project status, proposals, or technical issues, such as software system design or maintenance.\nCustomizing the backup environment, managing number of groups, number of clients in each group, backup schedule times and configuring policy directive resource.\nConfiguring Data Domain to Networker for de-duplication using boost protocol.\nAnalyze information to determine, recommend, and plan the installation of a new system or modification of an existing system.\nConduct network vulnerability assessments using tools to evaluate attack vectors, identify system vulnerabilities and develop remediation plans and security procedures.\nUpgrading backup agent and plugins on multiple backup clients for maintaining supports on all software., United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "Backup and Storage Engineer", "location": "", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/backup-and-storage-engineer/", "description": "Job Posted Date: September 4th, 2018 A Backup and Storage Engineer with expertise in planning, designing and implementing complex enterprise backup and storage recovery solutions using a various technology across the Data Centers. Constantly devising and implementing new efficiencies that directly contribute to the success of the company. The most critical tasks to be performed on the job are identifying and clearly defining the needs of the customer. A Backup and Storage Engineer is responsible for creating enterprise solutions for new and existing customers, as well as increasing the customer satisfaction by strategically aligning their critical business needs with the ever evolving technology. Continuously reviewing the environment and assessing the needs. Partnering with the sales pursuit team for providing technical design, and based on that delivering solutions to the customer executive team. Working with a broad level of enterprise products to deliver customer needs, in addition to evaluating those products to add and balance our services. Designing and implementing TSM, Avamar and Networker servers across the sites and perform administrative activities like backup management, capacity management, etc. and leverage it to the operations team. Implemented the designed Networker data zone at multiple datacenters configured the libraries and data domains to be ready for backups and restores. Performed migration of clients from old Networker data zones to the new Networker data zones. Customizing the backup environment, managing number of groups, number of clients in each group, backup schedule times, and configuring policy directive resource. Configuring Data Domain to Networker for de-duplication using boost protocol. Setting up network environments using TCP/IP, NIS, NFS, DNS, SNMP, VSFTP and DHCP. Builds, Install, configure brand new virtual and physical servers, test, deploy RHEL 6 and 7, CentOS 6. 4 servers to the network, OS installation and configuration – standard and advanced (net installation and jumpstart, kick start). Installation, Configuration and maintenance of VMware in an Enterprise Environment and also configuring Virtual Machines on the VMware host. Primarily coordinate with various third-party teams (IBM, EMC and Red hat) across the globe, Delivery and Implementation Managers, and Consultants in monitoring, identifying and resolving client issues/requests /Incidents/Change Requests (RFC) and Problem Tickets. Experience on supporting heterogeneous environments with NetBackup Configuration & management of Virtual Tape Libraries, Enterprise to mid-level tape libraries. Installing and configuring Avamar clients, SQL and oracle with DDBOOST Monitoring System Performance of Virtual memory, Management Swap Space, Disk utilization and CPU utilization. Configuring and Maintaining LVM on Linux servers with flexibility of data storage and data migration. Consult with customers or other departments on project status, proposals, or technical issues, such as software system design or maintenance. Customizing the backup environment, managing number of groups, number of clients in each group, backup schedule times and configuring policy directive resource. Configuring Data Domain to Networker for de-duplication using boost protocol. Analyze information to determine, recommend, and plan the installation of a new system or modification of an existing system. Conduct network vulnerability assessments using tools to evaluate attack vectors, identify system vulnerabilities and develop remediation plans and security procedures. Upgrading backup agent and plugins on multiple backup clients for maintaining supports on all software. Qualification: Bachelor’s degree in Computer Applications, Computer Science, Electronics and Communication Engineering. Skills: TSM, LVM, SQL and oracle with DDBOOST, Avamar Location: Doral, Florida. * Relocation may be required. Send Resume to: HR Dept. , Iblesoft Inc. ,7801 NW 37TH Street, Suite LP-104, Doral, FL 33195. All employees of Iblesoft, Inc. are automatically enrolled in the employee referral program of the company. Referral fee of $1,000 will be paid if referred candidate is hired by the company.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/business-intelligence-analyst/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Perform statistical analysis on data that is modelled using SSIS and develop visualizations for business users.\nUtilizing share point server for deploying the developed reports and allowing users to access the reports from there to further perform the statistical evaluation on top of reports according to their need. Reports can be accessed only depending on the privileges each member have on the server.\nReport development includes analysis of the data and generating visualizations like dashboards using SSRS for presenting and easy understanding to business users., Apply SQL Server Reporting Services (SSRS) to format the extracted data within the pipeline by grouping and deduping mechanisms before passing it to the front end server.\nDevelop, maintain and execution of extraction, transformation and load (ETL) routines for importing data from heterogeneous data sources into the data warehouse using SQL Server Integration Services (SSIS). using SQL Server Integration Services (SSIS).\nDevelop SSIS packages to pull the data from API and store the data into the Data warehouse.\nChange to parallel path processing from single path for data pulling process to improve the timing of a SSIS package., Develop database objects and create ETL processes.\nDepending on the requirements of users, ETL processes are created in SQL Server Business Intelligence Studio and various data sources used for creating those packages are documented in Microsoft One Note for getting sign off from Team Lead.\nCreate databases, table spaces, tables, indexes, triggers, procedures, and other database objects.\nWays to improve the performance of existing packages are to be analyzed very often and are implemented by inserting stored procedures, views, clusters, indexes using SQL query. Generate Data structure diagrams (DSD) to describe conceptual data models., Involve in development, bug-fix, troubleshooting issues, performance tuning efforts.\nTroubleshooting production servers and fixed the user and application issues.\nIdentify the reason, troubleshoot and fix bugs if there are any, run SSIS packages\nPerform quantitative and qualitative analysis of these data sets and summarize findings and recommendations in reports using QlikView reporting tool., Interaction with teams for gathering system requirements and determining the technical feasibility of the requirements.\nDepending on the requirements of users, ETL processes are created in SQL Server Business Intelligence Studio and various data sources used for creating those packages are documented in Microsoft One Note for getting sign off from Team Lead.\nBusiness intelligence Visual studio is used in performing all ETL functions needed for creating multi-dimensional data packages where normalized data is stored in staging database and then performed transformations depending on the requirements by users which are used for reporting, contestations and financial reconciliations., Collaborate with users, business analysts, database administrators, and project managers on reporting and data management projects.\nDevelop information communication procedures for gathering ETL requirements and analyzing the requirements of users thoroughly. Can be achieved by attending daily scrum calls and face to face discussions with business analysts whenever needed., Collaborate with data architect to create conceptual, logical, and physical data models for reporting databases.\nCreating ER diagrams in MS Visio and presenting to data architects and Project Managers for elaborating the existing solutions and incorporating information about new centers and new line of business., Analyze competitive market strategies through analysis of related product, market, or share trends\nIdentify and analyze industry or geographic trends with business strategy implications\nAnalyze and identify opportunities for business process improvements.\nUse best practices to carry out business process analysis, re-engineering, process measurements and change management activities., Define, implement and maintain business processes and procedures to meet business objectives.\nProviding technical support to existing solutions in current and previous line of business and exploring new solutions with improved performance.\nSupport to existing solutions are performed by monitoring the schedules and evaluating incidents/defects, investigate, identify root causes on priority basis using Assembla ticket generating system., Changes to existing solutions in terms of transformations are performed based on requirements provided by users using Visual Studio 2015.\nImplement changes and provide post-implementation user support and system support.\nImplement change to parallel path processing from single path for data pulling process to improve the timing of a SSIS package, Document Offer Data mapping between the product catalog and the cache to effectively maintain a sync between multiple data sources\nMaintaining a data mapping document for clearly explaining various data points captured through EMR. Tables and views from reference database will help in providing detailed information\nProgressively document the evolving data architecture with every major feature on the global documentation platform to maintain visibility and provide status updates for all involved stakeholders., United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "Business Intelligence Analyst", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/business-intelligence-analyst/", "description": "Develop Reports & Dashboards using Microsoft BI Suite (Statistical Analysis, Trending Route Cause Analysis, What-if Scenarios, Network Design, Data Management Service Automated Alters Reporting, Data-Intensive). – 10% Perform statistical analysis on data that is modelled using SSIS and develop visualizations for business users. Utilizing share point server for deploying the developed reports and allowing users to access the reports from there to further perform the statistical evaluation on top of reports according to their need. Reports can be accessed only depending on the privileges each member have on the server. Report development includes analysis of the data and generating visualizations like dashboards using SSRS for presenting and easy understanding to business users. Review SQL Code and SSIS packages to ensure compliance with the defined standards and best practices and ensure optimal performance when released to the production environment. -10% Apply SQL Server Reporting Services (SSRS) to format the extracted data within the pipeline by grouping and deduping mechanisms before passing it to the front end server. Develop, maintain and execution of extraction, transformation and load (ETL) routines for importing data from heterogeneous data sources into the data warehouse using SQL Server Integration Services (SSIS). using SQL Server Integration Services (SSIS). Develop SSIS packages to pull the data from API and store the data into the Data warehouse. Change to parallel path processing from single path for data pulling process to improve the timing of a SSIS package. Assess data retrieved from various data sources for data integrity for analytical purposes, sort, partition and segment data for further analysis. -10% Develop database objects and create ETL processes. Depending on the requirements of users, ETL processes are created in SQL Server Business Intelligence Studio and various data sources used for creating those packages are documented in Microsoft One Note for getting sign off from Team Lead. Create databases, table spaces, tables, indexes, triggers, procedures, and other database objects. Ways to improve the performance of existing packages are to be analyzed very often and are implemented by inserting stored procedures, views, clusters, indexes using SQL query. Generate Data structure diagrams (DSD) to describe conceptual data models. Troubleshoot data issues, verification and validation of result sets, recommended and implemented process improvements. -10% Involve in development, bug-fix, troubleshooting issues, performance tuning efforts. Troubleshooting production servers and fixed the user and application issues. Identify the reason, troubleshoot and fix bugs if there are any, run SSIS packages Perform quantitative and qualitative analysis of these data sets and summarize findings and recommendations in reports using QlikView reporting tool. Design BI solutions appropriate to meet the information and decision support requirement soft he business. -10% Interaction with teams for gathering system requirements and determining the technical feasibility of the requirements. Depending on the requirements of users, ETL processes are created in SQL Server Business Intelligence Studio and various data sources used for creating those packages are documented in Microsoft One Note for getting sign off from Team Lead. Business intelligence Visual studio is used in performing all ETL functions needed for creating multi-dimensional data packages where normalized data is stored in staging database and then performed transformations depending on the requirements by users which are used for reporting, contestations and financial reconciliations. Liaise with business analyst to develop BI solutions. -5% Collaborate with users, business analysts, database administrators, and project managers on reporting and data management projects. Develop information communication procedures for gathering ETL requirements and analyzing the requirements of users thoroughly. Can be achieved by attending daily scrum calls and face to face discussions with business analysts whenever needed. Liaise with external IT support providers to ensure effective delivery of relevant services for BI solutions. - 5% Collaborate with data architect to create conceptual, logical, and physical data models for reporting databases. Creating ER diagrams in MS Visio and presenting to data architects and Project Managers for elaborating the existing solutions and incorporating information about new centers and new line of business. Analyze data to identify business problems and provide solutions. -10% Analyze competitive market strategies through analysis of related product, market, or share trends Identify and analyze industry or geographic trends with business strategy implications Analyze and identify opportunities for business process improvements. Use best practices to carry out business process analysis, re-engineering, process measurements and change management activities. Support and maintain existing database and BI systems. -10% Define, implement and maintain business processes and procedures to meet business objectives. Providing technical support to existing solutions in current and previous line of business and exploring new solutions with improved performance. Support to existing solutions are performed by monitoring the schedules and evaluating incidents/defects, investigate, identify root causes on priority basis using Assembla ticket generating system. Implement changes to existing BI systems. -10% Changes to existing solutions in terms of transformations are performed based on requirements provided by users using Visual Studio 2015. Implement changes and provide post-implementation user support and system support. Implement change to parallel path processing from single path for data pulling process to improve the timing of a SSIS package Maintain full documentation for BI solutions and related services together with associated communications documents. 10% Document Offer Data mapping between the product catalog and the cache to effectively maintain a sync between multiple data sources Maintaining a data mapping document for clearly explaining various data points captured through EMR. Tables and views from reference database will help in providing detailed information Progressively document the evolving data architecture with every major feature on the global documentation platform to maintain visibility and provide status updates for all involved stakeholders.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/hadoop-developer-2/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Working closely with product development and provides verification and validation on products, solutions and services to ensure a high-quality customer experience.\nStore, retrieve, and manipulate data for analysis of system capabilities and requirements.\nPerform validations on new features developed by team and performs customer delivery readiness across software, firmware and hardware, covering functional and non-functional characteristics of products and solutions.\nFocused on Functional, Contract/SLA, Performance, Security and tests that reflect E2E customer usage and deliver differentiated customer experience.\nWork on customer usage scenarios, focus on test design and support the quality engineering group’s commitment to driving continuous quality.\nCreate Automation tests and support development of products and solutions using test-driven development, continuous integration and other comparable practices to iterative development and incremental delivery\nBuild tests day-to-day, collaboratively, incrementally and iteratively with DevOps process and with security.\nCollaborate in a deeply cross functional way with developers, designers, product manager to support and brainstorm requirements for feature tests, end-to-end tests and ideate solutions for thorny issues.\nUnderstanding customers’ usage scenarios for products and solutions and support the process of providing assurance to the product manager and system integration teams.\nTest and learn to identify problems and improvement opportunities, formulate hypotheses, and experiment with solutions\nDesigning, executing and improving product and solution focused automated tests in a DevOps centric continuous integration / continuous delivery (CI/CD) tooling and automation infrastructure\nEnd to end solution testing in a lab environment by setting up both device and software\nDetermine to identify issues, support and provide real time solutions., United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "<PERSON><PERSON>", "location": "", "location_type": null, "job_type": "part_time", "min_experience": null, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/hadoop-developer-2/", "description": "Job Posted Date: June 25, 2018 A Test Specialist is essentially one who researches, analyzes and designs computer-based solutions for defined, scientific or engineering problems. This includes planning, developing new automating frameworks or customized to achieve solution of a specifically defined need or problem. The first and foremost critical tasks of a Test Specialist are identification and clear definition of needs of the customer. This implies that the analyst has to understand the operations of the customer, including an intimate understanding of the end purpose of the operation. Working closely with product development and provides verification and validation on products, solutions and services to ensure a high-quality customer experience. Store, retrieve, and manipulate data for analysis of system capabilities and requirements. Perform validations on new features developed by team and performs customer delivery readiness across software, firmware and hardware, covering functional and non-functional characteristics of products and solutions. Focused on Functional, Contract/SLA, Performance, Security and tests that reflect E2E customer usage and deliver differentiated customer experience. Work on customer usage scenarios, focus on test design and support the quality engineering group’s commitment to driving continuous quality. Create Automation tests and support development of products and solutions using test-driven development, continuous integration and other comparable practices to iterative development and incremental delivery Build tests day-to-day, collaboratively, incrementally and iteratively with DevOps process and with security. Collaborate in a deeply cross functional way with developers, designers, product manager to support and brainstorm requirements for feature tests, end-to-end tests and ideate solutions for thorny issues. Understanding customers’ usage scenarios for products and solutions and support the process of providing assurance to the product manager and system integration teams. Test and learn to identify problems and improvement opportunities, formulate hypotheses, and experiment with solutions Designing, executing and improving product and solution focused automated tests in a DevOps centric continuous integration / continuous delivery (CI/CD) tooling and automation infrastructure End to end solution testing in a lab environment by setting up both device and software Determine to identify issues, support and provide real time solutions. Qualification: Bachelor’s degree in Computer Applications, Computer Science, Electronics and Communication Engineering. Location: Doral, Florida. * Relocation may be required. Send Resume to: HR Dept. , Iblesoft Inc. ,7801 NW 37TH Street, Suite LP-104, Doral, FL 33195. All employees of Iblesoft, Inc. are automatically enrolled in the employee referral program of the company. Referral fee of $1,000 will be paid if referred candidate is hired by the company.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/it-business-system-analyst-qa-2/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Analyze, develop, document and communicate business requirements.\nAnalyze problem at hand and work with Business, Product Manager and Engineering Lead to make requirements technically understandable for project team.\nPerform automation feasibility analysis and compartmentalize the areas for QA automation development and manual QA., Understand & capture the business requirement for Agile development stories and tasks for JIRA creation, analysis and level of effort estimation.\nSchedule automation design reviews and get required approvals for prioritization in Agile sprints.\nWork closely with Product and/or Business Analysts, Developers, and managers to understand the product requirements and use cases in order to deliver high quality software on schedule for production releases. Understand the project areas and software components that the requirements roll down to and identify for QA automation and manual QA feasibility analysis.\nPerform automation feasibility analysis and compartmentalize the areas for QA automation development and manual QA.\nSchedule automation design reviews and get required approvals for prioritization in Agile sprints.\nEnsure all stakeholder sign-off is captured at required intervals throughout the design process., Collaborate with required teams for revisions and assignments.\nDeploy and execute testing automation code on various environments such as, Staging and Production environments.\nMonitor and perform testing tools and help to prepare reports based on analysis of the test results.\nSignoff or reject identified code build for Production release, based on functional and regression test suite results in Staging environment.\nBuild CI/CD pipelines based on various environments such as, Staging and Production that execute functional test suites to detect regression and verify new features., Create and implement reusable JARs to be used across the applications and maintain them in repository.\nCoordinate to implement software installation and upgrade periodically and monitor system functionality using Post Implementation Verification test.\nPlan, research and promote new technologies as required in various parts of the automated testing framework within the whole QA organization., Responsible to make sure the design developed meet the Acceptance Criteria of the requirement.\nAutomate the UI, also to ensure the new changes does not affect the existing design, Analyze problem at hand and work with Business, Product Manager and Engineering Lead to make requirements technically understandable for project team., Prepare detailed test schedule, test plan, test cases, and test data documents for business review.\nDevelop program to generate daily automated test execution reports.\nConfigure reports to cover test, project metrics and look & feel.\nDesign and deliver high level testing automation design and documentation, United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "IT Business System Analyst/QA", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/it-business-system-analyst-qa-2/", "description": "JobDuties:- Understand the requirements and project needs and subsequently create and execute plans for the successful execution of the project. – 10%. Analyze, develop, document and communicate business requirements. Analyze problem at hand and work with Business, Product Manager and Engineering Lead to make requirements technically understandable for project team. Perform automation feasibility analysis and compartmentalize the areas for QA automation development and manual QA. Participate in Agile/Scrum methodology and work on user stories for the sprint. – 10%. Understand & capture the business requirement for Agile development stories and tasks for JIRA creation, analysis and level of effort estimation. Schedule automation design reviews and get required approvals for prioritization in Agile sprints. Work closely with Product and/or Business Analysts, Developers, and managers to understand the product requirements and use cases in order to deliver high quality software on schedule for production releases. Understand the project areas and software components that the requirements roll down to and identify for QA automation and manual QA feasibility analysis. Perform automation feasibility analysis and compartmentalize the areas for QA automation development and manual QA. Schedule automation design reviews and get required approvals for prioritization in Agile sprints. Ensure all stakeholder sign-off is captured at required intervals throughout the design process. Collaborate with software/systems personnel in application testing, such as system, unit, regression, load, and acceptance testing methods. – 20% Collaborate with required teams for revisions and assignments. Deploy and execute testing automation code on various environments such as, Staging and Production environments. Monitor and perform testing tools and help to prepare reports based on analysis of the test results. Signoff or reject identified code build for Production release, based on functional and regression test suite results in Staging environment. Build CI/CD pipelines based on various environments such as, Staging and Production that execute functional test suites to detect regression and verify new features. Install, maintain, or use software testing programs. – 15% Create and implement reusable JARs to be used across the applications and maintain them in repository. Coordinate to implement software installation and upgrade periodically and monitor system functionality using Post Implementation Verification test. Plan, research and promote new technologies as required in various parts of the automated testing framework within the whole QA organization. Develop and/or execute automated Graphical User Interface (GUI) tests and/or ensure that new functionality is automated, and the complete end-to-end automated regression test suite is run continuously as part of a continuous integration process. – 10% Responsible to make sure the design developed meet the Acceptance Criteria of the requirement. Automate the UI, also to ensure the new changes does not affect the existing design Develop and establish quality assurance measures and testing standards for new applications, products, and/or enhancements to existing applications throughout the development/product lifecycles. – 25%. Analyze problem at hand and work with Business, Product Manager and Engineering Lead to make requirements technically understandable for project team. Document the manual QA identified areas and approach for execution and communication. – 10% Prepare detailed test schedule, test plan, test cases, and test data documents for business review. Develop program to generate daily automated test execution reports. Configure reports to cover test, project metrics and look & feel. Design and deliver high level testing automation design and documentation", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/hadoop-developer/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Gathering functional requirements from business and converting them to technical designs.\nDeveloping data pipeline using Big data Hadoop tools like Sqoop, Pig and Oozie to ingest T-Mobile customer’s data into HDFS for analysis.\nDeveloping complex HIVE queries to draw data patterns to improve reporting to business.\nExporting data from HDFS to Teradata/Oracle database and vice-versa using SQOOP.\nConfigured Hive metastore with MySQL, which stores the metadata for Hive tables.\nAutomated the workflow using shell scripts.\nMastering major Hadoop distros HDP/CDH and numerous Open Source projects.\nInvolved in processing data from Local files, HDFS and RDBMS sources by creating RDD and optimizing for performance.\nDeveloping and optimizing RDD by using Cache & Persist functions.\nHave thorough knowledge on spark Sql and Hive context.\nData processing using Spark & Scala programming language.\nBuilding code with like Jenkins, GIT and moving to production environment.\nDesigned the ETL process and created the high level design document including the logical data flows, source data extraction process, the database staging and the extract creation, source archival, job scheduling and Error Handling.\nInvolved in development, testing, migrating and L3 support.\nAnalyzed the data by performing Hive queries and running Pig scripts to study customer behavior\nPreparing data refresh strategy document & Capacity planning documents required for project development and support.\nPrototype various applications that utilize modern Big Data tools.\nParticipate in Daily Sprint Meetings; Work closely with analysts, designers and other project teams.\nImplement unit test cases for various modules and coordinated with QA team for production deployments/releases, United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "<PERSON><PERSON>", "location": "", "location_type": null, "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/hadoop-developer/", "description": "Job Posted Date : April 9, 2018 The project is to build an enterprise Big Data Lake to integrate SCM, Marketing, Sales, Customer and other important data related globally and analyze it to understand the overall performance of the organization and to improve the business. It involves extracting data from various source systems and stores it in a single data lake after applying required business transformations. The module requires an expertise in Hadoop tools & technologies development using Sqoop, Pig, Hive, Hbase, NiFi, Kafka, AWS cloud and capability to research on various suitable tools. To perform these services. Gathering functional requirements from business and converting them to technical designs. Developing data pipeline using Big data Hadoop tools like Sqoop, Pig and Oozie to ingest T-Mobile customer’s data into HDFS for analysis. Developing complex HIVE queries to draw data patterns to improve reporting to business. Exporting data from HDFS to Teradata/Oracle database and vice-versa using SQOOP. Configured Hive metastore with MySQL, which stores the metadata for Hive tables. Automated the workflow using shell scripts. Mastering major Hadoop distros HDP/CDH and numerous Open Source projects. Involved in processing data from Local files, HDFS and RDBMS sources by creating RDD and optimizing for performance. Developing and optimizing RDD by using Cache & Persist functions. Have thorough knowledge on spark Sql and Hive context. Data processing using Spark & Scala programming language. Building code with like <PERSON>, GIT and moving to production environment. Designed the ETL process and created the high level design document including the logical data flows, source data extraction process, the database staging and the extract creation, source archival, job scheduling and Error Handling. Involved in development, testing, migrating and L3 support. Analyzed the data by performing Hive queries and running Pig scripts to study customer behavior Preparing data refresh strategy document & Capacity planning documents required for project development and support. Prototype various applications that utilize modern Big Data tools. Participate in Daily Sprint Meetings; Work closely with analysts, designers and other project teams. Implement unit test cases for various modules and coordinated with QA team for production deployments/releases Experience : 3+ years. Qualification : Bachelor’s degree in Computer Applications, Computer Science, Electronics and Communication Engineering. Skills : Hadoop Big data, NIFI, Kafka, Sqoop, Pig, Hive, Oracle, MySQL. Location : Doral, Florida. * Relocation may be required. Send Resume to : HR Dept. , Iblesoft Inc. ,7801 NW 37TH Street, Suite LP-104, Doral, FL 33195. All employees of Iblesoft, Inc. are automatically enrolled in the employee referral program of the company. Referral fee of $1,000 will be paid if referred candidate is hired by the company.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/it-business-system-analyst-qa-3/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Analyze, develop, document, and communicate business requirements.\nAnalyze problems at hand and work with Business, Product Manager and Engineering Lead to make requirements technically understandable for project team.\nPerform automation feasibility analysis and compartmentalize the areas for QA automation development and manual QA.\nUnderstand & capture the business requirement for Agile development stories and tasks for JIRA creation, analysis, and level of effort estimation.\nSchedule automation design reviews and get required approvals for prioritization in Agile sprints.\nWork closely with Product and/or Business Analysts, Developers, and managers to understand the product requirements and use cases to deliver high quality software on schedule for production releases.\nAnalyze problems at hand and work with Business, Product Manager and Engineering Lead to make requirements technically understandable for project team.\nUnderstand the project areas and software components that the requirements roll down to and identify for QA automation and manual QA feasibility analysis.\nPerform automation feasibility analysis and compartmentalize the areas for QA automation development and manual QA.\nSchedule automation design reviews and get required approvals for prioritization in Agile sprints.\nEnsure all stakeholder sign-off is captured at required intervals throughout the design process.\nCollaborate with required teams for revisions and assignments.\nDeploy and execute testing automation code on various environments such as, Staging and Production environments.\nMonitor and perform testing tools and help to prepare reports based on analysis of the test results.\nSignoff or reject identified code build for Production release, based on functional and regression test suite results in Staging environment.\nCoordinate to implement software installation and upgrade periodically and monitor system functionality using Post Implementation Verification test.\nPlan, research and promote new technologies as required in various parts of the automated testing framework within the whole QA organization.\nResponsible to make sure the design developed meets the Acceptance Criteria of the requirement.\nAutomate the UI, also to ensure the new changes do not affect the existing design.\nPrepare detailed test schedules, test plans, test cases, and test data documents for business review.\nDevelop a program to generate daily automated test execution reports.\nConfigure reports covering tests, project metrics and look & feel.\nDesign and deliver high-level testing automation design and documentation., Quality Management Tools: Test Director or Test Manager\nDefect tracking tools: Bugzilla or Mantis\nProject management software: Jira\nDatabase: SQL\nAPI Testing:  Postman or SoapUI, A Bachelor’s degree in Computer Science, or related field., About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "IT Business System Analyst/QA", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/it-business-system-analyst-qa-3/", "description": "Job Responsibilities: Analyze, develop, document, and communicate business requirements. Analyze problems at hand and work with Business, Product Manager and Engineering Lead to make requirements technically understandable for project team. Perform automation feasibility analysis and compartmentalize the areas for QA automation development and manual QA. Understand & capture the business requirement for Agile development stories and tasks for JIRA creation, analysis, and level of effort estimation. Schedule automation design reviews and get required approvals for prioritization in Agile sprints. Work closely with Product and/or Business Analysts, Developers, and managers to understand the product requirements and use cases to deliver high quality software on schedule for production releases. Analyze problems at hand and work with Business, Product Manager and Engineering Lead to make requirements technically understandable for project team. Understand the project areas and software components that the requirements roll down to and identify for QA automation and manual QA feasibility analysis. Perform automation feasibility analysis and compartmentalize the areas for QA automation development and manual QA. Schedule automation design reviews and get required approvals for prioritization in Agile sprints. Ensure all stakeholder sign-off is captured at required intervals throughout the design process. Collaborate with required teams for revisions and assignments. Deploy and execute testing automation code on various environments such as, Staging and Production environments. Monitor and perform testing tools and help to prepare reports based on analysis of the test results. Signoff or reject identified code build for Production release, based on functional and regression test suite results in Staging environment. Coordinate to implement software installation and upgrade periodically and monitor system functionality using Post Implementation Verification test. Plan, research and promote new technologies as required in various parts of the automated testing framework within the whole QA organization. Responsible to make sure the design developed meets the Acceptance Criteria of the requirement. Automate the UI, also to ensure the new changes do not affect the existing design. Prepare detailed test schedules, test plans, test cases, and test data documents for business review. Develop a program to generate daily automated test execution reports. Configure reports covering tests, project metrics and look & feel. Design and deliver high-level testing automation design and documentation. TECHNICAL SKILLS REQUIREMENTS Quality Management Tools: Test Director or Test Manager Defect tracking tools: Bugzilla or Mantis Project management software: Jira Database: SQL API Testing: Postman or SoapUI EDUCATION REQUIREMENTS A Bachelor’s degree in Computer Science, or related field.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/it-business-system-analyst-qa/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Analyze, develop, document and communicate business requirements.\nAnalyze problem at hand and work with Business, Product Manager and Engineering Lead to make requirements technically understandable for project team.\nPerform automation feasibility analysis and compartmentalize the areas for QA automation development and manual QA., Understand & capture the business requirement for Agile development stories and tasks for JIRA creation, analysis and level of effort estimation.\nSchedule automation design reviews and get required approvals for prioritization in Agile sprints.\nWork closely with Product and/or Business Analysts, Developers, and managers to understand the product requirements and use cases in order to deliver high quality software on schedule for production releases., Analyze problem at hand and work with Business, Product Manager and Engineering Lead to make requirements technically understandable for project team.\nUnderstand the project areas and software components that the requirements roll down to and identify for QA automation and manual QA feasibility analysis.\nPerform automation feasibility analysis and compartmentalize the areas for QA automation development and manual QA.\nSchedule automation design reviews and get required approvals for prioritization in Agile sprints.\nEnsure all stakeholder sign-off is captured at required intervals throughout the design process., Collaborate with required teams for revisions and assignments.\nDeploy and execute testing automation code on various environments such as, Staging and Production environments.\nMonitor and perform testing tools and help to prepare reports based on analysis of the test results.\nSignoff or reject identified code build for Production release, based on functional and regression test suite results in Staging environment.\nBuild CI/CD pipelines based on various environments such as, Staging and Production that execute functional test suites to detect regression and verify new features., Create and implement reusable JARs to be used across the applications and maintain them in repository.\nCoordinate to implement software installation and upgrade periodically and monitor system functionality using Post Implementation Verification test.\nPlan, research and promote new technologies as required in various parts of the automated testing framework within the whole QA organization., Responsible to make sure the design developed meet the Acceptance Criteria of the requirement.\nAutomate the UI, also to ensure the new changes does not affect the existing design, Prepare detailed test schedule, test plan, test cases, and test data documents for business review.\nDevelop program to generate daily automated test execution reports.\nConfigure reports to cover test, project metrics and look & feel.\nDesign and deliver high level testing automation design and documentation, Quality Management Tools: Test Director or Test Manager\nDefect tracking tools : BugZilla or  Mantis\nProject management software : Jira\nDatabase : SQL\nAPI  Testing :  Postman or SoapUI, A Bachelor’s degree in Computer Science, or related field., N/A, 4800 Deerwood Campus Pkwy, Jacksonville, FL-32246., From March 16,2020 to April 31, 2022., The company will have the right to assign additional duties to the incumbent, The company will have the right to assign additional duties to the incumbent, The company will exercise full discretion over when and how long the incumbent will work, The incumbent will be paid a salary of US$ 60,000  payable annually  with all taxes imposed upon an employee deducted from that salary., The company alone hired the prospective incumbent, and it reserves the sole right to terminate his employment and otherwise control his work., No assistants will be utilized by the incumbent. The incumbent works will be performed as a part of the company’s regular business, which is the provision of IT services such as he will be providing., Social Security\nMedicare\nFederal Income Tax\nState Income Tax\nState Disability Ins, The incumbent work will be directly supervised by Project Manager., United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "IT Business System Analyst/QA", "location": "", "location_type": null, "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/it-business-system-analyst-qa/", "description": "Job Posted Date: March 1st week 2020 Develop and execute exploratory and automated tests to ensure product quality. Responsibilities include designing and implementing tests, debugging and defining corrective actions. Also review system requirements and track quality assurance metrics (e. g. defect densities and open defect counts. ) ESSENTIAL JOB FUNCTIONS/DUTIES Understand the requirements and project needs and subsequently create and execute plans for the successful execution of the project. – 10%. Analyze, develop, document and communicate business requirements. Analyze problem at hand and work with Business, Product Manager and Engineering Lead to make requirements technically understandable for project team. Perform automation feasibility analysis and compartmentalize the areas for QA automation development and manual QA. Participate in Agile/Scrum methodology and work on user stories for the sprint. – 10%. Understand & capture the business requirement for Agile development stories and tasks for JIRA creation, analysis and level of effort estimation. Schedule automation design reviews and get required approvals for prioritization in Agile sprints. Work closely with Product and/or Business Analysts, Developers, and managers to understand the product requirements and use cases in order to deliver high quality software on schedule for production releases. Develop and establish quality assurance measures and testing standards for new applications, products, and/or enhancements to existing applications throughout the development/product lifecycles. – 25%. Analyze problem at hand and work with Business, Product Manager and Engineering Lead to make requirements technically understandable for project team. Understand the project areas and software components that the requirements roll down to and identify for QA automation and manual QA feasibility analysis. Perform automation feasibility analysis and compartmentalize the areas for QA automation development and manual QA. Schedule automation design reviews and get required approvals for prioritization in Agile sprints. Ensure all stakeholder sign-off is captured at required intervals throughout the design process. Collaborate with software/systems personnel in application testing, such as system, unit, regression, load, and acceptance testing methods. – 20% Collaborate with required teams for revisions and assignments. Deploy and execute testing automation code on various environments such as, Staging and Production environments. Monitor and perform testing tools and help to prepare reports based on analysis of the test results. Signoff or reject identified code build for Production release, based on functional and regression test suite results in Staging environment. Build CI/CD pipelines based on various environments such as, Staging and Production that execute functional test suites to detect regression and verify new features. Install, maintain, or use software testing programs. – 15% Create and implement reusable JARs to be used across the applications and maintain them in repository. Coordinate to implement software installation and upgrade periodically and monitor system functionality using Post Implementation Verification test. Plan, research and promote new technologies as required in various parts of the automated testing framework within the whole QA organization. Develop and/or execute automated Graphical User Interface (GUI) tests and/or ensure that new functionality is automated, and the complete end-to-end automated regression test suite is run continuously as part of a continuous integration process. – 10% Responsible to make sure the design developed meet the Acceptance Criteria of the requirement. Automate the UI, also to ensure the new changes does not affect the existing design Document the manual QA identified areas and approach for execution and communication. – 10% Prepare detailed test schedule, test plan, test cases, and test data documents for business review. Develop program to generate daily automated test execution reports. Configure reports to cover test, project metrics and look & feel. Design and deliver high level testing automation design and documentation TECHNICAL SKILLS REQUIREMENTS Quality Management Tools: Test Director or Test Manager Defect tracking tools : BugZilla or Mantis Project management software : Jira Database : SQL API Testing : Postman or SoapUI EDUCATION REQUIREMENTS A Bachelor’s degree in Computer Science, or related field. LICENSING/CERTIFICATION REQUIREMENTS N/A VENUE AND LOCATION OF THE SERVICES TO BE PERFORMED 4800 Deerwood Campus Pkwy, Jacksonville, FL-32246. DURATION From March 16,2020 to April 31, 2022. RIGHT TO ASSIGNED ADDITIONAL DUTIES The company will have the right to assign additional duties to the incumbent RIGHT TO ASSIGNED ADDITIONAL DUTIES The company will have the right to assign additional duties to the incumbent DISCRETION OVER WORK The company will exercise full discretion over when and how long the incumbent will work SALARY The incumbent will be paid a salary of US$ 60,000 payable annually with all taxes imposed upon an employee deducted from that salary. HIRING The company alone hired the prospective incumbent, and it reserves the sole right to terminate his employment and otherwise control his work. ASSISTANTS No assistants will be utilized by the incumbent. The incumbent works will be performed as a part of the company’s regular business, which is the provision of IT services such as he will be providing. TAX TREATMENT The incumbent will be treated for all tax purposes as an employee, with the following tax deducted from the wages are: Social Security Medicare Federal Income Tax State Income Tax State Disability Ins AUTHORITY TO HIRE AND FIRE AND SET RULES The company can hire or fire the incumbent and set rules and regulations on the incumbent’s Work. SUPERVISION The incumbent work will be directly supervised by Project Manager. WORKING HOURS 9:00AM to 6:00PM, Monday through Friday, with an hour off for lunch.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/project-manager/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Lead projects to achieve key business objectives .\nPredict emerging customer needs and develops innovative solutions to meet them.\nParticipate in the development of business strategy .\nDevelop and manages business plans to achieve objectives.\nSolve unique and complex problems with broad impact on the business .\nTranslate highly complex concepts in ways that can be understood by a variety of audiences .\nInfluence senior leadership to adopt new ideas, products, and/or approaches.\nDevelops and manages business plans to achieve objectives. \nCreates, maintains, and communicates department plans. \nAssign duties or work schedules to employees., United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "Project Manager", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/project-manager/", "description": "Job Posted Date : October 10, 2017 The Project Manager, will be responsible for directing and supervising support resources for the performance of project assignments and activities, and for managing the technical direction of a project through the design, implementation and testing in accordance with project objectives by using our Client’s program management tools to manage project work plans, issues, risks, and dependencies. In addition, producing status reports and conducting status meetings, and will facilitate issue resolution and risk management sessions directly with project team. Lead projects to achieve key business objectives . Predict emerging customer needs and develops innovative solutions to meet them. Participate in the development of business strategy . Develop and manages business plans to achieve objectives. Solve unique and complex problems with broad impact on the business . Translate highly complex concepts in ways that can be understood by a variety of audiences . Influence senior leadership to adopt new ideas, products, and/or approaches. Develops and manages business plans to achieve objectives. Creates, maintains, and communicates department plans. Assign duties or work schedules to employees. Qualification : Bachelor’s degree in Computer Applications, Science, Engineering, Technology. Skills :Project Management, JIRA, Workflows, PPM Tools, SaaS SDLC. Location : Doral, Florida. * Relocation may be required. Send Resume to : HR Dept. , Iblesoft Inc. ,7801 NW 37TH Street, Suite LP-104, Doral, FL 33166. All employees of Iblesoft, Inc. are automatically enrolled in the employee referral program of the company. Referral fee of $1,000 will be paid if referred candidate is hired by the company.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/sap-hana-developer-2/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Facilitate modeling sessions and design walk-through reviews with development team members\nProvide highly innovate and ingenious solutions\nLead troubleshooting efforts on complex performance tuning problems\nCommunicate and articulate model designs to peers, customers and governance teams\nDemonstrate BI (Business Intelligence) Analytical tools skillset\nExtensive hands on experience managing data from multiple data sources like SAP ERP and Non SAP system.\nExposure to SAP HANA Modeling on SAP ERP modules like Finance, Sales & Distribution, Material Management and Purchasing.\nBasic understanding of SAP ERP table structures of FI and Purchasing modules\nExposure to at least one of the BI analytical tools (Webi, Lumira, Tableau, Design Studio)\nExposure and basic understanding of data provisioning tools like SLT, SRS\nHigh degree of ‘learning agility’ with the ability to readily consume and apply new information and concepts with developed analytical problem solving skills\nGood communication skills to articulate model designs\nStrong communication skills, including with employees, clients, senior management and vendors\nStrong results orientation (driving to deadlines, financial targets, project goals, etc.\nCollaborate with stakeholders to understand business requirements and design appropriate data solutions to meet business need.\nLeverage prior experience and industry standard best practices to analyze cross functional data and provide insights to business teams.\nAssist stakeholders with data analysis, design data models & develop DB Views, Calculation Views etc. in SAP HANA to meet business need.\nConverting the business requirements into functional specification documents, and then translating functional specifications into technical design documents.\nImplement snapshot processes, data refresh schedules, exception handling procedures as necessary.\nDefine required data integration requirements between various systems and work with extended team to get them created.\nFollow all documented architecture, design & deployment processes to ensure compliance with policies.\nDesign and develop scalable, reusable data models in SAP HANA in line with architectural standards and best practices.\nData Modeling by SAP HANA Studio for building attributes views, analytic views, calculation Views and Sequel Scripts.\nOptimizing the SAP HANA models, Sequel Procedures, Scripts for better performance.\nPartner with Global BI team to help implement solutions for end user adoption.\nValidating the Report Layouts in Tableau and Data in the Reports Vs Source data and the Hana layer data.\nKeep pace with latest technology advances in SAP HANA.\nDevelop functional subject matter expertise within various areas of enterprise business processes.\nCross train other team members and convey value delivered on key initiatives to broader organization.\nConducting UAT sessions with the business for validating the report data.\nDefect fixes for issues found in continuous integration of iterative application builds.\nCommunicate status regularly with stakeholders and executive sponsors, United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "SAP HANA DEVELOPER", "location": "", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": 10, "apply_link": "https://www.iblesoft.com/jobs/sap-hana-developer-2/", "description": "Job Posted Date: July 11th, 2018 SAP HANA Developer is the one who researching, analyzing and designing computer based solutions for defined, scientific or engineering problems. This includes planning, developing new computer systems, customized, to achieve solution of a specifically defined need or problem. The first and foremost critical tasks of an Analyst are identification and clear definition of needs of the customer. This implies that the analyst has to understand the operations of the customer, including an intimate understanding of the end purpose of the operation. Facilitate modeling sessions and design walk-through reviews with development team members Provide highly innovate and ingenious solutions Lead troubleshooting efforts on complex performance tuning problems Communicate and articulate model designs to peers, customers and governance teams Demonstrate BI (Business Intelligence) Analytical tools skillset Extensive hands on experience managing data from multiple data sources like SAP ERP and Non SAP system. Exposure to SAP HANA Modeling on SAP ERP modules like Finance, Sales & Distribution, Material Management and Purchasing. Basic understanding of SAP ERP table structures of FI and Purchasing modules Exposure to at least one of the BI analytical tools (Webi, Lumira, Tableau, Design Studio) Exposure and basic understanding of data provisioning tools like SLT, SRS High degree of ‘learning agility’ with the ability to readily consume and apply new information and concepts with developed analytical problem solving skills Good communication skills to articulate model designs Strong communication skills, including with employees, clients, senior management and vendors Strong results orientation (driving to deadlines, financial targets, project goals, etc. Collaborate with stakeholders to understand business requirements and design appropriate data solutions to meet business need. Leverage prior experience and industry standard best practices to analyze cross functional data and provide insights to business teams. Assist stakeholders with data analysis, design data models & develop DB Views, Calculation Views etc. in SAP HANA to meet business need. Converting the business requirements into functional specification documents, and then translating functional specifications into technical design documents. Implement snapshot processes, data refresh schedules, exception handling procedures as necessary. Define required data integration requirements between various systems and work with extended team to get them created. Follow all documented architecture, design & deployment processes to ensure compliance with policies. Design and develop scalable, reusable data models in SAP HANA in line with architectural standards and best practices. Data Modeling by SAP HANA Studio for building attributes views, analytic views, calculation Views and Sequel Scripts. Optimizing the SAP HANA models, Sequel Procedures, Scripts for better performance. Partner with Global BI team to help implement solutions for end user adoption. Validating the Report Layouts in Tableau and Data in the Reports Vs Source data and the Hana layer data. Keep pace with latest technology advances in SAP HANA. Develop functional subject matter expertise within various areas of enterprise business processes. Cross train other team members and convey value delivered on key initiatives to broader organization. Conducting UAT sessions with the business for validating the report data. Defect fixes for issues found in continuous integration of iterative application builds. Communicate status regularly with stakeholders and executive sponsors Qualification: Bachelor’s degree in Computer Applications, Computer Science, Electronics and Communication Engineering. Skills: Webi, Lumira, Tableau, Design Studio, SLT, SRS Location: Doral, Florida. * Relocation may be required. Send Resume to: HR Dept. , Iblesoft Inc. ,7801 NW 37TH Street, Suite LP-104, Doral, FL 33195. All employees of Iblesoft, Inc. are automatically enrolled in the employee referral program of the company. Referral fee of $1,000 will be paid if referred candidate is hired by the company. Job Features Job Category IT Experience 5-10 years Skills Webi, Lumira, Tableau, Design Studio, SLT, SRS No. of position Multiple", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/sap-solution-consultant/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Solution Manager 7.2 – Installation and E2E configuration of Solution Manager 7.2 (System Preparation, Basic and Managed System Configuration) using SOLMAN_SETUP, SMSY and SLD.\nTechnical Monitoring – System monitoring, end user experience monitoring, integration monitoring (Interface Channel, PI Monitoring), BI Monitoring (BI Report and BW process chain), development of custom metrics and alerts for technical monitoring.\nBusiness Process Monitoring – Set up and configured monitoring objects depending upon various KPIs for variable product based upon industry specific.\nRun SAP like a factory- Implemented business and technical monitoring for OCC as a part of RSLAF, simple roadmap configuration for RUN SAP and ALM modules for different projects.\nDiagnostic agent – Installation of Wily Inters cope Enterprise Manager, Solution Manager diagnostic agent (SMD) host agent and connect to Solution Manager.\nBusiness Process Analytics – Configured Business Process Operations and Business Process Analytics (BPA) dashboards, implemented Business Process Change Analyzer (BPCA).\nEarly Watch Alert- Implemented early watch alert and mail notification of EWA.\nChange and Request Management – Configured CHARM with CTS+ (Change and Transport System) for n+1 landscape and customized workflows for changes requests.\nIncident Management – Configured ITSM with customized workflow to create and assign incidents in SOLMAN and tracked progress. Configured SLA (Service Level Agreement) to prioritize tickets on severity impact.\nTransport Management – Configured and supported Transport Management System (TMS) in Solution Manager and Managed System Landscape and set up domain links between landscapes to automate transports through CHARM.\nProject Management – Created template, implementation, and maintenance projects for various teams to store documents for the team.\nDocument Management – Created Business Process Hierarchy (BPH), Schema updation, data quality revision for templates and documents stored in Solution Manager.\nTest Management – Drafted and executed test-cases to check end to end functionality for CHARM and Service Desk in HPQC. Responsible for validating test cases with client, defect logging, bug and status reporting to the designated team.\nABAP – Developed program and objects to solve issues for digital signature for templates and documents stored in Solution Manager, workflow configuration for managing user’s profiles.\nInvolved in multiple project life cycle from Project Preparation, Go-Live, Post-Production support, Upgrades in various industries like Manufacturing, Utilities and Pharmaceutical/Health Care.\nResponsible for managing QA. UAT and Go-Live implementation for KPI Basis Operations dashboards.\nCoaching –  Providedtraining to the junior team members and business users on various functionalities of Solution Manager and also documented various user’s manual on different topics related to the solution manager., United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "SAP Solution Consultant", "location": "", "location_type": null, "job_type": "contract", "min_experience": null, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/sap-solution-consultant/", "description": "Job Posted Date: July 3rd, 2018 A SAP Solution Consultant is involved in designing, implementing and testing ALM functionalities of SAP Solution like ChaRM, ITSM, E2E monitoring and etc. In this position, the consultant is will also be responsible for overall SAP Solution strategy roadmap and implementation timelines, successful delivery of the SAP Solution landscapes including integration to third party tools like ARIS, HP ALM etc. It also includes reviewing project requirements and identifying gaps in the functionality of the Solution application in both the project management, Process management and monitoring areas. He will Initiate, coordinate and deliver development and implementation of system changes and work closely with SAP Solution technical developers to ensure accurate and timely code that matches the designs. Solution Manager 7. 2 – Installation and E2E configuration of Solution Manager 7. 2 (System Preparation, Basic and Managed System Configuration) using SOLMAN_SETUP, SMSY and SLD. Technical Monitoring – System monitoring, end user experience monitoring, integration monitoring (Interface Channel, PI Monitoring), BI Monitoring (BI Report and BW process chain), development of custom metrics and alerts for technical monitoring. Business Process Monitoring – Set up and configured monitoring objects depending upon various KPIs for variable product based upon industry specific. Run SAP like a factory- Implemented business and technical monitoring for OCC as a part of RSLAF, simple roadmap configuration for RUN SAP and ALM modules for different projects. Diagnostic agent – Installation of Wily Inters cope Enterprise Manager, Solution Manager diagnostic agent (SMD) host agent and connect to Solution Manager. Business Process Analytics – Configured Business Process Operations and Business Process Analytics (BPA) dashboards, implemented Business Process Change Analyzer (BPCA). Early Watch Alert- Implemented early watch alert and mail notification of EWA. Change and Request Management – Configured CHARM with CTS+ (Change and Transport System) for n+1 landscape and customized workflows for changes requests. Incident Management – Configured ITSM with customized workflow to create and assign incidents in SOLMAN and tracked progress. Configured SLA (Service Level Agreement) to prioritize tickets on severity impact. Transport Management – Configured and supported Transport Management System (TMS) in Solution Manager and Managed System Landscape and set up domain links between landscapes to automate transports through CHARM. Project Management – Created template, implementation, and maintenance projects for various teams to store documents for the team. Document Management – Created Business Process Hierarchy (BPH), Schema updation, data quality revision for templates and documents stored in Solution Manager. Test Management – Drafted and executed test-cases to check end to end functionality for CHARM and Service Desk in HPQC. Responsible for validating test cases with client, defect logging, bug and status reporting to the designated team. ABAP – Developed program and objects to solve issues for digital signature for templates and documents stored in Solution Manager, workflow configuration for managing user’s profiles. Involved in multiple project life cycle from Project Preparation, Go-Live, Post-Production support, Upgrades in various industries like Manufacturing, Utilities and Pharmaceutical/Health Care. Responsible for managing QA. UAT and Go-Live implementation for KPI Basis Operations dashboards. Coaching – Providedtraining to the junior team members and business users on various functionalities of Solution Manager and also documented various user’s manual on different topics related to the solution manager. Qualification: Bachelor’s degree in Computer Applications, Computer Science, Electronics and Communication Engineering. Skills: ChaRM, ITSM, E2E monitoring, ARIS and HP ALM Location: Doral, Florida. * Relocation may be required. Send Resume to: HR Dept. , Iblesoft Inc. ,7801 NW 37TH Street, Suite LP-104, Doral, FL 33195. All employees of Iblesoft, Inc. are automatically enrolled in the employee referral program of the company. Referral fee of $1,000 will be paid if referred candidate is hired by the company.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/sap-hana-developer/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Facilitate modeling sessions and design walk-through reviews with development team members\nProvide highly innovate and ingenious solutions\nLead troubleshooting efforts on complex performance tuning problems\nCommunicate and articulate model designs to peers, customers and governance teams\nDemonstrate BI (Business Intelligence) Analytical tools skillset\nExtensive hands on experience managing data from multiple data sources like SAP ERP and Non SAP system.\nExposure to SAP HANA Modeling on SAP ERP modules like Finance, Sales & Distribution, Material Management and Purchasing.\nBasic understanding of SAP ERP table structures of FI and Purchasing modules\nExposure to at least one of the BI analytical tools (Webi, Lumira, Tableau, Design Studio)\nExposure and basic understanding of data provisioning tools like SLT, SRS\nHigh degree of ‘learning agility’ with the ability to readily consume and apply new information and concepts with developed analytical problem solving skills\nGood communication skills to articulate model designs\nStrong communication skills, including with employees, clients, senior management and vendors\nStrong results orientation (driving to deadlines, financial targets, project goals, etc.\nCollaborate with stakeholders to understand business requirements and design appropriate data solutions to meet business need.\nLeverage prior experience and industry standard best practices to analyze cross functional data and provide insights to business teams.\nAssist stakeholders with data analysis, design data models & develop DB Views, Calculation Views etc. in SAP HANA to meet business need.\nConverting the business requirements into functional specification documents, and then translating functional specifications into technical design documents.\nImplement snapshot processes, data refresh schedules, exception handling procedures as necessary.\nDefine required data integration requirements between various systems and work with extended team to get them created.\nFollow all documented architecture, design & deployment processes to ensure compliance with policies.\nDesign and develop scalable, reusable data models in SAP HANA in line with architectural standards and best practices.\nData Modeling by SAP HANA Studio for building attributes views, analytic views, calculation Views and Sequel Scripts.\nOptimizing the SAP HANA models, Sequel Procedures, Scripts for better performance.\nPartner with Global BI team to help implement solutions for end user adoption.\nValidating the Report Layouts in Tableau and Data in the Reports Vs Source data and the Hana layer data.\nKeep pace with latest technology advances in SAP HANA.\nDevelop functional subject matter expertise within various areas of enterprise business processes.\nCross train other team members and convey value delivered on key initiatives to broader organization.\nConducting UAT sessions with the business for validating the report data.\nDefect fixes for issues found in continuous integration of iterative application builds.\nCommunicate status regularly with stakeholders and executive sponsors, United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "SAP HANA DEVELOPER", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/sap-hana-developer/", "description": "Job Posted Date: July 11th, 2018 SAP HANA Developer is the one who researching, analyzing and designing computer based solutions for defined, scientific or engineering problems. This includes planning, developing new computer systems, customized, to achieve solution of a specifically defined need or problem. The first and foremost critical tasks of an Analyst are identification and clear definition of needs of the customer. This implies that the analyst has to understand the operations of the customer, including an intimate understanding of the end purpose of the operation. Facilitate modeling sessions and design walk-through reviews with development team members Provide highly innovate and ingenious solutions Lead troubleshooting efforts on complex performance tuning problems Communicate and articulate model designs to peers, customers and governance teams Demonstrate BI (Business Intelligence) Analytical tools skillset Extensive hands on experience managing data from multiple data sources like SAP ERP and Non SAP system. Exposure to SAP HANA Modeling on SAP ERP modules like Finance, Sales & Distribution, Material Management and Purchasing. Basic understanding of SAP ERP table structures of FI and Purchasing modules Exposure to at least one of the BI analytical tools (Webi, Lumira, Tableau, Design Studio) Exposure and basic understanding of data provisioning tools like SLT, SRS High degree of ‘learning agility’ with the ability to readily consume and apply new information and concepts with developed analytical problem solving skills Good communication skills to articulate model designs Strong communication skills, including with employees, clients, senior management and vendors Strong results orientation (driving to deadlines, financial targets, project goals, etc. Collaborate with stakeholders to understand business requirements and design appropriate data solutions to meet business need. Leverage prior experience and industry standard best practices to analyze cross functional data and provide insights to business teams. Assist stakeholders with data analysis, design data models & develop DB Views, Calculation Views etc. in SAP HANA to meet business need. Converting the business requirements into functional specification documents, and then translating functional specifications into technical design documents. Implement snapshot processes, data refresh schedules, exception handling procedures as necessary. Define required data integration requirements between various systems and work with extended team to get them created. Follow all documented architecture, design & deployment processes to ensure compliance with policies. Design and develop scalable, reusable data models in SAP HANA in line with architectural standards and best practices. Data Modeling by SAP HANA Studio for building attributes views, analytic views, calculation Views and Sequel Scripts. Optimizing the SAP HANA models, Sequel Procedures, Scripts for better performance. Partner with Global BI team to help implement solutions for end user adoption. Validating the Report Layouts in Tableau and Data in the Reports Vs Source data and the Hana layer data. Keep pace with latest technology advances in SAP HANA. Develop functional subject matter expertise within various areas of enterprise business processes. Cross train other team members and convey value delivered on key initiatives to broader organization. Conducting UAT sessions with the business for validating the report data. Defect fixes for issues found in continuous integration of iterative application builds. Communicate status regularly with stakeholders and executive sponsors Qualification: Bachelor’s degree in Computer Applications, Computer Science, Electronics and Communication Engineering. Skills: Webi, Lumira, Tableau, Design Studio, SLT, SRS Location: Doral, Florida. * Relocation may be required. Send Resume to: HR Dept. , Iblesoft Inc. ,7801 NW 37TH Street, Suite LP-104, Doral, FL 33195. All employees of Iblesoft, Inc. are automatically enrolled in the employee referral program of the company. Referral fee of $1,000 will be paid if referred candidate is hired by the company.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/sccm-engineer/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "SCCM Engineer", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/sccm-engineer/", "description": "Job Posted Date: January 15, 2018 The highly technical nature of the duties to be performed by the Lead Architect requires deployment of a theoretical, conceptual knowledge and experience in all phases of software development life cycle including systems analysis, software design and development techniques, software testing and debugging and computer architecture. All of our Architects are required to have at least the minimum of a Bachelor’s degree in the specific field of endeavor. These requirements are the standard minimum requirements within the industry as well within our organization. The complexity of the job duties and the responsibilities of the position also require an individual with at least a Bachelor’s degree in the field. We believe an individual without a Bachelor’s degree or equivalent would not be able to perform the job duties of the position. Required Responsibilities: * Providing technical services and support for management of the workstation environment within the IT Client Services group, such as application packaging and the engineering and maintenance of software distribution platforms. * Professional services in client management on various capital project initiatives, as may be assigned. * Expertise Integration of internally and externally developed software into client device environment. * Installation, configuration, and maintenance of client devices and associated infrastructure. * Responsible for troubleshooting and maintaining overall client and server health for entire SCCM infrastructure * Implemented solution to eliminate duplicate SCCM GUID’s thus creating client stability and integrity. * Creating and deploying advanced custom scripts to be used in upgrade and deployment of software through GPO and SCCM. * Manage and coordinate a team of Windows and Microsoft SCCM engineers to aid in support of the projects and current needs of the environment. * Performing installation, configuration, migration, upgrading and maintenance of desktop PC/Server, software and related peripherals. * Administrating Microsoft SCCM environment, including creating or editing security groups, application deployments (collections, advertisements, task sequences, and PowerShell scripts), patch management, general troubleshooting, log analysis, upgrade and recommendations on current infrastructure. Qualification: Bachelor’s degree in Computer Applications, Computer Science, Electrical and Electronics Engineering. Skills: MDT 2012, Booting WIMs to CSC_OSDBoot Location: Doral, Florida. * Relocation may be required. Send Resume to: HR Dept. , Iblesoft Inc. ,7801 NW 37TH Street, Suite LP-104, Doral, FL 33195. All employees of Iblesoft, Inc. are automatically enrolled in the employee referral program of the company. Referral fee of $1,000 will be paid if referred candidate is hired by the company.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/senior-citrix-consultant/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Design, Documentation, Implementation, migration of Citrix XenDesktop and Citrix provisioning server\nTaking care of projects in phases of requirement gathering, overall design, documentation, Implementation, Steady phase support., Implementation and administration of Workspace Environment Management to manage security for client provided machines., Troubleshooting and resolving issues in enterprise Citrix environment, Including Web Interface, application enumeration and performance issues, Printing and provisioning issues, for internal and external clients.\nImplementing and assisting in company-wide migration of around 15000 users to VDI desktop environment. Providing transition to team to handle issues related to VDI environment. Create and review run book for L2 support.\nImplementation of Netscaler VPX, MPX. Configure Load Balancing, VIP, and authentication. Working with multiple technology team to provide secure remote access for client operations.\nMaintaining physical servers using iLO. Working on migration of servers P2V and V2V, Server 2003 to server 2008.  Migrating all infrastructure servers from server 2003 to server 2008 for securing client infrastructure from vulnerabilities., Perform daily system monitoring using Citrix Director, Systrack. Verifying the integrity and availability of all hardware, server resources, systems and key processes, reviewing system and application logs, and verifying completion of scheduled job.\nResponsible for implementing LINUX desktop VDI for Ubuntu project. Client’s requirement for running virtualized Linux OS through Citrix infrastructure.\nDevelop and implement PowerShell script for Citrix environment monitoring and enhancements.\nCoordinating with SCCM team to create and deploy software packages to user’s VDI system;, United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "Senior Citrix Consultant", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/senior-citrix-consultant/", "description": "Full Time USA Job Posted Date: August 06, 2018 The Senior Citrix Consultant role is to ensure the stable operation, reliability, security and planned future of the assigned Citrix systems and the support of all the services on the Citrix environment. Under minimal supervision, performs work supporting the overall integration of the Citrix environment enterprise-wide including participation in planning, design, implementation, migration, management, and coordination with Global business operations. A person in this position is a technical contributor with superior knowledge and experience in the area of the Citrix technologies, communications interfaces, and related hardware/software. Design, Documentation, Implementation, migration of Citrix XenDesktop and Citrix provisioning server Taking care of projects in phases of requirement gathering, overall design, documentation, Implementation, Steady phase support. Implementation and administration of Workspace Environment Management to manage security for client provided machines. Troubleshooting and resolving issues in enterprise Citrix environment, Including Web Interface, application enumeration and performance issues, Printing and provisioning issues, for internal and external clients. Implementing and assisting in company-wide migration of around 15000 users to VDI desktop environment. Providing transition to team to handle issues related to VDI environment. Create and review run book for L2 support. Implementation of Netscaler VPX, MPX. Configure Load Balancing, VIP, and authentication. Working with multiple technology team to provide secure remote access for client operations. Maintaining physical servers using iLO. Working on migration of servers P2V and V2V, Server 2003 to server 2008. Migrating all infrastructure servers from server 2003 to server 2008 for securing client infrastructure from vulnerabilities. Perform daily system monitoring using Citrix Director, Systrack. Verifying the integrity and availability of all hardware, server resources, systems and key processes, reviewing system and application logs, and verifying completion of scheduled job. Responsible for implementing LINUX desktop VDI for Ubuntu project. Client’s requirement for running virtualized Linux OS through Citrix infrastructure. Develop and implement PowerShell script for Citrix environment monitoring and enhancements. Coordinating with SCCM team to create and deploy software packages to user’s VDI system; Qualification: Bachelor’s degree in Computer Science Engineering, Information Technology, Computer Applications, any other related field. Location: Doral, Florida. Send Resume to: HR Dept. , Iblesoft Inc. ,7801 NW 37TH Street, Suite LP-104, Doral, FL 33166. All employees of Iblesoft, Inc. are automatically enrolled in the employee referral program of the company. Referral fee of $1,000 will be paid if referred candidate is hired by the company.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/software-developer-2/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Gathering business requirements directly from the clients and Business Analysts through Joint Application Design meetings and group discussions and convert them into functional requirements following standard Software Development Life Cycle.\nDesigning DB2 Data Management modules and ETL architecture for client use by optimizing the operational efficiency.\nDevelop DB2 objects and the corresponding database objects and being available and responsible for the migrations to stage and production environments after client and business approvals.\n Mentoring the TCS offshore teams and conducting the User training for the clients at ECOLAB.\n Reviewing High-Level and Low-level Design documents and suggest the design solutions to the DB2 application developers.\n Creating a generic process to provide a better framework and reduce the development and testing turnaround time.\n Resolving the issues that may arise from DB2 batch Jobs in Delivery Stream.\nDesigning, reviewing and documenting system test plans, defining test Procedures and creating test cases based on client’s requirements and related technical design documents. Also, perform data validation testing using SQL.\n Collaborate with business analysts and stake holders to understand the business requirements and ensure the quality of data.\nPrepare project and system related documentation consistent with standards and procedures outlined in the data architecture approach including data quality, security and availability requirements.\n Use Jira or confluence for defect tracking and documentation.\n Create testing-related documentation, including test plans, test cases/scripts, and bug reports assessing quality and associated risk.\n Coordinate with other team members in understanding the requirements and provide best analysis for continuing with the development.\n Test-Driven Development, Agile principles and Scrum/Lean practices., United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "Software Developer", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/software-developer-2/", "description": "Job Posted Date: June 5th, 2018 The scope of the project is to build and maintain enterprise DB2 data warehouse to integrate sales of each product by division, sale, lease, unit and other important data related to globally and analyze it to understand the overall performance of the organization and to improve the revenue. It involves extraction of data from various source systems and store it in a single data lake after applying required business transformations. The module requires an expertise in DB2 development using tools, data profiling and capability to research on various platforms such as SQL server and UNIX. The module also interacts with various api’s for which C# and Java web interface experience is preferred Gathering business requirements directly from the clients and Business Analysts through Joint Application Design meetings and group discussions and convert them into functional requirements following standard Software Development Life Cycle. Designing DB2 Data Management modules and ETL architecture for client use by optimizing the operational efficiency. Develop DB2 objects and the corresponding database objects and being available and responsible for the migrations to stage and production environments after client and business approvals. Mentoring the TCS offshore teams and conducting the User training for the clients at ECOLAB. Reviewing High-Level and Low-level Design documents and suggest the design solutions to the DB2 application developers. Creating a generic process to provide a better framework and reduce the development and testing turnaround time. Resolving the issues that may arise from DB2 batch Jobs in Delivery Stream. Designing, reviewing and documenting system test plans, defining test Procedures and creating test cases based on client’s requirements and related technical design documents. Also, perform data validation testing using SQL. Collaborate with business analysts and stake holders to understand the business requirements and ensure the quality of data. Prepare project and system related documentation consistent with standards and procedures outlined in the data architecture approach including data quality, security and availability requirements. Use Jira or confluence for defect tracking and documentation. Create testing-related documentation, including test plans, test cases/scripts, and bug reports assessing quality and associated risk. Coordinate with other team members in understanding the requirements and provide best analysis for continuing with the development. Test-Driven Development, Agile principles and Scrum/Lean practices. Qualification: Bachelor’s degree in Computer Applications, Computer Science, Electronics and Communication Engineering. Skills:DB2, ELT, SQL, TSM, LVM and oracle with DDBOOST Location: Doral, Florida. * Relocation may be required. Send Resume to: HR Dept. , Iblesoft Inc. ,7801 NW 37TH Street, Suite LP-104, Doral, FL 33195. All employees of Iblesoft, Inc. are automatically enrolled in the employee referral program of the company. Referral fee of $1,000 will be paid if referred candidate is hired by the company.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/software-developer-3/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Analyze user needs and software requirements to determine feasibility of design within time and cost constraints;\nDevelops databases, programs and procedures necessary to integrate and/or implement the system;\nDevelop data-access layer for new datatypes and create (as well as enhance) functional test cases for existing datatypes.\nIdentifying gaps in the backend core services layer for achieving desired functionality.\nDefect fixes for issues found in continuous integration of iterative application builds\nCarries out fact finding and program analysis of problems; applies established procedures to bring resolution;\nCommunicating issues and status information to the program manager and practice manager concerning system development activities.\nAdhering to quality management best practices such as through testing of application updates, changes.;\nParticipate in Daily Sprint Meetings; Work closely with analysts, designers and other project teams;\nImplement exception handling mechanism through the system (web services, windows services, back end) using enterprise application blocks;\nStore, retrieve and manipulate data for analysis of system capabilities and requirements;\nImplement unit test cases for various modules and coordinated with QA team for production deployments/releases;, United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "Software Developer", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/software-developer-3/", "description": "Job Posted Date: March 05, 2018 Analyze, design, develop, implement, and troubleshoot systems. Convert user requirements and project specifications to detailed flow charts for coding. Design, develop, test, implement, and debug software products and applications using various programming languages, operating systems, and databases. Design systems/ applications to optimize operational efficiency. Analyze user needs and software requirements to determine feasibility of design within time and cost constraints; Develops databases, programs and procedures necessary to integrate and/or implement the system; Develop data-access layer for new datatypes and create (as well as enhance) functional test cases for existing datatypes. Identifying gaps in the backend core services layer for achieving desired functionality. Defect fixes for issues found in continuous integration of iterative application builds Carries out fact finding and program analysis of problems; applies established procedures to bring resolution; Communicating issues and status information to the program manager and practice manager concerning system development activities. Adhering to quality management best practices such as through testing of application updates, changes. ; Participate in Daily Sprint Meetings; Work closely with analysts, designers and other project teams; Implement exception handling mechanism through the system (web services, windows services, back end) using enterprise application blocks; Store, retrieve and manipulate data for analysis of system capabilities and requirements; Implement unit test cases for various modules and coordinated with QA team for production deployments/releases; Qualification: Bachelor’s degree in Computer Science, Information Technology, Computer Applications, any other related field. Location: Doral, Florida. Send Resume to: HR Dept. , Iblesoft Inc. ,7801 NW 37TH Street, Suite LP-104, Doral, FL 33195. All employees of Iblesoft, Inc. are automatically enrolled in the employee referral program of the company. Referral fee of $1,000 will be paid if referred candidate is hired by the company.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/software-developer-4/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Bachelor’s Degree, in Computer Science., or experience in related field., Understanding and Gathering requirements from business owners and creating the functional design documents for Online Providers Directory (OPD).\nUnderstanding the OPD Legacy programs and Code Base which are in the Websphere Server as well as the Spring Boot applications in the Openshift and suggest the changes required for the business requirement.\nParticipate in everyday sprint meeting and create the sprint log in Rally board.\nParticipating in problems and knowledge initiatives and other process improvements.\nLeads the identification and definition of key architectures, components and interfaces, design of data model (entity relationships) and data/informational flows.\nCreating the user stories in Rally board with the help of Business Owners and Analysts for the Business Requirements., Recommend and develop design standards for application Integration.\nWork with enterprise and application architects in designing application.\nWorked with the Business Analysts to capture the requirements and also involved in creating the Business Requirement Documents, High level design documents and Low level design documents.\nEnsured designs are in compliance with the specifications., Developed and maintain SOAP and Restful Webservices using JAX RS and JSON.\nDevelop and maintain the Spring Boot applications in Openshift environment and deploy those applications in Jenkins and monitor them in Open shift.\nModify existing legacy OPD applications to improve the performance.\nDevelop and Maintain the OPD service endpoints which provides the provider details to the down stream dependent applications like Under65, MWS, Truli Member, OPD Digital.\nWorked on various Core Java concepts such as Exception Handling, Collection APIs, Concurrency, Generics, Serialization, Garbage Collection and lamda expressions to implement various features and enhancements.\nInvolved in Developing and Maintaining the OPD User Interface pages which are developed using the nodejs, backbone. JS.\nProviding Support to the downstream applications like MWE, Truli Vistior, Truli Member, MHL, U65 which uses the OPD web pages as part of their application.\nInvolved in developing business components using plain old java objects.\nManaging tasks independently and take ownership of responsibilities\nInvolved in understanding existing applications to rewrite using new prototype and react framework.\nEnsured designs are in compliance with specifications\nWorked on GITSTASH for version controlled and as a centralized repository for the source code.\nWorked on Spring MVC architecture using the design patterns.\nInteracting with multiple teams which have end points with ECIR, PPS database and prototype teams.\nMaintain and Support for Dev, QA and Production.\nMonitoring the Control-M batch jobs and make sure that the batch jobs are executed without any issues.\nCarry out factfinding and program analysis of problems, applied established procedures to bring resolution., Unit test the application using JUNIT and also used the embedded servers to create the integration tests. Also used Fiddler and Postman to test the Restful web services.\nTest the Restful Web services by creating the Client programs wherever required.\nInvolve in providing UAT testing support and Production support.\nAdhering to quality management best practices such as thorough testing of application updates and changes.\nCoordinates software system installation and functioning to insure specifications are met.\nMonitors the operation of assigned programs and responds to problems by diagnosing and correcting errors in logic and coding., Daily meeting on the progress\nPresenting the completed tasks to the client\nGuides the development team in overall application technology design activities, leads the identification and definition of key architectures\nConsult with customers about software system design and maintenance.\nDevelop integration documentation to be published to internal and external partners\nWork with product owners and functional analysts to understand application business context.\nCommunicating issues and status information to the program manager and practice manager concerning system development activities.\nWork in the Agile development methodology.\nCreate and updated requirements and issues using Rally tool.\nCreate project documentation using Confluence and SharePoint tools., United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "Software  Developer", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/software-developer-4/", "description": "NEEDED QUALIFICATIONS: Bachelor’s Degree, in Computer Science. , or experience in related field. SERVICES TO BE PROVIDED: As an Software developer, required job responsibilities include: Requirements Understanding, Research and Communication (10%) Understanding and Gathering requirements from business owners and creating the functional design documents for Online Providers Directory (OPD). Understanding the OPD Legacy programs and Code Base which are in the Websphere Server as well as the Spring Boot applications in the Openshift and suggest the changes required for the business requirement. Participate in everyday sprint meeting and create the sprint log in Rally board. Participating in problems and knowledge initiatives and other process improvements. Leads the identification and definition of key architectures, components and interfaces, design of data model (entity relationships) and data/informational flows. Creating the user stories in Rally board with the help of Business Owners and Analysts for the Business Requirements. Design (15%) Recommend and develop design standards for application Integration. Work with enterprise and application architects in designing application. Worked with the Business Analysts to capture the requirements and also involved in creating the Business Requirement Documents, High level design documents and Low level design documents. Ensured designs are in compliance with the specifications. Development – Coding (50%) Developed and maintain SOAP and Restful Webservices using JAX RS and JSON. Develop and maintain the Spring Boot applications in Openshift environment and deploy those applications in Jenkins and monitor them in Open shift. Modify existing legacy OPD applications to improve the performance. Develop and Maintain the OPD service endpoints which provides the provider details to the down stream dependent applications like Under65, MWS, Truli Member, OPD Digital. Worked on various Core Java concepts such as Exception Handling, Collection APIs, Concurrency, Generics, Serialization, Garbage Collection and lamda expressions to implement various features and enhancements. Involved in Developing and Maintaining the OPD User Interface pages which are developed using the nodejs, backbone. JS. Providing Support to the downstream applications like MWE, Truli Vistior, Truli Member, MHL, U65 which uses the OPD web pages as part of their application. Involved in developing business components using plain old java objects. Managing tasks independently and take ownership of responsibilities Involved in understanding existing applications to rewrite using new prototype and react framework. Ensured designs are in compliance with specifications Worked on GITSTASH for version controlled and as a centralized repository for the source code. Worked on Spring MVC architecture using the design patterns. Interacting with multiple teams which have end points with ECIR, PPS database and prototype teams. Maintain and Support for Dev, QA and Production. Monitoring the Control-M batch jobs and make sure that the batch jobs are executed without any issues. Carry out factfinding and program analysis of problems, applied established procedures to bring resolution. Quality Analysis – QA (15%) Unit test the application using JUNIT and also used the embedded servers to create the integration tests. Also used Fiddler and Postman to test the Restful web services. Test the Restful Web services by creating the Client programs wherever required. Involve in providing UAT testing support and Production support. Adhering to quality management best practices such as thorough testing of application updates and changes. Coordinates software system installation and functioning to insure specifications are met. Monitors the operation of assigned programs and responds to problems by diagnosing and correcting errors in logic and coding. Meeting, Reviews and Documentation (10%) Daily meeting on the progress Presenting the completed tasks to the client Guides the development team in overall application technology design activities, leads the identification and definition of key architectures Consult with customers about software system design and maintenance. Develop integration documentation to be published to internal and external partners Work with product owners and functional analysts to understand application business context. Communicating issues and status information to the program manager and practice manager concerning system development activities. Work in the Agile development methodology. Create and updated requirements and issues using Rally tool. Create project documentation using Confluence and SharePoint tools.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/sr-software-developer/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Design application using Java, J2EE, Spring, micro services architecture, Hibernate, IBM symphony data grid Exadata and Oracle Coherence\nDevelop and Implement FXRS application’s Forwards and Options trade data loads from Fenics, Calypso and FTRB application using Java, J2EE, micro service architecture, Spring, Hibernate, Exadata, Shell scripting and Oracle Coherence.\nDevelop java application to calculate MTM, CVA and PNL for FXRS and Quants applications\nDesign, Develop and Implement to copy every day Forwards/Options trade data and market data into Exadata DB using Oracle Coherence and Unix shell scripts.\nFront End framework AngularJs5+ & Angular 4.0+.\nBackend and middleware development using J2EE (Spring Boot/ Spring Security and Java\nDevelop backend restful services using spring boot based Micro services\nWork on Oracle SQL profiling, Query optimization and performance enhancement on database objects like views, Stored Procedures.\nFrontend bundling and tooling like gulp, Bootstrap, css3, html5, providing POC & SEO solutions.\nWill assist in designing, refactoring and unit testing and identifying the technical tasks from the functional design document.\nTDD and BDD approach with emphasis Unit/Integration/Acceptance testing using Mockito, Jasmine, Protractor test tools.\nWill use Code coverage tools to ensure product code quality using sonarqube, jshint , git vcs.\nCreation of automated Jenkins pipe lines for CI/CD to S3 instances, Participate and Support build, deployment testing cycles\nOther core areas he will be handling as part of his daily job:\nSystems Evaluation — Identifying measures or indicators of system performance and the actions needed to improve or correct performance, relative to the goals of the system.\nOperations Analysis — Analyzing needs and product requirements to create a design.\nMonitoring & Systems Analysis — Monitoring/Assessing performance of application using Kibana/LogStash, to determine system performance., United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "Sr. Software Developer", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.iblesoft.com/jobs/sr-software-developer/", "description": "Job Posted Date: June 29th, 2018 “Self-Serve Data Extract” (“SSDE”) is a brand new tool being built within NEXEN Gateway that will augment Client Information Delivery capabilities within Middle Office Solutions and reporting requirements, which will allow Clients to create their own bespoke data extracts in raw form for the purpose of machine to machine data transfer to support their start of day and end of day data requirements. It provides a B2B mechanism to feed data, that can service a mission critical need that is a shared utility which is proactively monitored and assures delivery, providing benefits like, SSDE will reduce Clients’ dependency on technology to build and service machine to machine data transmissions, extracts are an alternative solution to NEXEN API’s, especially for clients who do not have technology resources to leverage the API framework directly, reduce the resource drain and implementation timeline of the current “custom build” per client which can take several weeks, ability to create/modify data feeds in real time, SSDE framework will allow greater flexibility to meet clients data needs (via update to APIs or customization), compared to structured reports where time to production is much longer In addition to data formatting and scheduling feature Design application using Java, J2EE, Spring, micro services architecture, Hibernate, IBM symphony data grid Exadata and Oracle Coherence Develop and Implement FXRS application’s Forwards and Options trade data loads from Fenics, Calypso and FTRB application using Java, J2EE, micro service architecture, Spring, Hibernate, Exadata, Shell scripting and Oracle Coherence. Develop java application to calculate MTM, CVA and PNL for FXRS and Quants applications Design, Develop and Implement to copy every day Forwards/Options trade data and market data into Exadata DB using Oracle Coherence and Unix shell scripts. Front End framework AngularJs5+ & Angular 4. 0+. Backend and middleware development using J2EE (Spring Boot/ Spring Security and Java Develop backend restful services using spring boot based Micro services Work on Oracle SQL profiling, Query optimization and performance enhancement on database objects like views, Stored Procedures. Frontend bundling and tooling like gulp, Bootstrap, css3, html5, providing POC & SEO solutions. Will assist in designing, refactoring and unit testing and identifying the technical tasks from the functional design document. TDD and BDD approach with emphasis Unit/Integration/Acceptance testing using Mockito, Jasmine, Protractor test tools. Will use Code coverage tools to ensure product code quality using sonarqube, jshint , git vcs. Creation of automated Jenkins pipe lines for CI/CD to S3 instances, Participate and Support build, deployment testing cycles Other core areas he will be handling as part of his daily job: Systems Evaluation — Identifying measures or indicators of system performance and the actions needed to improve or correct performance, relative to the goals of the system. Operations Analysis — Analyzing needs and product requirements to create a design. Monitoring & Systems Analysis — Monitoring/Assessing performance of application using Kibana/LogStash, to determine system performance. Qualification: Bachelor’s degree in Computer Applications, Computer Science, Electronics and Communication Engineering. Skills: Eclipse, JBOSS, Tomcat Apache, Net beans, SQDB, Toad, UML,Oracle SQl Developer, Microsoft Visio, Gap Analysis / Requirement gathering with Rational Unified Process, Unified Modeling Language and Business Modeling. Location: Doral, Florida. * Relocation may be required. Send Resume to: HR Dept. , Iblesoft Inc. ,7801 NW 37TH Street, Suite LP-104, Doral, FL 33195. All employees of Iblesoft, Inc. are automatically enrolled in the employee referral program of the company. Referral fee of $1,000 will be paid if referred candidate is hired by the company.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/web-methods-developer/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Providing inputs, technical leadership and support for full systems lifecycle management activities (analysis, technical requirements, design, coding, testing,deployment, implementation of systems and applications software, etc.). Has to participate in component and data architecture design, technology planning, and testing for Applications Development (AD) initiatives to meet business requirements in scope of the Customer Connect program\nResponsible to provide inputs and accurate estimates to applications development project plans and integrations, collaborates with internal and external teams, supports emerging technologies, and ensures effective communication and achievement of objectives.\nDevelopment, implement and sustaining complex enterprise-level integrations utilizing the SAG webMethodsAPI and B2B product suite. Working on MS Azure or AWS is preferred.\nHandle documents like EDI, Flat files, XML, IDOC’s and expertise in writing Flow services and Java services using WebMethods Developer. Integrating with various applications with WebMethods Adapters like (Flat file, JDBC, SAP R/3, EDI, MQ and SOAP).\nEnsures environments are available and secure for system testing, integration testing, pilot testing, internal customer testing, and production.\nStrong analytical skills in Developing and supporting webMethodsprojects using the webMethodsx/10.x and strategically build reusable assets utilizing webMethods and implement CI/CD. support for existing webMethods installation by handling incidents, troubleshooting, performance, problem resolution, and monitoring the flow of messages as needed.\nExplains/trains as subject matter expert on technical concepts of product environments, operating systems, and related utilities.\nEnsures adherence to appropriate design, coding and source control standards. Leads design and code walk-thru as a presenter or reviewer. Processes Change Requests and Quality Assurance (QA) documentation for project implementations and support changes\nAnalyzes patterns and trends and recommends/implements innovative solutions within IT and with vendors.\nBuild relationships with solution architects and ensure solutions meet enterprise standard and provide input to project managers and SCRUM Masters in planning activities.\nIntegrating with RDMS applications with writing JDBC adapter services and adapter notifications for select, upsert, delete, stored procedures operations and writing of custom SQL statements on the database.\nDevelopment of webMethodsmessaging triggers with join conditions to filter the messages, and flow services with enabling the run time properties to perform better for publish and subscribe pattern.\nIntegrate with ERP applications SAP Idocto webMethods canonical document format and canonical to desired target format like Flat Files and EDI’s.\nDevelop SOPA web services for synchronous and asynchronous communication it includes developing flow services and creating message provider descriptor and message consumer descriptor.\nGoverning of life cycle of APIs, services and associated policies. Virtualizing APIs and services, shielding consumers from back-end changes.\nDesign and development of SOAP and REST API’s and deployment of all integration artifacts including services, related documents and policies.\nDevelop B2B integration with business partners in trading networks, like management and electronic exchange of documents type’s like EDI (electronic data interchange), XML and Flat Files.\nsetting up trading networks to process documents, identifying the document attributes, document type’s and processing action in processing rules.\nConfiguration for on boarding new trading partners in trading Networks and exchange of B2B transactions, for supply chain of EDI transactions from purchase orders 850, advanced shipment notice 856 and invoices 810 in ANSI X12 format.\nSetting up partner profile and TPA’s for each specific partner and creating the document types, processing rule’s for routing the payload to appropriate partners., United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "Web Methods Developer", "location": "", "location_type": "hybrid", "job_type": "full_time", "min_experience": 3, "max_experience": 8, "apply_link": "https://www.iblesoft.com/jobs/web-methods-developer/", "description": "Position: Web Methods Developer Full Time USA Posted 5 Months ago Job Posted Date: December 12, 2018 Customer Connect is a multi-release/multi-year program that will implement the Software AG webMethods and SAP product suite including: Integration Servers, Universal Messaging, Optimize for Infrastructure, Terracotta, Hybris Cloud for Customer (C4C), Hybris Marketing, HANA Analytics and SAP ISU on S4HANA. Ultimately the program’s goal is to update and consolidate four legacy customer billing systems into one customer service platform to deliver the universal engagement experience customers expect. Internally, this multi-year program will positively impact our business by driving efficiencies, consistencies and insights. The program is using a hybrid waterfall / agile methodology. Providing inputs, technical leadership and support for full systems lifecycle management activities (analysis, technical requirements, design, coding, testing,deployment, implementation of systems and applications software, etc. ). Has to participate in component and data architecture design, technology planning, and testing for Applications Development (AD) initiatives to meet business requirements in scope of the Customer Connect program Responsible to provide inputs and accurate estimates to applications development project plans and integrations, collaborates with internal and external teams, supports emerging technologies, and ensures effective communication and achievement of objectives. Development, implement and sustaining complex enterprise-level integrations utilizing the SAG webMethodsAPI and B2B product suite. Working on MS Azure or AWS is preferred. Handle documents like EDI, Flat files, XML, IDOC’s and expertise in writing Flow services and Java services using WebMethods Developer. Integrating with various applications with WebMethods Adapters like (Flat file, JDBC, SAP R/3, EDI, MQ and SOAP). Ensures environments are available and secure for system testing, integration testing, pilot testing, internal customer testing, and production. Strong analytical skills in Developing and supporting webMethodsprojects using the webMethodsx/10. x and strategically build reusable assets utilizing webMethods and implement CI/CD. support for existing webMethods installation by handling incidents, troubleshooting, performance, problem resolution, and monitoring the flow of messages as needed. Explains/trains as subject matter expert on technical concepts of product environments, operating systems, and related utilities. Ensures adherence to appropriate design, coding and source control standards. Leads design and code walk-thru as a presenter or reviewer. Processes Change Requests and Quality Assurance (QA) documentation for project implementations and support changes Analyzes patterns and trends and recommends/implements innovative solutions within IT and with vendors. Build relationships with solution architects and ensure solutions meet enterprise standard and provide input to project managers and SCRUM Masters in planning activities. Integrating with RDMS applications with writing JDBC adapter services and adapter notifications for select, upsert, delete, stored procedures operations and writing of custom SQL statements on the database. Development of webMethodsmessaging triggers with join conditions to filter the messages, and flow services with enabling the run time properties to perform better for publish and subscribe pattern. Integrate with ERP applications SAP Idocto webMethods canonical document format and canonical to desired target format like Flat Files and EDI’s. Develop SOPA web services for synchronous and asynchronous communication it includes developing flow services and creating message provider descriptor and message consumer descriptor. Governing of life cycle of APIs, services and associated policies. Virtualizing APIs and services, shielding consumers from back-end changes. Design and development of SOAP and REST API’s and deployment of all integration artifacts including services, related documents and policies. Develop B2B integration with business partners in trading networks, like management and electronic exchange of documents type’s like EDI (electronic data interchange), XML and Flat Files. setting up trading networks to process documents, identifying the document attributes, document type’s and processing action in processing rules. Configuration for on boarding new trading partners in trading Networks and exchange of B2B transactions, for supply chain of EDI transactions from purchase orders 850, advanced shipment notice 856 and invoices 810 in ANSI X12 format. Setting up partner profile and TPA’s for each specific partner and creating the document types, processing rule’s for routing the payload to appropriate partners. Qualification: Bachelor’s degree in Computer Science, Information Technology, Computer Applications, any other related field. All employees of Iblesoft, Inc. are automatically enrolled in the employee referral program of the company. Referral fee of $1,000 will be paid if referred candidate is hired by the company. Job Features Job Category IT Experience 3-8 years No. of position Multiple Job Role Web Methods Developer", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.iblesoft.com/jobs/sr-big-data-analyst-developer/", "company_id": 3341, "source": 3, "skills": "Solutions\n\nProducts\n\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, Solutions\n\t\t\t\n\t\tSOFTWARE DEVELOPMENTWEB DEVELOPMENTCMS & ECOMMERCEOTHER SERVICES, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, Products, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, Type and press enter to search\n\t\t\t\t\t\n\t\t\t\t\n\t\t\t\n\t\t\n\tCategories\n\t\t\t\n\t\t\t\tNo categories, No categories, Solutions\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nProducts\n\n\t\t\t<img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >\n\n\t\t\nPortfolioConsulting ServicesNeed Resources?Get a Free Quote, <img class=\"alignnone size-medium wp-image-6026 postion-brd\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\tSOFTWARE DEVELOPMENTApplication Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development\nWEB DEVELOPMENTPHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design\nCMS & ECOMMERCEWordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront\nOTHER SERVICESSoftware Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development\n\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm\" src=\"https://iblesoft.com/wp-content/uploads/2016/12/logos-2.png\" />\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, Application Development & Management\nBusiness Intelligence & Analytics\nEnterprise Application Services\nMobile Application Development, PHP\nASP .Net\nRuby On Rails\nAngularJS\nCodeigniter\nUI/UX Design, WordPress\nMagento\nDrupal\nJoomla\nDotNetNuke\nAspDotNetStorefront, Software Testing\nInternet Marketing\nCloud Computing\nInternet Of Things\nIntegration & Migration\nProduct Development, <img class=\"alignnone size-medium wp-image-6026 position-app\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img/https://www.iblesoft.com/wp-content/uploads/2017/02/brd-image.png\" />\n\t\t\t\t\t\n<img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">\n\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t\n<img class=\"alignnone size-medium wp-image-6026 log-btm app-cls\" src=\"https://iblesoft.com/wp-content/uploads/2016/09/logos-2.png\" />\n\n\n\t\t\t\t\t\n1-************ \n<EMAIL>\n\n\t\t\t\t\t\nSOLUTION SECTION :\n<img class=\"img-responsive\" src=\"https://iblesoft.com/wp-content/uploads/myopenconnectlogo.png\" >, <img class=\"top\" width=\"200px\" src=\"https://sp-ao.shortpixel.ai/client/to_auto,q_lossless,ret_img,w_200/https://www.iblesoft.com/wp-content/uploads/2017/02/vivek-blackgrnd-1.png\">, United States+1United Kingdom+44Afghanistan (‫افغانستان‬‎)+93Albania (Shqipëri)+355Algeria (‫الجزائر‬‎)+213American Samoa+1684Andorra+376Angola+244Anguilla+1264Antigua and Barbuda+1268Argentina+54Armenia (Հայաստան)+374Aruba+297Australia+61Austria (Österreich)+43Azerbaijan (Azərbaycan)+994Bahamas+1242Bahrain (‫البحرين‬‎)+973Bangladesh (বাংলাদেশ)+880Barbados+1246Belarus (Беларусь)+375Belgium (België)+32Belize+501Benin (Bénin)+229Bermuda+1441Bhutan (འབྲུག)+975Bolivia+591Bosnia and Herzegovina (Босна и Херцеговина)+387Botswana+267Brazil (Brasil)+55British Indian Ocean Territory+246British Virgin Islands+1284Brunei+673Bulgaria (България)+359Burkina Faso+226Burundi (Uburundi)+257Cambodia (កម្ពុជា)+855Cameroon (Cameroun)+237Canada+1Cape Verde (Kabu Verdi)+238Caribbean Netherlands+599Cayman Islands+1345Central African Republic (République centrafricaine)+236Chad (Tchad)+235Chile+56China (中国)+86Christmas Island+61Cocos (Keeling) Islands+61Colombia+57Comoros (‫جزر القمر‬‎)+269Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)+243Congo (Republic) (Congo-Brazzaville)+242Cook Islands+682Costa Rica+506Côte d’Ivoire+225Croatia (Hrvatska)+385Cuba+53Curaçao+599Cyprus (Κύπρος)+357Czech Republic (Česká republika)+420Denmark (Danmark)+45Djibouti+253Dominica+1767Dominican Republic (República Dominicana)+1Ecuador+593Egypt (‫مصر‬‎)+20El Salvador+503Equatorial Guinea (Guinea Ecuatorial)+240Eritrea+291Estonia (Eesti)+372Ethiopia+251Falkland Islands (Islas Malvinas)+500Faroe Islands (Føroyar)+298Fiji+679Finland (Suomi)+358France+33French Guiana (Guyane française)+594French Polynesia (Polynésie française)+689Gabon+241Gambia+220Georgia (საქართველო)+995Germany (Deutschland)+49Ghana (Gaana)+233Gibraltar+350Greece (Ελλάδα)+30Greenland (Kalaallit Nunaat)+299Grenada+1473Guadeloupe+590Guam+1671Guatemala+502Guernsey+44Guinea (Guinée)+224Guinea-Bissau (Guiné Bissau)+245Guyana+592Haiti+509Honduras+504Hong Kong (香港)+852Hungary (Magyarország)+36Iceland (Ísland)+354India (भारत)+91Indonesia+62Iran (‫ایران‬‎)+98Iraq (‫العراق‬‎)+964Ireland+353Isle of Man+44Israel (‫ישראל‬‎)+972Italy (Italia)+39Jamaica+1876Japan (日本)+81Jersey+44Jordan (‫الأردن‬‎)+962Kazakhstan (Казахстан)+7Kenya+254Kiribati+686Kosovo+383Kuwait (‫الكويت‬‎)+965Kyrgyzstan (Кыргызстан)+996Laos (ລາວ)+856Latvia (Latvija)+371Lebanon (‫لبنان‬‎)+961Lesotho+266Liberia+231Libya (‫ليبيا‬‎)+218Liechtenstein+423Lithuania (Lietuva)+370Luxembourg+352Macau (澳門)+853Macedonia (FYROM) (Македонија)+389Madagascar (Madagasikara)+261Malawi+265Malaysia+60Maldives+960Mali+223Malta+356Marshall Islands+692Martinique+596Mauritania (‫موريتانيا‬‎)+222Mauritius (Moris)+230Mayotte+262Mexico (México)+52Micronesia+691Moldova (Republica Moldova)+373Monaco+377Mongolia (Монгол)+976Montenegro (Crna Gora)+382Montserrat+1664Morocco (‫المغرب‬‎)+212Mozambique (Moçambique)+258Myanmar (Burma) (မြန်မာ)+95Namibia (Namibië)+264Nauru+674Nepal (नेपाल)+977Netherlands (Nederland)+31New Caledonia (Nouvelle-Calédonie)+687New Zealand+64Nicaragua+505Niger (Nijar)+227Nigeria+234Niue+683Norfolk Island+672North Korea (조선 민주주의 인민 공화국)+850Northern Mariana Islands+1670Norway (Norge)+47Oman (‫عُمان‬‎)+968Pakistan (‫پاکستان‬‎)+92Palau+680Palestine (‫فلسطين‬‎)+970Panama (Panamá)+507Papua New Guinea+675Paraguay+595Peru (Perú)+51Philippines+63Poland (Polska)+48Portugal+351Puerto Rico+1Qatar (‫قطر‬‎)+974Réunion (La Réunion)+262Romania (România)+40Russia (Россия)+7Rwanda+250Saint Barthélemy (Saint-Barthélemy)+590Saint Helena+290Saint Kitts and Nevis+1869Saint Lucia+1758Saint Martin (Saint-Martin (partie française))+590Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)+508Saint Vincent and the Grenadines+1784Samoa+685San Marino+378São Tomé and Príncipe (São Tomé e Príncipe)+239Saudi Arabia (‫المملكة العربية السعودية‬‎)+966Senegal (Sénégal)+221Serbia (Србија)+381Seychelles+248Sierra Leone+232Singapore+65Sint Maarten+1721Slovakia (Slovensko)+421Slovenia (Slovenija)+386Solomon Islands+677Somalia (Soomaaliya)+252South Africa+27South Korea (대한민국)+82South Sudan (‫جنوب السودان‬‎)+211Spain (España)+34Sri Lanka (ශ්‍රී ලංකාව)+94Sudan (‫السودان‬‎)+249Suriname+597Svalbard and Jan Mayen+47Swaziland+268Sweden (Sverige)+46Switzerland (Schweiz)+41Syria (‫سوريا‬‎)+963Taiwan (台灣)+886Tajikistan+992Tanzania+255Thailand (ไทย)+66Timor-Leste+670Togo+228Tokelau+690Tonga+676Trinidad and Tobago+1868Tunisia (‫تونس‬‎)+216Turkey (Türkiye)+90Turkmenistan+993Turks and Caicos Islands+1649Tuvalu+688U.S. Virgin Islands+1340Uganda+256Ukraine (Україна)+380United Arab Emirates (‫الإمارات العربية المتحدة‬‎)+971United Kingdom+44United States+1Uruguay+598Uzbekistan (Oʻzbekiston)+998Vanuatu+678Vatican City (Città del Vaticano)+39Venezuela+58Vietnam (Việt Nam)+84Wallis and Futuna+681Western Sahara (‫الصحراء الغربية‬‎)+212Yemen (‫اليمن‬‎)+967Zambia+260Zimbabwe+263Åland Islands+358, About Us\nPartners\nContact Us\nCareers\nTestimonials\nOur Clients\nERP\nTerms & Conditions\nBlog, Follow Us On:", "title": "Sr. Big Data Analyst / Developer", "location": "", "location_type": null, "job_type": "full_time", "min_experience": 3, "max_experience": 9, "apply_link": "https://www.iblesoft.com/jobs/sr-big-data-analyst-developer/", "description": "Sr. Big Data Analyst / Developer Full Time USA Job Posted Date: September 16, 2019 The scope of the project is to build an enterprise data warehouse to integrate property, lease, unit and other important data related to prologis globally and analyze it to understand the overall performance of the organization and to improve the same. It involves extracting data from various source systems and store it in a single data store after applying required business transformations. · Develop batch and streaming analytics pipelines using Apache Spark and Scala programming to materialize and refine the event results and improve its processing time · Write advanced Hive, Spark SQL queries against data warehouses utilizing large datasets · Built object-oriented and functional Scala programs with Apache Spark clean and transform features that performs aggregations to integrate to machine learning models · Ensure that all feature deliverable’s meet quality objectives in functionality, methodologies, performance, stability, security, accessibility and data quality · Design and develop advanced analytics software components using Apache Spark, Scala, AWS and Hadoop Ecosystem and integrate it with various machine learning models that are architected for re-use · Design, document, and develop complex data extracts, applications and ad-hoc queries as requested by internal and external customers using Hive and Scala transformations · Develop Hive scripts and EMR (Elastic Map Reduce) mappings using Amazon Web Services EMR to integrate data from different sources and apply required transformations before loading to target databases · Manage the implementation of statistical data quality procedures on new data sources by applying rigorous iterative data analytics to improve scaling and system performance · Develop ETL code in various databases like snowflake computing and Denoda · Work on performance tuning and resource optimization of databases like SQL Server to improve the overall performance of the system · Develop and maintain data engineering best practices and contribute to Insights on data analytics and visualization concepts, methods and techniques to make the system robust and compatible to adhere the changes in data requirements in future · Work closely with other Data Scientists, analysts, designers and project teams to identify new areas of research · Analyze the production data issues to find the root cause and provide permanent fix to avoid similar issues in future · Creates and maintains Big Data Analytic pipeline engine specifications and process documentation to produce the required data deliverables (data transformation/actions, data profiling, source to target maps, ETL flows) · Collaborate with business analysts and stake holders to understand the business requirements and ensure the quality of data · Create solutions to real world problems and verify the results of the work deployed · Prepare project and system related documentation consistent with standards and procedures outlined in the data architecture approach including data quality, security and availability requirements · Work with DBA’s and QA team to move the code developed to production environment using GIT version control and BitBucket · Work with cross-functional teams, such as Product management, Platform, Operations and Mobile device engineering to elaborate on functional requirements/designs of the mobile wireless analytics modules, based on the product level requirements and designs · Use Jira or confluence for defect tracking and documentation · Implement unit test cases for various modules and coordinated with QA team for production deployments/releases · Participate in Daily Sprint Meetings; Coordinate with other team members in understanding the requirements and provide best analysis for continuing with the development Qualification: Master ’s degree in Computer Science Engineering, Information Technology, Computer Applications, any other related field. Location: Doral, Florida. Send Resume to: HR Dept. , Iblesoft Inc. ,7801 NW 37TH Street, Suite LP-104, Doral, FL 33195. All employees of Iblesoft, Inc. are automatically enrolled in the employee referral program of the company. Referral fee of $1,000 will be paid if referred candidate is hired by the company. Job Features Job Category IT Experience 3-9 years No. of position Multiple Job Role Big Data Analyst / Developer", "ctc": null, "currency": null, "meta": {}}]