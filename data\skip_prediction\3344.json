[{"jd_link": "https://singlemind-consulting.breezy.hr/p/40cdd006c664-front-end-react-developer", "company_id": 3344, "source": 3, "skills": "You are comfortable working directly with client UX/Product teamsYou enjoy rapid iteration and feedback cyclesYou love working with GraphQL APIs to bridge communication between appsY<PERSON> are comfortable working with senior developers for architectural guidance, Strong expertise with React, NextJS, and TypeScript as the core tech stackExperience with GraphQL APIs and integrationUnderstanding of building maintainable code while balancing rapid prototype development needsExperience with translating Figma designs to code, Experience with prototype development and MVP iterationsYou're familiar with modern design tools as part of engineering/design collaboration (Figma, etc)You're fluent in code repositories and team branch/merge flows (Git)You're willing to take ownership of your own qualityYou're experienced working as a member of a professional services teamYou enjoy working collaboratively, Apply NowUse My Indeed ResumeApply Using LinkedIn", "title": "", "location": "", "location_type": "remote", "job_type": "part_time", "min_experience": null, "max_experience": null, "apply_link": "https://singlemind-consulting.breezy.hr/p/40cdd006c664-front-end-react-developer", "description": "This is a remote contract position with an initial project duration of 2-5 months, with potential for continued collaboration based on project success. Pacific Daylight Time core business hours. Who is Singlemind? Based in the Pacific Northwest, Singlemind is a digital design and development agency that specializes in creating compelling digital solutions including applications, websites, and IoT products. Working collaboratively with clients, our teams help companies build, launch, and scale new concepts and products that keep them competitive in today's digital landscape. Who we are looking forWe have an immediate opening for a React developer to work on an exciting prototype project. The ideal candidate will be able to build a clean, maintainable frontend while working directly with the client's UX/Product team and collaborating with our senior developers on an architectural approach. This role will be managed by our Customer Success Manager to ensure smooth project delivery. Primary Requirements You are comfortable working directly with client UX/Product teamsYou enjoy rapid iteration and feedback cyclesYou love working with GraphQL APIs to bridge communication between appsYou are comfortable working with senior developers for architectural guidanceMinimum Qualifications Strong expertise with React, NextJS, and TypeScript as the core tech stackExperience with GraphQL APIs and integrationUnderstanding of building maintainable code while balancing rapid prototype development needsExperience with translating Figma designs to code The Ideal Candidate Experience with prototype development and MVP iterationsYou're familiar with modern design tools as part of engineering/design collaboration (Figma, etc)You're fluent in code repositories and team branch/merge flows (Git)You're willing to take ownership of your own qualityYou're experienced working as a member of a professional services teamYou enjoy working collaboratively CompensationHourly rate will be based on experience and qualifications. About UsWe are a globally distributed team of professionals with the large majority of our team located in Oregon. We work flexible hours and operate as a team. Our clients range from small well-funded start-ups to large multi-billion dollar international companies. We truly believe in a balanced work/life ethic. Singlemind is an Equal Opportunity Employer. This is a remote contract position with an initial project duration of 2-5 months, with potential for continued collaboration based on project success. Pacific Daylight Time core business hours.", "ctc": null, "currency": null, "meta": {}}]