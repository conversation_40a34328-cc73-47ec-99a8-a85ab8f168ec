[{"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000020759001/FullStack-Engineer?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   06/17/2025        Job Type   Full time        Work Experience   4-5 years        Industry   Technology        Remote Job, Develop, test, and maintain robust backend solutions using .NET, Core NodeJS, Java, or similar technologies.\n    Build dynamic, user-friendly frontend applications with JavaScript, TypeScript, and React.\n    Collaborate with cross-functional teams to design, implement, and optimize scalable solutions.\n    Leverage cloud platforms (Azure, AWS, GCP) for deploying and managing applications.\n    Implement CI/CD pipelines to ensure seamless integration and delivery processes.\n    Work within Agile frameworks to drive iterative development and continuous improvement.\n    Ensure code quality and application security through rigorous testing and review processes., 4+ years of professional experience as a Full Stack Engineer.\n    Proficiency in backend development with .NET, NodeJS, Java, or similar frameworks.\n    Strong experience with frontend technologies: JavaScript, TypeScript, and React.\n    Advanced English communication skills (both verbal and written).\n    Hands-on experience with cloud platforms (Azure, AWS, or GCP).\n    Familiarity with CI/CD practices and tools.\n    Solid understanding of Agile methodologies and principles.\n    Excellent problem-solving skills and attention to detail., View all jobs      Visit website", "title": "FullStack Engineer", "location": "Remote Job", "location_type": "remote", "job_type": null, "min_experience": 4, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000020759001/FullStack-Engineer?source=CareerSite", "description": "Job Description This is a remote position. Who We Are:At Advancio, we are passionate about technology and its ability to transform the world. We are rapidly expanding and building a company where we serve exceptional businesses, hire top talent, and have a lot of fun doing what we love! Job Summary: We are seeking a skilled Full Stack Engineer with expertise in modern backend and frontend technologies to join our dynamic team. The ideal candidate will have a strong foundation in software development, cloud technologies, and agile practices. Requirements Develop, test, and maintain robust backend solutions using . NET, Core NodeJS, Java, or similar technologies. Build dynamic, user-friendly frontend applications with JavaScript, TypeScript, and React. Collaborate with cross-functional teams to design, implement, and optimize scalable solutions. Leverage cloud platforms (Azure, AWS, GCP) for deploying and managing applications. Implement CI/CD pipelines to ensure seamless integration and delivery processes. Work within Agile frameworks to drive iterative development and continuous improvement. Ensure code quality and application security through rigorous testing and review processes. Qualifications: 4+ years of professional experience as a Full Stack Engineer. Proficiency in backend development with . NET, NodeJS, Java, or similar frameworks. Strong experience with frontend technologies: JavaScript, TypeScript, and React. Advanced English communication skills (both verbal and written). Hands-on experience with cloud platforms (Azure, AWS, or GCP). Familiarity with CI/CD practices and tools. Solid understanding of Agile methodologies and principles. Excellent problem-solving skills and attention to detail. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000020759053/Software-Architect?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   04/18/2025        Job Type   Full time        Work Experience   5+ years        Industry   Technology        Remote Job, Design and oversee the architecture of complex software systems using Microsoft technologies.\n    Provide technical leadership in areas such as .NET Core, C#, Azure, and SQL Server.\n    Collaborate with stakeholders to gather requirements, define technical solutions, and ensure alignment with business goals.\n    Drive the adoption of best practices in software engineering, including design patterns, performance optimization, and security standards.\n    Guide development teams in implementing microservices-based architectures and distributed systems.\n    Ensure seamless integration of solutions with existing systems and third-party applications.\n    Oversee the implementation of DevOps practices, including CI/CD pipelines and automated testing.\n    Stay up-to-date with emerging trends in Microsoft technologies and evaluate their relevance for the organization., 10+ years of professional experience in software development, with at least 3 years in a software architecture role.\n    Expert-level knowledge of Microsoft technologies, including .NET Core, C#, and SQL Server.\n    Strong experience with Azure services such as App Services, Functions, Kubernetes, and Azure DevOps.\n    In-depth understanding of microservices architecture, APIs, and cloud-native development.\n    Proven ability to design and implement scalable, secure, and resilient systems.\n    Hands-on experience with Agile methodologies and cross-functional team collaboration.\n    Exceptional problem-solving skills and the ability to communicate complex technical concepts clearly., View all jobs      Visit website", "title": "Software Architect", "location": "Remote Job", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": 3, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000020759053/Software-Architect?source=CareerSite", "description": "Job Description This is a remote position. Who We Are:At Advancio, we are passionate about technology and its ability to transform the world. We are rapidly expanding and building a company where we serve exceptional businesses, hire top talent, and have a lot of fun doing what we love! Job Summary: ​ We are looking for an experienced Software Architect with deep expertise in Microsoft technologies to lead the design and implementation of enterprise-level solutions. The ideal candidate will have a proven track record in architecting scalable, secure, and high-performance systems while mentoring development teams and ensuring best practices in software design. Requirements Design and oversee the architecture of complex software systems using Microsoft technologies. Provide technical leadership in areas such as . NET Core, C#, Azure, and SQL Server. Collaborate with stakeholders to gather requirements, define technical solutions, and ensure alignment with business goals. Drive the adoption of best practices in software engineering, including design patterns, performance optimization, and security standards. Guide development teams in implementing microservices-based architectures and distributed systems. Ensure seamless integration of solutions with existing systems and third-party applications. Oversee the implementation of DevOps practices, including CI/CD pipelines and automated testing. Stay up-to-date with emerging trends in Microsoft technologies and evaluate their relevance for the organization. Qualifications: 10+ years of professional experience in software development, with at least 3 years in a software architecture role. Expert-level knowledge of Microsoft technologies, including . NET Core, C#, and SQL Server. Strong experience with Azure services such as App Services, Functions, Kubernetes, and Azure DevOps. In-depth understanding of microservices architecture, APIs, and cloud-native development. Proven ability to design and implement scalable, secure, and resilient systems. Hands-on experience with Agile methodologies and cross-functional team collaboration. Exceptional problem-solving skills and the ability to communicate complex technical concepts clearly. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000020759078/AI-Developer?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   01/07/2025        Job Type   Full time        Work Experience   4-5 years        Industry   Technology        Remote Job, Develop and implement AI and machine learning models to solve specific business challenges.\n    Collaborate with cross-functional teams to identify opportunities for AI-driven solutions.\n    Train, fine-tune, and deploy models using frameworks like TensorFlow, PyTorch, or Scikit-learn.\n    Design and optimize AI pipelines for data preprocessing, model training, and deployment.\n    Build and maintain APIs or microservices to integrate AI solutions into existing applications.\n    Monitor and evaluate the performance of deployed models, ensuring scalability and reliability.\n    Stay updated with the latest advancements in AI/ML technologies and best practices., 4+ years of professional experience in AI/ML development.\n    Advanced English communication skills, both written and verbal.\n    Strong programming skills in Python, with experience in libraries like NumPy, Pandas, and Scikit-learn.\n    Proficiency in AI/ML frameworks such as TensorFlow, PyTorch, or Keras.\n    Experience in data preprocessing, feature engineering, and working with large datasets.\n    Knowledge of cloud platforms (Azure, AWS, or GCP) and deploying AI models in production.\n    Familiarity with version control systems (e.g., Git) and CI/CD pipelines.\n    Strong understanding of algorithms, data structures, and mathematics behind AI models., View all jobs      Visit website", "title": "AI Developer", "location": "Remote Job", "location_type": "remote", "job_type": null, "min_experience": 4, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000020759078/AI-Developer?source=CareerSite", "description": "Job Description This is a remote position. Who We Are:At Advancio, we are passionate about technology and its ability to transform the world. We are rapidly expanding and building a company where we serve exceptional businesses, hire top talent, and have a lot of fun doing what we love! Job Summary: ​ We are seeking a talented AI Developer to design, develop, and deploy intelligent solutions that solve complex problems. The ideal candidate has hands-on experience with machine learning frameworks, AI algorithms, and integrating AI models into scalable applications. ​ Requirements Develop and implement AI and machine learning models to solve specific business challenges. Collaborate with cross-functional teams to identify opportunities for AI-driven solutions. Train, fine-tune, and deploy models using frameworks like TensorFlow, PyTorch, or Scikit-learn. Design and optimize AI pipelines for data preprocessing, model training, and deployment. Build and maintain APIs or microservices to integrate AI solutions into existing applications. Monitor and evaluate the performance of deployed models, ensuring scalability and reliability. Stay updated with the latest advancements in AI/ML technologies and best practices. Qualifications: 4+ years of professional experience in AI/ML development. Advanced English communication skills, both written and verbal. Strong programming skills in Python, with experience in libraries like NumPy, Pandas, and Scikit-learn. Proficiency in AI/ML frameworks such as TensorFlow, PyTorch, or Keras. Experience in data preprocessing, feature engineering, and working with large datasets. Knowledge of cloud platforms (Azure, AWS, or GCP) and deploying AI models in production. Familiarity with version control systems (e. g. , Git) and CI/CD pipelines. Strong understanding of algorithms, data structures, and mathematics behind AI models. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000020759102/DevOps-Cloud-Engineer?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   01/07/2025        Job Type   Full time        Work Experience   4-5 years        Industry   Technology        Remote Job, Design, deploy, and manage cloud infrastructure on platforms such as AWS, Azure, or Google Cloud Platform (GCP).\n    Build and maintain CI/CD pipelines to streamline development and deployment processes.\n    Automate infrastructure provisioning, configuration, and monitoring using tools like Terraform, Ansible, or similar.\n    Ensure system reliability, availability, and performance through robust monitoring and alerting.\n    Collaborate with development teams to optimize the delivery and scalability of applications.\n    Manage containerized workloads using Docker and orchestration platforms such as Kubernetes.\n    Implement security best practices for cloud environments, including identity management, encryption, and compliance adherence.\n    Stay updated with the latest DevOps tools and methodologies to enhance team efficiency., 5+ years of experience in DevOps, cloud engineering, or related roles.\n    Advanced English communication skills, both verbal and written.\n    Proficiency in at least one major cloud platform (AWS, Azure, or GCP).\n    Hands-on experience with CI/CD tools (e.g., Jenkins, GitLab CI/CD, CircleCI).\n    Strong scripting skills in Python, Bash, or similar languages.\n    Solid knowledge of infrastructure-as-code (IaC) tools like Terraform or CloudFormation.\n    Experience with containerization (Docker) and orchestration (Kubernetes).\n    Familiarity with monitoring and logging tools like Prometheus, Grafana, or ELK Stack.\n    Strong understanding of networking, security, and system architecture., View all jobs      Visit website", "title": "DevOps Cloud Engineer", "location": "Remote Job", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000020759102/DevOps-Cloud-Engineer?source=CareerSite", "description": "Job Description This is a remote position. Who We Are:At Advancio, we are passionate about technology and its ability to transform the world. We are rapidly expanding and building a company where we serve exceptional businesses, hire top talent, and have a lot of fun doing what we love! Job Summary: ​ We are seeking a skilled DevOps Cloud Engineer to design, implement, and manage scalable cloud-based infrastructure and DevOps processes. The ideal candidate will have extensive experience with cloud platforms, CI/CD pipelines, and automation tools, ensuring the efficient deployment and operation of applications. Requirements Design, deploy, and manage cloud infrastructure on platforms such as AWS, Azure, or Google Cloud Platform (GCP). Build and maintain CI/CD pipelines to streamline development and deployment processes. Automate infrastructure provisioning, configuration, and monitoring using tools like Terraform, Ansible, or similar. Ensure system reliability, availability, and performance through robust monitoring and alerting. Collaborate with development teams to optimize the delivery and scalability of applications. Manage containerized workloads using Docker and orchestration platforms such as Kubernetes. Implement security best practices for cloud environments, including identity management, encryption, and compliance adherence. Stay updated with the latest DevOps tools and methodologies to enhance team efficiency. Qualifications: 5+ years of experience in DevOps, cloud engineering, or related roles. Advanced English communication skills, both verbal and written. Proficiency in at least one major cloud platform (AWS, Azure, or GCP). Hands-on experience with CI/CD tools (e. g. , Jenkins, GitLab CI/CD, CircleCI). Strong scripting skills in Python, Bash, or similar languages. Solid knowledge of infrastructure-as-code (IaC) tools like Terraform or CloudFormation. Experience with containerization (Docker) and orchestration (Kubernetes). Familiarity with monitoring and logging tools like Prometheus, Grafana, or ELK Stack. Strong understanding of networking, security, and system architecture. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000020759126/Data-Engineer?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   01/07/2025        Job Type   Full time        Work Experience   4-5 years        Industry   Technology        Remote Job, Analyze large and diverse datasets to uncover patterns, trends, and actionable insights.\n    Develop, train, and deploy machine learning models to solve complex business problems.\n    Collaborate with cross-functional teams to identify opportunities for data-driven improvements.\n    Build and maintain predictive models, recommendation systems, or optimization algorithms.\n    Design and implement data preprocessing pipelines, ensuring data quality and accessibility.\n    Create compelling visualizations and reports to communicate findings effectively to stakeholders.\n    Stay updated with the latest advancements in data science, machine learning, and AI technologies., 5+ years of professional experience in data science or related fields.\n    Advanced English communication skills, both verbal and written.\n    Proficiency in programming languages such as Python or R, with experience in data manipulation libraries (e.g., Pandas, NumPy).\n    Expertise in machine learning frameworks such as TensorFlow, PyTorch, or Scikit-learn.\n    Strong statistical analysis skills and knowledge of algorithms and data structures.\n    Experience with SQL and working with relational databases.\n    Familiarity with big data tools (e.g., Spark, Hadoop) and cloud platforms (AWS, Azure, GCP).\n    Ability to create data visualizations using tools like Tableau, Power BI, or Matplotlib., View all jobs      Visit website", "title": "Data Engineer", "location": "Remote Job", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000020759126/Data-Engineer?source=CareerSite", "description": "Job Description This is a remote position. Who We Are:At Advancio, we are passionate about technology and its ability to transform the world. We are rapidly expanding and building a company where we serve exceptional businesses, hire top talent, and have a lot of fun doing what we love! Job Summary: ​ We are seeking an experienced Data Scientist to extract actionable insights from complex datasets and develop advanced models to drive business growth. The ideal candidate has a deep understanding of machine learning, statistical analysis, and data visualization, with the ability to translate findings into impactful solutions. Requirements Analyze large and diverse datasets to uncover patterns, trends, and actionable insights. Develop, train, and deploy machine learning models to solve complex business problems. Collaborate with cross-functional teams to identify opportunities for data-driven improvements. Build and maintain predictive models, recommendation systems, or optimization algorithms. Design and implement data preprocessing pipelines, ensuring data quality and accessibility. Create compelling visualizations and reports to communicate findings effectively to stakeholders. Stay updated with the latest advancements in data science, machine learning, and AI technologies. Qualifications: 5+ years of professional experience in data science or related fields. Advanced English communication skills, both verbal and written. Proficiency in programming languages such as Python or R, with experience in data manipulation libraries (e. g. , Pandas, NumPy). Expertise in machine learning frameworks such as TensorFlow, PyTorch, or Scikit-learn. Strong statistical analysis skills and knowledge of algorithms and data structures. Experience with SQL and working with relational databases. Familiarity with big data tools (e. g. , Spark, Hadoop) and cloud platforms (AWS, Azure, GCP). Ability to create data visualizations using tools like Tableau, Power BI, or Matplotlib. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000020759150/Product-Owner-Enterprise-Tool-Specialist?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   01/07/2025        Job Type   Full time        Work Experience   4-5 years        Industry   Technology        Remote Job, Serve as the Product Owner for enterprise tools, defining and prioritizing product backlogs.\n    Collaborate with stakeholders to understand business requirements and translate them into tool enhancements and integrations.\n    Oversee the configuration, implementation, and optimization of enterprise tools.\n    Develop and maintain a strategic roadmap for tools, aligning with business goals and objectives.\n    Ensure tools meet compliance, security, and performance standards.\n    Monitor tool performance and user adoption, providing training and support as needed.\n    Stay updated on the latest trends and updates in enterprise tools and technology.\n    Act as the primary point of contact for vendor relationships and tool licensing., 5+ years of professional experience in product ownership, enterprise tool management, or a related role.\n    Advanced English communication skills, both verbal and written.\n    Proven experience with enterprise tools (e.g., CRM, ERP, collaboration platforms, or data visualization tools).\n    Strong knowledge of Agile methodologies, including Scrum and Kanban.\n    Ability to analyze business processes and recommend technical solutions.\n    Excellent stakeholder management and prioritization skills.\n    Familiarity with API integrations, workflow automation, and system architecture.\n    Experience working with cross-functional teams in a dynamic environment., View all jobs      Visit website", "title": "Product Owner / Enterprise Tool Specialist", "location": "Remote Job", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000020759150/Product-Owner-Enterprise-Tool-Specialist?source=CareerSite", "description": "Job Description This is a remote position. Who We Are:At Advancio, we are passionate about technology and its ability to transform the world. We are rapidly expanding and building a company where we serve exceptional businesses, hire top talent, and have a lot of fun doing what we love! Job Summary: ​ We are seeking an experienced Product Owner/Enterprise Tool Specialist to manage and optimize enterprise tools, ensuring they align with business needs and deliver value. The ideal candidate will bridge technical and business teams, driving tool adoption, efficiency, and continuous improvement. Requirements Serve as the Product Owner for enterprise tools, defining and prioritizing product backlogs. Collaborate with stakeholders to understand business requirements and translate them into tool enhancements and integrations. Oversee the configuration, implementation, and optimization of enterprise tools. Develop and maintain a strategic roadmap for tools, aligning with business goals and objectives. Ensure tools meet compliance, security, and performance standards. Monitor tool performance and user adoption, providing training and support as needed. Stay updated on the latest trends and updates in enterprise tools and technology. Act as the primary point of contact for vendor relationships and tool licensing. Qualifications: 5+ years of professional experience in product ownership, enterprise tool management, or a related role. Advanced English communication skills, both verbal and written. Proven experience with enterprise tools (e. g. , CRM, ERP, collaboration platforms, or data visualization tools). Strong knowledge of Agile methodologies, including Scrum and Kanban. Ability to analyze business processes and recommend technical solutions. Excellent stakeholder management and prioritization skills. Familiarity with API integrations, workflow automation, and system architecture. Experience working with cross-functional teams in a dynamic environment.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000021222419/Freelance-Technical-Interviewer?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   02/05/2025        Job Type   Freelance        Work Experience   5+ years        Industry   Technology        City   Bogota        State/Province   Distrito Capital de Bogotá        Country   Colombia        Zip/Postal Code   110111, View all jobs      Visit website", "title": "Freelance Technical Interviewer", "location": "", "location_type": "remote", "job_type": "contract", "min_experience": null, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000021222419/Freelance-Technical-Interviewer?source=CareerSite", "description": "Job Description 100% Remote (Colombia Preferred) | Freelance | Flexible HoursAbout Advancio Advancio is a global technology solutions company dedicated to building high-quality software teams and delivering top-tier engineering talent. As part of our ongoing commitment to hiring and maintaining technical excellence, we are building a network of freelance technical interviewers to conduct structured, high-quality technical assessments for candidates across different technology stacks. We are seeking experienced software engineers and technical leads who have prior experience conducting technical interviews, evaluating candidates for engineering roles, and providing structured feedback. Role OverviewAs a Freelance Technical Interviewer, you will be responsible for assessing developers, engineers, and technical specialists applying for positions through <PERSON><PERSON><PERSON>. Your expertise will help us identify top-tier talent and ensure our clients receive high-quality, vetted engineers. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022589006/Principal-Engineer?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   05/16/2025        Job Type   Full time        Work Experience   5+ years        Industry   Technology        Remote Job, Own architecture decisions and select the stack for our core platform\n    Build core features for our MVP — everything from APIs to frontend integrations\n    Implement coding standards, version control workflows, and CI/CD pipelines\n    Collaborate closely with Product Manager on technical feasibility and sprint planning\n    Set up our development environments (local, staging, prod) and workflows\n    Partner with design to bring product specs to life with high attention to usability and performance\n    Review code, mentor future engineers, and foster a healthy engineering culture\n    Collaborate on sprint velocity, estimation, and delivery metrics\n    Help recruit, onboard, and grow a high-performing dev team as we scale, 5+ years of professional experience in software engineering, ideally full-stack\n    Proven track record of building and launching products from scratch\n    Strong in backend and systems design (Node.js, .NET, Python, or similar)\n    Solid experience with front-end frameworks (React, Angular, or Vue)\n    Familiar with DevOps tooling, CI/CD, and cloud infrastructure (Azure, AWS, GCP)\n    Deep understanding of Agile delivery, Git workflows, and modern development pipelines\n    Comfortable collaborating with PMs, designers, and clients/stakeholders\n    Bonus: Experience in SaaS, internal tools, developer platforms, or B2B products\n    Bonus: Experience mentoring junior engineers or leading pods/squads, MVP launched and in users’ hands in 90 days\n    Engineering standards and environments fully established\n    Sprint delivery runs smoothly with high-quality code\n    Product–engineering collaboration is seamless\n    You’re coaching the next engineer(s) we hire, and loving it, View all jobs      Visit website", "title": "Principal Engineer", "location": "Remote Job", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022589006/Principal-Engineer?source=CareerSite", "description": "Job Description This is a remote position. ***THIS POSITION IS ONLY FOR CANDIDATES BASED IN MEXICO OR COLOMBIA** Who We Are: At Advancio, we believe in building powerful technology with meaningful impact. As a trusted partner to high-performing businesses across the U. S. , we help our clients scale smarter with top-tier talent and solutions. We’re fun, fast-moving, and laser-focused on quality—and we’re looking for someone like you to join our global team. What You’ll Do Own architecture decisions and select the stack for our core platform Build core features for our MVP — everything from APIs to frontend integrations Implement coding standards, version control workflows, and CI/CD pipelines Collaborate closely with Product Manager on technical feasibility and sprint planning Set up our development environments (local, staging, prod) and workflows Partner with design to bring product specs to life with high attention to usability and performance Review code, mentor future engineers, and foster a healthy engineering culture Collaborate on sprint velocity, estimation, and delivery metrics Help recruit, onboard, and grow a high-performing dev team as we scale RequirementsWho You Are 5+ years of professional experience in software engineering, ideally full-stack Proven track record of building and launching products from scratch Strong in backend and systems design (Node. js, . NET, Python, or similar) Solid experience with front-end frameworks (React, Angular, or Vue) Familiar with DevOps tooling, CI/CD, and cloud infrastructure (Azure, AWS, GCP) Deep understanding of Agile delivery, Git workflows, and modern development pipelines Comfortable collaborating with PMs, designers, and clients/stakeholders Bonus: Experience in SaaS, internal tools, developer platforms, or B2B products Bonus: Experience mentoring junior engineers or leading pods/squads What Success Looks Like MVP launched and in users’ hands in 90 days Engineering standards and environments fully established Sprint delivery runs smoothly with high-quality code Product–engineering collaboration is seamless You’re coaching the next engineer(s) we hire, and loving it I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022589086/Principal-Engineer?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   05/16/2025        Job Type   Full time        Work Experience   5+ years        Industry   Technology        Remote Job, Own architecture decisions and select the stack for our core platform\n    Build core features for our MVP — everything from APIs to frontend integrations\n    Implement coding standards, version control workflows, and CI/CD pipelines\n    Collaborate closely with Product Manager on technical feasibility and sprint planning\n    Set up our development environments (local, staging, prod) and workflows\n    Partner with design to bring product specs to life with high attention to usability and performance\n    Review code, mentor future engineers, and foster a healthy engineering culture\n    Collaborate on sprint velocity, estimation, and delivery metrics\n    Help recruit, onboard, and grow a high-performing dev team as we scale, 5+ years of professional experience in software engineering, ideally full-stack\n    Proven track record of building and launching products from scratch\n    Strong in backend and systems design (Node.js, .NET, Python, or similar)\n    Solid experience with front-end frameworks (React, Angular, or Vue)\n    Familiar with DevOps tooling, CI/CD, and cloud infrastructure (Azure, AWS, GCP)\n    Deep understanding of Agile delivery, Git workflows, and modern development pipelines\n    Comfortable collaborating with PMs, designers, and clients/stakeholders\n    Bonus: Experience in SaaS, internal tools, developer platforms, or B2B products\n    Bonus: Experience mentoring junior engineers or leading pods/squads, MVP launched and in users’ hands in 90 days\n    Engineering standards and environments fully established\n    Sprint delivery runs smoothly with high-quality code\n    Product–engineering collaboration is seamless\n    You’re coaching the next engineer(s) we hire, and loving it, View all jobs      Visit website", "title": "Principal Engineer", "location": "Remote Job", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022589086/Principal-Engineer?source=CareerSite", "description": "Job Description This is a remote position. ***THIS POSITION IS ONLY FOR CANDIDATES BASED IN UNITED STATES *** Who We Are: At Advancio, we believe in building powerful technology with meaningful impact. As a trusted partner to high-performing businesses across the U. S. , we help our clients scale smarter with top-tier talent and solutions. We’re fun, fast-moving, and laser-focused on quality—and we’re looking for someone like you to join our global team. What You’ll Do Own architecture decisions and select the stack for our core platform Build core features for our MVP — everything from APIs to frontend integrations Implement coding standards, version control workflows, and CI/CD pipelines Collaborate closely with Product Manager on technical feasibility and sprint planning Set up our development environments (local, staging, prod) and workflows Partner with design to bring product specs to life with high attention to usability and performance Review code, mentor future engineers, and foster a healthy engineering culture Collaborate on sprint velocity, estimation, and delivery metrics Help recruit, onboard, and grow a high-performing dev team as we scale RequirementsWho You Are 5+ years of professional experience in software engineering, ideally full-stack Proven track record of building and launching products from scratch Strong in backend and systems design (Node. js, . NET, Python, or similar) Solid experience with front-end frameworks (React, Angular, or Vue) Familiar with DevOps tooling, CI/CD, and cloud infrastructure (Azure, AWS, GCP) Deep understanding of Agile delivery, Git workflows, and modern development pipelines Comfortable collaborating with PMs, designers, and clients/stakeholders Bonus: Experience in SaaS, internal tools, developer platforms, or B2B products Bonus: Experience mentoring junior engineers or leading pods/squads What Success Looks Like MVP launched and in users’ hands in 90 days Engineering standards and environments fully established Sprint delivery runs smoothly with high-quality code Product–engineering collaboration is seamless You’re coaching the next engineer(s) we hire, and loving it I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022589121/Technical-Product-Manager?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   05/16/2025        Job Type   Full time        Work Experience   5+ years        Industry   Technology        Remote Job, Own the product roadmap — from problem discovery through feature release\n    Write technically sound requirements — specs that engineers trust and customers understand\n    Embed with engineering — participate in sprint planning, architecture review, backlog grooming\n    Lead cross-functional execution — collaborate with design, marketing, support, and founders\n    Translate business needs into developer workflows — for APIs, SDKs, integrations, and platform features\n    Validate feasibility — engage in architectural discussions and challenge assumptions when necessary\n    Manage product delivery cycle — prioritize features and bugs in agile sprints, define acceptance criteria\n    Drive internal education — demos, enablement, and internal documentation\n    Support go-to-market strategy — work with Product Marketing to define positioning and messaging\n    Engage users directly — customer interviews, feedback sessions, and developer community involvement, 3–5+ years in a technical product management or product owner role\n    Background as a developer, QA engineer, or technical solutions engineer strongly preferred\n    Direct experience working with SDKs, APIs, platforms, or integration-heavy products\n    Demonstrated ability to write specs for engineering and understand the implications of architectural choices\n    Deep understanding of Agile/Scrum, sprint planning, and dev tooling (Jira, Git, CI/CD, Postman, etc.)\n    Highly analytical — can read logs, use API docs, or test workflows without needing constant support\n    Startup-ready: thrives in ambiguity, takes initiative, builds processes from scratch, View all jobs      Visit website", "title": "Technical Product Manager", "location": "Remote Job", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022589121/Technical-Product-Manager?source=CareerSite", "description": "Job Description This is a remote position. ***THIS POSITION IS ONLY FOR CANDIDATES BASED IN UNITED STATES *** Who We Are: At Advancio, we believe in building powerful technology with meaningful impact. As a trusted partner to high-performing businesses across the U. S. , we help our clients scale smarter with top-tier talent and solutions. We’re fun, fast-moving, and laser-focused on quality—and we’re looking for someone like you to join our global team. Core Responsibilities Own the product roadmap — from problem discovery through feature release Write technically sound requirements — specs that engineers trust and customers understand Embed with engineering — participate in sprint planning, architecture review, backlog grooming Lead cross-functional execution — collaborate with design, marketing, support, and founders Translate business needs into developer workflows — for APIs, SDKs, integrations, and platform features Validate feasibility — engage in architectural discussions and challenge assumptions when necessary Manage product delivery cycle — prioritize features and bugs in agile sprints, define acceptance criteria Drive internal education — demos, enablement, and internal documentation Support go-to-market strategy — work with Product Marketing to define positioning and messaging Engage users directly — customer interviews, feedback sessions, and developer community involvement RequirementsMust-Have Qualifications 3–5+ years in a technical product management or product owner role Background as a developer, QA engineer, or technical solutions engineer strongly preferred Direct experience working with SDKs, APIs, platforms, or integration-heavy products Demonstrated ability to write specs for engineering and understand the implications of architectural choices Deep understanding of Agile/Scrum, sprint planning, and dev tooling (Jira, Git, CI/CD, Postman, etc. ) Highly analytical — can read logs, use API docs, or test workflows without needing constant support Startup-ready: thrives in ambiguity, takes initiative, builds processes from scratch I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022589143/Technical-Product-Manager?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   05/16/2025        Job Type   Full time        Work Experience   5+ years        Industry   Technology        Remote Job, Own the product roadmap — from problem discovery through feature release\n    Write technically sound requirements — specs that engineers trust and customers understand\n    Embed with engineering — participate in sprint planning, architecture review, backlog grooming\n    Lead cross-functional execution — collaborate with design, marketing, support, and founders\n    Translate business needs into developer workflows — for APIs, SDKs, integrations, and platform features\n    Validate feasibility — engage in architectural discussions and challenge assumptions when necessary\n    Manage product delivery cycle — prioritize features and bugs in agile sprints, define acceptance criteria\n    Drive internal education — demos, enablement, and internal documentation\n    Support go-to-market strategy — work with Product Marketing to define positioning and messaging\n    Engage users directly — customer interviews, feedback sessions, and developer community involvement, 3–5+ years in a technical product management or product owner role\n    Background as a developer, QA engineer, or technical solutions engineer strongly preferred\n    Direct experience working with SDKs, APIs, platforms, or integration-heavy products\n    Demonstrated ability to write specs for engineering and understand the implications of architectural choices\n    Deep understanding of Agile/Scrum, sprint planning, and dev tooling (Jira, Git, CI/CD, Postman, etc.)\n    Highly analytical — can read logs, use API docs, or test workflows without needing constant support\n    Startup-ready: thrives in ambiguity, takes initiative, builds processes from scratch, View all jobs      Visit website", "title": "Technical Product Manager", "location": "Remote Job", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022589143/Technical-Product-Manager?source=CareerSite", "description": "Job Description This is a remote position. ***THIS POSITION IS ONLY FOR CANDIDATES BASED IN MEXICO OR COLOMBIA *** Who We Are: At Advancio, we believe in building powerful technology with meaningful impact. As a trusted partner to high-performing businesses across the U. S. , we help our clients scale smarter with top-tier talent and solutions. We’re fun, fast-moving, and laser-focused on quality—and we’re looking for someone like you to join our global team. Core Responsibilities Own the product roadmap — from problem discovery through feature release Write technically sound requirements — specs that engineers trust and customers understand Embed with engineering — participate in sprint planning, architecture review, backlog grooming Lead cross-functional execution — collaborate with design, marketing, support, and founders Translate business needs into developer workflows — for APIs, SDKs, integrations, and platform features Validate feasibility — engage in architectural discussions and challenge assumptions when necessary Manage product delivery cycle — prioritize features and bugs in agile sprints, define acceptance criteria Drive internal education — demos, enablement, and internal documentation Support go-to-market strategy — work with Product Marketing to define positioning and messaging Engage users directly — customer interviews, feedback sessions, and developer community involvement RequirementsMust-Have Qualifications 3–5+ years in a technical product management or product owner role Background as a developer, QA engineer, or technical solutions engineer strongly preferred Direct experience working with SDKs, APIs, platforms, or integration-heavy products Demonstrated ability to write specs for engineering and understand the implications of architectural choices Deep understanding of Agile/Scrum, sprint planning, and dev tooling (Jira, Git, CI/CD, Postman, etc. ) Highly analytical — can read logs, use API docs, or test workflows without needing constant support Startup-ready: thrives in ambiguity, takes initiative, builds processes from scratch I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022671082/Delivery-Manager?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   05/21/2025        Job Type   Full time        Work Experience   5+ years        Industry   Technology        Remote Job, Serve as the Product Owner for enterprise tools, defining and prioritizing product backlogs. \n   Conduct client discovery sessions to gather business and digital needs. \n   Collaborate with clients to understand business requirements and translate them into technical solutions. \n   Act as a liaison for a client who doesn’t have a CTO or a formal IT department. \n   Develop and maintain a strategic roadmap for digital solutions, aligning with business goals and objectives. \n   Ensure digital solutions meet compliance, security, and performance standards. \n   Monitor service performance and capacity allocation to ensure client success. \n   Collaborate with engineering teams to ensure seamless execution. \n   Manage client expectations and nurture long-term relationships. \n   Measure service delivery and continuously adapt to maximize effectiveness., 5+ years of professional experience as a Business Analyst, Product Owner, or a related role. \n   Advanced English communication skills, both verbal and written. \n   Basic experience in Business Information Systems (CRM, ERP, SCM, FMS). \n   Proven experience documenting and communicating technical requirement and aligning them to business needs. \n   Strong knowledge of Agile and traditional methodologies, including Scrum, Kanban and Project Management. \n   Ability to analyze business processes and recommend technical solutions. \n   Excellent stakeholder management and prioritization skills. \n   Strong leadership and communication skills, both with technical and non-technical teams., Experience working with cross-functional teams in a dynamic environment. \n   Previous experience as a software engineer., View all jobs      Visit website", "title": "Delivery Manager", "location": "Remote Job", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022671082/Delivery-Manager?source=CareerSite", "description": "Job Description Job Summary: ***THIS POSITION IS ONLY FOR CANDIDATES BASED IN MEXICO OR COLOMBIA *** We are seeking an experienced Service Delivery Manager to gather technical needs from U. S. based customers and translate them into structured technical requirements. This role will be responsible for defining technical solutions such as new feature development, app maintenance, process automation and platform customization, usually aligned to Digital Transformation initiatives. The ideal candidate will bridge technical and business teams and manage capacity allocation, driving agility, efficiency, and continuous improvement. Responsibilities: Serve as the Product Owner for enterprise tools, defining and prioritizing product backlogs. Conduct client discovery sessions to gather business and digital needs. Collaborate with clients to understand business requirements and translate them into technical solutions. Act as a liaison for a client who doesn’t have a CTO or a formal IT department. Develop and maintain a strategic roadmap for digital solutions, aligning with business goals and objectives. Ensure digital solutions meet compliance, security, and performance standards. Monitor service performance and capacity allocation to ensure client success. Collaborate with engineering teams to ensure seamless execution. Manage client expectations and nurture long-term relationships. Measure service delivery and continuously adapt to maximize effectiveness. Requirements 5+ years of professional experience as a Business Analyst, Product Owner, or a related role. Advanced English communication skills, both verbal and written. Basic experience in Business Information Systems (CRM, ERP, SCM, FMS). Proven experience documenting and communicating technical requirement and aligning them to business needs. Strong knowledge of Agile and traditional methodologies, including Scrum, Kanban and Project Management. Ability to analyze business processes and recommend technical solutions. Excellent stakeholder management and prioritization skills. Strong leadership and communication skills, both with technical and non-technical teams. Experience working with cross-functional teams in a dynamic environment. Previous experience as a software engineer. This is a remote position. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022750464/Product-Manager?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   05/29/2025        Job Type   Full time        Work Experience   4-5 years        Industry   Technology        City   Mexico        State/Province   México        Country   Mexico        Zip/Postal Code   111112, Serve as the Product Owner for enterprise tools, defining and prioritizing product backlogs.\n\nConduct client discovery sessions to gather business and digital needs.\n\nCollaborate with clients to understand business requirements and translate them into technical solutions.\n\nAct as a liaison for a client who doesn’t have a CTO or a formal IT department.\n\nDevelop and maintain a strategic roadmap for digital solutions, aligning with business goals and objectives.\n\nEnsure digital solutions meet compliance, security, and performance standards.\n\nMonitor service performance and capacity allocation to ensure client success.\n\nCollaborate with engineering teams to ensure seamless execution.\n\nManage client expectations and nurture long-term relationships.\n\nMeasure service delivery and continuously adapt to maximize effectiveness., 4+ years of professional experience as a Business Analyst, Product Owner, or a related role.\n\nAdvanced English communication skills, both verbal and written.\n\nBasic experience in Business Information Systems (CRM, ERP, SCM, FMS).\n\nProven experience documenting and communicating technical requirement and aligning them to business needs.\n\nStrong knowledge of Agile and traditional methodologies, including Scrum, Kanban and Project Management.\n\nAbility to analyze business processes and recommend technical solutions.\n\nExcellent stakeholder management and prioritization skills.\n\nStrong leadership and communication skills, both with technical and non-technical teams., Experience working with cross-functional teams in a dynamic environment.\n\nPrevious experience as a software engineer., View all jobs      Visit website", "title": "Product Manager", "location": "", "location_type": null, "job_type": null, "min_experience": 4, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022750464/Product-Manager?source=CareerSite", "description": "Job Description Job Summary: ***THIS POSITION IS ONLY FOR CANDIDATES BASED IN MEXICO OR COLOMBIA *** We are seeking an experienced Service Delivery Manager to gather technical needs from U. S. based customers and translate them into structured technical requirements. This role will be responsible for defining technical solutions such as new feature development, app maintenance, process automation and platform customization, usually aligned to Digital Transformation initiatives. The ideal candidate will bridge technical and business teams and manage capacity allocation, driving agility, efficiency, and continuous improvement. Responsibilities: Serve as the Product Owner for enterprise tools, defining and prioritizing product backlogs. Conduct client discovery sessions to gather business and digital needs. Collaborate with clients to understand business requirements and translate them into technical solutions. Act as a liaison for a client who doesn’t have a CTO or a formal IT department. Develop and maintain a strategic roadmap for digital solutions, aligning with business goals and objectives. Ensure digital solutions meet compliance, security, and performance standards. Monitor service performance and capacity allocation to ensure client success. Collaborate with engineering teams to ensure seamless execution. Manage client expectations and nurture long-term relationships. Measure service delivery and continuously adapt to maximize effectiveness. Requirements 4+ years of professional experience as a Business Analyst, Product Owner, or a related role. Advanced English communication skills, both verbal and written. Basic experience in Business Information Systems (CRM, ERP, SCM, FMS). Proven experience documenting and communicating technical requirement and aligning them to business needs. Strong knowledge of Agile and traditional methodologies, including Scrum, Kanban and Project Management. Ability to analyze business processes and recommend technical solutions. Excellent stakeholder management and prioritization skills. Strong leadership and communication skills, both with technical and non-technical teams. Experience working with cross-functional teams in a dynamic environment. Previous experience as a software engineer. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022781284/Delivery-Manager?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   06/02/2025        Job Type   Full time        Work Experience   5+ years        Industry   Technology        City   Merida        State/Province   Yucatán        Country   Mexico        Zip/Postal Code   97203, Serve as the Product Owner for enterprise tools, defining and prioritizing product backlogs.\n\nConduct client discovery sessions to gather business and digital needs.\n\nCollaborate with clients to understand business requirements and translate them into technical solutions.\n\nAct as a liaison for a client who doesn’t have a CTO or a formal IT department.\n\nDevelop and maintain a strategic roadmap for digital solutions, aligning with business goals and objectives.\n\nEnsure digital solutions meet compliance, security, and performance standards.\n\nMonitor service performance and capacity allocation to ensure client success.\n\nCollaborate with engineering teams to ensure seamless execution.\n\nManage client expectations and nurture long-term relationships.\n\nMeasure service delivery and continuously adapt to maximize effectiveness., , 5+ years of professional experience as a Business Analyst, Product Owner, or a related role.Advanced English communication skills, both verbal and written.Basic experience in Business Information Systems (CRM, ERP, SCM, FMS).Proven experience documenting and communicating technical requirement and aligning them to business needs.Strong knowledge of Agile and traditional methodologies, including Scrum, Kanban and Project Management.Ability to analyze business processes and recommend technical solutions.Excellent stakeholder management and prioritization skills.Strong leadership and communication skills, both with technical and non-technical teams.Experience working with cross-functional teams in a dynamic environment.Previous experience as a software engineer., 5+ years of professional experience as a Business Analyst, Product Owner, or a related role.Advanced English communication skills, both verbal and written.Basic experience in Business Information Systems (CRM, ERP, SCM, FMS).Proven experience documenting and communicating technical requirement and aligning them to business needs.Strong knowledge of Agile and traditional methodologies, including Scrum, Kanban and Project Management.Ability to analyze business processes and recommend technical solutions.Excellent stakeholder management and prioritization skills.Strong leadership and communication skills, both with technical and non-technical teams., Experience working with cross-functional teams in a dynamic environment.Previous experience as a software engineer., View all jobs      Visit website", "title": "Delivery Manager", "location": "", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022781284/Delivery-Manager?source=CareerSite", "description": "Job Description Job Summary: ***THIS POSITION IS ONLY FOR CANDIDATES BASED IN MEXICO OR COLOMBIA *** We are seeking an experienced Service Delivery Manager to gather technical needs from U. S. based customers and translate them into structured technical requirements. This role will be responsible for defining technical solutions such as new feature development, app maintenance, process automation and platform customization, usually aligned to Digital Transformation initiatives. The ideal candidate will bridge technical and business teams and manage capacity allocation, driving agility, efficiency, and continuous improvement. Responsibilities: Serve as the Product Owner for enterprise tools, defining and prioritizing product backlogs. Conduct client discovery sessions to gather business and digital needs. Collaborate with clients to understand business requirements and translate them into technical solutions. Act as a liaison for a client who doesn’t have a CTO or a formal IT department. Develop and maintain a strategic roadmap for digital solutions, aligning with business goals and objectives. Ensure digital solutions meet compliance, security, and performance standards. Monitor service performance and capacity allocation to ensure client success. Collaborate with engineering teams to ensure seamless execution. Manage client expectations and nurture long-term relationships. Measure service delivery and continuously adapt to maximize effectiveness. Requirements5+ years of professional experience as a Business Analyst, Product Owner, or a related role. Advanced English communication skills, both verbal and written. Basic experience in Business Information Systems (CRM, ERP, SCM, FMS). Proven experience documenting and communicating technical requirement and aligning them to business needs. Strong knowledge of Agile and traditional methodologies, including Scrum, Kanban and Project Management. Ability to analyze business processes and recommend technical solutions. Excellent stakeholder management and prioritization skills. Strong leadership and communication skills, both with technical and non-technical teams. Experience working with cross-functional teams in a dynamic environment. Previous experience as a software engineer. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022893356/Principal-Engineer-Java-Based-in-U-S-A?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   06/25/2025        Job Type   Full time        Work Experience   7+ years        Industry   Technology        Remote Job, View all jobs      Visit website", "title": "Principal Engineer (Java) Based in U.S.A", "location": "Remote Job", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022893356/Principal-Engineer-Java-Based-in-U-S-A?source=CareerSite", "description": "Job Description This is a remote position. Remote – USA | Full-Time ​ I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022897128/Service-Delivery-Manager-_-Based-in-U-S-A?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   06/24/2025        Job Type   Full time        Industry   Technology        City   Austin        State/Province   Texas        Country   United States        Zip/Postal Code   73301, Serve as the Product Owner for enterprise tools, defining and prioritizing product backlogs.Conduct client discovery sessions to gather business and digital needs.Collaborate with clients to understand business requirements and translate them into technical solutions.Act as a liaison for a client who doesn’t have a CTO or a formal IT department.Develop and maintain a strategic roadmap for digital solutions, aligning with business goals and objectives.Ensure digital solutions meet compliance, security, and performance standards.Monitor service performance and capacity allocation to ensure client success.Collaborate with engineering teams to ensure seamless execution.Manage client expectations and nurture long-term relationships.Measure service delivery and continuously adapt to maximize effectiveness., 4+ years of professional experience as a Business Analyst, Product Owner, or a related role.Basic experience in Business Information Systems (CRM, ERP, SCM, FMS).Proven experience documenting and communicating technical requirement and aligning them to business needs.Strong knowledge of Agile and traditional methodologies, including Scrum, Kanban and Project Management.Ability to analyze business processes and recommend technical solutions.Excellent stakeholder management and prioritization skills.Strong leadership and communication skills, both with technical and non-technical teams.Experience working with cross-functional teams in a dynamic environment.Previous experience as a software engineer., View all jobs      Visit website", "title": "Service Delivery Manager  _  Based in U.S.A", "location": "", "location_type": null, "job_type": null, "min_experience": 4, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022897128/Service-Delivery-Manager-_-Based-in-U-S-A?source=CareerSite", "description": "Job Description ***THIS POSITION IS ONLY FOR CANDIDATES BASED IN UNITED STATES ***We are seeking an experienced Service Delivery Manager to gather technical needs from customers and translate them into structured technical requirements. This role will be responsible for defining technical solutions such as new feature development, app maintenance, process automation and platform customization, usually aligned to Digital Transformation initiatives. The ideal candidate will bridge technical and business teams and manage capacity allocation, driving agility, efficiency, and continuous improvement. Responsibilities:Serve as the Product Owner for enterprise tools, defining and prioritizing product backlogs. Conduct client discovery sessions to gather business and digital needs. Collaborate with clients to understand business requirements and translate them into technical solutions. Act as a liaison for a client who doesn’t have a CTO or a formal IT department. Develop and maintain a strategic roadmap for digital solutions, aligning with business goals and objectives. Ensure digital solutions meet compliance, security, and performance standards. Monitor service performance and capacity allocation to ensure client success. Collaborate with engineering teams to ensure seamless execution. Manage client expectations and nurture long-term relationships. Measure service delivery and continuously adapt to maximize effectiveness. Requirements4+ years of professional experience as a Business Analyst, Product Owner, or a related role. Basic experience in Business Information Systems (CRM, ERP, SCM, FMS). Proven experience documenting and communicating technical requirement and aligning them to business needs. Strong knowledge of Agile and traditional methodologies, including Scrum, Kanban and Project Management. Ability to analyze business processes and recommend technical solutions. Excellent stakeholder management and prioritization skills. Strong leadership and communication skills, both with technical and non-technical teams. Experience working with cross-functional teams in a dynamic environment. Previous experience as a software engineer. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022904137/Product-Manager-_-Based-in-U-S-A-?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   06/25/2025        Job Type   Full time        Industry   Technology        City   Dallas        State/Province   Texas        Country   United States        Zip/Postal Code   75201, Lead the product discovery process., Define the MVP and key features to get continuous value delivery., Create and own the product roadmap from problem discovery through feature delivery., Validate product outcomes with users and stakeholders., Translate business needs into product features, epics, user stories., Write technically sound requirements that engineers trust and customers understand., Collaborate with UX, Engineering and business., Create, prioritize, refine, and communicate the product backlog., Support the go-to-market strategy., Support the validation of desirability, feasibility, and viability for the product., Engage with stakeholders and users to ensure alignment., 3+ years of experience as a Product Owner or Product Manager., Deep understanding of Agile Product Development.  Excellent stakeholder management and prioritization skills., Strong leadership and communication skills, both with technical and non-technical teams., Experience working with cross-functional teams in dynamic environments., Familiar with startup environments and models., Basic experience in Design Thinking techniques., Familiar with the startup process and frameworks., View all jobs      Visit website", "title": "Product Manager _ Based in U.S.A.", "location": "", "location_type": null, "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022904137/Product-Manager-_-Based-in-U-S-A-?source=CareerSite", "description": "Job Description ***THIS POSITION IS ONLY FOR CANDIDATES BASED IN UNITED STATES ***We are seeking an experienced Product Manager to identify business and user needs from customers, define, and deliver digital products that are user-centered, technically feasible and aligned with business goals. This role will be responsible for creating product artifacts such as roadmap, vision board, backlog, customer journey, wireframes, and release notes. The ideal candidate will bridge business, technical teams and users, driving innovation, agility, and continuous discovery. Core Responsibilities:Lead the product discovery process. Define the MVP and key features to get continuous value delivery. Create and own the product roadmap from problem discovery through feature delivery. Validate product outcomes with users and stakeholders. Translate business needs into product features, epics, user stories. Write technically sound requirements that engineers trust and customers understand. Collaborate with UX, Engineering and business. Create, prioritize, refine, and communicate the product backlog. Support the go-to-market strategy. Support the validation of desirability, feasibility, and viability for the product. Engage with stakeholders and users to ensure alignment. Requirements3+ years of experience as a Product Owner or Product Manager. Deep understanding of Agile Product Development. Excellent stakeholder management and prioritization skills. Strong leadership and communication skills, both with technical and non-technical teams. Experience working with cross-functional teams in dynamic environments. Familiar with startup environments and models. Basic experience in Design Thinking techniques. Familiar with the startup process and frameworks. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022904172/Scrum-Master-Based-in-U-S-A?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   06/25/2025        Job Type   Full time        Industry   Technology        City   Houston        State/Province   Minnesota        Country   United States        Zip/Postal Code   55943, Facilitate daily stand-ups, sprint planning, reviews, and retrospectives.Remove blockers and help the team stay focused and productive.Coach team members on Agile principles and foster a culture of continuous improvement.Support Product Owners with backlog grooming and sprint goals.Monitor team progress using tools like Jira, ClickUp, or Trello.Promote open communication and collaboration across teams.Adapt processes and workflows as needed to meet startup-level agility and pace., View all jobs      Visit website", "title": "Scrum Master Based in U.S.A", "location": "", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": 3, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022904172/Scrum-Master-Based-in-U-S-A?source=CareerSite", "description": "Job Description ***THIS POSITION IS ONLY FOR CANDIDATES BASED IN UNITED STATES ***We’re looking for a passionate and proactive Scrum Master with at least 3 years of hands-on experience working in fast-paced, startup-like environments. If you thrive in dynamic teams, love helping others stay aligned and productive, and know how to keep Agile ceremonies engaging and purposeful — we want to meet you! What You’ll DoFacilitate daily stand-ups, sprint planning, reviews, and retrospectives. Remove blockers and help the team stay focused and productive. Coach team members on Agile principles and foster a culture of continuous improvement. Support Product Owners with backlog grooming and sprint goals. Monitor team progress using tools like Jira, ClickUp, or Trello. Promote open communication and collaboration across teams. Adapt processes and workflows as needed to meet startup-level agility and pace. Requirements3+ years as a Scrum Master or Agile Facilitator, ideally in a startup or fast-scaling environment. Strong understanding of Scrum, Agile methodologies, and Lean principles. Excellent communication, facilitation, and conflict resolution skills. Experience working with cross-functional, remote teams. Comfortable using Agile tools (e. g. , <PERSON>ra, Confluence, Notion, Miro). Certified Scrum Master (CSM) or equivalent is a plus. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022966321/Administrative-Assistant-Based-In-U-S-A?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   06/27/2025        Job Type   Full time        Industry   Technology        City   San Felipe        State/Province   Texas        Country   United States        Zip/Postal Code   77473, Manage daily administrative tasks, including filing, data entry, and document preparation.Answer and direct phone calls, emails, and other correspondence.Schedule meetings, appointments, and coordinate calendars.Assist in preparing reports, presentations, and other materials as needed.Maintain office supplies inventory and place orders when necessary.Support the team with travel arrangements, expense reports, and other logistical needs.Handle confidential information with discretion., View all jobs      Visit website", "title": "Administrative Assistant Based In U.S.A", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022966321/Administrative-Assistant-Based-In-U-S-A?source=CareerSite", "description": "Job Description ***THIS POSITION IS ONLY FOR CANDIDATES BASED IN UNITED STATES ***We are seeking a detail-oriented and proactive Administrative Assistant to join our team. This person will provide essential support to ensure the smooth and efficient operation of our office. The ideal candidate is organized, reliable, and capable of handling multiple tasks with accuracy and professionalism. Key ResponsibilitiesManage daily administrative tasks, including filing, data entry, and document preparation. Answer and direct phone calls, emails, and other correspondence. Schedule meetings, appointments, and coordinate calendars. Assist in preparing reports, presentations, and other materials as needed. Maintain office supplies inventory and place orders when necessary. Support the team with travel arrangements, expense reports, and other logistical needs. Handle confidential information with discretion. RequirementsProven experience as an administrative assistant or in a similar role. Excellent organizational and time-management skills. Strong written and verbal communication abilities in English. Proficiency in MS Office (Word, Excel, PowerPoint, Outlook) and other office management tools. Ability to work independently and as part of a team. Attention to detail and problem-solving skills. Associate’s or Bachelor’s degree in business administration or related field. Experience supporting multiple team members or executives. I'm interested", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022966346/Product-Owner-Based-in-U-S-A?source=CareerSite", "company_id": 3351, "source": 3, "skills": ", Job listing    Job details, Date Opened   06/27/2025        Job Type   Full time        Industry   Technology        City   Houston        State/Province   Missouri        Country   United States        Zip/Postal Code   65483, Serve as the Product Owner for enterprise tools, defining and prioritizing product backlogs.Collaborate with stakeholders to understand business requirements and translate them into tool enhancements and integrations.Oversee the configuration, implementation, and optimization of enterprise tools.Develop and maintain a strategic roadmap for tools, aligning with business goals and objectives.Ensure tools meet compliance, security, and performance standards.Monitor tool performance and user adoption, providing training and support as needed.Stay updated on the latest trends and updates in enterprise tools and technology.Act as the primary point of contact for vendor relationships and tool licensing., 5+ years of professional experience in product ownership, enterprise tool management, or a related role.Advanced English communication skills, both verbal and written.Proven experience with enterprise tools (e.g., CRM, ERP, collaboration platforms, or data visualization tools).Strong knowledge of Agile methodologies, including Scrum and Kanban.Ability to analyze business processes and recommend technical solutions.Excellent stakeholder management and prioritization skills.Familiarity with API integrations, workflow automation, and system architecture.Experience working with cross-functional teams in a dynamic environment., View all jobs      Visit website", "title": "Product Owner Based in U.S.A", "location": "", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://advanc.zohorecruit.com/jobs/Careers/570719000022966346/Product-Owner-Based-in-U-S-A?source=CareerSite", "description": "Job Description We are seeking an experienced Product Owner/Enterprise Tool Specialist to manage and optimize enterprise tools, ensuring they align with business needs and deliver value. The ideal candidate will bridge technical and business teams, driving tool adoption, efficiency, and continuous improvement. RequirementsServe as the Product Owner for enterprise tools, defining and prioritizing product backlogs. Collaborate with stakeholders to understand business requirements and translate them into tool enhancements and integrations. Oversee the configuration, implementation, and optimization of enterprise tools. Develop and maintain a strategic roadmap for tools, aligning with business goals and objectives. Ensure tools meet compliance, security, and performance standards. Monitor tool performance and user adoption, providing training and support as needed. Stay updated on the latest trends and updates in enterprise tools and technology. Act as the primary point of contact for vendor relationships and tool licensing. Requirements5+ years of professional experience in product ownership, enterprise tool management, or a related role. Advanced English communication skills, both verbal and written. Proven experience with enterprise tools (e. g. , CRM, ERP, collaboration platforms, or data visualization tools). Strong knowledge of Agile methodologies, including Scrum and Kanban. Ability to analyze business processes and recommend technical solutions. Excellent stakeholder management and prioritization skills. Familiarity with API integrations, workflow automation, and system architecture. Experience working with cross-functional teams in a dynamic environment. I'm interested", "ctc": null, "currency": null, "meta": {}}]