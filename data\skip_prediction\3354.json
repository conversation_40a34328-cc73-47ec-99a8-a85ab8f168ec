[{"jd_link": "https://www.datadotlabs.com/jobs/junior-net-software-engineer-5-10-positions/", "company_id": 3354, "source": 3, "skills": "Managed IT Services, Cloud Engineering, Data Analytics & AI\n\nBI Solutions\nBig Data\nAI\nChatbot\nMachine Learning\nDeep Learning\nAdvanced Analytics, Chatbot, Parking Management System, Careers\n\nCurrent Openings\nLife at Datadot\nApply for Job, Awards & Accreditations, Carry out research on new technologies., Required skills: ASP.NET MVC, C#, Entity Framework, LINQ, Web API, MSSQL., Life @ Datadot, Digital Marketing Services, Managed IT Services, Cloud Engineering, Data Analytics & AI\n\nBI Solutions\nBig Data\nAI\nChatbot\nMachine Learning\nDeep Learning\nAdvanced Analytics, Chatbot, Parking Management System, Careers\n\nCurrent Openings\nLife at Datadot\nApply for Job, Awards & Accreditations", "title": "Junior .NET Software Engineer (5-10 positions)", "location": "Malaysia", "location_type": null, "job_type": "full_time", "min_experience": 2, "max_experience": 2, "apply_link": "https://www.datadotlabs.com/jobs/junior-net-software-engineer-5-10-positions/", "description": "Job Description Participate in software development life cycle, all aspects of requirement gathering, analysis, design, development, testing, implementation and support. Ensure software quality in terms of functionalities, reliability, performance, security and ease of maintenance. Diagnosing issues and performing bug fixes. Carry out research on new technologies. Prepare technical documents. Required Candidate profile Minimum Bachelor’s Degree in Computer Science/Information Technology/Software Engineering or equivalent. At least 2 years of working experience in Web Application Development. Fresh graduate with outstanding academic results are encouraged to apply. Required skills: ASP. NET MVC, C#, Entity Framework, LINQ, Web API, MSSQL. Preferred skills: HTML5, JavaScript, jQuery, Bootstrap, CSS3. Experience in mobile app development (Android native script, Swift) will be an added advantage. Knowledge and experience in Object Oriented Design methodology and Design Pattern. Able to perform the application design using OOAD independently. Experience will full SDLC cycle in actual project implementation. Experience in source control system like TortoiseSVN and Visual SVN. Must be positive, hardworking and able to meet tight deadlines. Able to work independently as well as in a team and have self-initiative. Good Remuneration Package 13th month salary per year Additional Yearly Performance Bonus ESOP (Shares awarded to employee that 4X-5X increase in value per year) Specific Question How many years of experience do you have, respectively, for the following skills/technologies: C#, ASP. NET MVC, ASP. NET Web API, Entity Framework? Position: Junior . NET Software Engineer (5-10 positions) Nationality: Malaysia Employment type: Permanent", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.datadotlabs.com/jobs/senior-net-software-engineer/", "company_id": 3354, "source": 3, "skills": "Managed IT Services, Cloud Engineering, Data Analytics & AI\n\nBI Solutions\nBig Data\nAI\nChatbot\nMachine Learning\nDeep Learning\nAdvanced Analytics, Chatbot, Parking Management System, Careers\n\nCurrent Openings\nLife at Datadot\nApply for Job, Awards & Accreditations, Carry out research on new technologies., Preferred skills: HTML5, JavaScript, jQuery, Bootstrap, CSS3., Life @ Datadot, Digital Marketing Services, Managed IT Services, Cloud Engineering, Data Analytics & AI\n\nBI Solutions\nBig Data\nAI\nChatbot\nMachine Learning\nDeep Learning\nAdvanced Analytics, Chatbot, Parking Management System, Careers\n\nCurrent Openings\nLife at Datadot\nApply for Job, Awards & Accreditations", "title": "Senior .NET Software Engineer", "location": "Malaysia", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": 5, "apply_link": "https://www.datadotlabs.com/jobs/senior-net-software-engineer/", "description": "Job Description Participate in software development life cycle, all aspects of requirement gathering, analysis, design, development, testing, implementation and support. Ensure software quality in terms of functionalities, reliability, performance, security and ease of maintenance. Diagnosing issues and performing bug fixes. Carry out research on new technologies. Prepare technical documents. Required Candidate profile Minimum Bachelor’s Degree in Computer Science/Information Technology/Software Engineering or equivalent. At least 5 years of working experience in Web Application Development. Required skills: ASP. NET MVC, C#, Entity Framework, LINQ, Web API, MSSQL. Preferred skills: HTML5, JavaScript, jQuery, Bootstrap, CSS3. Experience in mobile app development (Android native script, Swift) will be an added advantage. Knowledge and experience in Object Oriented Design methodology and Design Pattern. Able to perform the application design using OOAD independently. Experience will full SDLC cycle in actual project implementation. Experience in source control system like TortoiseSVN and Visual SVN. Must be positive, hardworking and able to meet tight deadlines. Able to work independently as well as in a team and have self-initiative Good Remuneration Package 13th month salary per year Additional Yearly Performance Bonus ESOP (Shares awarded to employee that 4X-5X increase in value per year) Specific Question How many years of experience do you have, respectively, for the following skills/technologies: C#, ASP. NET MVC, ASP. NET Web API, Entity Framework? Position: Senior . NET Software Engineer Nationality: MalaysiaEarliest Join Date: Anytime", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.datadotlabs.com/jobs/senior-software-engineer-java-2/", "company_id": 3354, "source": 3, "skills": "Managed IT Services, Cloud Engineering, Data Analytics & AI\n\nBI Solutions\nBig Data\nAI\nChatbot\nMachine Learning\nDeep Learning\nAdvanced Analytics, Chatbot, Parking Management System, Careers\n\nCurrent Openings\nLife at Datadot\nApply for Job, Awards & Accreditations, Maintain and support project post-implementation activities., MySQL, Ms. SQL or other relational database design, Life @ Datadot, Digital Marketing Services, Managed IT Services, Cloud Engineering, Data Analytics & AI\n\nBI Solutions\nBig Data\nAI\nChatbot\nMachine Learning\nDeep Learning\nAdvanced Analytics, Chatbot, Parking Management System, Careers\n\nCurrent Openings\nLife at Datadot\nApply for Job, Awards & Accreditations", "title": "Senior Software Engineer – Java", "location": "India", "location_type": null, "job_type": "full_time", "min_experience": 4, "max_experience": 4, "apply_link": "https://www.datadotlabs.com/jobs/senior-software-engineer-java-2/", "description": "Job Description Participate in custom enterprise application development and maintenance for large corporations both within Asia and worldwide. Participate in the software development life-cycle. Perform system analysis, design, testing and documentation whenever necessary. Perform unit testing and system integration. Maintain and support project post-implementation activities. Ensure timely delivery of assigned work. Provide recommendations and to assist in the preparation of project plans, time and resource estimates, and prioritiesResponsibility & accountability for the task tracking, reporting & completion to deadline of assigned tasks or team. Mentoring and education of junior staff. Any other tasks assigned by the Management as and when required. Required Candidate profile A minimum of 4 years solid experience, relevant to the following skill sets: Java (J2EE/JEE) SQL, HTML, CSS, and JavaScript MySQL, Ms. SQL or other relational database design Insurance knowledge is a plus Opportunity to travel to Asia Pacific country. Good Command of English communication skills – both written and spoken. Able to communicate in Mandarin and Cantonese is a plus point. Enthusiastic and self-motivated. Ability to work together with other developers in a team environment. Strong problem solving skills. Ability to see a task through from assignment to completion with minimal supervision. Ability to work efficiently to meet tight deadlines. Position: Senior Software Engineer – JavaNationality: IndiaEmployment: Permanent", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.datadotlabs.com/jobs/senior-software-engineer-java/", "company_id": 3354, "source": 3, "skills": "Managed IT Services, Cloud Engineering, Data Analytics & AI\n\nBI Solutions\nBig Data\nAI\nChatbot\nMachine Learning\nDeep Learning\nAdvanced Analytics, Chatbot, Parking Management System, Careers\n\nCurrent Openings\nLife at Datadot\nApply for Job, Awards & Accreditations, Maintain and support project post-implementation activities., MySQL, Ms. SQL or other relational database design, Life @ Datadot, Digital Marketing Services, Managed IT Services, Cloud Engineering, Data Analytics & AI\n\nBI Solutions\nBig Data\nAI\nChatbot\nMachine Learning\nDeep Learning\nAdvanced Analytics, Chatbot, Parking Management System, Careers\n\nCurrent Openings\nLife at Datadot\nApply for Job, Awards & Accreditations", "title": "Senior Software Engineer – Java", "location": "Malaysia", "location_type": null, "job_type": "full_time", "min_experience": 5, "max_experience": 5, "apply_link": "https://www.datadotlabs.com/jobs/senior-software-engineer-java/", "description": "Job Description Participate in custom enterprise application development and maintenance for large corporations both within Asia and worldwide. Participate in the software development life-cycle. Perform system analysis, design, testing and documentation whenever necessary. Perform unit testing and system integration. Maintain and support project post-implementation activities. Ensure timely delivery of assigned work. Provide recommendations and to assist in the preparation of project plans, time and resource estimates, and prioritiesResponsibility & accountability for the task tracking, reporting & completion to deadline of assigned tasks or team. Mentoring and education of junior staff. Any other tasks assigned by the Management as and when required. Required Candidate profile A minimum of 5 years solid experience, relevant to the following skill sets: Java (J2EE/JEE) SQL, HTML, CSS, and JavaScript MySQL, Ms. SQL or other relational database design Insurance knowledge is a plus Opportunity to travel to Asia Pacific country. Good Command of English communication skills – both written and spoken. Able to communicate in Mandarin and Cantonese is a plus point. Enthusiastic and self-motivated. Ability to work together with other developers in a team environment. Strong problem solving skills. Ability to see a task through from assignment to completion with minimal supervision. Ability to work efficiently to meet tight deadlines. Position: Senior Software Engineer – JavaNationality: MalaysiaEmployment: Permanent", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.datadotlabs.com/jobs/voice-translation-native-taiwanese/", "company_id": 3354, "source": 3, "skills": "Managed IT Services, Cloud Engineering, Data Analytics & AI\n\nBI Solutions\nBig Data\nAI\nChatbot\nMachine Learning\nDeep Learning\nAdvanced Analytics, Chatbot, Parking Management System, Careers\n\nCurrent Openings\nLife at Datadot\nApply for Job, Awards & Accreditations, Work Mode: Fully Remote Job  (Work from Home in Taiwan), 職務類型：合約制 (一年後可續約), Life @ Datadot, Digital Marketing Services, Managed IT Services, Cloud Engineering, Data Analytics & AI\n\nBI Solutions\nBig Data\nAI\nChatbot\nMachine Learning\nDeep Learning\nAdvanced Analytics, Chatbot, Parking Management System, Careers\n\nCurrent Openings\nLife at Datadot\nApply for Job, Awards & Accreditations", "title": "Voice Translation (Native Taiwanese)", "location": "Taiwan", "location_type": "remote", "job_type": "full_time", "min_experience": 1, "max_experience": 1, "apply_link": "https://www.datadotlabs.com/jobs/voice-translation-native-taiwanese/", "description": "Job Description We are hiring for Translator and Interpreting Customer Support for our Client. Position: Translation and Interpreting SupportRole: Responsible for Handling Inbound calls – listening to, understanding, and interpreting/translating spoken or written statements from one language to another. Reproduce statements in another language for listening or reading audience. Language: Taiwanese + EnglishWork Mode: Fully Remote Job (Work from Home in Taiwan)Job Type: Contractual (1 year Renewable Contract)Work device : You can use your own laptop/ desktop / mobile device with iOS or android to work. Company will provide you good headphones to attend the calls and conferences. Salary: 50,000 TWD + Night shift allowance 2,000 TWDMedical Benefits as per the law. Annual Leaves and Medical Leaves provided. Shift: Rotational (Mon-Sun) 5 days a weekShift schedule: Permanent Night Shift (PST-USA time zone) – 9 hours a day ( 8 hours working and 1 hour break time) Apply Now: https://forms. gle/4p88PnsRGA1CibDV8 我們目前正在為客戶招聘翻譯及口譯客戶支援人員 負責處理來電，並將口語或書面內容從一種語言翻譯/口譯成另一種語言，為讀者或聽眾用另一種語言重現語句語種：中文 + 英語工作模式：完全遠端工作 (在台灣居家辦公)職務類型：合約制 (一年後可續約)工作設備：可使用自己的筆記本電腦/桌上型電腦/iOS或Android行動裝置工作。公司將提供良好的視聽設備（如：耳機），方便您接聽電話和參加會議。薪資：50,000 台幣 + 夜班津貼 2,000 台幣依法提供醫療福利提供年假和病假輪班制 (週一至週日)，每週上班 5 天班別時間：永久夜班 (美國PST時區) – 每天9小時（工作8小時，休息1小時) Apply Now: https://forms. gle/4p88PnsRGA1CibDV8", "ctc": null, "currency": null, "meta": {}}]