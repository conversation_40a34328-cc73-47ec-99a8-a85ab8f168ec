[{"jd_link": "https://apriorit.breezy.hr/p/63b81f525671-advanced-c-and-low-level-programming-online-courses", "company_id": 3363, "source": 3, "skills": "Company WebsiteEmployees, Dnipro, UA - Remote (any location)Full-TimeCourses, Job Openings\"Advanced C++ and Low-Level Programming\" online courses, Apply To PositionUse My Indeed ResumeApply Using LinkedIn, Duration: just 3 months long, allows you to learn without committing to a long-term program.Cost: the course is completely free!Flexibility: it is entirely remote, so you can learn from the comfort of your own homeStudy program: includes 1.5-hour lectures three times per week, homework to consolidate acquired knowledge, and a practical part where you will work on the project with your team supported by experienced mentors.Mentors and tutors: our best programming specialists will guide you through the course and make sure you understand everything.You will also try a range of languages, platforms, and technologies such as C++, С, Windows, Linux, macOS, blockchain, reverse engineering, and many more., Remote work or a comfortable office (Kyiv, Dnipro, Poznan), depending on what suits you betterAll the equipment you need for daily work is provided30-40 flexible working hours per week to accommodate your schedulePersonal mentor who helps you in professional and career developmentIndividual development plan to make your professional growth fasterPaid time for self-education and an extensive base of internal resourcesCorporate University with lectures on hard skills, soft skills, and meetupsFree corporate English lessons with native speakers to improve your levelProjects based on low-level programming in the areas of cybersecurity, reverse engineering, and blockchain-based solutionsMedical insurance or compensation for sports for your health20 paid workdays of annual leave, plus sick leave, are guaranteedAdditional paid educational leave for taking exams/defending the diplomaSupport of a psychologist for your wellbeingFriendly working atmosphere and an open corporate culture which help you always feel comfortable, Students in their 3rd to 5th years and recent IT graduatesWith basic knowledge in C++, algorithms, and object-oriented programmingAnd with a Pre-Intermediate English level, wait for a vacancy for admission to coursessend us your resume pass a technical test in C++ and OOP, including questions on knowledge of OOP and algorithmic problems. Also, past English test., Apply To PositionUse My Indeed ResumeApply Using LinkedIn", "title": "\"Advanced C++ and Low-Level Programming\" online courses", "location": "Dnipro, UA", "location_type": "remote", "job_type": null, "min_experience": 12, "max_experience": 12, "apply_link": "https://apriorit.breezy.hr/p/63b81f525671-advanced-c-and-low-level-programming-online-courses", "description": "Hey there! We are excited to announce that we are offering online courses that come with an opportunity to work with our company. During our course, you will get the skills and knowledge necessary to succeed in the industry, so you can launch your developer's career in just 3 months! We are a software engineering company that was established in 2002. We have extensive experience in system programming, cybersecurity, reverse engineering, SaaS/web, blockchain-based solutions, and artificial intelligence. Over the past 12 years, we have trained over 600 specialists from 31 different groups. We are proud to say that many of them are now a part of our team. Besides that, they have become experts who already teach students in our courses. Here are some advantages of our courses: Duration: just 3 months long, allows you to learn without committing to a long-term program. Cost: the course is completely free! Flexibility: it is entirely remote, so you can learn from the comfort of your own homeStudy program: includes 1. 5-hour lectures three times per week, homework to consolidate acquired knowledge, and a practical part where you will work on the project with your team supported by experienced mentors. Mentors and tutors: our best programming specialists will guide you through the course and make sure you understand everything. You will also try a range of languages, platforms, and technologies such as C++, С, Windows, Linux, macOS, blockchain, reverse engineering, and many more. If you're one of our most successful graduates, we'll offer you long-term employment with our company. Plus, all graduates who complete the course will receive a personal certificate. After employment, you can count on the following benefits: Remote work or a comfortable office (Kyiv, Dnipro, Poznan), depending on what suits you betterAll the equipment you need for daily work is provided30-40 flexible working hours per week to accommodate your schedulePersonal mentor who helps you in professional and career developmentIndividual development plan to make your professional growth fasterPaid time for self-education and an extensive base of internal resourcesCorporate University with lectures on hard skills, soft skills, and meetupsFree corporate English lessons with native speakers to improve your levelProjects based on low-level programming in the areas of cybersecurity, reverse engineering, and blockchain-based solutionsMedical insurance or compensation for sports for your health20 paid workdays of annual leave, plus sick leave, are guaranteedAdditional paid educational leave for taking exams/defending the diplomaSupport of a psychologist for your wellbeingFriendly working atmosphere and an open corporate culture which help you always feel comfortable If you are eager to apply for this opportunity, please note that we're looking for: Students in their 3rd to 5th years and recent IT graduatesWith basic knowledge in C++, algorithms, and object-oriented programmingAnd with a Pre-Intermediate English level To enroll in courses, all you need to do is: wait for a vacancy for admission to coursessend us your resume pass a technical test in C++ and OOP, including questions on knowledge of OOP and algorithmic problems. Also, past English test. If you'd like to start preparing for the entrance test now, apply for this announcement and we'll send you a list of useful books for it and notify you when the next course will start. Thank you for your interest in our courses and we hope to hear from you soon! Apriorit - A Priority Choice!", "ctc": null, "currency": null, "meta": {}}]