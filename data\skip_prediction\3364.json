[{"jd_link": "https://www.trantorinc.com/careers/data-engineer/", "company_id": 3364, "source": 3, "skills": "Job Category: data engineer Job Type: Full Time Job Location: Chandigarh/Gurgaon/Noida/Remote Shift Timing: From 9:06 PM Onwards", "title": "Data Engineer", "location": "Chandigarh/Gurgaon/Noida/Remote", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.trantorinc.com/careers/data-engineer/", "description": "About TrantorTrantor is a technology services company focused on outsourced product development and digital re-engineering. Leveraging our CaptiveCoE™ engagement model, we operate as a seamless extension of our clients’ teams to provide rapid scalability with predictable budgets. Founded in 2012, Trantor has worked with customers across Tech, FinTech, Media & Cyber Security industries. We have centers in the US, India, Canada, and Costa Rica. We are consistently rated as the #1 employer in the region with the ability to attract and retain technical talent. Our commitment to excellence and impactful results has translated to long-term relationships and value for our clients and solution partners Please visit us at: https://trantorinc. com In this critical role, you’ll design scalable and organized data architectures, integrate diverse systems, and establish clear governance frameworks to ensure the quality, reliability, and accessibility of our data. This role is ideal for a driven professional who thrives in solving challenges that arise from fragmented or inconsistent data and who is eager to implement systems that empower teams to focus on strategic objectives rather than firefighting data issues. If you’re passionate about shaping data strategy and governance while tackling real-world challenges with a hands-on approach, we’d love to hear from you! Role and ResponsibilitiesData Organization and Governance: Define and maintain governance standards that span multiple systems (AWS, Fivetran, Snowflake, PostgreSQL, Salesforce/nCino, Looker), ensuring that data remains accurate, accessible, and organized across the organization. Solve Data Problems Proactively: Address recurring data issues that sidetrack operational and strategic initiatives by implementing processes and tools to anticipate, identify, and resolve root causes effectively. System Integration: Lead the integration of diverse systems into a cohesive data environment, optimizing workflows and minimizing manual intervention. Hands-On Problem Solving: Take a hands-on approach to resolving reporting issues and troubleshooting data challenges when necessary, ensuring minimal disruption to business operations. Collaboration Across Teams: Work closely with business and technical stakeholders to understand and solve our biggest challengesMentorship and Leadership: Guide and mentor team members, fostering a culture of accountability and excellence in data management practices. Strategic Data Support: Ensure that marketing, analytics, and other strategic initiatives are not derailed by data integrity issues, enabling the organization to focus on growth and innovation Required SkillsProficiency with AWS AppFlow, S3, Lambda and Fivetran for data processing and integration across platforms. Expertise in PostgreSQL and Snowflake, including SnowSQL, Snowpipe, and advanced data warehousing concepts. Strong experience with Salesforce (including nCino), including API integration and data modeling. Strong experience with Looker’s LookML language and best practices in developmentUnderstanding of data modeling techniques and experience designing modern, scalable data architectureData GovernanceFamiliarity with Secoda or similar tools for managing data governance, metadata, and documentation. Experience in designing and enforcing data governance frameworks, including establishing data dictionaries, standards, and policies to ensure consistency and quality. Proven ability to work with product development and business teams to integrate governance processes into system designs and workflows, ensuring alignment with organizational objectives. Expertise in managing large datasets with numerous fields, addressing organizational challenges by creating clear documentation and streamlined processes for field usage and ownership. Nice-to-Have Skills:Familiarity with RESTful APIs and Salesforce APIs. Knowledge of financial services, particularly Loan Origination Systems (LOS), credit risk, and predictive analytics. FacebookMastodonEmailShare", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.trantorinc.com/careers/dot-net-developer-lead/", "company_id": 3364, "source": 3, "skills": "Job Category: .Net Developer Job Type: Full Time Job Location: Remote/Chandigarh/Noida/Bangalore Shift Timing: General Shift", "title": "Dot Net Developer/Lead", "location": "Remote/Chandigarh/Noida/Bangalore", "location_type": null, "job_type": null, "min_experience": 6, "max_experience": null, "apply_link": "https://www.trantorinc.com/careers/dot-net-developer-lead/", "description": "About TrantorTrantor is a technology services company focused on outsourced product development and digital re-engineering. Leveraging our CaptiveCoE™ engagement model, we operate as a seamless extension of our clients’ teams to provide rapid scalability with predictable budgets. Founded in 2012, Trantor has worked with customers across Tech, FinTech, Media & Cyber Security industries. We have centers in the US, India, Canada, and Costa Rica. We are consistently rated as the #1 employer in the region with the ability to attract and retain technical talent. Our commitment to excellence and impactful results has translated to long-term relationships and value for our clients and solution partners Please visit us at: https://trantorinc. comJob DescriptionWe are seeking a talented and motivated . NET Developer to join our dynamic team. As a . NET Developer, you will be responsible for designing, developing, testing, and maintaining applications built on the Microsoft . NET framework, while leveraging various technologies such as Redis, Elasticache, SQL Server, and Git. You will also have the opportunity to work with AWS EC2 instances and utilize . NET Core and . NET Standard to build cutting-edge solutions. Role & Responsibilities:Full-Stack Development: Collaborate with cross-functional teams to understand project requirements and translate them into technical specifications. Develop end-to-end applications using the Microsoft . NET framework, . NET Core, and . NET Standard, integrating front-end technologies like HTML, CSS, and JavaScript. Database Integration: Design, develop, and maintain SQL Server databases, ensuring efficient data storage, retrieval, and manipulation. Implement data caching solutions using Redis and Elastic ache to enhance application performance. API Development: Build robust and scalable APIs (REST and/or SOAP) to facilitate seamless communication between various components of the application and external systems. Version Control & Collaboration: Utilize Git for version control, branching, and code merging, ensuring smooth collaboration within the development team and adherence to Agile development methodologies. Cloud Deployment: Deploy applications on AWS EC2 instances, leveraging cloud technologies to optimize performance, scalability, and availability. Test-Driven Development: Implement unit tests and participate in automated testing processes to ensure the reliability and quality of developed code. Perform thorough testing and validation of software modules before deployment. Performance Optimization: Identify and address performance bottlenecks in applications, databases, and caching layers to ensure optimal system performance. Research & Innovation: Stay updated with the latest . NET technologies, AWS services, and best practices. Proactively suggest innovative solutions and improvements to enhance the overall development process. Documentation: Create and maintain technical documentation, including application architecture, code comments, and user guides, to facilitate seamless collaboration and knowledge transfer. Required SkillsBachelor’s degree in Computer Science, Software Engineering, or a related field. 6+ years of Proven experience in . NET development with strong proficiency in C#, ASP. NET, . NET Core, and . NET Standard. Solid understanding of object-oriented programming (OOP) principles and design patterns. Experience with SQL Server or other relational databases for data modeling and integration. Familiarity with Redis and Elasticache for caching and data storage optimization. Knowledge of web services and API development (REST, SOAP) is desirable. Familiarity with AWS services, particularly EC2, is a plus. Proficient with Git for version control and collaborative development. Ability to work both independently and collaboratively in a fast-paced team environment. Strong problem-solving skills and a detail-oriented approach to development. Excellent communication skills, both written and verbal, to effectively convey technical concepts to non-technical stakeholders. FacebookMastodonEmailShare", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.trantorinc.com/careers/account-manager/", "company_id": 3364, "source": 3, "skills": "Job Category: Account Manager Job Type: Full Time Job Location: Noida/ Chandigarh Shift Timing: General/PST", "title": "Account Manager", "location": "Noida/ Chandigarh", "location_type": null, "job_type": null, "min_experience": 8, "max_experience": null, "apply_link": "https://www.trantorinc.com/careers/account-manager/", "description": "About TrantorTrantor is a technology services company focused on outsourced product development and digital re-engineering. Leveraging our CaptiveCoE™ engagement model, we operate as a seamless extension of our client’s teams to provide rapid scalability with predictable budgets. Founded in 2012, Trantor has worked with customers across Tech, FinTech, Media & Cybersecurity industries. We have centers in the US, India, Canada, and Costa Rica. We are consistently rated as the #1 employer in the region with the ability to attract and retain technical talent. Our commitment to excellence and impactful results has translated to long-term relationships and value for our clients and solution partners. Role Overview We are seeking a Strategic Account Manager who has proven experience managing and growing business units independently. The ideal candidate will have extensive expertise in full P&L management, driving revenue growth, profitability, and long-term strategic account expansion. This role goes beyond traditional project management—it requires strategic business leadership, financial acumen, and entrepreneurial thinking. Role and ResponsibilitiesOwn full Profit & Loss responsibility for assigned strategic accounts. Lead strategic account planning and execution to achieve sustained revenue growth and profitability. Act as the primary relationship owner, strategically engaging with key client stakeholders, including senior executives and decision-makers. Develop and execute business strategies to maximize client profitability and growth. Negotiate and structure profitable contracts and agreements. Analyze financial metrics, forecast account performance, and report regularly to executive leadership. Identify strategic growth opportunities within existing accounts and develop actionable roadmaps to capture additional business. Manage client expectations and ensure high levels of customer satisfaction through strategic account engagement. Proactively identify risks, opportunities, and market shifts, implementing effective mitigation and growth strategies. Collaborate closely with internal delivery and operational teams to align service execution with strategic account objectives. Required Skills:Minimum 8+ years of experience in strategic account management, business unit leadership, or similar roles with direct P&L accountability. Proven track record of successfully growing accounts in a services or technology-driven environment. Extensive experience managing revenue targets, profitability, and financial forecasting. Strong business acumen with demonstrated ability to strategically expand and nurture client relationships. Skilled negotiator with experience structuring and closing high-value agreements. Exceptional leadership, strategic thinking, and entrepreneurial mindset. Excellent communication and influencing skills, capable of engaging senior executives and key stakeholders. Ability to thrive under pressure, manage multiple priorities, and deliver impactful business results. Solid analytical capabilities, adept at identifying business opportunities, trends, and risks from financial and market data. FacebookMastodonEmailShare", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.trantorinc.com/careers/java-full-stack-developer/", "company_id": 3364, "source": 3, "skills": "Job Category: Java Full Stack Job Type: Full Time Job Location: Bangalore Shift Timing: General Shift", "title": "Java Full Stack Developer", "location": "Bangalore", "location_type": "hybrid", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://www.trantorinc.com/careers/java-full-stack-developer/", "description": "About TrantorTrantor is a technology services company focused on outsourced product development and digital re-engineering. Leveraging our CaptiveCoE™ engagement model, we operate as a seamless extension of our client’s teams to provide rapid scalability with predictable budgets. Founded in 2012, Trantor has worked with customers across Tech, FinTech, Media & Cybersecurity industries. We have centers in the US, India, Canada, and Costa Rica. We are consistently rated as the #1 employer in the region with the ability to attract and retain technical talent. Our commitment to excellence and impactful results has translated to long-term relationships and value for our clients and solution partners. Job Description:Develop and maintain key features of our GRC platform across the full stack. Collaborate with product managers, designers, and other engineers to define and implement new functionalities. Optimize application performance and scalability to handle large volumes of data. Implement robust security measures to protect sensitive client information. Participate in code reviews and contribute to improving our development processes. Troubleshoot and resolve complex technical issues. Required Skills:Challenge status quo. Bachelor’s degree in computer science, Software Engineering, or a related field (or equivalent experience). 3+ years of experience in a combination of backend and frontend development. Proficiency in front-end technologies such as JavaScript, React, and CSSStrong knowledge of Access Management (i. e. , Role/Groups/Record Permissions), Forms/sub forms/questionnaires, Data Driven Events, Advanced Workflow, Data Feeds, APIs, Notifications, Dashboards, Reports, etc. Experience with SQL, PL/SQL, data modeling and Relational databases. Strong back-end skills with languages like Python, Java, or Node. jsEnterprise Application working experienceFamiliarity with cloud platforms like AWS and containerization technologies. Understanding of RESTful APIs and microservices architectureKnowledge of version control systems. Excellent problem-solving and communication skills. Bonus: Experience in the GRC domain. Work from Bangalore Location , Hybrid Model FacebookMastodonEmailShare", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.trantorinc.com/careers/react-js-developer/", "company_id": 3364, "source": 3, "skills": "Job Type: Full Time Job Location: Bangalore Shift Timing: General Shift", "title": "React.JS Developer", "location": "Bangalore", "location_type": "hybrid", "job_type": null, "min_experience": 4, "max_experience": 7, "apply_link": "https://www.trantorinc.com/careers/react-js-developer/", "description": "About TrantorTrantor is a technology services company focused on outsourced product development and digital re-engineering. Leveraging our CaptiveCoE™ engagement model, we operate as a seamless extension of our client’s teams to provide rapid scalability with predictable budgets. Founded in 2012, Trantor has worked with customers across Tech, FinTech, Media & Cybersecurity industries. We have centers in the US, India, Canada, and Costa Rica. We are consistently rated as the #1 employer in the region with the ability to attract and retain technical talent. Our commitment to excellence and impactful results has translated to long-term relationships and value for our clients and solution partners. Key Responsibilities:Experience Range 4 Years to 7 YearsExtensive programming experience in UI technologies, responsive web and mobile applicationsSound in Designing and Implementation of Scalable features, Knowledge of Patterns and Industry best practicesHands-on with UI frameworks like React, Angular, Vue. js, Backbone. js, Bootstrap, jQuery, Marionette, etc. Expertise in OO JavaScript, HTML, CSS, Typescript / Flow, ECMA-Script6. Understanding of OOP patterns and methodologies and experience with OO languages. Expert knowledge of CSS, CSS Framework, various CSS pre-processors like LESS / SAAS. Knowledge on bundlers like Webpack, Roll-up, etc. Proficiency with web-related protocols and architectures like MVC, MVVM, REST APIs, JSON, HTTP. Experience with Accessibility WCAG standards, cross-browser compatibility, Strong debugging and troubleshooting skillsRequired Skills:Strong UI Development skillsFramework – ReactExpertise in OO JavaScriptStrong software engineering skills – OOPs concepts, Reusable code components etc. Strong experience on CSS pre-processors i. e. LESS / SAAS – any one of themEnterprise Application working experienceREST API integration, JSON, MVC and MVVMStrong debugging and troubleshooting skillsWork from Bangalore Location , Hybrid Model FacebookMastodonEmailShare", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.trantorinc.com/careers/prisma-access/", "company_id": 3364, "source": 3, "skills": "Job Category: Prisma Access Job Type: Full Time Job Location: Noida/Remote Shift Timing: EST/APAC/EMEA", "title": "Prisma Access", "location": "Noida/Remote", "location_type": "remote", "job_type": null, "min_experience": 8, "max_experience": 8, "apply_link": "https://www.trantorinc.com/careers/prisma-access/", "description": "About TrantorTrantor is a technology services company focused on outsourced product development and digital re-engineering. Leveraging our CaptiveCoE™ engagement model, we operate as a seamless extension of our clients’ teams to provide rapid scalability with predictable budgets. Founded in 2012, Trantor has worked with customers across Tech, FinTech, Media & Cyber Security industries. We have centers in the US, India, Canada, and Costa Rica. We are consistently rated as the #1 employer in the region with the ability to attract and retain technical talent. Our commitment to excellence and impactful results has translated to long-term relationships and value for our clients and solution partners Please visit us at: https://trantorinc. com You will work firsthand with our valued customers to address their complex post-sales concerns where analysis of situations or data requires an in-depth evaluation of many factors. You will regularly participate in technical discussions with multi-functional teams, creating an environment of transparency that ultimately leads to better products, better working environments, and better cybersecurity. You will need to provide technical assistance as needed (often, in high-pressure situations). Role and ResponsibilitiesProvide Technical Support to customers and partnersProvide configurations, troubleshooting, and best practices to customersManage support cases to ensure issues are recorded, tracked, resolved, and follow-ups are completed in a timely mannerProvide fault isolation and root cause analysis for technical issuesPublish Technical Support Bulletins and other technical documentation in the Knowledge BaseReview of technical documentation for training materials, technical marketing collateral, manuals, troubleshooting guides, etc. Qualification:6 – 8 years prior experience in a Technical Support environment is requiredExpertise with Remote Access VPN solutions, IPSEC, PKI & SSL, TCP/IP, Authentication Protocols (LDAP, RADIUS, etc. )In-depth understanding of Networking and Network Security concepts and experience with multi-vendor networking devices such as routers, switches, firewalls, traffic generators, etc. Experience with Palo Alto, Cisco, Checkpoint, Juniper (Netscreen), and Fortinet products a plusExperience working with Firewall Central Management SystemsExperience working with a multi-factor authentication security system (tokens, certificates, CAC cards, and similar)Working knowledge of Security services (IDS/IPS, Firewalls, etc. )Strong ability to independently debug broad, complex, and unique networks with mixed media and protocols requiredVirtualization experience will be a plus. (AWS, Azure, VMWare, OpenStack)Experience with Windows and MAC OS is a plus (Debugging, Editing Registries, Plist, etc. )Knowledge of LinuxExcellent knowledge of Python and BASH ScriptingExcellent written and verbal communication skillsBE/B. Tech in Computer Engineering / Electronics & Communications Engineering or their equivalent. FacebookMastodonEmailShare", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.trantorinc.com/careers/salesforce-architect/", "company_id": 3364, "source": 3, "skills": "Job Category: Salesforce Developer Salesforce Lead Job Type: Full Time Job Location: Chandigarh/Gurgaon/Noida/Remote Shift Timing: 2 PM to 10 PM IST", "title": "Salesforce Developer / Lead", "location": "Chandigarh/Gurgaon/Noida/Remote", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://www.trantorinc.com/careers/salesforce-architect/", "description": "About TrantorTrantor is a technology services company focused on outsourced product development and digital re-engineering. Leveraging our CaptiveCoE™ engagement model, we operate as a seamless extension of our clients’ teams to provide rapid scalability with predictable budgets. Founded in 2012, Trantor has worked with customers across Tech, FinTech, Media & Cyber Security industries. We have centers in the US, India, Canada, and Costa Rica. We are consistently rated as the #1 employer in the region with the ability to attract and retain technical talent. Our commitment to excellence and impactful results has translated to long-term relationships and value for our clients and solution partners Please visit us at: https://trantorinc. com Role and ResponsibilitiesDesign and deliver solutions for enterprise-grade customers using Salesforce products. Primary responsibility is to recommend the best solution for a given set of requirements and articulate the risks and trade-offs involved in choosing one solution over another. Provide technical leadership for IT solutions to which you have ownershipArchitect Solutions that are scalable, secure & sustainableProvide scalable, maintainable and robust designs/solutions, with the code reviews, suggesting industry and Salesforce best coding practices. Conduct research into clients use of Salesforce, producing a report of findings with recommended steps and a roadmap for Salesforce solutions. Groom and refine the Salesforce design backlog, in liaison with stakeholders on the project,Approving configuration and coded items to be deployed between environments. Where appropriate, this could include deploying changes. Describe and illustrate designs with appropriate justifications. Supervise the configuration, quality assurance, testing, and deployment of Salesforce Required Skills:5+ years’ experience Hands-on experience on writing Apex, LW components. Experience in building Salesforce DevOps, CI/CD pipelines is preferable. Experience with HTML, CSS and JavaScript is preferable. Experience in designing enterprise grade Salesforce cloud solutionsA pro-active personality, with a can-do attitude. Strong analytical skills, with the ability to observe trends. Able to actively make suggestions based on the results. Ability to tailor communication to multiple audiences at all levels. A self-starter, experienced in leading projects or delivery work streams. Ability to demonstrate concepts visually and in writing in a way that enables stakeholders to decide with confidence. A strategic approach to solution design, with proven ability to develop and execute tactical plans to support the strategy. FacebookMastodonEmailShare", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.trantorinc.com/careers/salesforce-business-analyst/", "company_id": 3364, "source": 3, "skills": "Job Category: Business Analyst Job Type: Full Time Job Location: Chandigarh/Gurgaon/Noida/Remote Shift Timing: 1.00 pm - 10.00 pm", "title": "Salesforce Business Analyst", "location": "Chandigarh/Gurgaon/Noida/Remote", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.trantorinc.com/careers/salesforce-business-analyst/", "description": "About TrantorTrantor is a technology services company focused on outsourced product development and digital re-engineering. Leveraging our CaptiveCoE™ engagement model, we operate as a seamless extension of our client’s teams to provide rapid scalability with predictable budgets. Founded in 2012, Trantor has worked with customers across Tech, FinTech, Media & Cybersecurity industries. We have centers in the US, India, Canada, and Costa Rica. We are consistently rated as the #1 employer in the region with the ability to attract and retain technical talent. Our commitment to excellence and impactful results has translated to long-term relationships and value for our clients and solution partners. Job DescriptionWe are seeking an experienced Business Analyst to play a pivotal role in a Salesforce Org Consolidation and Optimization Project. The Business Analyst will work closely with stakeholders, Salesforce Architects, and project teams to gather, analyze, and document business requirements. This role will bridge the gap between business needs and technical implementation, ensuring that the consolidation and optimization initiatives align with organizational goals and deliver measurable value. Key Responsibilities:1. Requirement Gathering and Analysis:○ Collaborate with business stakeholders to understand and document current Salesforce processes, pain points, and improvement opportunities. ○ Conduct detailed gap analysis between existing Salesforce orgs and future-state requirements. ○ Define clear, actionable business requirements and user stories that align with project objectives. 2. Process Documentation and Optimization:○ Map existing workflows, processes, and data flows across multiple Salesforce orgs. ○ Identify redundancies, inefficiencies, and inconsistencies in current processes. ○ Propose optimized workflows and process enhancements to streamline operations post-consolidation. 3. Collaboration with Technical Teams:○ Partner with Salesforce Architects, developers, and administrators to translate business requirements into technical solutions. ○ Provide input on solution design, ensuring alignment with business needs and priorities. ○ Participate in design reviews and provide feedback on usability, functionality, and performance. 4. Data Analysis and Validation:○ Work with stakeholders to define data migration and cleansing requirements. ○ Analyze data quality across orgs and develop recommendations for data standardization. ○ Collaborate on data mapping and validation efforts during the migration process. 5. Stakeholder Management and Communication:○ Act as the primary liaison between business units and the technical team. ○ Communicate project progress, risks, and issues to stakeholders in a clear and timely manner. ○ Facilitate workshops, meetings, and training sessions to engage users and gather feedback. 6. Testing and User Acceptance:○ Develop test plans, scenarios, and cases to validate the functionality of the consolidated Salesforce org. ○ Coordinate and support user acceptance testing (UAT), ensuring alignment with business expectations. ○ Document and track issues identified during testing and work with the technical team for resolution. Required Skills:1. Technical and Functional Expertise:○ Proven experience as a Business Analyst in Salesforce projects, with a focus on org consolidation, optimization, or large-scale transformations. ○ Strong knowledge of Salesforce features, capabilities, and limitations, including Sales Cloud, Service Cloud, and platform configurations. ○ Familiarity with Salesforce data model, reporting, and dashboard capabilities. 2. Skills and Tools:○ Proficiency in business process modeling and documentation tools (e. g. , Lucidchart, Visio). ○ Experience with Agile/Scrum methodologies and tools like Jira. ○ Basic understanding of data migration and integration processes3. Soft Skills:○ Exceptional analytical and problem-solving skills. ○ Strong verbal and written communication skills, with the ability to convey complex concepts to both technical and non-technical audiences. ○ Excellent organizational skills, with the ability to manage multiple tasks and priorities. FacebookMastodonEmailShare", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.trantorinc.com/careers/senior-analyst/", "company_id": 3364, "source": 3, "skills": "Job Category: Google Analytics Job Type: Full Time Job Location: Chandigarh/Gurgaon/Noida/Remote Shift Timing: 2:00 PM Onwards", "title": "Senior Analyst", "location": "Chandigarh/Gurgaon/Noida/Remote", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.trantorinc.com/careers/senior-analyst/", "description": "About TrantorTrantor is a technology services company focused on outsourced product development and digital re-engineering. Leveraging our CaptiveCoE™ engagement model, we operate as a seamless extension of our clients’ teams to provide rapid scalability with predictable budgets. Founded in 2012, Trantor has worked with customers across Tech, FinTech, Media & Cyber Security industries. We have centers in the US, India, Canada, and Costa Rica. We are consistently rated as the #1 employer in the region with the ability to attract and retain technical talent. Our commitment to excellence and impactful results has translated to long-term relationships and value for our clients and solution partners Please visit us at: https://trantorinc. com Job DescriptionLeverage multiple data platforms (GA4, ContentSquare, survey tools, etc. ) to analyze userbehavior, identify performance gaps, and recommend actionable strategies that enhance product experiences and marketing effectiveness. Role and ResponsibilitiesWeb Analytics ExpertiseUse GA4 (or similar tools) to monitor user paths, funnels, and engagement metrics. Combine data from ContentSquare, surveys, or other sources for comprehensive insights. Critical Thinking & Problem-SolvingPerform gap analyses to uncover deficiencies or missed opportunities. Generate hypotheses for optimization and design tests to validate them. Product & Marketing AcumenAlign analytics findings with broader business goals, optimizing both product and marketingfunnels. Recommend data-driven changes that enhance user experience and drive KPIs. Reporting & StorytellingCreate clear dashboards/reports to highlight key metrics and trends. Present actionable recommendations to stakeholders using outcome-focused narratives. Required Skills:Analytics Tools: Proficiency in GA4 or equivalents (Content Square, Adobe Analytics), plusfamiliarity with survey data. Gap Analysis: Proven ability to spot and articulate performance gaps, offering pragmaticsolutions. Communication Skills: Skilled at translating data insights into concise, actionable briefs forvaried audiences. Strategic Mindset: Knowledge of product lifecycles, marketing funnels, and KPI frameworks toguide decision-making. FacebookMastodonEmailShare", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.trantorinc.com/careers/senior-performance-tester-lead/", "company_id": 3364, "source": 3, "skills": "Job Category: Performance Testing Job Type: Full Time Job Location: Remote/Chandigarh/Noida/Bangalore Shift Timing: General Shift", "title": "Senior Performance Tester/ Lead", "location": "Remote/Chandigarh/Noida/Bangalore", "location_type": null, "job_type": null, "min_experience": 6, "max_experience": 6, "apply_link": "https://www.trantorinc.com/careers/senior-performance-tester-lead/", "description": "About TrantorTrantor is a technology services company focused on outsourced product development and digital re-engineering. Leveraging our CaptiveCoE™ engagement model, we operate as a seamless extension of our clients’ teams to provide rapid scalability with predictable budgets. Founded in 2012, Trantor has worked with customers across Tech, FinTech, Media & Cyber Security industries. We have centers in the US, India, Canada, and Costa Rica. We are consistently rated as the #1 employer in the region with the ability to attract and retain technical talent. Our commitment to excellence and impactful results has translated to long-term relationships and value for our clients and solution partners Please visit us at: https://trantorinc. comJob Description6 years of experience in Performance TestingPerformance Test Planning: Develop comprehensive performance test plans based on system requirements, ensuring alignment with project timelines and goals. Tool Proficiency: Possess advanced proficiency in using performance testing tools such as LoadRunner, JMeter, Gatling, or similar tools to simulate real-world scenarios and identify performance bottlenecks. Proficiency in scripting and programming languages (e. g. , Java, Python, or similar). Performance Analysis: Analyze test results and collaborate with development and infrastructure teams to identify, troubleshoot, and resolve performance issues. Scalability Assessment: Assess system scalability, providing recommendations and implementing enhancements to optimize application performance under various load conditions. Reporting and Documentation: Generate detailed performance test reports, documenting findings, recommendations, and performance metrics for stakeholders. Continuous Improvement: Stay updated with industry trends, best practices, and emerging technologies related to performance testing to enhance the efficiency of testing processes. Extensive experience in performance testing methodologies, tools, and techniques. Strong analytical and problem-solving skills with the ability to analyze complex systems. Excellent communication and collaboration skills to effectively work in a team environment and interact with cross-functional teams. Proven track record in leading performance testing efforts for large-scale projects. Relevant certifications (e. g. , Certified Software Test Professional, Certified Performance Testing Professional) are a plus. Experience with cloud-based performance testing and monitoring tools (AWS, Azure, etc. ) is highly desirable, and understanding complex systems w. r. t architecture. Knows The Importance of Load Balancers I. e. a performance engineer should understand the concept of available load balancing algorithms when you conduct load testing in load balancing environments. Good experience with DataDog monitoring tool. Performance Testing Leadership: Lead and oversee performance testing initiatives across multiple projects, guiding the testing team in implementing effective strategies and methodologies. FacebookMastodonEmailShare", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.trantorinc.com/careers/software-developer-product-support-maintenance-vb-net/", "company_id": 3364, "source": 3, "skills": "Job Category: VB.NET Job Type: Full Time Job Location: Chandigarh/Gurgaon/Noida/Remote Shift Timing: 3pm - 12.00am", "title": "Software Developer – Product Support & Maintenance (VB.NET)", "location": "Chandigarh/Gurgaon/Noida/Remote", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://www.trantorinc.com/careers/software-developer-product-support-maintenance-vb-net/", "description": "About TrantorTrantor is a technology services company focused on outsourced product development and digital re-engineering. Leveraging our CaptiveCoE™ engagement model, we operate as a seamless extension of our client’s teams to provide rapid scalability with predictable budgets. Founded in 2012, Trantor has worked with customers across Tech, FinTech, Media & Cybersecurity industries. We have centers in the US, India, Canada, and Costa Rica. We are consistently rated as the #1 employer in the region with the ability to attract and retain technical talent. Our commitment to excellence and impactful results has translated to long-term relationships and value for our clients and solution partners. Job Summary:We are looking for a skilled Software Developer to support and maintain Esuite, an Environmental Data Management Solution. The role involves working on a legacy VB. NET (http://vb. net/) application with a Microsoft SQL Server backend, ensuring smooth system operation, implementing bug fixes, and optimizing the business logic within SQL stored procedures. The ideal candidate should have strong experience in VB. NET (http://vb. net/), SQL stored procedures, debugging legacy applications, and system maintenance. Role & Responsibilities:Maintain & Support Esuite Application – Troubleshoot issues, optimize performance, and ensure system stability. Work with SQL Stored Procedures – Modify, optimize, and debug SQL-based business logic. Collaborate with QA & Development Teams – Participate in Agile workflows, handle tickets, and fix bugs. Understand Legacy Codebase – Analyze and work with existing VB. NET (http://vb. net/) code. Ensure Data Integrity & Synchronization – Work with the team to maintain accurate Air, Waste, Water, and Energy data synchronization processes. Work within Agile/Kanban Framework – Manage tasks via JIRA and attend daily stand-ups. Required Skills :5+ years of experience in software development & maintenanceStrong knowledge of VB. NET (http://vb. net/) applicationsExpertise in Microsoft SQL Server & stored proceduresHands-on experience wit GitHub (code repositories & version control)Familiarity with Agile/Kanban methodologiesAbility to debug, optimize, and maintain legacy applicationsStrong problem-solving skills and ability to work independentlyNice-to-Have Skills:Experience with environmental data management solutionsKnowledge of data synchronization & automationUnderstanding of cloud-based deployment & infrastructureExperience working in cross-functional global teams FacebookMastodonEmailShare", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.trantorinc.com/careers/software-developer-product-support-maintenance/", "company_id": 3364, "source": 3, "skills": "Job Type: Full Time Job Location: Chandigarh/Gurgaon/Noida/Remote", "title": "Software Developer – Product Support & Maintenance", "location": "Chandigarh/Gurgaon/Noida/Remote", "location_type": null, "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://www.trantorinc.com/careers/software-developer-product-support-maintenance/", "description": "About Trantor:Trantor is a technology services company focused on outsourced product development and digital re-engineering. Leveraging our CaptiveCoE™ engagement model, we operate as a seamless extension of our client’s teams to provide rapid scalability with predictable budgets. Founded in 2012, Trantor has worked with customers across Tech, FinTech, Media & Cybersecurity industries. We have centers in the US, India, Canada, and Costa Rica. We are consistently rated as the #1 employer in the region with the ability to attract and retain technical talent. Our commitment to excellence and impactful results has translated to long-term relationships and value for our clients and solution partners. We are looking for a skilled Software Developer to support and maintain Esuite, an Environmental Data Management Solution. The role involves working on a legacy VB. NET application with a Microsoft SQL Server backend, ensuring smooth system operation, implementing bug fixes, and optimizing the business logic within SQL stored procedures. The ideal candidate should have strong experience in VB. NET, SQL stored procedures, debugging legacy applications, and system maintenance. Role & Responsibilities:Maintain & Support Esuite Application – Troubleshoot issues, optimize performance, and ensure system stability. Work with SQL Stored Procedures – Modify, optimize, and debug SQL-based business logic. Collaborate with QA & Development Teams – Participate in Agile workflows, handle tickets, and fix bugs. Understand Legacy Codebase – Analyze and work with existing VB. NET code. Ensure Data Integrity & Synchronization – Work with the team to maintain accurate Air, Waste, Water, and Energy data synchronization processes. Work within Agile/Kanban Framework – Manage tasks via JIRA and attend daily stand-ups. Required Skills:5+ years of experience in software development & maintenanceStrong knowledge of VB. NET applications Expertise in Microsoft SQL Server & stored proceduresHands-on experience with GitHub (code repositories & version control)Familiarity with Agile/Kanban methodologies Ability to debug, optimize, and maintain legacy applicationsStrong problem-solving skills and ability to work independently Nice-to-Have Skills:Experience with environmental data management solutionsKnowledge of data synchronization & automationUnderstanding of cloud-based deployment & infrastructureExperience working in cross-functional global teams FacebookMastodonEmailShare", "ctc": null, "currency": null, "meta": {}}]