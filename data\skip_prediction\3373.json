[{"jd_link": "http://www.ikayzo.com/jobs/senior-ruby-on-rails-software-engineer", "company_id": 3373, "source": 3, "skills": "About\n      \n        \n        \tHome IconIkayzo, Inc.\n        \n    \t\t\n        \tTeam IconTeam\n        \n    \t\t\n        \tNews IconNews\n        \n        \n          News IconIkayzo Group\n        \n      \n    \n    Services\n      \n        \n        \tDesign IconDesign\n        \n        \n        \tBuild IconBuild\n        \n        \n        \tLocalize IconLocalize\n        \n        \n        \tAudit IconAudit\n        \n      \n    \n    Clients\n      \n          \n          \tCustomer IconOur Customers\n          \n      \n    \n    Portfolio\n      \n        \n        \tProject IconWeb & Mobile\n        \n      \t\n        \tBranding IconBranding\n        \n      \t\n        \tIllustration IconIllustration\n        \n      \t\n        \tLab IconLab\n        \n      \n    \n    Industries\n      \n      \t\n        \tFinance IconFinance\n        \n    \t\n        \tGovernment IconGovernment\n        \n   \t\t\n        \tEntertainment IconEntertainment\n        \n        \n            Travel IconTourism\n        \n      \n    \n    Contact\n    \t\n      \t\n        \tOffice IconOffice Locations\n        \n      \n    \n    Languages\n    \t\n      \t日本語, Home IconIkayzo, Inc.\n        \n    \t\t\n        \tTeam IconTeam\n        \n    \t\t\n        \tNews IconNews\n        \n        \n          News IconIkayzo Group, Design IconDesign\n        \n        \n        \tBuild IconBuild\n        \n        \n        \tLocalize IconLocalize\n        \n        \n        \tAudit IconAudit, Customer IconOur Customers, Project IconWeb & Mobile\n        \n      \t\n        \tBranding IconBranding\n        \n      \t\n        \tIllustration IconIllustration\n        \n      \t\n        \tLab IconLab, Finance IconFinance\n        \n    \t\n        \tGovernment IconGovernment\n        \n   \t\t\n        \tEntertainment IconEntertainment\n        \n        \n            Travel IconTourism, Office IconOffice Locations, 日本語, • A solid grounding in OO software architecture / design patterns\n\n• Experience designing and implementing successful non-trivial enterprise Ruby on Rails projects\n\n• Experience with agile development methodologies\n\n• Deep knowledge of the Rails tech stack\n\n• Firm grasp of data modeling\n\n• A passion for software development, • PostgreSQL and MongoDB experience\n• Familiarity with popular Javascript frameworks for full stack development\n• Experience with Node.js\n• Domain experience in fintech, adtech, renewable energy or life sciences\n• Open source contributions, AboutServicesClientsPortfolioIndustriesContactJobs", "title": "Full Stack Software Engineer, Ruby on Rails", "location": "Honolulu, Hawaii (relocation available)", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "http://www.ikayzo.com/jobs/senior-ruby-on-rails-software-engineer", "description": "Full Stack Software Engineer, Ruby on Rails Honolulu, Hawaii (relocation available) Work with an award-winning software team in Honolulu leading projects in fintech, adtech, sustainability and education from New York to Tokyo. Ikayzo, a creative agency and software development firm, is looking for Senior Full Stack Ruby on Rails Developers capable of taking complex projects from ideation to release. In this role he/she will be responsible for building high quality, secure and scalable web applications across diverse industries. Ikayzo offers a competitive compensation package including medical coverage and 24 days of paid time off per annum. This is a local position in Honolulu. We provide relocation assistance for candidates who pass our interview process. We are not currently sponsoring visas or accepting candidates from recruiters for this position. Job Description The position of Senior Rails Developer entails software architecture, implementation, development of best operational practices, testing and documentation of non-trivial systems with massive scalability requirements. Fullstack experience, including building dynamic web UIs with Angular. js and/or React. js is preferred. As a boutique consultancy we are looking for motivated individuals who do not require step-by-step directions and can work cohesively with our team. Successful applicants will have a proven track record with non-trivial Ruby on Rails projects, be comfortable with agile development methodologies, have a solid grounding in OO software architecture / design patterns and be comfortable with the Rails technology stack from front to back. Most importantly, candidates must have a passion for programming. Our interviews are rigorous, so only serious developers with the requisite skills need apply. Send your resume and a link to your GitHub profile to career@ikayzo. com. Requirements • A solid grounding in OO software architecture / design patterns • Experience designing and implementing successful non-trivial enterprise Ruby on Rails projects • Experience with agile development methodologies • Deep knowledge of the Rails tech stack • Firm grasp of data modeling • A passion for software development Preferred • PostgreSQL and MongoDB experience • Familiarity with popular Javascript frameworks for full stack development • Experience with Node. js • Domain experience in fintech, adtech, renewable energy or life sciences • Open source contributions About Ikayzo Ikayzo is a creative agency and software development firm. We are a community minded Hawaii company with global reach. By bringing together world class designers and published software engineers under one roof, we are able to build and deliver high-performing software to clients in the US and Japan across a range of industries, including finance, adtech, sustainability, and e-commerce. We are proud to have long-standing client relationships with money center banks, e-commerce leaders and renewable energy companies, as well as government agencies at the federal, state, and municipal levels. You will find Ikayzo's designs and software at work on trading floors, e-commerce sites, and social networks across desktops, smartphones, tablets, and web-enabled kiosks.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "http://www.ikayzo.com/jobs/senior-software-engineer-java", "company_id": 3373, "source": 3, "skills": "About\n      \n        \n        \tHome IconIkayzo, Inc.\n        \n    \t\t\n        \tTeam IconTeam\n        \n    \t\t\n        \tNews IconNews\n        \n        \n          News IconIkayzo Group\n        \n      \n    \n    Services\n      \n        \n        \tDesign IconDesign\n        \n        \n        \tBuild IconBuild\n        \n        \n        \tLocalize IconLocalize\n        \n        \n        \tAudit IconAudit\n        \n      \n    \n    Clients\n      \n          \n          \tCustomer IconOur Customers\n          \n      \n    \n    Portfolio\n      \n        \n        \tProject IconWeb & Mobile\n        \n      \t\n        \tBranding IconBranding\n        \n      \t\n        \tIllustration IconIllustration\n        \n      \t\n        \tLab IconLab\n        \n      \n    \n    Industries\n      \n      \t\n        \tFinance IconFinance\n        \n    \t\n        \tGovernment IconGovernment\n        \n   \t\t\n        \tEntertainment IconEntertainment\n        \n        \n            Travel IconTourism\n        \n      \n    \n    Contact\n    \t\n      \t\n        \tOffice IconOffice Locations\n        \n      \n    \n    Languages\n    \t\n      \t日本語, Home IconIkayzo, Inc.\n        \n    \t\t\n        \tTeam IconTeam\n        \n    \t\t\n        \tNews IconNews\n        \n        \n          News IconIkayzo Group, Design IconDesign\n        \n        \n        \tBuild IconBuild\n        \n        \n        \tLocalize IconLocalize\n        \n        \n        \tAudit IconAudit, Customer IconOur Customers, Project IconWeb & Mobile\n        \n      \t\n        \tBranding IconBranding\n        \n      \t\n        \tIllustration IconIllustration\n        \n      \t\n        \tLab IconLab, Finance IconFinance\n        \n    \t\n        \tGovernment IconGovernment\n        \n   \t\t\n        \tEntertainment IconEntertainment\n        \n        \n            Travel IconTourism, Office IconOffice Locations, 日本語, • A solid grounding in OO software architecture / design patterns\n\n• Experience designing and implementing successful non-trivial enterprise Java projects\n\n• Experience with agile development methodologies\n\n• Deep knowledge of the de facto Java enterprise stack including Spring and Hibernate\n\n• Firm grasp of data modeling\n\n• A passion for software development, • Familiarity with Angular.js or React.js for full stack development\n\n• Experience with Ruby on Rails\n\n• PostgreSQL and MongoDB expertise\n\n• Contributions to open source projects\n\n• Financial systems experience a plus, AboutServicesClientsPortfolioIndustriesContactJobs", "title": "Senior Software Engineer, Java", "location": "Locations: Honolulu, HI", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "http://www.ikayzo.com/jobs/senior-software-engineer-java", "description": "Senior Software Engineer, Java Locations: Honolulu, HI Work with an award-winning team designing and implementing systems for customers from New York to Tokyo. Ikayzo, a boutique software development and interactive design agency, is looking for a Senior Java Software Engineer capable of taking complex projects from ideation to release. In this role he/she will be responsible for designing and implementing applications for a wide range of customers ranging from web startups to energy companies and fintech startups to global banks. We promote from within, and this position has the potential to develop into a tech lead role. Ikayzo offers a competitive compensation package including medical coverage and 24 days of paid time off per annum. Job Description The position of Senior Java Software Engineer entails software architecture, implementation, development of best operational practices, testing and documentation of non-trivial systems with massive scalability requirements. Full stack experience with popular client frameworks such as Angular. js and React. js is preferred. As a boutique consultancy we are looking for motivated individuals who do not require step-by-step directions and can work cohesively with our team. Successful applicants will have a proven track record with non-trivial enterprise Java projects, be comfortable with agile development methodologies, have a solid grounding in OO software architecture / design patterns and be comfortable with the enterprise Java technology stack including frameworks such as Spring and Hibernate from front to back. Most importantly, candidates must have a passion for programming. Our interviews are rigorous, so only serious developers with the requisite skills need apply. Send your resume and a link to your GitHub profile to career@ikayzo. com. Requirements • A solid grounding in OO software architecture / design patterns • Experience designing and implementing successful non-trivial enterprise Java projects • Experience with agile development methodologies • Deep knowledge of the de facto Java enterprise stack including Spring and Hibernate • Firm grasp of data modeling • A passion for software development Preferred • Familiarity with Angular. js or React. js for full stack development • Experience with Ruby on Rails • PostgreSQL and MongoDB expertise • Contributions to open source projects • Financial systems experience a plus About Ikayzo Ikayzo is a boutique interactive design, software development and localization agency. We have a proven track record of designing and building systems ranging from Japanese social networking tools to enterprise financial systems. We develop desktop, web and mobile applications for multiple platforms including Ruby on Rails, Java, iPhone, Android, and . NET. Ikayzo has worked with Google, IBM, Intel, Sun, Adobe, Oracle, Apache, Red Hat and SAP on developing technology standards for everything from enterprise software to scientific applications. From New York to Tokyo, our customers range from database leader Oracle to trusted financial institutions like Bank of America. We work with customers of all sizes in a variety of industries including biotechnology, banking, renewable energy, social media / web 2. 0 and software.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.ikayzo.com/jobs/senior-software-engineer-ruby-on-rails", "company_id": 3373, "source": 3, "skills": "About\n      \n        \n        \tHome IconIkayzo, Inc.\n        \n    \t\t\n        \tTeam IconTeam\n        \n    \t\t\n        \tNews IconNews\n        \n        \n          News IconIkayzo Group\n        \n      \n    \n    Services\n      \n        \n        \tDesign IconDesign\n        \n        \n        \tBuild IconBuild\n        \n        \n        \tLocalize IconLocalize\n        \n        \n        \tAudit IconAudit\n        \n      \n    \n    Clients\n      \n          \n          \tCustomer IconOur Customers\n          \n      \n    \n    Portfolio\n      \n        \n        \tProject IconWeb & Mobile\n        \n      \t\n        \tBranding IconBranding\n        \n      \t\n        \tIllustration IconIllustration\n        \n      \t\n        \tLab IconLab\n        \n      \n    \n    Industries\n      \n      \t\n        \tFinance IconFinance\n        \n    \t\n        \tGovernment IconGovernment\n        \n   \t\t\n        \tEntertainment IconEntertainment\n        \n        \n            Travel IconTourism\n        \n      \n    \n    Contact\n    \t\n      \t\n        \tOffice IconOffice Locations\n        \n      \n    \n    Languages\n    \t\n      \t日本語, Home IconIkayzo, Inc.\n        \n    \t\t\n        \tTeam IconTeam\n        \n    \t\t\n        \tNews IconNews\n        \n        \n          News IconIkayzo Group, Design IconDesign\n        \n        \n        \tBuild IconBuild\n        \n        \n        \tLocalize IconLocalize\n        \n        \n        \tAudit IconAudit, Customer IconOur Customers, Project IconWeb & Mobile\n        \n      \t\n        \tBranding IconBranding\n        \n      \t\n        \tIllustration IconIllustration\n        \n      \t\n        \tLab IconLab, Finance IconFinance\n        \n    \t\n        \tGovernment IconGovernment\n        \n   \t\t\n        \tEntertainment IconEntertainment\n        \n        \n            Travel IconTourism, Office IconOffice Locations, 日本語, • A solid grounding in OO software architecture / design patterns\n\n• Experience designing and implementing successful non-trivial enterprise Ruby on Rails projects\n\n• Experience with agile development methodologies\n\n• Deep knowledge of the Rails tech stack\n\n• Firm grasp of data modeling\n\n• Proficiency in spoken and written English\n\n• A passion for software development, • PostgreSQL and MongoDB experience\n• Familiarity with popular Javascript frameworks for full stack development\n• Experience with Node.js\n• Domain experience in fintech, adtech, renewable energy or life sciences\n• Open source contributions, AboutServicesClientsPortfolioIndustriesContactJobs", "title": "Senior Software Engineer, Ruby on Rails", "location": "Tokyo, Japan", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.ikayzo.com/jobs/senior-software-engineer-ruby-on-rails", "description": "Senior Software Engineer, <PERSON> on Rails Tokyo, Japan Work with an award-winning software engineering team on projects in fintech and renewable energy from New York to Tokyo. Ikayzo, a software development firm and UX design agency, is looking for Ruby on Rails Developers capable of taking complex projects from ideation to release. In this role he/she will be responsible for building high quality, secure and scalable web applications across diverse industries. Ikayzo offers a competitive compensation package including medical coverage and 24 days of paid time off per annum. This is a local position for applicants in the Tokyo Metropolitan area. The position of Senior Rails Developer entails software architecture, implementation, development of best operational practices, testing and documentation of non-trivial systems with massive scalability requirements. Fullstack experience, including building dynamic web UIs with React and/or Angular is preferred but not required. As a boutique consultancy we are looking for motivated individuals who do not require step-by-step directions and can work cohesively with our team. Successful applicants will have a proven track record with non-trivial Ruby on Rails projects, be comfortable with agile development methodologies, have a solid grounding in OO software architecture / design patterns and be comfortable with the Rails technology stack from front to back. Most importantly, candidates must have a passion for programming. Our interviews are rigorous, so only serious developers with the requisite skills need apply. Send your resume and sample code or a link to your GitHub profile to career@ikayzo. com. Requirements • A solid grounding in OO software architecture / design patterns • Experience designing and implementing successful non-trivial enterprise Ruby on Rails projects • Experience with agile development methodologies • Deep knowledge of the Rails tech stack • Firm grasp of data modeling • Proficiency in spoken and written English • A passion for software development Preferred • PostgreSQL and MongoDB experience • Familiarity with popular Javascript frameworks for full stack development • Experience with Node. js • Domain experience in fintech, adtech, renewable energy or life sciences • Open source contributions", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://ikayzo.com/jobs/senior-kotlin-enterprise-engineer", "company_id": 3373, "source": 3, "skills": "About\n      \n        \n        \tHome IconIkayzo, Inc.\n        \n    \t\t\n        \tTeam IconTeam\n        \n    \t\t\n        \tNews IconNews\n        \n        \n          News IconIkayzo Group\n        \n      \n    \n    Services\n      \n        \n        \tDesign IconDesign\n        \n        \n        \tBuild IconBuild\n        \n        \n        \tLocalize IconLocalize\n        \n        \n        \tAudit IconAudit\n        \n      \n    \n    Clients\n      \n          \n          \tCustomer IconOur Customers\n          \n      \n    \n    Portfolio\n      \n        \n        \tProject IconWeb & Mobile\n        \n      \t\n        \tBranding IconBranding\n        \n      \t\n        \tIllustration IconIllustration\n        \n      \t\n        \tLab IconLab\n        \n      \n    \n    Industries\n      \n      \t\n        \tFinance IconFinance\n        \n    \t\n        \tGovernment IconGovernment\n        \n   \t\t\n        \tEntertainment IconEntertainment\n        \n        \n            Travel IconTourism\n        \n      \n    \n    Contact\n    \t\n      \t\n        \tOffice IconOffice Locations\n        \n      \n    \n    Languages\n    \t\n      \t日本語, Home IconIkayzo, Inc.\n        \n    \t\t\n        \tTeam IconTeam\n        \n    \t\t\n        \tNews IconNews\n        \n        \n          News IconIkayzo Group, Design IconDesign\n        \n        \n        \tBuild IconBuild\n        \n        \n        \tLocalize IconLocalize\n        \n        \n        \tAudit IconAudit, Customer IconOur Customers, Project IconWeb & Mobile\n        \n      \t\n        \tBranding IconBranding\n        \n      \t\n        \tIllustration IconIllustration\n        \n      \t\n        \tLab IconLab, Finance IconFinance\n        \n    \t\n        \tGovernment IconGovernment\n        \n   \t\t\n        \tEntertainment IconEntertainment\n        \n        \n            Travel IconTourism, Office IconOffice Locations, 日本語, • A solid grounding in OO software architecture / design patterns\n\n• Experience designing and implementing successful enterprise (server-side) Kotlin projects\n• Experience with agile development methodologies\n\n• Deep knowledge of the de facto Kotlin enterprise stack including Spring or Ktor\n\n• Firm grasp of data modeling\n\n• A passion for software development, • Familiarity with React.js for full stack development\n\n• PostgreSQL and MongoDB expertise\n\n• Contributions to open source projects, AboutServicesClientsPortfolioIndustriesContactJobs", "title": "Senior Enterprise Engineer, <PERSON><PERSON><PERSON>", "location": "Locations: US (contiguous states), Canada or Japan", "location_type": null, "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://ikayzo.com/jobs/senior-kotlin-enterprise-engineer", "description": "Senior Enterprise Engineer, Kotlin Locations: US (contiguous states), Canada or Japan Work with an award-winning team designing and implementing systems for customers from New York to Tokyo. Ikayzo, a boutique software development and interactive design agency, is looking for a Senior Kotlin Server-side Software Engineer capable of taking complex projects from ideation to release. In this role he/she will be responsible for designing and implementing applications for a wide range of customers ranging from web startups to energy companies and fintech startups to global banks. We promote from within, and this position has the potential to develop into a tech lead role. Ikayzo offers a competitive compensation package for full time employees including medical coverage and 24 days of paid time off per annum. Job Description The position of Senior Kotlin Enterprise Engineer entails software architecture, implementation, development of best operational practices, testing and documentation of non-trivial systems with massive scalability requirements. Full stack experience with React. js is preferred but not required. As a boutique consultancy we are looking for motivated individuals who do not require step-by-step directions and can work cohesively with our team. Successful applicants will have a proven track record with non-trivial enterprise Kotlin projects, be comfortable with agile development methodologies, have a solid grounding in OO software architecture / design patterns and be comfortable with the enterprise Kotlin technology stack including frameworks such as Ktor or Spring Boot from front to back. Most importantly, candidates must have a passion for programming. Our interviews are rigorous, so only serious developers with the requisite skills need apply. Send your resume and a link to your GitHub profile to career@ikayzo. com. Requirements • A solid grounding in OO software architecture / design patterns • Experience designing and implementing successful enterprise (server-side) Kotlin projects • Experience with agile development methodologies • Deep knowledge of the de facto Kotlin enterprise stack including Spring or Ktor • Firm grasp of data modeling • A passion for software development Preferred • Familiarity with React. js for full stack development • PostgreSQL and MongoDB expertise • Contributions to open source projects Eligibility We are currently only accepting applicants who reside in, and can legally work in, the US, Canada or Japan. We are not able to sponsor visas at this time. About Ikayzo Ikayzo is a boutique interactive design, software development and localization agency. We have a proven track record of designing and building systems ranging from Japanese social networking tools to enterprise financial systems. We develop desktop, web and mobile applications for multiple platforms including Ruby on Rails, Kotlin / Ktor, Java, iOS, Android, and Flutter. Ikayzo has worked with Google, IBM, Intel, Sun, Adobe, Oracle, Apache, Red Hat and SAP on developing technology standards for everything from enterprise software to scientific applications. From New York to Tokyo, our customers range from database leader Oracle to trusted financial institutions like Bank of America. We work with customers of all sizes in a variety of industries including biotechnology, banking, renewable energy, social media / web 2. 0 and software.", "ctc": null, "currency": null, "meta": {}}]