[{"jd_link": "https://www.magicfinserv.com/career/business-analyst/", "company_id": 3377, "source": 3, "skills": "Data & AI \n\t\t\t\t\n\t\t\t\t\tMagic DeepSight\n\t\t\t\t\tInvoice Processing Automation\n\t\t\t\t\tLoan Processing Automation\n\t\t\t\t\tKYC Automation\n\t\t\t\t\tAnti-Money Laundering (AML) Optimization \n        Contract Data Management\n        SEC EDGAR Data Automation\n        T+1 Settlement Automation\n        Developer Companion\n\t\t\t\t\tQA Companion\n\t\t\t\t\n\t\t\t\n\n\n\t\t\t  Services\n\t\t    \n        Consulting\n        Interactive Design\n        Quality Engineering\n        Platform Engineering\n\n            Cloud & Application Support\n\n            Machine Learning Implementation\n\n\n\n\n                \n\n\n\t\t  \n          Solutions\n\n\t\t    \n\n\n                  FinTech Growth Accelerator\n                  \nSecuritized Token Offering\nEnterprise Data Management\n  \n\n\n\n  Testing and Test Automation\n  \n Cloud Transformation & DevOps\n  Deepsight – AI Based Operations Optimization\n\n\n\n\n                \n\n\t\t  \n\n    \n\n      About Us\n\n\n                  Careers\n                  Our Culture\n                  Core Values\n                  Our Leadership\n\n\n\n\n      About Us\n\t\t    \n\n                  Careers\n                  Our Culture\n                  Core Values\n                  Our Leadership\n\n                \n\n\t\t  \n\n        \n          Resources\n\n\t\t    \n        Insights\n\n        Case Studies\n        Press Release\n        Education\n\n        Brochures\n        Media Coverage\n                \n\n\n      \n\n\n          Contact Us, Magic DeepSight\n\t\t\t\t\tInvoice Processing Automation\n\t\t\t\t\tLoan Processing Automation\n\t\t\t\t\tKYC Automation\n\t\t\t\t\tAnti-Money Laundering (AML) Optimization \n        Contract Data Management\n        SEC EDGAR Data Automation\n        T+1 Settlement Automation\n        Developer Companion\n\t\t\t\t\tQA Companion, Consulting\n        Interactive Design\n        Quality Engineering\n        Platform Engineering\n\n            Cloud & Application Support\n\n            Machine Learning Implementation, FinTech Growth Accelerator\n                  \nSecuritized Token Offering\nEnterprise Data Management\n  \n\n\n\n  Testing and Test Automation\n  \n Cloud Transformation & DevOps\n  Deepsight – AI Based Operations Optimization, Careers\n                  Our Culture\n                  Core Values\n                  Our Leadership, Insights\n\n        Case Studies\n        Press Release\n        Education\n\n        Brochures\n        Media Coverage, Working knowledge of the regulatory reporting for Investment Bank, with very strong functional and domain knowledge of the various reporting regulation like ESMA/ /MIFID/EMIR/SFTR\n \tUnderstand the basics of Trade Life Cycle and its different events.\n \tRich experience in the capital markets domain and understanding of various financial instruments (Equity, Fixed Income, Derivatives etc) and their valuation\n \tGood Data Analysis skills preferably in regulatory reporting\n \tGood stakeholder and client management skills and understanding of BAU processes\n \tHands-on experience in Excel for data analysis\n \tSQL knowledge ability to understand product data models\n \tProject skills – tracking tasks, JIRA, reporting skills, working across multiple teams\n \tSmart worker - Innovative mindset with an eye for detail for any process optimization/automation using technology\n \tExcellent communication skills.\n \tKnowledge of the front-to-back processes needed to maintain a stable and controlled trade environment and regulatory reporting requirements, Home\nAbout Us \n Our Leadership\nMagic Pathshala\nPrivacy Policy\nCareers \nContacts, , <EMAIL>\nParamount Building, 28th Floor, 1501 Broadway, New York, NY 10036\n2nd Floor, Tower B, Smartworks Corporate Park, Sec 125, Noida 201303", "title": "Business Analyst", "location": "Location:  Noida / Permanent Work from home\nExperience Required:  6-12 Years\nRequirement:\n\n6+ years of experience in Business Analysis in investment banking domain preferably in Regulatory Reporting like EMIR, MIFID, SFTR\n\nMust-Have:\nUpload your resume (.pdf, .doc) *\nFirst Name *\nLast Name *\nEmail *\nMobile No\nPlease leave this field empty.\n", "location_type": "remote", "job_type": "full_time", "min_experience": 6, "max_experience": 12, "apply_link": "https://www.magicfinserv.com/career/business-analyst/", "description": "Business Analyst Location: Noida / Permanent Work from home Experience Required: 6-12 Years Requirement: 6+ years of experience in Business Analysis in investment banking domain preferably in Regulatory Reporting like EMIR, MIFID, SFTR Must-Have: Working knowledge of the regulatory reporting for Investment Bank, with very strong functional and domain knowledge of the various reporting regulation like ESMA/ /MIFID/EMIR/SFTR Understand the basics of Trade Life Cycle and its different events. Rich experience in the capital markets domain and understanding of various financial instruments (Equity, Fixed Income, Derivatives etc) and their valuation Good Data Analysis skills preferably in regulatory reporting Good stakeholder and client management skills and understanding of BAU processes Hands-on experience in Excel for data analysis SQL knowledge ability to understand product data models Project skills – tracking tasks, JIRA, reporting skills, working across multiple teams Smart worker - Innovative mindset with an eye for detail for any process optimization/automation using technology Excellent communication skills. Knowledge of the front-to-back processes needed to maintain a stable and controlled trade environment and regulatory reporting requirements About Magic FinServ Magic FinServ is a leading digital technology services company for the Financial services industry. We bring a rare combination of Capital Markets domain knowledge & new-age technology skills in Blockchain & Artificial Intelligence. We help the buy side firms optimize their front & middle office operations using Artificial Intelligence. Our team brings strong capital markets domain knowledge AND experience working with technologies like Blockchain, NLP, Machine Learning & Cloud. Visit us at Magic FinServ, Magic BlockchainQA, & Solmark to know more. Upload your resume (. pdf, . doc) * First Name * Last Name * Email * Mobile No <div class=\"grecaptcha-noscript\"> <iframe src=\"https://www. google. com/recaptcha/api/fallback? k=6LdnpcsUAAAAAPLEAwaTJqZZe4Mbh6k5MeFbpm9g\" frameborder=\"0\" scrolling=\"no\" width=\"310\" height=\"430\"> </iframe> <textarea name=\"g-recaptcha-response\" rows=\"3\" cols=\"40\" placeholder=\"reCaptcha Response Here\"> </textarea> </div> Please leave this field empty.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.magicfinserv.com/career/net-kubernetes/", "company_id": 3377, "source": 3, "skills": "Data & AI \n\t\t\t\t\n\t\t\t\t\tMagic DeepSight\n\t\t\t\t\tInvoice Processing Automation\n\t\t\t\t\tLoan Processing Automation\n\t\t\t\t\tKYC Automation\n\t\t\t\t\tAnti-Money Laundering (AML) Optimization \n        Contract Data Management\n        SEC EDGAR Data Automation\n        T+1 Settlement Automation\n        Developer Companion\n\t\t\t\t\tQA Companion\n\t\t\t\t\n\t\t\t\n\n\n\t\t\t  Services\n\t\t    \n        Consulting\n        Interactive Design\n        Quality Engineering\n        Platform Engineering\n\n            Cloud & Application Support\n\n            Machine Learning Implementation\n\n\n\n\n                \n\n\n\t\t  \n          Solutions\n\n\t\t    \n\n\n                  FinTech Growth Accelerator\n                  \nSecuritized Token Offering\nEnterprise Data Management\n  \n\n\n\n  Testing and Test Automation\n  \n Cloud Transformation & DevOps\n  Deepsight – AI Based Operations Optimization\n\n\n\n\n                \n\n\t\t  \n\n    \n\n      About Us\n\n\n                  Careers\n                  Our Culture\n                  Core Values\n                  Our Leadership\n\n\n\n\n      About Us\n\t\t    \n\n                  Careers\n                  Our Culture\n                  Core Values\n                  Our Leadership\n\n                \n\n\t\t  \n\n        \n          Resources\n\n\t\t    \n        Insights\n\n        Case Studies\n        Press Release\n        Education\n\n        Brochures\n        Media Coverage\n                \n\n\n      \n\n\n          Contact Us, Magic DeepSight\n\t\t\t\t\tInvoice Processing Automation\n\t\t\t\t\tLoan Processing Automation\n\t\t\t\t\tKYC Automation\n\t\t\t\t\tAnti-Money Laundering (AML) Optimization \n        Contract Data Management\n        SEC EDGAR Data Automation\n        T+1 Settlement Automation\n        Developer Companion\n\t\t\t\t\tQA Companion, Consulting\n        Interactive Design\n        Quality Engineering\n        Platform Engineering\n\n            Cloud & Application Support\n\n            Machine Learning Implementation, FinTech Growth Accelerator\n                  \nSecuritized Token Offering\nEnterprise Data Management\n  \n\n\n\n  Testing and Test Automation\n  \n Cloud Transformation & DevOps\n  Deepsight – AI Based Operations Optimization, Careers\n                  Our Culture\n                  Core Values\n                  Our Leadership, Insights\n\n        Case Studies\n        Press Release\n        Education\n\n        Brochures\n        Media Coverage, Participate in design and development of complex software projects, writing technical specification.\n \tWork with product managers and QA engineers from development to production.\n \tContribute to software architecture design, development of software applications, and integration into\nenterprise systems\n \t7+ years of working Experience.\n \t7+ Experience in .NET Framework, .NET Core, package management, and dependency injection.\n \texperience working with Kubernetes and Docker\n \tDatabase experience (SQL Server) and good knowledge in query optimization.\n \tExperience with distributed architectures and REST APIs.\n \tExperience with gRPC APIs is a big plus.\n \tExperience with continuous integration and continuous delivery.\n \tProficiency at implementing responsive designs using HTML and modern CSS.\n \tExperience supporting and scaling consumer-facing web applications.\n \tAgile/SCRUM Software Development Process experience.\n \tKubernetes experience a plus.\n \tFluent English – both speaking and writing.\n \tFlexible working time zones., Home\nAbout Us \n Our Leadership\nMagic Pathshala\nPrivacy Policy\nCareers \nContacts, , <EMAIL>\nParamount Building, 28th Floor, 1501 Broadway, New York, NY 10036\n2nd Floor, Tower B, Smartworks Corporate Park, Sec 125, Noida 201303", "title": "Net + Kubernetes", "location": "Experience Required: 8 to 10 Years\nKey Technologies:  C#, .Net, SQL Server, .Netcore, Kubernetes or Docker.\nAbout the Role:\n\nWe are looking for an experienced full-stack .NET developer to join a global development organization that\ndevelops and supports products within the Compliance, Reporting &amp; Services Division. This resource will be\nresponsible to work as part of a small team to cover all aspects of design, development, implementation, and\nsupport.\n\nResponsibilities:\nUpload your resume (.pdf, .doc) *\nFirst Name *\nLast Name *\nEmail *\nMobile No\nPlease leave this field empty.\n", "location_type": "flexible", "job_type": null, "min_experience": 7, "max_experience": 10, "apply_link": "https://www.magicfinserv.com/career/net-kubernetes/", "description": "Net + Kubernetes Experience Required: 8 to 10 Years Key Technologies: C#, . Net, SQL Server, . Netcore, Kubernetes or Docker. About the Role: We are looking for an experienced full-stack . NET developer to join a global development organization that develops and supports products within the Compliance, Reporting &amp; Services Division. This resource will be responsible to work as part of a small team to cover all aspects of design, development, implementation, and support. Responsibilities: Participate in design and development of complex software projects, writing technical specification. Work with product managers and QA engineers from development to production. Contribute to software architecture design, development of software applications, and integration into enterprise systems 7+ years of working Experience. 7+ Experience in . NET Framework, . NET Core, package management, and dependency injection. experience working with Kubernetes and Docker Database experience (SQL Server) and good knowledge in query optimization. Experience with distributed architectures and REST APIs. Experience with gRPC APIs is a big plus. Experience with continuous integration and continuous delivery. Proficiency at implementing responsive designs using HTML and modern CSS. Experience supporting and scaling consumer-facing web applications. Agile/SCRUM Software Development Process experience. Kubernetes experience a plus. Fluent English – both speaking and writing. Flexible working time zones. About Magic FinServ Magic FinServ is a leading digital technology services company for the Financial services industry. We bring a rare combination of Capital Markets domain knowledge & new-age technology skills in Blockchain & Artificial Intelligence. We help the buy side firms optimize their front & middle office operations using Artificial Intelligence. Our team brings strong capital markets domain knowledge AND experience working with technologies like Blockchain, NLP, Machine Learning & Cloud. Visit us at Magic FinServ, Magic BlockchainQA, & Solmark to know more. Upload your resume (. pdf, . doc) * First Name * Last Name * Email * Mobile No <div class=\"grecaptcha-noscript\"> <iframe src=\"https://www. google. com/recaptcha/api/fallback? k=6LdnpcsUAAAAAPLEAwaTJqZZe4Mbh6k5MeFbpm9g\" frameborder=\"0\" scrolling=\"no\" width=\"310\" height=\"430\"> </iframe> <textarea name=\"g-recaptcha-response\" rows=\"3\" cols=\"40\" placeholder=\"reCaptcha Response Here\"> </textarea> </div> Please leave this field empty.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.magicfinserv.com/career/java-lead/", "company_id": 3377, "source": 3, "skills": "Data & AI \n\t\t\t\t\n\t\t\t\t\tMagic DeepSight\n\t\t\t\t\tInvoice Processing Automation\n\t\t\t\t\tLoan Processing Automation\n\t\t\t\t\tKYC Automation\n\t\t\t\t\tAnti-Money Laundering (AML) Optimization \n        Contract Data Management\n        SEC EDGAR Data Automation\n        T+1 Settlement Automation\n        Developer Companion\n\t\t\t\t\tQA Companion\n\t\t\t\t\n\t\t\t\n\n\n\t\t\t  Services\n\t\t    \n        Consulting\n        Interactive Design\n        Quality Engineering\n        Platform Engineering\n\n            Cloud & Application Support\n\n            Machine Learning Implementation\n\n\n\n\n                \n\n\n\t\t  \n          Solutions\n\n\t\t    \n\n\n                  FinTech Growth Accelerator\n                  \nSecuritized Token Offering\nEnterprise Data Management\n  \n\n\n\n  Testing and Test Automation\n  \n Cloud Transformation & DevOps\n  Deepsight – AI Based Operations Optimization\n\n\n\n\n                \n\n\t\t  \n\n    \n\n      About Us\n\n\n                  Careers\n                  Our Culture\n                  Core Values\n                  Our Leadership\n\n\n\n\n      About Us\n\t\t    \n\n                  Careers\n                  Our Culture\n                  Core Values\n                  Our Leadership\n\n                \n\n\t\t  \n\n        \n          Resources\n\n\t\t    \n        Insights\n\n        Case Studies\n        Press Release\n        Education\n\n        Brochures\n        Media Coverage\n                \n\n\n      \n\n\n          Contact Us, Magic DeepSight\n\t\t\t\t\tInvoice Processing Automation\n\t\t\t\t\tLoan Processing Automation\n\t\t\t\t\tKYC Automation\n\t\t\t\t\tAnti-Money Laundering (AML) Optimization \n        Contract Data Management\n        SEC EDGAR Data Automation\n        T+1 Settlement Automation\n        Developer Companion\n\t\t\t\t\tQA Companion, Consulting\n        Interactive Design\n        Quality Engineering\n        Platform Engineering\n\n            Cloud & Application Support\n\n            Machine Learning Implementation, FinTech Growth Accelerator\n                  \nSecuritized Token Offering\nEnterprise Data Management\n  \n\n\n\n  Testing and Test Automation\n  \n Cloud Transformation & DevOps\n  Deepsight – AI Based Operations Optimization, Careers\n                  Our Culture\n                  Core Values\n                  Our Leadership, Insights\n\n        Case Studies\n        Press Release\n        Education\n\n        Brochures\n        Media Coverage, Writing scalable, robust, testable, efficient, and easily maintainable code\n \tAble to understand the assigned tasks and deliver with in given timelines., Strong knowledge of Java/J2EE\n \tStrong knowledge of OOPS\n \tStrong knowledge of React/Angular, HTML,CSS and JS\n \tGood knowledge of Spring & SpringBoot\n \tGood knowledge of Web API, Web Services\n \tGood knowledge of SDLC\n \tGood to have experience on JMS\n \tGood to have experience on Apache Camel\n \tUnderstanding of Design Patterns & Coding Standards\n \tFamiliarity with code versioning tools such as Git, SVN, VSTS\n \tGood to have experience in DB(Postgres/SQL Server).\n \tGood to have a Capital market skills, Home\nAbout Us \n Our Leadership\nMagic Pathshala\nPrivacy Policy\nCareers \nContacts, , <EMAIL>\nParamount Building, 28th Floor, 1501 Broadway, New York, NY 10036\n2nd Floor, Tower B, Smartworks Corporate Park, Sec 125, Noida 201303", "title": "Java Lead", "location": "Experience Required:  8+ Years\n\nPrimary Skills:\n\nLooking for a candidate having 8 to 12 Years of strong development experience using Java/J2EE, HTML/JS, web application technologies stack, such as Spring, SpringBoot, Web Services, Web API and experience on DB such as Postgres/SQL Server Candidate should be capable of developing,\nindependently covering all stages of the development cycle. Strong software development skills and ability to write professional code.\n\nResponsibilities:\nUpload your resume (.pdf, .doc) *\nFirst Name *\nLast Name *\nEmail *\nMobile No\nPlease leave this field empty.\n", "location_type": null, "job_type": null, "min_experience": 8, "max_experience": 12, "apply_link": "https://www.magicfinserv.com/career/java-lead/", "description": "Java Lead Experience Required: 8+ Years Primary Skills: Looking for a candidate having 8 to 12 Years of strong development experience using Java/J2EE, HTML/JS, web application technologies stack, such as Spring, SpringBoot, Web Services, Web API and experience on DB such as Postgres/SQL Server Candidate should be capable of developing, independently covering all stages of the development cycle. Strong software development skills and ability to write professional code. Responsibilities: Writing scalable, robust, testable, efficient, and easily maintainable code Able to understand the assigned tasks and deliver with in given timelines. Technical Skills: Strong knowledge of Java/J2EE Strong knowledge of OOPS Strong knowledge of React/Angular, HTML,CSS and JS Good knowledge of Spring & SpringBoot Good knowledge of Web API, Web Services Good knowledge of SDLC Good to have experience on JMS Good to have experience on Apache Camel Understanding of Design Patterns & Coding Standards Familiarity with code versioning tools such as Git, SVN, VSTS Good to have experience in DB(Postgres/SQL Server). Good to have a Capital market skills About Magic FinServ Magic FinServ is a leading digital technology services company for the Financial services industry. We bring a rare combination of Capital Markets domain knowledge & new-age technology skills in Blockchain & Artificial Intelligence. We help the buy side firms optimize their front & middle office operations using Artificial Intelligence. Our team brings strong capital markets domain knowledge AND experience working with technologies like Blockchain, NLP, Machine Learning & Cloud. Visit us at Magic FinServ, Magic BlockchainQA, & Solmark to know more. Upload your resume (. pdf, . doc) * First Name * Last Name * Email * Mobile No <div class=\"grecaptcha-noscript\"> <iframe src=\"https://www. google. com/recaptcha/api/fallback? k=6LdnpcsUAAAAAPLEAwaTJqZZe4Mbh6k5MeFbpm9g\" frameborder=\"0\" scrolling=\"no\" width=\"310\" height=\"430\"> </iframe> <textarea name=\"g-recaptcha-response\" rows=\"3\" cols=\"40\" placeholder=\"reCaptcha Response Here\"> </textarea> </div> Please leave this field empty.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.magicfinserv.com/career/project-manager/", "company_id": 3377, "source": 3, "skills": "Data & AI \n\t\t\t\t\n\t\t\t\t\tMagic DeepSight\n\t\t\t\t\tInvoice Processing Automation\n\t\t\t\t\tLoan Processing Automation\n\t\t\t\t\tKYC Automation\n\t\t\t\t\tAnti-Money Laundering (AML) Optimization \n        Contract Data Management\n        SEC EDGAR Data Automation\n        T+1 Settlement Automation\n        Developer Companion\n\t\t\t\t\tQA Companion\n\t\t\t\t\n\t\t\t\n\n\n\t\t\t  Services\n\t\t    \n        Consulting\n        Interactive Design\n        Quality Engineering\n        Platform Engineering\n\n            Cloud & Application Support\n\n            Machine Learning Implementation\n\n\n\n\n                \n\n\n\t\t  \n          Solutions\n\n\t\t    \n\n\n                  FinTech Growth Accelerator\n                  \nSecuritized Token Offering\nEnterprise Data Management\n  \n\n\n\n  Testing and Test Automation\n  \n Cloud Transformation & DevOps\n  Deepsight – AI Based Operations Optimization\n\n\n\n\n                \n\n\t\t  \n\n    \n\n      About Us\n\n\n                  Careers\n                  Our Culture\n                  Core Values\n                  Our Leadership\n\n\n\n\n      About Us\n\t\t    \n\n                  Careers\n                  Our Culture\n                  Core Values\n                  Our Leadership\n\n                \n\n\t\t  \n\n        \n          Resources\n\n\t\t    \n        Insights\n\n        Case Studies\n        Press Release\n        Education\n\n        Brochures\n        Media Coverage\n                \n\n\n      \n\n\n          Contact Us, Magic DeepSight\n\t\t\t\t\tInvoice Processing Automation\n\t\t\t\t\tLoan Processing Automation\n\t\t\t\t\tKYC Automation\n\t\t\t\t\tAnti-Money Laundering (AML) Optimization \n        Contract Data Management\n        SEC EDGAR Data Automation\n        T+1 Settlement Automation\n        Developer Companion\n\t\t\t\t\tQA Companion, Consulting\n        Interactive Design\n        Quality Engineering\n        Platform Engineering\n\n            Cloud & Application Support\n\n            Machine Learning Implementation, FinTech Growth Accelerator\n                  \nSecuritized Token Offering\nEnterprise Data Management\n  \n\n\n\n  Testing and Test Automation\n  \n Cloud Transformation & DevOps\n  Deepsight – AI Based Operations Optimization, Careers\n                  Our Culture\n                  Core Values\n                  Our Leadership, Insights\n\n        Case Studies\n        Press Release\n        Education\n\n        Brochures\n        Media Coverage, Take responsibility for the project from project scoping, requirements definition, project planning and financial planning/budget perspective.\n \tManage the project within the defined budget.\n \tSet up project Steering Committee meetings – lead the meetings providing status updates for projects.\n \tLiaise with management and PMO from a resourcing perspective to ensure all resourcing plans are aligned with the project deliverables..\n \tThroughout the project life cycle, identify and assess all risks, issues, assumptions & dependencies and identify mitigating actions for same.\n \tManage external vendors to ensure they deliver to time and budget deliverables.\n \tEnsure all project documentation is maintained and of adequate quality.\n \tEnsure quality gates are in place and achieved.\n \tEnsures efficient communication with all relevant stakeholders., Experienced in a PM role;\n \tExperience in working in an Agile environment;\n \tGood to have PM Certification (SAFe, PRINCE2 or PMP);\n \tStrong leadership and influencing capabilities;\n \tExcellent presentation skills and communication skills;\n \tExperience in the BFSI industry.\n \tStrong relationship management skills, Sign off of PID with project sponsor\n \tDelivery of project to PID\n \tGood engagement with project sponsor and SteerCo\n \tGood engagement with PMO and other project managers\n \tProject documentation, Home\nAbout Us \n Our Leadership\nMagic Pathshala\nPrivacy Policy\nCareers \nContacts, , <EMAIL>\nParamount Building, 28th Floor, 1501 Broadway, New York, NY 10036\n2nd Floor, Tower B, Smartworks Corporate Park, Sec 125, Noida 201303", "title": "Project Manager", "location": "Location:  Noida\nExperience Required:  8-14 Years\n\nThe Project Manager will be responsible for ensuring the project scope, plan, and execution of projects are delivered to meet the business requirements.\n\nThe Project Manager will report to the Head of PMO.\n\nThe principal responsibilities are:\nUpload your resume (.pdf, .doc) *\nFirst Name *\nLast Name *\nEmail *\nMobile No\nPlease leave this field empty.\n", "location_type": null, "job_type": null, "min_experience": 8, "max_experience": 14, "apply_link": "https://www.magicfinserv.com/career/project-manager/", "description": "Project Manager Location: Noida Experience Required: 8-14 Years The Project Manager will be responsible for ensuring the project scope, plan, and execution of projects are delivered to meet the business requirements. The Project Manager will report to the Head of PMO. The principal responsibilities are: Take responsibility for the project from project scoping, requirements definition, project planning and financial planning/budget perspective. Manage the project within the defined budget. Set up project Steering Committee meetings – lead the meetings providing status updates for projects. Liaise with management and PMO from a resourcing perspective to ensure all resourcing plans are aligned with the project deliverables. . Throughout the project life cycle, identify and assess all risks, issues, assumptions & dependencies and identify mitigating actions for same. Manage external vendors to ensure they deliver to time and budget deliverables. Ensure all project documentation is maintained and of adequate quality. Ensure quality gates are in place and achieved. Ensures efficient communication with all relevant stakeholders. Experience and Knowledge: Experienced in a PM role; Experience in working in an Agile environment; Good to have PM Certification (SAFe, PRINCE2 or PMP); Strong leadership and influencing capabilities; Excellent presentation skills and communication skills; Experience in the BFSI industry. Strong relationship management skills Key Performance indicators: Sign off of PID with project sponsor Delivery of project to PID Good engagement with project sponsor and SteerCo Good engagement with PMO and other project managers Project documentation Upload your resume (. pdf, . doc) * First Name * Last Name * Email * Mobile No <div class=\"grecaptcha-noscript\"> <iframe src=\"https://www. google. com/recaptcha/api/fallback? k=6LdnpcsUAAAAAPLEAwaTJqZZe4Mbh6k5MeFbpm9g\" frameborder=\"0\" scrolling=\"no\" width=\"310\" height=\"430\"> </iframe> <textarea name=\"g-recaptcha-response\" rows=\"3\" cols=\"40\" placeholder=\"reCaptcha Response Here\"> </textarea> </div> Please leave this field empty.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.magicfinserv.com/career/python-testing/", "company_id": 3377, "source": 3, "skills": "Data & AI \n\t\t\t\t\n\t\t\t\t\tMagic DeepSight\n\t\t\t\t\tInvoice Processing Automation\n\t\t\t\t\tLoan Processing Automation\n\t\t\t\t\tKYC Automation\n\t\t\t\t\tAnti-Money Laundering (AML) Optimization \n        Contract Data Management\n        SEC EDGAR Data Automation\n        T+1 Settlement Automation\n        Developer Companion\n\t\t\t\t\tQA Companion\n\t\t\t\t\n\t\t\t\n\n\n\t\t\t  Services\n\t\t    \n        Consulting\n        Interactive Design\n        Quality Engineering\n        Platform Engineering\n\n            Cloud & Application Support\n\n            Machine Learning Implementation\n\n\n\n\n                \n\n\n\t\t  \n          Solutions\n\n\t\t    \n\n\n                  FinTech Growth Accelerator\n                  \nSecuritized Token Offering\nEnterprise Data Management\n  \n\n\n\n  Testing and Test Automation\n  \n Cloud Transformation & DevOps\n  Deepsight – AI Based Operations Optimization\n\n\n\n\n                \n\n\t\t  \n\n    \n\n      About Us\n\n\n                  Careers\n                  Our Culture\n                  Core Values\n                  Our Leadership\n\n\n\n\n      About Us\n\t\t    \n\n                  Careers\n                  Our Culture\n                  Core Values\n                  Our Leadership\n\n                \n\n\t\t  \n\n        \n          Resources\n\n\t\t    \n        Insights\n\n        Case Studies\n        Press Release\n        Education\n\n        Brochures\n        Media Coverage\n                \n\n\n      \n\n\n          Contact Us, Magic DeepSight\n\t\t\t\t\tInvoice Processing Automation\n\t\t\t\t\tLoan Processing Automation\n\t\t\t\t\tKYC Automation\n\t\t\t\t\tAnti-Money Laundering (AML) Optimization \n        Contract Data Management\n        SEC EDGAR Data Automation\n        T+1 Settlement Automation\n        Developer Companion\n\t\t\t\t\tQA Companion, Consulting\n        Interactive Design\n        Quality Engineering\n        Platform Engineering\n\n            Cloud & Application Support\n\n            Machine Learning Implementation, FinTech Growth Accelerator\n                  \nSecuritized Token Offering\nEnterprise Data Management\n  \n\n\n\n  Testing and Test Automation\n  \n Cloud Transformation & DevOps\n  Deepsight – AI Based Operations Optimization, Careers\n                  Our Culture\n                  Core Values\n                  Our Leadership, Insights\n\n        Case Studies\n        Press Release\n        Education\n\n        Brochures\n        Media Coverage, Understand and Experience Capital market Domain, Bonds, Asset classes, FIX msg and Equity.\n \tDevelopment experience in any following language – Python\n \tUnderstanding the requirements and formulating the Test strategies, Test schedules and test plans\n \tClient interaction in a convincing manner is the key.\n \tDevelop and execute automated scripts during the release schedule\n \tProvide technical support to resolve any technical issues during the design and execution of test\ncases.\n \tParticipates in walkthrough sessions/meetings.\n \tGuides teams in the framework and tool selection process.\n \tEnsure implementation of standard processes, and perform script reviews.\n \tGood Knowledge of Databases.\n \tKnowledge of Inbound and outbound files, Publishing to downstream systems, Capital Markets experience.\n \tAsset Management Experience\n \tAzure/AWS basics., Home\nAbout Us \n Our Leadership\nMagic Pathshala\nPrivacy Policy\nCareers \nContacts, , <EMAIL>\nParamount Building, 28th Floor, 1501 Broadway, New York, NY 10036\n2nd Floor, Tower B, Smartworks Corporate Park, Sec 125, Noida 201303", "title": "Python Automation Engineer", "location": "Location:  Noida\nExperience Required:  2-5 Years\n\n\nJob Description:-\nUpload your resume (.pdf, .doc) *\nFirst Name *\nLast Name *\nEmail *\nMobile No\nPlease leave this field empty.\n", "location_type": null, "job_type": null, "min_experience": 2, "max_experience": 5, "apply_link": "https://www.magicfinserv.com/career/python-testing/", "description": "Python Automation Engineer Location: Noida Experience Required: 2-5 Years Job Description:- Understand and Experience Capital market Domain, Bonds, Asset classes, FIX msg and Equity. Development experience in any following language – Python Understanding the requirements and formulating the Test strategies, Test schedules and test plans Client interaction in a convincing manner is the key. Develop and execute automated scripts during the release schedule Provide technical support to resolve any technical issues during the design and execution of test cases. Participates in walkthrough sessions/meetings. Guides teams in the framework and tool selection process. Ensure implementation of standard processes, and perform script reviews. Good Knowledge of Databases. Knowledge of Inbound and outbound files, Publishing to downstream systems Secondary:- Capital Markets experience. Asset Management Experience Azure/AWS basics. About Magic FinServ Magic FinServ is a leading digital technology services company for the Financial services industry. We bring a rare combination of Capital Markets domain knowledge & new-age technology skills in Blockchain & Artificial Intelligence. We help the buy side firms optimize their front & middle office operations using Artificial Intelligence. Our team brings strong capital markets domain knowledge AND experience working with technologies like Blockchain, NLP, Machine Learning & Cloud. Visit us at Magic FinServ, Magic BlockchainQA, & Solmark to know more. Upload your resume (. pdf, . doc) * First Name * Last Name * Email * Mobile No <div class=\"grecaptcha-noscript\"> <iframe src=\"https://www. google. com/recaptcha/api/fallback? k=6LdnpcsUAAAAAPLEAwaTJqZZe4Mbh6k5MeFbpm9g\" frameborder=\"0\" scrolling=\"no\" width=\"310\" height=\"430\"> </iframe> <textarea name=\"g-recaptcha-response\" rows=\"3\" cols=\"40\" placeholder=\"reCaptcha Response Here\"> </textarea> </div> Please leave this field empty.", "ctc": null, "currency": null, "meta": {}}]