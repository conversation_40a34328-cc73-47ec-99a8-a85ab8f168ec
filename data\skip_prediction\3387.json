[{"jd_link": "https://jobs.innovecs.com/vacancies/**********-technical-pre-sales-advisor-supply-chain-execution-2606/", "company_id": 3387, "source": 3, "skills": "Required Skills\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tSales: strong\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tEnglish: strong", "title": "Functional Solution Architect", "location": "Europe\nUS\nRemote", "location_type": "remote", "job_type": "part_time", "min_experience": null, "max_experience": null, "apply_link": "https://jobs.innovecs.com/vacancies/**********-technical-pre-sales-advisor-supply-chain-execution-2606/#block-id-single-vacancies-v2-apply-now", "description": "We are looking for an experienced Functional Solution Architect (part-time) to support us with WMS Integration, Implementation projects and Consulting projects. Innovecs is a global digital services company with a presence in the US, the UK, the EU, Israel, Australia, and Ukraine. Specializing in software solutions, the Innovecs team has experience in Supply Chain, Healthtech, Collaboration Tech, and Gaming. For the fifth year in a row, Innovecs is included in the Inc. 5000, the list of fastest-growing private companies in the US, and a ranking of the best global outsourcing service providers by IAOP. Recently, Innovecs was honored with the prestigious Global Good Awards for the Employee Engagement & Wellbeing, won gold at the Employer Brand Management Awards, and was included in the Global Top 100 Inspiring Workplaces Ranking. Our value to you: Flexible hours and remote-first mode, Competitive compensation, Complete Hardware/Software setup – anything you need for work, Open-door culture, transparent communication, and top management at a handshake distance, Health insurance, vacation, sick leaves, holidays, paid maternity/paternity leave, Access to our learning & development center: workshops, webinars, training platform, and edutainment events, Virtual team buildings and social activities. If you feel like you’re the perfect match for this role, drop us your CV! There are no limitations, no barriers when the right people are on your way — apply for the vacancy and succeed with us! At least six years of proven track in sales/sales support in the Supply Chain Execution domain; Have WMS/TMS/OMS/ERP etc pre-sales experience in more than one major vendor; Have strong sales skills, including the ability to identify customer needs, develop relationships with customers, and close sales; Be skilled at managing customer relationships, building rapport with customers, understanding their needs, and providing ongoing support after the sale; Deep understanding of Supply Chain Execution products and services. This includes knowledge and the ability to explain technical concepts in a way that is easy for customers to understand; Have a degree in Engineering or Computer science. Personal Attributes: Strong interpersonal skills; Exceptional customer communication and presentation skills; Ability to work well individually and in a team environment; Ability to deliver for results. Provide recommendations on how to orchestrate, test, optimize, and understand prospective customers' software builds leveraging Innovecs services offering that result in closing new business and reaching revenue goals; Work closely with the sales team to understand customer requirements and provide guidance throughout the sales process; Deliver inspiring and compelling technical and solution presentations, workshops, and prototypes demos showcasing Innovecs value; Provide support to customers during the pre-sales and sales processes, including answering technical questions and providing solutions to technical challenges; Work closely with the development team to understand Innovecs Supply Chain Execution offerings and provide feedback on customer requirements; Collaborate with the Engineering team subject matter experts to implement specific use cases that are high value for prospects and customers; Act as a customer relationship manager, building and maintaining solid client relationships throughout the sales process and beyond; Collaborate with other departments within Innovecs, such as customer success and product management, to ensure a seamless experience for clients; Act as a liaison between customers and Innovecs, advocating for customer needs and ensuring that their concerns are addressed promptly and satisfactorily; Attend and present at events and tradeshows to build presence and brand awareness; Willingness to travel to industry events and meet customers onsite; Stay up to date with industry trends and technologies related to Supply Chain Execution.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.innovecs.com/vacancies/**********-senior-2d-concept-artist-2666/", "company_id": 3387, "source": 3, "skills": "Required Skills\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tIllustrator: strong", "title": "Senior 2D concept Artist (freelance)", "location": "Ukraine\nPoland\nRomania\nRemote", "location_type": "remote", "job_type": null, "min_experience": 4, "max_experience": null, "apply_link": "https://jobs.innovecs.com/vacancies/**********-senior-2d-concept-artist-2666/#block-id-single-vacancies-v2-apply-now", "description": "Innovecs is a global digital services company with a presence in the US, the UK, the EU, Israel, Australia, and Ukraine. Specializing in software solutions, the Innovecs team has experience in Supply Chain, Healthtech, Collaboration Tech, and Gaming. For the fifth year in a row, Innovecs is included in the Inc. 5000 and recognized in IAOP’s ranking of the best global outsourcing service providers. Innovecs is featured in the Global Top 100 Inspiring Workplaces Ranking, and won gold at the Employer Brand Management Awards. About the client Our partner is a leading entertainment company in the casual gaming world. They are serious about making great casual games with engaging content that takes playtime to a whole new level. They build unique games with cutting-edge technology, beautiful art, and exciting graphics combined with original features to make their games that much more enjoyable. Our value to you: Flexible hours and remote-first mode Competitive compensation Complete Hardware/Software setup – anything you need for work Open-door culture, transparent communication, and top management at a handshake distance Health insurance, vacation, sick leaves, holidays, paid maternity/paternity leave Access to our learning & development center: workshops, webinars, training platform, and edutainment events Virtual team buildings and social activities to celebrate the Innovecs lifestyle Does this resonate with you? Send over your CV, and let's get to know each other better. 4+ years of experience as a Concept Artist, preferably in the gaming or iGaming industry. Strong portfolio showcasing slot game concepts or similar work, with an understanding of symbol hierarchy and slot mechanics. Proficiency in concept art tools such as Photoshop, Illustrator. Knowledge of slot game structure, including reels, pay lines, symbols, and bonus features. Ability to adapt and iterate on feedback while maintaining the integrity of the visual concept. Create engaging concept art for slot games, including characters, environments, and symbols (wilds, scatters, etc. ). Develop visual themes and game worlds that align with the game’s narrative and design objectives. Collaborate with game designers and animators to ensure concept art translates well into final assets. Ensure a strong visual hierarchy of slot symbols and other key game elements to enhance user experience. Deliver high-quality artwork within project timelines while maintaining consistent style and brand guidelines. Communication with client.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.innovecs.com/vacancies/**********-sql-server-production-dba-aristocrat-2657/", "company_id": 3387, "source": 3, "skills": "Required Skills\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tSQL: strong\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tEnglish: middle", "title": "SQL Server Production DBA", "location": "Warsaw\nCracow", "location_type": null, "job_type": null, "min_experience": 3, "max_experience": 3, "apply_link": "https://jobs.innovecs.com/vacancies/**********-sql-server-production-dba-aristocrat-2657/#block-id-single-vacancies-v2-apply-now", "description": "We seek a skilled SQL Server Production DBA to enhance our web presence and platform functionality. ABOUT INNOVECS: Innovecs is a global digital transformation tech company with a presence in the US, the UK, the EU, Israel, Australia, and Ukraine. Specializing in software solutions, the Innovecs team has experience in Supply Chain, Healthtech, Software & Hightech, and Gaming. For the fifth year in a row, Innovecs is included in the Inc. 5000, the list of fastest-growing private companies in the US, and a ranking of the best global outsourcing service providers by IAOP. Recently, Innovecs was honored with the prestigious Global Good Awards for the Employee Engagement & Wellbeing, won gold at the Employer Brand Management Awards, and was included in the Global Top 100 Inspiring Workplaces Ranking. At least 3 years of experience as a DBA/MS SQL Server Developer. Deep and wide understanding of databases’ structure and architecture. Writing complicated T-SQL, stored procedures and functions. Motivated, team player with ability to learn fast and great analysis skills. Performs performance tuning, Query optimization, database installations, configurations, updates, backups and monitoring. Provide support for multiple environments — development, test and production. Anticipates, mitigates, identifies, responds and resolves problems affecting database performance, efficiency, and availability. Improve and support processes to ensure zero application downtime on critical issues or during DB deployment of new application releases. Work according to documentation, guidelines and policies of working procedures. Clearly communicates problems and resolution process to customers and/or management. Demonstrates commitment to outstanding customer service by continuously striving to provide the highest possible customer service; makes recommendations for improving service and efficiencies. Work according to documentation, guidelines and policies of working procedures. Provide support for the operation teams 24/7 — on call in off work hours only for critical issues (one week a month). Maintains database security in compliance with APEI policies, GDPR and PCI security standards.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.innovecs.com/vacancies/6168698002-strong-middle-frontend-engineer-2686/", "company_id": 3387, "source": 3, "skills": "Required Skills\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tJavaScript: strong\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tTypeScript: middle\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tHTML: strong", "title": "Strong Middle Frontend Engineer", "location": "Ukraine\nPoland\nRomania\nRemote", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://jobs.innovecs.com/vacancies/6168698002-strong-middle-frontend-engineer-2686/#block-id-single-vacancies-v2-apply-now", "description": "Join Innovecs Games on your way to adventures and professional growth in the ever-growing Game Development industry! We’re looking for a skilled Middle Frontend Engineer who is eager and can translate bold game ideas into high-quality code structure. Every day we sharpen our skills in co-op quests, working side by side with proven leaders. We value teamwork, knowledge sharing, and curiosity that empower and motivate us to grow. Joining the team, you will be involved in various aspects of game development, starting from the very concept through release and beyond. You will virtuously manipulate 2D and 3D dimensions, blaze a trail of user experience, and will juggle different solutions to realize novel game design ideas. We offer you the opportunity to break barriers, obtain new and strengthen existing expertise! About Innovecs Games At Innovecs Games, we build, create, and deliver robust mobile and iGaming games for clients around the world, helping them do what they do best: engage audiences and support communities. We have a diverse group of 200 talented individuals with expansive knowledge and skill sets; we celebrate their unique talents and encourage personal growth and development. Our company strives for transparency and open communication, both within our team and with our 100+ happy clients. We respect our teammates and cheer for each other’s success. Innovecs Games is a part of Innovecs, a global digital transformation company with business hubs across the US, Europe and Asia, and R&D centers in Poland and Ukraine. We have more than a decade of hands-on experience shipping award-winning products worldwide. Our value to you Flexible hours and remote-first mode; Competitive compensation; Complete Hardware/Software setup – anything you need for work; Open-door culture, transparent communication, and top management at a handshake distance; Health insurance, vacation, sick leaves, holidays, paid maternity/paternity leave; Access to our learning & development center: workshops, webinars, training platform, and edutainment events; Virtual team buildings and social activities to celebrate the Innovecs lifestyle. Experience in slot game development - MUST; 3+ years of JavaScript / TypeScript / HTML / CSS experience; Experience with JavaScript ES6+; Experience with libraries/frameworks (Phaser, Pixi, Three. js); Experience with Spine animations, Adobe Animate CC and Adobe Photoshop; Understanding of performance optimization algorithms, memory management, and threading; Strong knowledge of OOP fundamentals, game programming patterns and SOLID principles; Experience with integrating third-party APIs and analytics; Git advanced knowledge; Creative mind; Good spoken and written English skills (Upper-Intermediate+); Ability to work independently and in a team environment; Ability to give and receive constructive feedback in a positive/professional manner. Would be a plus: * Understanding of casino games mathematics; * Knowledge/experience with WebGL and 3D; * Basic knowledge of Adobe Animate and/or Photoshop; * UI/UX experience, particularly with Figma; * Examples of your code/pet projects. Take part in the full development cycle and ensure smooth development and cooperation between the dev team and product owners from sprint planning to release cycles; Ensure adherence to best practices to achieve high-quality deliverables; Write well-designed, testable, and scalable code within given timelines; Closely cooperate with other team members: QA, Artists, Animators, Designers, Developers; Continually improve code quality throughout the team through automation and code reviews; Maintain up-to-date working knowledge of all appropriate existing technologies as well as new techniques and tech; Contribute innovative and original ideas.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.innovecs.com/vacancies/**********-senior-software-engineer-react-2692/", "company_id": 3387, "source": 3, "skills": "Required Skills\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tReact: strong\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tUI/UX: middle", "title": "Senior Software Engineer (React)", "location": "Ukraine\nRemote", "location_type": "remote", "job_type": null, "min_experience": 4, "max_experience": 4, "apply_link": "https://jobs.innovecs.com/vacancies/**********-senior-software-engineer-react-2692/#block-id-single-vacancies-v2-apply-now", "description": "Innovecs is a global digital services company with a presence in the US, the UK, the EU, Israel, Australia, and Ukraine. Specializing in software solutions, the Innovecs team has experience in Supply Chain, Healthtech, Collaboration Tech, and Gaming. For the fifth year in a row, Innovecs is included in the Inc. 5000, the list of fastest-growing private companies in the US, and a ranking of the best global outsourcing service providers by IAOP. Recently, Innovecs was honored with the prestigious Global Good Awards for the Employee Engagement & Wellbeing, won gold at the Employer Brand Management Awards, and was included in the Global Top 100 Inspiring Workplaces Ranking. Our value to you: Flexible hours and remote-first mode Competitive compensation Complete Hardware/Software setup – anything you need for work Open-door culture, transparent communication, and top management at a handshake distance Health insurance, vacation, sick leaves, holidays, paid maternity/paternity leave Access to our learning & development center: workshops, webinars, training platform, and edutainment events Virtual team buildings and social activities to celebrate the Innovecs lifestyle We need YOU to help us grow. Are you ready for this challenge? Educational Background: Bachelor’s degree in Computer Science, Engineering, or a related field. Experience: Minimum of 4 years of experience in front-end development, with a strong focus on React. Proven experience building and maintaining complex web applications. Experience working in cross-functional teams with designers, back-end developers, and product managers. Prior experience in logistics or supply chain domains is a plus. Technical Skills: Expert-level knowledge of JavaScript (ES6+), TypeScript, and React. Solid understanding of front-end architecture, design systems, and component-driven development. Experience with state management libraries (e. g. , Redux). Familiarity with RESTful APIs. . Strong knowledge of HTML5, CSS3, and modern pre/post-processors (e. g. , SASS, PostCSS, Styled Components). Experience with unit testing and integration testing (e. g. , Jest, Testing Library). Familiarity with build tools and bundlers (e. g. , Webpack, Vite, NX Monorepository). Experience with version control systems like Git. Familiarity with cloud platforms (e. g. , Azure) is a plus. Problem-Solving Skills: Strong analytical thinking and problem-solving capabilities. Ability to optimize front-end performance and troubleshoot complex UI issues. Experience debugging across different browsers and devices. Communication Skills: Excellent written and verbal communication skills. Ability to translate business and user requirements into technical solutions. Strong collaboration skills and ability to give and receive constructive feedback in code reviews. Other Requirements: Experience working in Agile/Scrum environments. Understanding of CI/CD pipelines and front-end deployment processes. Passion for creating clean, maintainable, and accessible code. Develop and Maintain Front-End Applications: Design, build, and maintain high-quality, scalable, and reusable React components. Collaborate with back-end developers and designers to integrate APIs and deliver seamless user experiences. Ensure cross-browser compatibility and mobile responsiveness. Optimize User Interface Performance: Identify front-end performance bottlenecks and implement optimizations. Continuously improve application speed, responsiveness, and accessibility. Implement lazy loading, code splitting, and other advanced performance techniques. UI/UX Implementation and Collaboration: Translate design mockups and wireframes into high-quality code. Work closely with UI/UX designers to bring interactive interfaces to life. Code Quality and Best Practices: Conduct peer code reviews and provide constructive feedback to team members. Follow best practices in code organization, testing (unit/integration), and version control. Contribute to the evolution of front-end architecture and tooling. Cross-Team Collaboration and Communication: Collaborate with product managers and stakeholders to define and deliver product requirements. Proactively suggest and drive improvements to front-end processes and developer experience. Additional Responsibilities (Optional/Desirable): Contribute to design systems and component libraries. Support CI/CD efforts for front-end deployments. Stay current with front-end technologies and trends and advocate for their adoption when appropriate.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.innovecs.com/vacancies/6182807002-lead-software-engineer-go-2703-2/", "company_id": 3387, "source": 3, "skills": "Required Skills\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tGoLang: strong\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tNode.js: strong\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tEnglish: strong", "title": "Lead Software Engineer - Go + Node.js", "location": "Ukraine\nEurope\nRemote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://jobs.innovecs.com/vacancies/6182807002-lead-software-engineer-go-2703-2/#block-id-single-vacancies-v2-apply-now", "description": "We are seeking a Lead Software Engineer to work on the core platform connecting to various ledger and blockchain technologies and contributes to the core distributed infrastructure. This is a hands-on role focused on building performant backend services in a blockchain-enabled financial ecosystem. About ClientAn institutional-grade open network for digital securities based on the FinP2P protocol, where all network nodes are regulated financial entities, on which the market can compete to manage all regulated assets and trades. Through the FinP2P initiative Dozens of the world's top financial institutions are coming together to create a new digital shares network. About Innovecs Innovecs is a global digital transformation tech company with a presence in the US, the UK, the EU, Israel, Australia, and Ukraine. Specializing in software solutions, the Innovecs team has experience in Supply Chain, Healthtech, Software & Hightech, and Gaming. For the fifth year in a row, Innovecs is included in the Inc. 5000 and recognized in IAOP’s ranking of the best global outsourcing service providers. Innovecs is featured in the Global Top 100 Inspiring Workplaces Ranking, and won gold at the Employer Brand Management Awards. Our value to you: Flexible hours and remote-first mode Competitive compensation Complete Hardware/Software setup – anything you need for work Open-door culture, transparent communication, and top management at a handshake distance Health insurance, vacation, sick leaves, holidays, paid maternity/paternity leave Access to our learning & development center: workshops, webinars, training platform, and edutainment events Virtual team buildings and social activities to celebrate the Innovecs lifestyle Caught your interest? Drop us your CV, and let’s chat about the opportunities ahead. 5+ years of backend development experience. Strong proficiency in Go or Node. js (final tech stack to be decided). Experience working with distributed ledger technologies (DLT) / blockchain — required. Solid experience implementing and consuming RESTful APIs. Hands-on experience with PostgreSQL and data modeling. Understanding of microservice architecture and containerized environments (Docker, Kubernetes). Familiarity with financial systems and data flows – advantage. Excellent communication skills in English, both written and verbal. BSc/MSc in Computer Science or related field. Nice to Have Experience working on or contributing to open source blockchain projects. Familiarity with Swagger/OpenAPI, or API Gateways. Experience working in fintech, capital markets, or regulated industries. Design, develop, and maintain backend services (in Go or Node. js) that integrate with external partner systems using REST APIs. Build secure and scalable server-side infrastructure to execute mission-critical financial flows. Implement and manage relational databases using PostgreSQL. Work on blockchain-related integrations and contribute to the core of a DLT-based network. Collaborate with a global team of engineers, product managers, and business stakeholders. Write clean, maintainable, and well-documented code adhering to best practices.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.innovecs.com/vacancies/6172674002-senior-back-end-engineer-2693/", "company_id": 3387, "source": 3, "skills": "Required Skills\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tNode.js: strong\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tReact: strong\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tAWS: strong", "title": "Senior Software Engineer (Node.js/React.js)", "location": "Ukraine\nEurope\nRemote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": 5, "apply_link": "https://jobs.innovecs.com/vacancies/6172674002-senior-back-end-engineer-2693/#block-id-single-vacancies-v2-apply-now", "description": "Are you infatuated with both front-end and back-end development? Then we’d love to hear from you as we're currently seeking a passionate Senior Back-end Engineer (Node. js). You'll be involved in developing both sides of the application — UI features parts and API endpoints. Moreover, you'll have a real impact on your work since we are solid vendors representing only the final result to the client. Sounds interesting? If so, we’d be delighted to receive your resume! Innovecs is a global digital services company with a presence in the US, the UK, the EU, Israel, Australia, and Ukraine. Specializing in software solutions, the Innovecs team has experience in Supply Chain, Healthtech, Collaboration Tech, and Gaming. For the fifth year in a row, Innovecs is included in the Inc. 5000, the list of fastest-growing private companies in the US, and a ranking of the best global outsourcing service providers by IAOP. Recently, Innovecs was honored with the prestigious Global Good Awards for the Employee Engagement & Wellbeing, won gold at the Employer Brand Management Awards, and was included in the Global Top 100 Inspiring Workplaces Ranking. Our value to you: Flexible hours and remote-first mode Competitive compensation Complete Hardware/Software setup – anything you need for work Open-door culture, transparent communication, and top management at a handshake distance Health insurance, vacation, sick leaves, holidays, paid maternity/paternity leave Access to our learning & development center: workshops, webinars, training platform, and edutainment events Virtual team buildings and social activities to celebrate the Innovecs lifestyle Does this resonate with you? Send over your CV, and let's get to know each other better. Experience: Minimum 5 years of professional experience in fullstack development in B2B SaaS, preferably in ESG/sustainability/fintech. Environment: Expert understanding of Node. js and TypeScript Required Technologies: AWS, TypeScript, GraphQL, NoSQL, React, Tailwind Nice to Have: CDK, DynamoDB, Lambda / Serverless, Github Actions, AppSync, SQS / SNS Languages: Candidates must be fluent in English. Design and implement robust, scalable, and secure systems while addressing privacy and compliance concerns. Independently scope and deliver solutions for highly complex projects within the team's domain. Communicate effectively with both technical and non-technical stakeholders to establish shared understanding and context. Mentor team members through detailed feedback on code reviews, demos, and architectural decisions. Take ownership of end-to-end delivery of features—from planning to deployment and monitoring. Provide accurate work estimates and set realistic deadlines while managing scope trade-offs to ensure timely delivery. Actively contribute to the vision for the team's products and systems while balancing technical debt with business goals. Act as a trusted technical leader within the team, influencing decisions on design, processes, and roadmaps. Broaden your impact by considering how your work affects other teams while proactively resolving interdependencies.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.innovecs.com/vacancies/**********-senior-data-engineer-2704/", "company_id": 3387, "source": 3, "skills": "Required Skills\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tData Analytics: strong\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tEnglish: strong\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tDBA: strong", "title": "Senior Data Engineer (with ETL expertise)", "location": "Ukraine\nPoland\nRomania\nSpain\nRemote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://jobs.innovecs.com/vacancies/**********-senior-data-engineer-2704/#block-id-single-vacancies-v2-apply-now", "description": "Innovecs is a global digital services company with a presence in the US, the UK, the EU, Israel, Australia, and Ukraine. Specializing in software solutions, the Innovecs team has experience in Supply Chain, Healthtech, Collaboration Tech, and Gaming. For the fifth year in a row, Innovecs is included in the Inc. 5000, the list of fastest-growing private companies in the US, and a ranking of the best global outsourcing service providers by IAOP. Recently, Innovecs was honored with the prestigious Global Good Awards for the Employee Engagement & Wellbeing, won gold at the Employer Brand Management Awards, and was included in the Global Top 100 Inspiring Workplaces Ranking. Our value to you: Flexible hours and remote-first mode Competitive compensation Complete Hardware/Software setup – anything you need for work Open-door culture, transparent communication, and top management at a handshake distance Health insurance, vacation, sick leaves, holidays, paid maternity/paternity leave Access to our learning & development center: workshops, webinars, training platform, and edutainment events Virtual team buildings and social activities to celebrate the Innovecs lifestyle We need YOU to help us grow. Are you ready for this challenge? 5+ years of experience as a Data Engineer or in a similar backend engineering role. Proven experience building production-grade data pipelines (ETL/ELT). Strong proficiency with SQL and TSQL in conjunction with Python or Java. Solid understanding of cloud platforms (e. g. ,Azure AWS, or GCP). Strong grasp of data modeling, data architecture, and data governance principles. Strong grasp of data modeling, data architecture, and data governance principles. Excellent problem-solving skills and the ability to work independently in a fast-paced environment. Strong knowledge of database normalization, indexing strategies, and execution plans. Experience in Supply Chain & Logistics domain would be preferred. Design and implement robust, scalable, and high-performance data pipelines to ingest, process, and serve structured and unstructured data from various internal and external sources (e. g. , TMS, WMS, ERP, telematics, IoT). Optimize system performance and scalability for large logistics and operational data volumes. Build and maintain data models, data marts, and data warehouses/lakes that support analytics and decision-making. Partner with the team to translate business requirements into technical data solutions. Implement data quality, lineage, and observability practices. Drive ETL/ELT best practices, automation, and operational excellence in data workflows. Ensure security, compliance, and privacy standards in data processing. Take ownership of production support.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.innovecs.com/vacancies/**********-ai-artist-producer-2705/", "company_id": 3387, "source": 3, "skills": "Required Skills", "title": "AI Artist/Producer", "location": "Ukraine\nPoland\nWorldwide\nRemote", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://jobs.innovecs.com/vacancies/**********-ai-artist-producer-2705/#block-id-single-vacancies-v2-apply-now", "description": "We're seeking AI artists and producers who will work on diverse projects ranging from commercial campaigns to experimental art installations. Innovecs is a global digital services company with a presence in the US, the UK, the EU, Israel, Australia, and Ukraine. Specializing in software solutions, the Innovecs team has experience in Supply Chain, Healthtech, Collaboration Tech, and Gaming. For the fifth year in a row, Innovecs is included in the Inc. 5000, the list of fastest-growing private companies in the US, and a ranking of the best global outsourcing service providers by IAOP. Recently, Innovecs was honored with the prestigious Global Good Awards for the Employee Engagement & Wellbeing, won gold at the Employer Brand Management Awards, and was included in the Global Top 100 Inspiring Workplaces Ranking. Our value to you: Flexible hours and remote-first mode Competitive compensation Complete Hardware/Software setup – anything you need for work Open-door culture, transparent communication, and top management at a handshake distance Health insurance, vacation, sick leaves, holidays, paid maternity/paternity leave Access to our learning & development center: workshops, webinars, training platform, and edutainment events Virtual team buildings and social activities to celebrate the Innovecs lifestyle Does this resonate with you? Send over your CV, and let's get to know each other better. 3+ years of professional artistic experience with demonstrated AI tool proficiency. Strong portfolio showcasing AI-generated artwork across multiple mediums. Expert knowledge of at least 3 major AI art generation platforms. Understanding of prompt engineering and AI workflow optimization. Traditional art background preferred (illustration, digital art, photography, or video). Ability to work independently and manage multiple projects simultaneously. Strong communication skills for client interaction and team collaboration. Adaptability to rapidly evolving AI technology landscape. Create high-quality AI-generated artwork for commercial and artistic projects. Develop and refine AI workflows to optimize creative output. Collaborate with clients to understand project requirements and deliver exceptional results. Stay current with emerging AI art technologies and integrate them into production workflows. Mentor junior team members and share knowledge of AI techniques. Participate in creative brainstorming sessions and concept development. Maintain organized project files and documentation of AI processes. Quality control and post-processing of AI-generated content.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.innovecs.com/vacancies/**********-comfyui-web-developer-freelance-2706/", "company_id": 3387, "source": 3, "skills": "Required Skills\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tHTML: strong\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tJavaScript: strong", "title": "ComfyUI Web developer (freelance)", "location": "Poland\nUkraine\nWorldwide", "location_type": "remote", "job_type": "contract", "min_experience": 1, "max_experience": null, "apply_link": "https://jobs.innovecs.com/vacancies/**********-comfyui-web-developer-freelance-2706/#block-id-single-vacancies-v2-apply-now", "description": "We are seeking a skilled freelance developer with specific expertise in ComfyUI to create and maintain web-based configuration interfaces for AI production workflows. Innovecs is a global digital services company with a presence in the US, the UK, the EU, Israel, Australia, and Ukraine. Specializing in software solutions, the Innovecs team has experience in Supply Chain, Healthtech, Collaboration Tech, and Gaming. For the fifth year in a row, Innovecs is included in the Inc. 5000, the list of fastest-growing private companies in the US, and a ranking of the best global outsourcing service providers by IAOP. Recently, Innovecs was honored with the prestigious Global Good Awards for the Employee Engagement & Wellbeing, won gold at the Employer Brand Management Awards, and was included in the Global Top 100 Inspiring Workplaces Ranking. Our value to you: Flexible hours and remote-first mode Competitive compensation Complete Hardware/Software setup – anything you need for work Open-door culture, transparent communication, and top management at a handshake distance Health insurance, vacation, sick leaves, holidays, paid maternity/paternity leave Access to our learning & development center: workshops, webinars, training platform, and edutainment events Virtual team buildings and social activities to celebrate the Innovecs lifestyle Does this resonate with you? Send over your CV, and let's get to know each other better. 1+ year of hands-on experience with ComfyUI. Strong web development skills (HTML, CSS, JavaScript). Experience with workflow automation and configuration management. Understanding of AI/ML pipeline concepts. Ability to create intuitive user interfaces for technical workflows. Experience with API integration and data management. Self-motivated with excellent time management skills. Available for flexible scheduling based on project needs. Develop web-based interfaces for ComfyUI workflow configuration. Create intuitive forms and controls for workflow parameter adjustment. Implement real-time workflow visualization and monitoring features. Maintain and update existing web configuration tools. Troubleshoot and optimize ComfyUI workflow performance. Document configuration processes and create user guides. Collaborate with AI artists to understand workflow requirements. Ensure cross-browser compatibility and responsive design.", "ctc": null, "currency": null, "meta": {}}]