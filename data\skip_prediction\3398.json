[{"jd_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000005502005/Full-Stack-Developer-Python-React-?source=CareerSite", "company_id": 3398, "source": 3, "skills": ", Job listing    Job details, Date Opened   04/05/2024        Job Type   Full time        Industry   IT Services        Work Experience   4-6 years        City   Ahmedabad        State/Province   Gujarat        Country   India        Zip/Postal Code   380060, Develop accounting and fintech software products AI & ML Models using python and react.jsIntegrate APIs with React applications, enhancing user interface functionality and responsiveness.Integrate advanced AI technology from OpenAI ( ChatGPT ) for developing innovative products and elevating customer experience.Stay updated on the latest trends in AI development and integration technologies.You will be responsible for work on development and integration of Generative AI based technologies in various client projects..Contribute to open-source projects, fostering community-driven innovation and collaboration., Must have strong experience in using Python as a backend technology to develop web applications.Must have strong experience in use of Relational Database for develop high performing APIsHaving knowledge of development of ML and AI models using python would be an added advantage., Good to have experience with other NoSQL databases like mangoDBGood to have experience of AWS and Azure for cloud native product development Good to have domain knowledge of fintech and accounting, applying expertise in the development and optimization of financial applications., Communication: Must have the ability to effectively communicate in English with clients, and stakeholders, both verbally and in writing. Problem-solving: Able to analyze and find solutions to complex problems. Time management: Able to prioritize tasks and manage time effectively to meet deadlines. Adaptability: Quickly adapt to new technologies and tools. Collaboration: Work well with others, both as part of a team and in a leadership role.Attention to detail: Able to pay close attention in order to ensure the quality of code and avoid bugs.Continuous learning: The desire to continuously learn and improve one's skills and knowledge in order to stay up-to-date with industry trends and advancements. Initiative: The ability to take initiative and be proactive in finding solutions, without waiting for instructions or direction. Creativity: The ability to think creatively and come up with innovative solutions to problems., 5-day work-week (Last Saturday working)Access to the latest technology and state-of-the-art equipmentEmphasis on continuous feedback and improvementA flexible working model with a blend of remote and in-office workEmployee financial assistanceCollaborative and teamwork-oriented environmentRegular annual health check-upsSuperior and spacious workstationsRewards and recognition programsAccess to top-notch training and development opportunitiesScope for both professional and personal growth., View all jobs      Visit website", "title": "Full Stack Developer (Python + React)", "location": "Ahmedabad, India   | Posted on 04/05/2024", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000005502005/Full-Stack-Developer-Python-React-?source=CareerSite", "description": "Job Summary: As a Python + React Developer focused on research and development, your role is instrumental in shaping the future of our organization. With advanced technical skills, you will drive innovation, contributing to both the growth of our research capabilities and the overall expansion of the organization. . Key responsibilities:Develop accounting and fintech software products AI & ML Models using python and react. jsIntegrate APIs with React applications, enhancing user interface functionality and responsiveness. Integrate advanced AI technology from OpenAI ( ChatGPT ) for developing innovative products and elevating customer experience. Stay updated on the latest trends in AI development and integration technologies. You will be responsible for work on development and integration of Generative AI based technologies in various client projects. . Contribute to open-source projects, fostering community-driven innovation and collaboration. RequirementsTechnical Skills:Primary Technical Skills:Must have strong experience in using Python as a backend technology to develop web applications. Must have strong experience in use of Relational Database for develop high performing APIsHaving knowledge of development of ML and AI models using python would be an added advantage. Secondary Technical Skill:Good to have experience with other NoSQL databases like mangoDBGood to have experience of AWS and Azure for cloud native product development Good to have domain knowledge of fintech and accounting, applying expertise in the development and optimization of financial applications. Non-technical Skills: Communication: Must have the ability to effectively communicate in English with clients, and stakeholders, both verbally and in writing. Problem-solving: Able to analyze and find solutions to complex problems. Time management: Able to prioritize tasks and manage time effectively to meet deadlines. Adaptability: Quickly adapt to new technologies and tools. Collaboration: Work well with others, both as part of a team and in a leadership role. Attention to detail: Able to pay close attention in order to ensure the quality of code and avoid bugs. Continuous learning: The desire to continuously learn and improve one's skills and knowledge in order to stay up-to-date with industry trends and advancements. Initiative: The ability to take initiative and be proactive in finding solutions, without waiting for instructions or direction. Creativity: The ability to think creatively and come up with innovative solutions to problems. BenefitsThe happiness of Satva Solutions:5-day work-week (Last Saturday working)Access to the latest technology and state-of-the-art equipmentEmphasis on continuous feedback and improvementA flexible working model with a blend of remote and in-office workEmployee financial assistanceCollaborative and teamwork-oriented environmentRegular annual health check-upsSuperior and spacious workstationsRewards and recognition programsAccess to top-notch training and development opportunitiesScope for both professional and personal growth.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000006962038/Senior-Software-Developer-net-?source=CareerSite", "company_id": 3398, "source": 3, "skills": ", Job listing    Job details, Date Opened   06/12/2024        Job Type   Full time        Industry   IT Services        Work Experience   4-6 years        City   Ahmedabad        State/Province   Gujarat        Country   India        Zip/Postal Code   380060, Develop, Unit test, and deploy custom web applicationsDevelop, and maintain efficient, reusable, and reliable codePerformance tuning of database and web applications (Add in 3 + Years team members)Ensure the best possible performance, quality, and responsiveness of applicationsGather requirements directly from the client and make a development plan and forecast timeline for completion. Provide demo of work to client sprint wiseTrain, mentor, and rescue junior developers when they are facing blockages in development. Do the code review of self and juniors. Help TL with quarterly appraisal feedback of mid-developers working with you.Responsible for developing a high-quality product for the clientDevelop the training plan for new technology for junior and mid developersTake interviewsLearn new technology and help juniors for skills development, Must have a clear concept of OOPSMust have strong hands-on experience in ASP.Net MVC, ASP .Net Core C#, Entity Framework, LINQ, JavascriptMust be able to write efficient SQL Stored ProcedureMust be proficient in technical analysis of requirement.Must have API development and integrationMust be able to analyze and integrate 3rd party APIsDifferent types of authentication and authorizationVersion Control: Utilizing version control systems such as Git for efficient source code management, team collaboration, and maintaining a comprehensive code history.Coding Standards: Adhering to established coding standards, best practices, and guidelines to ensure the development of a consistent and high-quality codebase.Security Best Practices: Displaying an understanding of security vulnerabilities and employing best practices for secure coding, thereby enhancing protection against common cyber threats and attacks., Good to have Experience of Microsoft Azure Deployment.Good to have knowledge on consuming SOAP services.Should have a good understanding of Agile / Scrum FrameworkHaving relevant Microsoft certification on the latest technology would be added as an advantage, Communication: Must have the ability to effectively communicate with colleagues, clients, and stakeholders, both verbally and in writing. Problem-solving: Able to analyze and find solutions to complex problems. Time management: Able to prioritize tasks and manage time effectively to meet deadlines. Adaptability: Quickly adapt to new technologies and tools. Collaboration: Work well with others, both as part of a team and in a leadership role. Attention to detail: Able to pay close attention in order to ensure the quality of code and avoid bugs.Continuous learning: The desire to continuously learn and improve one's skills and knowledge in order to stay up-to-date with industry trends and advancements. Initiative: The ability to take initiative and be proactive in finding solutions, without waiting for instructions or direction. Creativity: The ability to think creatively and come up with innovative solutions to problems. Organization: The ability to keep track of multiple tasks, projects, and deadlines, and maintain an organized workflow., A 5-day workweekOpportunities to work with the latest technology and state-of-the-art equipmentA culture of continuous feedbackA flexible working model that combines remote and in-office workAssistance with employee financial needsAn environment that encourages collaboration and teamworkAnnual health check-upsSuperior and spacious workstationsRewards and recognition programsAccess to the best training and development opportunitiesOpportunities for both professional and personal development, View all jobs      Visit website", "title": "Senior Software Developer (.net)", "location": "Ahmedabad, India   | Posted on 06/12/2024", "location_type": "remote", "job_type": null, "min_experience": 10, "max_experience": null, "apply_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000006962038/Senior-Software-Developer-net-?source=CareerSite", "description": "Company Overview:​Satva Solutions is a reputed, dynamic and innovative Accounting Integration and FinTech Product company that has been serving our valued customers for 10+ years. Our dedicated team works collaboratively to deliver innovative solutions and provide exceptional customer service. We take pride in our commitment to Crafting intelligent business solutions to increase productivity, automate workflows, adhere to compliances, and gain insights to make financial decisions. Job Summary: We are looking for a highly experienced Senior Developer who will be responsible for interacting with the TL/PM to plan project schedules and technical direction. Develop high-level system design diagrams for program design, coding, testing, debugging and documentation. Key responsibilities: Develop, Unit test, and deploy custom web applicationsDevelop, and maintain efficient, reusable, and reliable codePerformance tuning of database and web applications (Add in 3 + Years team members)Ensure the best possible performance, quality, and responsiveness of applicationsGather requirements directly from the client and make a development plan and forecast timeline for completion. Provide demo of work to client sprint wiseTrain, mentor, and rescue junior developers when they are facing blockages in development. Do the code review of self and juniors. Help TL with quarterly appraisal feedback of mid-developers working with you. Responsible for developing a high-quality product for the clientDevelop the training plan for new technology for junior and mid developersTake interviewsLearn new technology and help juniors for skills developmentRequirementsTechnical Skills:Primary SkillMust have a clear concept of OOPSMust have strong hands-on experience in ASP. Net MVC, ASP . Net Core C#, Entity Framework, LINQ, JavascriptMust be able to write efficient SQL Stored ProcedureMust be proficient in technical analysis of requirement. Must have API development and integrationMust be able to analyze and integrate 3rd party APIsDifferent types of authentication and authorizationVersion Control: Utilizing version control systems such as Git for efficient source code management, team collaboration, and maintaining a comprehensive code history. Coding Standards: Adhering to established coding standards, best practices, and guidelines to ensure the development of a consistent and high-quality codebase. Security Best Practices: Displaying an understanding of security vulnerabilities and employing best practices for secure coding, thereby enhancing protection against common cyber threats and attacks. Secondary SkillGood to have Experience of Microsoft Azure Deployment. Good to have knowledge on consuming SOAP services. Should have a good understanding of Agile / Scrum FrameworkHaving relevant Microsoft certification on the latest technology would be added as an advantageNon-technical Skills: Communication: Must have the ability to effectively communicate with colleagues, clients, and stakeholders, both verbally and in writing. Problem-solving: Able to analyze and find solutions to complex problems. Time management: Able to prioritize tasks and manage time effectively to meet deadlines. Adaptability: Quickly adapt to new technologies and tools. Collaboration: Work well with others, both as part of a team and in a leadership role. Attention to detail: Able to pay close attention in order to ensure the quality of code and avoid bugs. Continuous learning: The desire to continuously learn and improve one's skills and knowledge in order to stay up-to-date with industry trends and advancements. Initiative: The ability to take initiative and be proactive in finding solutions, without waiting for instructions or direction. Creativity: The ability to think creatively and come up with innovative solutions to problems. Organization: The ability to keep track of multiple tasks, projects, and deadlines, and maintain an organized workflow. BenefitsHappiness of Satva: ​A 5-day workweekOpportunities to work with the latest technology and state-of-the-art equipmentA culture of continuous feedbackA flexible working model that combines remote and in-office workAssistance with employee financial needsAn environment that encourages collaboration and teamworkAnnual health check-upsSuperior and spacious workstationsRewards and recognition programsAccess to the best training and development opportunitiesOpportunities for both professional and personal development", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000006970137/Junior-Software-Developer?source=CareerSite", "company_id": 3398, "source": 3, "skills": ", Job listing    Job details, Date Opened   06/12/2024        Job Type   Full time        Industry   Financial Services        Work Experience   2-4 years        City   Ahmedabad        State/Province   Gujarat        Country   India        Zip/Postal Code   380060, 5-day work-week (last sat working)Access to the latest technology and state-of-the-art equipmentEmphasis on continuous feedback and improvementA flexible working model with a blend of remote and in-office workEmployee financial assistanceCollaborative and teamwork-oriented environmentRegular annual health check-upsSuperior and spacious workstationsRewards and recognition programsAccess to top-notch training and development opportunitiesScope for both professional and personal growth, View all jobs      Visit website", "title": "Junior Software Developer", "location": "Ahmedabad, India   | Posted on 06/12/2024", "location_type": "remote", "job_type": null, "min_experience": 10, "max_experience": null, "apply_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000006970137/Junior-Software-Developer?source=CareerSite", "description": "Company OverviewSatva Solutions is a reputed, dynamic and innovative Accounting Integration and FinTech Product company that has been serving our valued customers for 10+ years. Our dedicated team works collaboratively to deliver innovative solutions and provide exceptional customer service. We take pride in our commitment to Crafting intelligent business solutions to increase productivity, automate workflows, adhere to compliances, and gain insights to make financial decisions. Job SummaryWe are looking for entry-level software developers who will assist the development team with all aspects of software design and coding. Your primary role is to learn the codebase, attend design meetings, write basic code, fix bugs, and assist seniors in all design-related tasks. Key responsibilities❖ Give and Take the new technology training ❖ Develop and Unit test assigned tasks as per UI Mockup ❖ Provide daily progress report to seniors ❖ Keep task status up to date in project management ❖ Write the acceptance criteria based on the user story Experience in development of web applications ❖ Must have strong hands-on experience in ASP. Net MVC with jQuery and Ajax ❖ Must have a clear Concept of OOPS ❖ Must be able to write efficient SQL Stored Procedure ❖ Good to have Experience of Microsoft Azure Deployment ❖ Should have a good understanding of Agile Methodologies ❖ Quickbooks Project ❖ Must have knowledge of accounting RequirementsTechnical SkillsPrimary Skill ❖ Must have strong hands-on experience in ASP. Net MVC, SQL Server ❖ Must be able to practically use the OOPS concept in c# 20 Designation ❖ Must know CRUD operations using EF or Ado. net with Stored Procedure ❖ Should have the debugging skill Secondary Skill ❖ Should have a good understanding of Agile Methodologies ❖ Should pay good attention to details ❖ Having a Proactive attitude would be added as an advantage Non-technical Skills❖ Communication: Must have the ability to effectively communicate with colleagues, clients, and stakeholders, both verbally and in writing. ❖ Problem-solving: Able to analyze and find solutions to complex problems. ❖ Time management: Able to prioritize tasks and manage time effectively to meet deadlines. ❖ Adaptability: Quickly adapt to new technologies and tools. ❖ Collaboration: Work well with others, both as part of a team and in a leadership role. Attention to detail: Able to pay close attention in order to ensure the quality of code and avoid bugs. ❖ Continuous learning: The desire to continuously learn and improve one's skills and knowledge in order to stay up-to-date with industry trends and advancements. ❖ Initiative: The ability to take initiative and be proactive in finding solutions, without waiting for instructions or directions. ❖ Creativity: The ability to think creatively and come up with innovative solutions to problems. ❖ Organization: The ability to keep track of multiple tasks, projects, and deadlines, and maintain an organized workflow. Educational Qualification: ❖ Bachelor or Master in Computer Science, Software Engineering, or a related field. BenefitsHappiness at Satva Solutions5-day work-week (last sat working)Access to the latest technology and state-of-the-art equipmentEmphasis on continuous feedback and improvementA flexible working model with a blend of remote and in-office workEmployee financial assistanceCollaborative and teamwork-oriented environmentRegular annual health check-upsSuperior and spacious workstationsRewards and recognition programsAccess to top-notch training and development opportunitiesScope for both professional and personal growth", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000007731014/Business-Development-Executive?source=CareerSite", "company_id": 3398, "source": 3, "skills": ", Job listing    Job details, Date Opened   09/20/2024        Job Type   Full time        Industry   IT Services        Work Experience   2-4 years        City   Ahmedabad        State/Province   Gujarat        Country   India        Zip/Postal Code   380060, The primary responsibility is to generate leads for the company's services and meet the quarterly sales targetsDeveloping and executing sales and marketing strategies.Give presentations and demonstrations to potential clients.Set up introductory calls between the clients & development teamTake training on sales and give training to juniors for improving their performance.Follow the sales process rigorouslyCommunicating with and informing existing clients in a way that supports an ongoing relationship.Excellent knowledge of quotations, proposals, and Cover letters.Manage all lead information in the CRM tool.Oversee, maintain, and upgrade existing client accounts to ensure ongoing client satisfaction., Communication: Must have the ability to effectively communicate with colleagues, clients, and stakeholders, both verbally and in writing.Client relationship: Be responsive to their inquiries, address concerns promptly, and keep them informed about relevant updates and opportunities.Sales and Marketing Skills: Marketing strategies or negotiation skills can be advantageous for a Business Development Executive role.Time management: Able to prioritize tasks and manage time effectively to meet deadlines.Collaboration: Work with BA, TA and PMs for best outcome for projects and product development.Attention to detail: Able to pay close attention in order to ensure the quality of code and avoid bugs.Continuous learning: The desire to continuously learn and improve one's skills and knowledge in order to stay up-to-date with industry trends and advancements.Initiative: The ability to take initiative and be proactive in ﬁnding solutions, without waiting for instructions or direction.Organization: The ability to keep track of multiple tasks, projects, and deadlines, and maintain an organized workﬂow., Bachelor's or Master’s Degree in Computer Science, Software Engineering, Master in Business Administration, or a related ﬁeld., View all jobs      Visit website", "title": "Business Development Executive", "location": "Ahmedabad, India   | Posted on 09/20/2024", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000007731014/Business-Development-Executive?source=CareerSite", "description": "Job Summary:We are looking for Business development executives who are familiar with fundamental drivers to source business and new clients. They should be creative in their approach and thus spend the most crucial time executing a business plan and creating long-term business value. As such, business development executives play an integral role in companies' longevity. Roles G Responsibilities:The primary responsibility is to generate leads for the company's services and meet the quarterly sales targetsDeveloping and executing sales and marketing strategies. Give presentations and demonstrations to potential clients. Set up introductory calls between the clients & development teamTake training on sales and give training to juniors for improving their performance. Follow the sales process rigorouslyCommunicating with and informing existing clients in a way that supports an ongoing relationship. Excellent knowledge of quotations, proposals, and Cover letters. Manage all lead information in the CRM tool. Oversee, maintain, and upgrade existing client accounts to ensure ongoing client satisfaction. Key Competencies:Communication: Must have the ability to effectively communicate with colleagues, clients, and stakeholders, both verbally and in writing. Client relationship: Be responsive to their inquiries, address concerns promptly, and keep them informed about relevant updates and opportunities. Sales and Marketing Skills: Marketing strategies or negotiation skills can be advantageous for a Business Development Executive role. Time management: Able to prioritize tasks and manage time effectively to meet deadlines. Collaboration: Work with BA, TA and PMs for best outcome for projects and product development. Attention to detail: Able to pay close attention in order to ensure the quality of code and avoid bugs. Continuous learning: The desire to continuously learn and improve one's skills and knowledge in order to stay up-to-date with industry trends and advancements. Initiative: The ability to take initiative and be proactive in ﬁnding solutions, without waiting for instructions or direction. Organization: The ability to keep track of multiple tasks, projects, and deadlines, and maintain an organized workﬂow. Educational Qualifications:Bachelor's or Master’s Degree in Computer Science, Software Engineering, Master in Business Administration, or a related ﬁeld.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000008387183/Senior-Business-Analyst?source=CareerSite", "company_id": 3398, "source": 3, "skills": ", Job listing    Job details, Date Opened   03/11/2025        Job Type   Full time        Industry   IT Services        Work Experience   4-6 years        City   Ahmedabad        State/Province   Gujarat        Country   India        Zip/Postal Code   380060, Designation: Senior Business Analyst (Pre Sales)Department: Pre-SalesExperience: Required 4+YearsEmployment Type: Full timeLocation: Ahmedabad, Conduct discovery sessions with potential clients to gather business requirements and pain pointsPerform research and prepare proposed solutions during the discovery sprintPrepare all required documents like minutes of meetings, SOW, BRD,FRD etcDevelop compelling proof of concept demonstrations, Rapid prototyping & quick mockups for client’s requirementsWork closely with sales executives to complete the proposal.Keep yourself up to date with the latest trends and practices in company’s servicesHas quality experience in implementing Projects with cross functional teamsExcellence in stakeholder management and communicationEnsure that product is developed as per requirements by providing feedback during internal demo.Prepare proof of concept during discovery sprint by implementing different software.Provide a demo of newly developed features to the client at the end of the sprint.Complete the discovery in the defined time frames based on criticality of the project.Experience with common SaaS integration patterns and APIsMust be aware of the Scrum Framework (Agile methodology)Any business analysis certification will be considered beneficial, Must have strong hands on experience in using Google Spreadsheet, Slide and Docs, Draw.io, Miro.Must have knowledge of finance domain & accounting fundamentalsStrong communication, presentation, organizational and interpersonal skillsAbility to work independently and Good Team PlayerExcellent analysis, problem-solving, team, conflict management, and time management skills, Must be BE in Computers \\ IT or equivalent technical degreeMBA - finance will be considered beneficial, 5-day work-week (Last Saturday working)Access to the latest technology and state-of-the-art equipmentEmphasis on continuous feedback and improvementA flexible working model with a blend of remote and in-office workEmployee financial assistanceCollaborative and teamwork-oriented environmentRegular annual health check-upsSuperior and spacious workstationsRewards and recognition programsAccess to top-notch training and development opportunitiesScope for both professional and personal growth., View all jobs      Visit website", "title": "Senior Business Analyst", "location": "Ahmedabad, India   | Posted on 03/11/2025", "location_type": "remote", "job_type": null, "min_experience": 4, "max_experience": null, "apply_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000008387183/Senior-Business-Analyst?source=CareerSite", "description": "Job DescriptionDesignation: Senior Business Analyst (Pre Sales)Department: Pre-SalesExperience: Required 4+YearsEmployment Type: Full timeLocation: AhmedabadJob Summary:We are looking for a skilled Senior Business Analyst(Pre Sales), who will be primarily responsible for understanding the business requirements, completing discovery sprint in least time, preparing the technical and functional documents to keep client and team on the same page. Responsibilities:Conduct discovery sessions with potential clients to gather business requirements and pain pointsPerform research and prepare proposed solutions during the discovery sprintPrepare all required documents like minutes of meetings, SOW, BRD,FRD etcDevelop compelling proof of concept demonstrations, Rapid prototyping & quick mockups for client’s requirementsWork closely with sales executives to complete the proposal. Keep yourself up to date with the latest trends and practices in company’s servicesHas quality experience in implementing Projects with cross functional teamsExcellence in stakeholder management and communicationEnsure that product is developed as per requirements by providing feedback during internal demo. Prepare proof of concept during discovery sprint by implementing different software. Provide a demo of newly developed features to the client at the end of the sprint. Complete the discovery in the defined time frames based on criticality of the project. Experience with common SaaS integration patterns and APIsMust be aware of the Scrum Framework (Agile methodology)Any business analysis certification will be considered beneficialRequirementsEssential Skills:Must have strong hands on experience in using Google Spreadsheet, Slide and Docs, Draw. io, Miro. Must have knowledge of finance domain & accounting fundamentalsStrong communication, presentation, organizational and interpersonal skillsAbility to work independently and Good Team PlayerExcellent analysis, problem-solving, team, conflict management, and time management skillsEducation Qualification:Must be BE in Computers \\ IT or equivalent technical degreeMBA - finance will be considered beneficialBenefitsThe happiness of Satva Solutions:5-day work-week (Last Saturday working)Access to the latest technology and state-of-the-art equipmentEmphasis on continuous feedback and improvementA flexible working model with a blend of remote and in-office workEmployee financial assistanceCollaborative and teamwork-oriented environmentRegular annual health check-upsSuperior and spacious workstationsRewards and recognition programsAccess to top-notch training and development opportunitiesScope for both professional and personal growth.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000008550034/Junior-Business-Analyst?source=CareerSite", "company_id": 3398, "source": 3, "skills": ", Job listing    Job details, Date Opened   02/18/2025        Job Type   Full time        Industry   IT Services        Work Experience   2-4 years        City   Ahmedabad        State/Province   Gujarat        Country   India        Zip/Postal Code   380060, Designation: Junior Business AnalystDepartment: Pre SalesExperience Required: 2 to 4 YearsEmployment Type: Full timeLocation: Zambia (South Africa), Gather information and understand business requirements from the client and document them.Perform research and prepare proof of concept during the discovery sprint.Lead the implementation and onboarding process for clients in Zambia, ensuring smooth adoption of the payroll SaaS product.Provide training and support to clients during the initial setup and onboarding phase.Prepare documents like minutes of meetings, requirements documents, BRD, architecture diagrams, flow charts, etc., and help sales executives complete the proposal.Prepare low-fidelity mockups during the discovery sprints.Have logical capabilities to replicate the client’s problem and suggest solutions.Keep yourself up-to-date with the latest trends and practices in the company’s services.Knowledge of basic accounting and finance fundamentals will be considered an added advantage for this role., Communication: Must be able to communicate with colleagues effectively, clients, and stakeholders, both verbally and in writing.Client relationship: Be responsive to their new Client in the discovery phase, address concerns promptly, and keep the informed about relevant updates and opportunities.Documentation : Must have strong hands on experience in using Google Spreadsheet, Slide and Docs, should have hands on experience in Draw.io, Miro etc.Time management: Able to prioritize tasks and manage time effectively to finish the Discovery Sprint in time.++Collaboration: Work well with others, both as part of a team and in a leadership role.Attention to detail: Able to pay close attention in order to ensure the quality of code and avoid bugs.Continuous learning: The desire to learn continuously and improve one's skills and knowledge in order to stay up-to-date with industry trends and advancements.Initiative: The ability to take initiative and be proactive in finding solutions, without waiting for instructions or direction Take Ownership for assigned tasks.Creativity: The ability to think creatively and come up with innovative solutions to problems.Organization: The ability to keep track of multiple tasks, projects, and deadlines, and maintain an organized workflow, Bachelor's or Master’s Degree in Computer Science, Software Engineering, Master in Business Administration, or a related field., 5-day work-week (Last Saturday working)Access to the latest technology and state-of-the-art equipmentEmphasis on continuous feedback and improvementA flexible working model with a blend of remote and in-office workEmployee financial assistanceCollaborative and teamwork-oriented environmentRegular annual health check-upsSuperior and spacious workstationsRewards and recognition programsAccess to top-notch training and development opportunitiesScope for both professional and personal growth., View all jobs      Visit website", "title": "Junior Business Analyst", "location": "Ahmedabad, India   | Posted on 02/18/2025", "location_type": "onsite", "job_type": null, "min_experience": 4, "max_experience": 4, "apply_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000008550034/Junior-Business-Analyst?source=CareerSite", "description": "Job Description:Designation: Junior Business AnalystDepartment: Pre SalesExperience Required: 2 to 4 YearsEmployment Type: Full timeLocation: Zambia (South Africa)Job Summary:We are seeking a dynamic and enthusiastic Junior Business Analyst to join our team. The ideal candidate will play a pivotal role in assisting with the analysis, documentation, and improvement of business processes, systems, and operations. The Pre-Sales Junior Business Analyst will tightly collaborate with the Client and Sales team, leading the Discovery phase and striving to win the client’s confidence. This is an onsite role based in Zambia for a 1-year assignment, where the candidate will be responsible for implementing and onboarding clients onto our payroll SaaS product. The role requires strong client interaction, business process understanding, and hands-on involvement in configuring and optimizing the payroll system for smooth adoption. Roles & Responsibilities:Gather information and understand business requirements from the client and document them. Perform research and prepare proof of concept during the discovery sprint. Lead the implementation and onboarding process for clients in Zambia, ensuring smooth adoption of the payroll SaaS product. Provide training and support to clients during the initial setup and onboarding phase. Prepare documents like minutes of meetings, requirements documents, BRD, architecture diagrams, flow charts, etc. , and help sales executives complete the proposal. Prepare low-fidelity mockups during the discovery sprints. Have logical capabilities to replicate the client’s problem and suggest solutions. Keep yourself up-to-date with the latest trends and practices in the company’s services. Knowledge of basic accounting and finance fundamentals will be considered an added advantage for this role. Requirements<PERSON>ey Competencies:Communication: Must be able to communicate with colleagues effectively, clients, and stakeholders, both verbally and in writing. Client relationship: Be responsive to their new Client in the discovery phase, address concerns promptly, and keep the informed about relevant updates and opportunities. Documentation : Must have strong hands on experience in using Google Spreadsheet, Slide and Docs, should have hands on experience in Draw. io, Miro etc. Time management: Able to prioritize tasks and manage time effectively to finish the Discovery Sprint in time. ++Collaboration: Work well with others, both as part of a team and in a leadership role. Attention to detail: Able to pay close attention in order to ensure the quality of code and avoid bugs. Continuous learning: The desire to learn continuously and improve one's skills and knowledge in order to stay up-to-date with industry trends and advancements. Initiative: The ability to take initiative and be proactive in finding solutions, without waiting for instructions or direction Take Ownership for assigned tasks. Creativity: The ability to think creatively and come up with innovative solutions to problems. Organization: The ability to keep track of multiple tasks, projects, and deadlines, and maintain an organized workflow Educational Qualifications:Bachelor's or Master’s Degree in Computer Science, Software Engineering, Master in Business Administration, or a related field. BenefitsBenefitsThe happiness of Satva Solutions:5-day work-week (Last Saturday working)Access to the latest technology and state-of-the-art equipmentEmphasis on continuous feedback and improvementA flexible working model with a blend of remote and in-office workEmployee financial assistanceCollaborative and teamwork-oriented environmentRegular annual health check-upsSuperior and spacious workstationsRewards and recognition programsAccess to top-notch training and development opportunitiesScope for both professional and personal growth.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000008886015/Software-Developer-Net-Core-React-?source=CareerSite", "company_id": 3398, "source": 3, "skills": ", Job listing    Job details, Date Opened   05/02/2025        Job Type   Full time        Industry   IT Services        Work Experience   4-6 years        City   Ahmedabad        State/Province   Gujarat        Country   India        Zip/Postal Code   380060, View all jobs      Visit website", "title": "Software Developer (.Net Core + React)", "location": "Ahmedabad, India   | Posted on 05/02/2025", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000008886015/Software-Developer-Net-Core-React-?source=CareerSite", "description": "Job Summary: We are looking for a highly experienced Senior Developer who will be responsible for interacting with the TL/PM to plan project schedules and technical direction. Develop high-level system design diagrams for program design, coding, testing, debugging and documentation. Key responsibilities: ❖ Develop, Unit test, and deploy custom web applications ❖ Develop, and maintain efficient, reusable, and reliable code ❖ Performance tuning of database and web applications (Add in 3 + Years team members) ❖ Ensure the best possible performance, quality, and responsiveness of applications ❖ Gather requirements directly from the client and make a development plan and forecast timeline for completion. ❖ Provide demo of work to client sprint wise ❖ Train, mentor, and rescue junior developers when they are facing blockages in development. Do the code review of self and juniors. ❖ Help TL with quarterly appraisal feedback of mid-developers working with you. ❖ Responsible for developing a high-quality product for the client ❖ eBev required ❖ Develop the training plan for new technology for junior and mid developers ❖ Take interviews ❖ Learn new technology and help juniors for skills developmentRequirementsTechnical Skills: Primary Skill ❖ Must have a clear concept of OOPS ❖ Must have strong hands-on experience in ASP. Net MVC, Entity Framework, JavaScript, ASP. NET Core & WebAPI, React 19, Docker containers running on Linux, C# 13, . NET 9, gRPC services, Git ❖ Must be able to write efficient SǪL Stored Procedure ❖ Must be proficient in technical analysis of requirement. ❖ Must have API development and integration ❖ Must be able to analyze and integrate 3rd party APIs ❖ Different types of authentication and authorization ❖ Version Control: Utilizing version control systems such as Git for efficient source code management, team collaboration, and maintaining a comprehensive code history. ❖ Coding Standards: Adhering to established coding standards, best practices, and guidelines to ensure the development of a consistent and high-quality codebase. ❖ Security Best Practices: Displaying an understanding of security vulnerabilities and employing best practices for secure coding, thereby enhancing protection against common cyber threats and attacks. Secondary Skill ❖ Good to have Experience of Microsoft Azure Deployment. ❖ Good to have knowledge on consuming SOAP services. ❖ Should have a good understanding of Agile / Scrum Framework ❖ Having relevant Microsoft certification on the latest technology would be added as an advantage BenefitsNon-technical Skills: ❖ Communication: Must have the ability to effectively communicate with colleagues, clients, and stakeholders, both verbally and in writing. ❖ Problem-solving: Able to analyze and find solutions to complex problems. ❖ Time management: Able to prioritize tasks and manage time effectively to meet deadlines. ❖ Adaptability: Ǫuickly adapt to new technologies and tools. ❖ Collaboration: Work well with others, both as part of a team and in a leadership role. Attention to detail: Able to pay close attention in order to ensure the quality of code and avoid bugs. ❖ Continuous learning: The desire to continuously learn and improve one's skills and knowledge in order to stay up-to-date with industry trends and advancements. ❖ Initiative: The ability to take initiative and be proactive in finding solutions, without waiting for instructions or directions. ❖ Creativity: The ability to think creatively and come up with innovative solutions to problems. ❖ Organization: The ability to keep track of multiple tasks, projects, and deadlines, and maintain an organized workflow. Educational Qualification: ❖ Bachelor or Master in Computer Science, Software Engineering, or a related field.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000008887367/Digital-Marketing-Manager?source=CareerSite", "company_id": 3398, "source": 3, "skills": ", Job listing    Job details, Date Opened   05/04/2025        Job Type   Full time        Industry   IT Services        Work Experience   6+ years        City   Ahmedabad        State/Province   Gujarat        Country   India        Zip/Postal Code   380060, Designation: Digital Marketing Manager Department: Marketing Experience Required: 10 to 12 YearsEmployment Type: Full-time Location: Ahmedabad, Develop and execute a comprehensive digital marketing strategy aligned with company goals. Lead performance marketing (SEO, SEM, PPC, social media, email, affiliate) to generate qualified leads. Oversee content strategy including blogs, whitepapers, landing pages, case studies, and video marketing. Manage website performance, UI/UX improvements, and conversion optimization. Implement marketing automation and CRM integrations (HubSpot, Zoho, etc.). Drive targeted campaigns for FinTech services and SycTool product across digital channels. Collaborate with Sales, Product, and Tech teams for go-to-market and product positioning. Manage digital marketing budget, vendors, and external agencies. Analyze KPIs, prepare performance reports, and present insights to the CEO., Bachelor's/Master’s degree in Marketing, Communications, or related field. 10–12 years of digital marketing experience, with strong exposure to FinTech/SaaS/B2B segments. Proven expertise in Google Ads, LinkedIn Ads, SEO tools (Ahrefs, SEMrush), and marketing automation. Strong analytical skills with data-driven decision-making. Excellent leadership, communication, and cross-functional collaboration skills. Experience in global B2B marketing will be an advantage., Opportunity to work directly with the CEO and leadership team. High-impact role in a fast-growing FinTech environment. Hybrid work model/flexible hours (if applicable). Competitive compensation and growth path., 5-day work-week Access to the latest technology and state-of-the-art equipmentEmphasis on continuous feedback and improvementA flexible working model with a blend of remote and in-office work Employee financial assistance Collaborative and teamwork-oriented environment  Regular annual health check-ups  Superior and spacious workstations  Rewards and recognition programs  Access to top-notch training and development opportunities Scope for both professional and personal growth., View all jobs      Visit website", "title": "Digital Marketing Manager", "location": "Ahmedabad, India   | Posted on 05/04/2025", "location_type": "flexible", "job_type": "full_time", "min_experience": 10, "max_experience": 12, "apply_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000008887367/Digital-Marketing-Manager?source=CareerSite", "description": "Job Description Designation: Digital Marketing Manager Department: Marketing Experience Required: 10 to 12 YearsEmployment Type: Full-time Location: Ahmedabad Position Overview: We are seeking a seasoned Digital Marketing Manager with 10–12 years of proven experience in digital strategy, performance marketing, and brand positioning—especially in B2B FinTech and SaaS domains. This strategic role reports directly to the CEO and is instrumental in shaping Satva Solution’s online presence and demand generation. Key Responsibilities: Develop and execute a comprehensive digital marketing strategy aligned with company goals. Lead performance marketing (SEO, SEM, PPC, social media, email, affiliate) to generate qualified leads. Oversee content strategy including blogs, whitepapers, landing pages, case studies, and video marketing. Manage website performance, UI/UX improvements, and conversion optimization. Implement marketing automation and CRM integrations (HubSpot, Zoho, etc. ). Drive targeted campaigns for FinTech services and SycTool product across digital channels. Collaborate with Sales, Product, and Tech teams for go-to-market and product positioning. Manage digital marketing budget, vendors, and external agencies. Analyze KPIs, prepare performance reports, and present insights to the CEO. RequirementsKey Skills & Qualifications: Bachelor's/Master’s degree in Marketing, Communications, or related field. 10–12 years of digital marketing experience, with strong exposure to FinTech/SaaS/B2B segments. Proven expertise in Google Ads, LinkedIn Ads, SEO tools (Ahrefs, SEMrush), and marketing automation. Strong analytical skills with data-driven decision-making. Excellent leadership, communication, and cross-functional collaboration skills. Experience in global B2B marketing will be an advantage. BenefitsWhat We Offer: Opportunity to work directly with the CEO and leadership team. High-impact role in a fast-growing FinTech environment. Hybrid work model/flexible hours (if applicable). Competitive compensation and growth path. The happiness of Satva Solutions:5-day work-week Access to the latest technology and state-of-the-art equipmentEmphasis on continuous feedback and improvementA flexible working model with a blend of remote and in-office work Employee financial assistance Collaborative and teamwork-oriented environment Regular annual health check-ups Superior and spacious workstations Rewards and recognition programs Access to top-notch training and development opportunities Scope for both professional and personal growth.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000008971060/Business-Development-Manager?source=CareerSite", "company_id": 3398, "source": 3, "skills": ", Job listing    Job details, Date Opened   05/13/2025        Job Type   Full time        Industry   IT Services        Work Experience   6+ years        City   Ahmedabad        State/Province   Gujarat        Country   India        Zip/Postal Code   380060, Define and implement outbound sales strategies aligned with company revenue goalsEstablish territories, account distribution, and targeting criteriaSet individual and team KPIs, quotas, and activity metricsDevelop and refine sales methodologies specific to accounting softwareCreate strategic approaches for different market segments within the accounting sectorImplement effective pipeline management and forecasting processes, Monitor workflow transitions between DRA, SDR, and Pre-Sales teamsIdentify and resolve process bottlenecks in the sales cycleImplement best practices for CRM usage and data management in PipedriveAnalyze conversion metrics at each stage of the sales processDrive continuous improvement in outreach effectiveness and efficiencyStandardize successful approaches into repeatable playbooks, Develop comprehensive training programs for team membersCreate effective scripts, email templates, and objection handling guidesDesign and implement sales collateral specific to accounting industry needsCollaborate with marketing on content and messaging for outbound campaignsStay updated on industry trends and competitive intelligenceLead regular training sessions on new features, market changes, and selling techniques, Track and analyze team performance metrics and KPIsConduct regular pipeline reviews and forecast accuracy checksPrepare comprehensive sales reports for executive leadershipManage team budget and resource allocationParticipate in strategic deals as neededHold team accountable to performance standards, Lead, coach, and develop a team of Data Research Assistants and Sales Development RepresentativesRecruit, hire, and onboard new team members as the organization growsSet clear performance expectations and provide regular constructive feedbackConduct one-on-one coaching sessions to improve individual performanceCreate career development paths for team membersFoster a positive, collaborative, and high-achieving sales culture, Coordinate with Pre-Sales team to ensure technical alignmentCollaborate with Product teams to provide market feedbackWork with Marketing on lead generation initiativesPartner with Customer Success for smooth client transitionsRepresent sales development in company leadership meetings, 7 to 12 years of sales management experience, preferably in SaaS or accounting softwareProven track record of meeting or exceeding team sales targetsExperience managing and developing entry to mid-level sales talentStrong understanding of outbound B2B sales methodologiesExcellent communication, presentation, and leadership skillsData-driven approach to decision making and performance analysisProficiency with CRM systems (Pipedrive experience preferred)Experience with sales enablement and training program developmentUnderstanding of the accounting software market and buyer personas, Background in accounting, finance, or related software salesExperience with sales operations and process designKnowledge of sales automation tools and technologiesHistory of scaling sales development teamsExperience implementing or optimizing Pipedrive CRMTrack record of success in full-cycle B2B sales before managementMBA or relevant business degree, Full-time, in-office position with regular business hoursReports to Director of Sales or similar executive leadershipLeads team of DRAs and SDRs with dotted-line collaboration with Pre-SalesMay require occasional travel for client meetings or industry events, Team revenue and pipeline generation against targetsConversion rates through sales stagesTeam member retention and developmentProcess efficiency improvementsQuality of sales enablement materialsOverall growth in accounting software market share, Competitive base salary with performance-based bonus structureLeadership development opportunitiesInfluence in company strategy and directionRecognition as a key member of the leadership team, View all jobs      Visit website", "title": "Business Development Manager", "location": "Ahmedabad, India   | Posted on 05/13/2025", "location_type": "onsite", "job_type": "full_time", "min_experience": 12, "max_experience": 12, "apply_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000008971060/Business-Development-Manager?source=CareerSite", "description": "Job Summary: We are seeking an experienced Sales Development Manager to lead our outbound sales efforts for our accounting software services. This strategic leadership role will oversee the entire sales development function, including Data Research Assistants (DRAs) and Sales Development Representatives (SDRs), while coordinating with the Pre-Sales team. The ideal candidate will drive revenue growth through effective team management, process optimization, and coaching while establishing a high-performance sales culture. Primary Responsibilities: Sales Strategy & Execution:Define and implement outbound sales strategies aligned with company revenue goalsEstablish territories, account distribution, and targeting criteriaSet individual and team KPIs, quotas, and activity metricsDevelop and refine sales methodologies specific to accounting softwareCreate strategic approaches for different market segments within the accounting sectorImplement effective pipeline management and forecasting processesProcess Optimization: Monitor workflow transitions between DRA, SDR, and Pre-Sales teamsIdentify and resolve process bottlenecks in the sales cycleImplement best practices for CRM usage and data management in PipedriveAnalyze conversion metrics at each stage of the sales processDrive continuous improvement in outreach effectiveness and efficiencyStandardize successful approaches into repeatable playbooksSales Enablement: Develop comprehensive training programs for team membersCreate effective scripts, email templates, and objection handling guidesDesign and implement sales collateral specific to accounting industry needsCollaborate with marketing on content and messaging for outbound campaignsStay updated on industry trends and competitive intelligenceLead regular training sessions on new features, market changes, and selling techniquesPerformance Management & Reporting: Track and analyze team performance metrics and KPIsConduct regular pipeline reviews and forecast accuracy checksPrepare comprehensive sales reports for executive leadershipManage team budget and resource allocationParticipate in strategic deals as neededHold team accountable to performance standardsTeam Leadership & DevelopmentLead, coach, and develop a team of Data Research Assistants and Sales Development RepresentativesRecruit, hire, and onboard new team members as the organization growsSet clear performance expectations and provide regular constructive feedbackConduct one-on-one coaching sessions to improve individual performanceCreate career development paths for team membersFoster a positive, collaborative, and high-achieving sales cultureCross-Functional LeadershipCoordinate with Pre-Sales team to ensure technical alignmentCollaborate with Product teams to provide market feedbackWork with Marketing on lead generation initiativesPartner with Customer Success for smooth client transitionsRepresent sales development in company leadership meetingsRequirementsRequired Skills & Qualifications: 7 to 12 years of sales management experience, preferably in SaaS or accounting softwareProven track record of meeting or exceeding team sales targetsExperience managing and developing entry to mid-level sales talentStrong understanding of outbound B2B sales methodologiesExcellent communication, presentation, and leadership skillsData-driven approach to decision making and performance analysisProficiency with CRM systems (Pipedrive experience preferred)Experience with sales enablement and training program developmentUnderstanding of the accounting software market and buyer personas Nice-to-Have Skills:Background in accounting, finance, or related software salesExperience with sales operations and process designKnowledge of sales automation tools and technologiesHistory of scaling sales development teamsExperience implementing or optimizing Pipedrive CRMTrack record of success in full-cycle B2B sales before managementMBA or relevant business degree Working Conditions:​Full-time, in-office position with regular business hoursReports to Director of Sales or similar executive leadershipLeads team of DRAs and SDRs with dotted-line collaboration with Pre-SalesMay require occasional travel for client meetings or industry events Performance Metrics: Success in this role will be measured by:Team revenue and pipeline generation against targetsConversion rates through sales stagesTeam member retention and developmentProcess efficiency improvementsQuality of sales enablement materialsOverall growth in accounting software market shareBenefitsCompany Benefits: ​Competitive base salary with performance-based bonus structureLeadership development opportunitiesInfluence in company strategy and directionRecognition as a key member of the leadership teamQualified candidates who are passionate about sales leadership, have a track record of team success, and are interested in driving growth in the accounting software sector are encouraged to apply.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000009151025/Technical-Project-Manager?source=CareerSite", "company_id": 3398, "source": 3, "skills": ", Job listing    Job details, Date Opened   06/12/2025        Job Type   Full time        Industry   IT Services        Work Experience   6+ years        City   Ahmedabad        State/Province   Gujarat        Country   India        Zip/Postal Code   380060, Ensure accuracy of team plans and consider dependencies between teams. Coordinate internal resources for project execution. Manage client relationships. Perform risk management to minimize project risks. Ensure timely, quality, and within-scope project delivery. Conduct capacity planning and forecasting based on resource availability and skillset. Alert the team on available resources and assign work for skill improvement. Manage changes to project scope and schedule. Measure project performance using appropriate tools. Utilize tools like JIRA, Confluence, TFS, Github, Basecamp. Attend daily scrum and provide weekly status updates to clients.Generate and review timesheets post-project completion. Review weekly time logs, issue warnings for discrepancies. Provide improvement points to HR for team members' performance enhancement. Prepare sprint lifecycle documents (sprint plan, user story, acceptance criteria, progress report, retrospective analysis). Monthly reporting of team productivity to the CEO., Effective communication with colleagues, clients, and stakeholders. Confidence to extract the best from the team and deliver quality for clients. Strong organizational skills with attention to detail, effective multitasking, and time management abilities for prioritizing tasks and meeting deadlines across multiple projects. Knowledge of complete SDLC (software development life cycle). Strong problem-solving skills. Ability to grasp technical concepts quickly and develop the appropriate test approach. Experience in handling large/mid-size enterprise projects in web development/mobile development/designing/testing. Experience in working in an agile environment. Hands-on experience with project communication tools (e.g., Basecamp or Trello) and project management tools like Jira, Azure DevOps. Analytical skills to find solutions to complex problems. Adaptability to learn quickly and adapt to new technologies and tools. Collaboration skills, both as part of a team and in a leadership role. Attention to detail to ensure code quality and avoid bugs. Continuous learning mindset to stay up-to-date with industry trends. Initiative to take proactive steps in finding solutions without waiting for instructions. Creativity for thinking innovatively and proposing creative problem-solving ideas., Bachelor's or Master’s Degree in Computer Science, Software Engineering, Master in Business Administration, or a related field., Opportunity to work directly with the CEO and leadership team. High-impact role in a fast-growing FinTech environment. Hybrid work model/flexible hours (if applicable). Competitive compensation and growth path., 5-day work-week Access to the latest technology and state-of-the-art equipmentEmphasis on continuous feedback and improvementA flexible working model with a blend of remote and in-office work Employee financial assistance Collaborative and teamwork-oriented environment   Superior and spacious workstations  Rewards and recognition programs  Access to top-notch training and development opportunities Scope for both professional and personal growth., View all jobs      Visit website", "title": "Technical Project Manager", "location": "Ahmedabad, India   | Posted on 06/12/2025", "location_type": "flexible", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://jobs.satvasolutions.com/jobs/Careers/446792000009151025/Technical-Project-Manager?source=CareerSite", "description": "Job Summary: We are looking for a Technical Project Manager who oversees end-to-end project delivery, ensuring adherence to timelines, budgets, and scope. The person will be leading cross-functional teams, managing resources, mitigating risks, and maintaining strong communication with the clients. With a focus on quality assurance, the Technical Project Manager combines technical expertise with effective leadership to drive successful project outcomes. ​Roles & Responsibilities: Ensure accuracy of team plans and consider dependencies between teams. Coordinate internal resources for project execution. Manage client relationships. Perform risk management to minimize project risks. Ensure timely, quality, and within-scope project delivery. Conduct capacity planning and forecasting based on resource availability and skillset. Alert the team on available resources and assign work for skill improvement. Manage changes to project scope and schedule. Measure project performance using appropriate tools. Utilize tools like JIRA, Confluence, TFS, Github, Basecamp. Attend daily scrum and provide weekly status updates to clients. Generate and review timesheets post-project completion. Review weekly time logs, issue warnings for discrepancies. Provide improvement points to HR for team members' performance enhancement. Prepare sprint lifecycle documents (sprint plan, user story, acceptance criteria, progress report, retrospective analysis). Monthly reporting of team productivity to the CEO. ​RequirementsKey Competencies: Effective communication with colleagues, clients, and stakeholders. Confidence to extract the best from the team and deliver quality for clients. Strong organizational skills with attention to detail, effective multitasking, and time management abilities for prioritizing tasks and meeting deadlines across multiple projects. Knowledge of complete SDLC (software development life cycle). Strong problem-solving skills. Ability to grasp technical concepts quickly and develop the appropriate test approach. Experience in handling large/mid-size enterprise projects in web development/mobile development/designing/testing. Experience in working in an agile environment. Hands-on experience with project communication tools (e. g. , Basecamp or Trello) and project management tools like Jira, Azure DevOps. Analytical skills to find solutions to complex problems. Adaptability to learn quickly and adapt to new technologies and tools. Collaboration skills, both as part of a team and in a leadership role. Attention to detail to ensure code quality and avoid bugs. Continuous learning mindset to stay up-to-date with industry trends. Initiative to take proactive steps in finding solutions without waiting for instructions. Creativity for thinking innovatively and proposing creative problem-solving ideas. Educational Qualifications: Bachelor's or Master’s Degree in Computer Science, Software Engineering, Master in Business Administration, or a related field. BenefitsWhat We Offer: Opportunity to work directly with the CEO and leadership team. High-impact role in a fast-growing FinTech environment. Hybrid work model/flexible hours (if applicable). Competitive compensation and growth path. The happiness of Satva Solutions:5-day work-week Access to the latest technology and state-of-the-art equipmentEmphasis on continuous feedback and improvementA flexible working model with a blend of remote and in-office work Employee financial assistance Collaborative and teamwork-oriented environment Superior and spacious workstations Rewards and recognition programs Access to top-notch training and development opportunities Scope for both professional and personal growth.", "ctc": null, "currency": null, "meta": {}}]