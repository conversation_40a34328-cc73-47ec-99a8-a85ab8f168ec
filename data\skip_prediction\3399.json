[{"jd_link": "https://uapp.group/vacancies/back-end-developer/", "company_id": 3399, "source": 3, "skills": "Cookie_GRECAPTCHADuration6 monthsDescriptionGoogle Recaptcha service sets this cookie to identify bots to protect the website against malicious spam attacks., Cookierc::aDurationNever ExpiresDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., Cookierc::fDurationNever ExpiresDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., CookiewpEmojiSettingsSupportsDurationsessionDescriptionWordPress sets this cookie when a user interacts with emojis on a WordPress site.  It helps determine if the user's browser can display emojis properly., Cookierc::cDurationsessionDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., Cookierc::bDurationsessionDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., Cookiecookieyes-consentDuration1 yearDescriptionCookieYes sets this cookie to remember users' consent preferences so that their preferences are respected on subsequent visits to this site. It does not collect or store any personal information about the site visitors., Cookie_dc_gtm_UA-*Duration1 minuteDescriptionGoogle Analytics sets this cookie to load the Google Analytics script tag., <PERSON>ie_ga_*Duration1 year 1 month 4 daysDescriptionGoogle Analytics sets this cookie to store and count page views., Cookie_gaDuration1 year 1 month 4 daysDescriptionGoogle Analytics sets this cookie to calculate visitor, session and campaign data and track site usage for the site's analytics report. The cookie stores information anonymously and assigns a randomly generated number to recognise unique visitors., Cookie_gidDuration1 dayDescriptionGoogle Analytics sets this cookie to store information on how visitors use a website while also creating an analytics report of the website's performance. Some of the collected data includes the number of visitors, their source, and the pages they visit anonymously., Cookie_fbpDuration3 monthsDescriptionFacebook sets this cookie to display advertisements when either on Facebook or on a digital platform powered by Facebook advertising after visiting the website., Services\n\n\tTechnical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services\n\n\nExpertise\n\n\tMarketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems\n\n\nInsights\nU-Camp\nCareers\nOur Team, Technical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services, Marketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems, Services\n\n\tTechnical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services\n\n\nExpertise\n\n\tMarketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems\n\n\nInsights\nU-Camp\nCareers\nOur Team, Technical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services, Marketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems, Business Analysis Services\nUI/UX Design Services\nSoftware Engineering Services\nCybersecurity\nTechnical Support\nDigital Marketing, Digital Presence\nBlockchain-based Development\nLoyalty and Multi-level Marketing\nAutomation, Bots and AI\nBusiness Management Systems\nUser Interfaces – Portals and Apps, Digital Wallets\nMarketplaces, Auctions and Crowdfunding, Experience For Web Applications;\nExperience In Java;\nExcellent Skills In OOP;\nKnowledge Of SQL And JPA / Hibernate;\nExperience In Developing REST APIs;\nBasic Knowledge Of Algorithms;\nStrong Abilities To Learn New Technologies., Experience In Kotlin;\nExperience In Integration With Third-Party Services Via API, SDK., Career and professional growth;\nDecent salary;\nInteresting projects in a professional team;\nTrainings, seminars, meetings and conferences at company expense;\nPleasant and friendly working atmosphere., Business Analysis Services\nUI/UX Design Services\nSoftware Engineering Services\nCybersecurity\nTechnical Support\nDigital Marketing, Digital Presence\nBlockchain-based Development\nLoyalty and Multi-level Marketing\nAutomation, Bots and AI\nBusiness Management Systems\nUser Interfaces – Portals and Apps, Digital Wallets\nMarketplaces, Auctions and Crowdfunding, Privacy Policy\nTerms of use\nCookies Settings", "title": "JOB #: 1\n \nWe are looking for a Middle Back-end Developer for a full-time office position\n \n\nRequirements:\n\nExperience For Web Applications;\nExperience In Java;\nExcellent Skills In OOP;\nKnowledge Of SQL And JPA / Hibernate;\nExperience In Developing REST APIs;\nBasic Knowledge Of Algorithms;\nStrong Abilities To Learn New Technologies.\n\n \n\nWill be a plus:\n\nExperience In Kotlin;\nExperience In Integration With Third-Party Services Via API, SDK.\n\n \n\nWe offer:\n\nCareer and professional growth;\nDecent salary;\nInteresting projects in a professional team;\nTrainings, seminars, meetings and conferences at company expense;\nPleasant and friendly working atmosphere.\n\n        \n      \n              \n          Other vacancies\n          \n            \n              QA Engineer\n              \n                View and Apply\n                \n\n\n\n\n\n\n\n\n\n              \n            \n\n          \n            \n              Frontend Developer\n              \n                View and Apply\n                \n\n\n\n\n\n\n\n\n\n              \n            \n\n          \n            \n              WordPress Developer\n              \n                View and Apply", "location": "", "location_type": null, "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://uapp.group/vacancies/back-end-developer/", "description": "JOB #: 1 We are looking for a Middle Back-end Developer for a full-time office position Requirements: Experience For Web Applications; Experience In Java; Excellent Skills In OOP; Knowledge Of SQL And JPA / Hibernate; Experience In Developing REST APIs; Basic Knowledge Of Algorithms; Strong Abilities To Learn New Technologies. Will be a plus: Experience In Kotlin; Experience In Integration With Third-Party Services Via API, SDK. We offer: Career and professional growth; Decent salary; Interesting projects in a professional team; Trainings, seminars, meetings and conferences at company expense; Pleasant and friendly working atmosphere.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://uapp.group/vacancies/frontend-developer/", "company_id": 3399, "source": 3, "skills": "Cookie_GRECAPTCHADuration6 monthsDescriptionGoogle Recaptcha service sets this cookie to identify bots to protect the website against malicious spam attacks., Cookierc::aDurationNever ExpiresDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., Cookierc::fDurationNever ExpiresDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., CookiewpEmojiSettingsSupportsDurationsessionDescriptionWordPress sets this cookie when a user interacts with emojis on a WordPress site.  It helps determine if the user's browser can display emojis properly., Cookierc::cDurationsessionDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., Cookierc::bDurationsessionDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., Cookiecookieyes-consentDuration1 yearDescriptionCookieYes sets this cookie to remember users' consent preferences so that their preferences are respected on subsequent visits to this site. It does not collect or store any personal information about the site visitors., Cookie_dc_gtm_UA-*Duration1 minuteDescriptionGoogle Analytics sets this cookie to load the Google Analytics script tag., <PERSON>ie_ga_*Duration1 year 1 month 4 daysDescriptionGoogle Analytics sets this cookie to store and count page views., Cookie_gaDuration1 year 1 month 4 daysDescriptionGoogle Analytics sets this cookie to calculate visitor, session and campaign data and track site usage for the site's analytics report. The cookie stores information anonymously and assigns a randomly generated number to recognise unique visitors., Cookie_gidDuration1 dayDescriptionGoogle Analytics sets this cookie to store information on how visitors use a website while also creating an analytics report of the website's performance. Some of the collected data includes the number of visitors, their source, and the pages they visit anonymously., Cookie_fbpDuration3 monthsDescriptionFacebook sets this cookie to display advertisements when either on Facebook or on a digital platform powered by Facebook advertising after visiting the website., Services\n\n\tTechnical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services\n\n\nExpertise\n\n\tMarketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems\n\n\nInsights\nU-Camp\nCareers\nOur Team, Technical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services, Marketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems, Services\n\n\tTechnical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services\n\n\nExpertise\n\n\tMarketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems\n\n\nInsights\nU-Camp\nCareers\nOur Team, Technical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services, Marketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems, Business Analysis Services\nUI/UX Design Services\nSoftware Engineering Services\nCybersecurity\nTechnical Support\nDigital Marketing, Digital Presence\nBlockchain-based Development\nLoyalty and Multi-level Marketing\nAutomation, Bots and AI\nBusiness Management Systems\nUser Interfaces – Portals and Apps, Digital Wallets\nMarketplaces, Auctions and Crowdfunding, Strong knowledge of HTML, CSS, and JavaScript;\nExperience with frontend frameworks/libraries such as React, Angular, Vue.js, or similar;\nUnderstanding of application architecture, design patterns, OOP, and functional programming principles;\nExperience with responsive and cross-browser layout;\nGood grasp of adaptive and mobile-first design principles;\nUnderstanding of REST API integration;\nExperience writing tests (unit and e2e) and ensuring proper test coverage;\nExperience with version control systems (e.g., Git);\nUnderstanding of website performance optimization and security best practices;\nAbility and willingness to learn new technologies;\nEnglish proficiency – B1 level or higher., Experience with WordPress, including custom theme and plugin development;\nExperience with UI frameworks (Bootstrap, Tailwind CSS, etc.);\nKnowledge of build tools (Webpack, Vite, etc.);\nUnderstanding of SEO best practices., Career and professional growth;\nDecent salary;\nInteresting projects in a professional team;\nTrainings, seminars, meetings and conferences at company expense;\nPleasant and friendly working atmosphere., Business Analysis Services\nUI/UX Design Services\nSoftware Engineering Services\nCybersecurity\nTechnical Support\nDigital Marketing, Digital Presence\nBlockchain-based Development\nLoyalty and Multi-level Marketing\nAutomation, Bots and AI\nBusiness Management Systems\nUser Interfaces – Portals and Apps, Digital Wallets\nMarketplaces, Auctions and Crowdfunding, Privacy Policy\nTerms of use\nCookies Settings", "title": "JOB #: 3\n \nWe are looking for a Frontend Developer (<PERSON> Junior/Middle with 2+ years of experience) for a full-time position in the office.\n \n\nRequirements:\n\nStrong knowledge of HTML, CSS, and JavaScript;\nExperience with frontend frameworks/libraries such as React, Angular, Vue.js, or similar;\nUnderstanding of application architecture, design patterns, OOP, and functional programming principles;\nExperience with responsive and cross-browser layout;\nGood grasp of adaptive and mobile-first design principles;\nUnderstanding of REST API integration;\nExperience writing tests (unit and e2e) and ensuring proper test coverage;\nExperience with version control systems (e.g., Git);\nUnderstanding of website performance optimization and security best practices;\nAbility and willingness to learn new technologies;\nEnglish proficiency – B1 level or higher.\n\n \n\nWill be a plus:\n\nExperience with WordPress, including custom theme and plugin development;\nExperience with UI frameworks (Bootstrap, Tailwind CSS, etc.);\nKnowledge of build tools (Webpack, Vite, etc.);\nUnderstanding of SEO best practices.\n\n \n\nWe offer:\n\nCareer and professional growth;\nDecent salary;\nInteresting projects in a professional team;\nTrainings, seminars, meetings and conferences at company expense;\nPleasant and friendly working atmosphere.\n\n        \n      \n              \n          Other vacancies\n          \n            \n              Back-end Developer\n              \n                View and Apply\n                \n\n\n\n\n\n\n\n\n\n              \n            \n\n          \n            \n              QA Engineer\n              \n                View and Apply\n                \n\n\n\n\n\n\n\n\n\n              \n            \n\n          \n            \n              WordPress Developer\n              \n                View and Apply", "location": "", "location_type": null, "job_type": "full_time", "min_experience": 2, "max_experience": null, "apply_link": "https://uapp.group/vacancies/frontend-developer/", "description": "JOB #: 3 We are looking for a Frontend Developer (<PERSON> Junior/Middle with 2+ years of experience) for a full-time position in the office. Requirements: Strong knowledge of HTML, CSS, and JavaScript; Experience with frontend frameworks/libraries such as React, Angular, Vue. js, or similar; Understanding of application architecture, design patterns, OOP, and functional programming principles; Experience with responsive and cross-browser layout; Good grasp of adaptive and mobile-first design principles; Understanding of REST API integration; Experience writing tests (unit and e2e) and ensuring proper test coverage; Experience with version control systems (e. g. , Git); Understanding of website performance optimization and security best practices; Ability and willingness to learn new technologies; English proficiency – B1 level or higher. Will be a plus: Experience with WordPress, including custom theme and plugin development; Experience with UI frameworks (Bootstrap, Tailwind CSS, etc. ); Knowledge of build tools (<PERSON>pack, Vite, etc. ); Understanding of SEO best practices. We offer: Career and professional growth; Decent salary; Interesting projects in a professional team; Trainings, seminars, meetings and conferences at company expense; Pleasant and friendly working atmosphere.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://uapp.group/vacancies/qa-engineer/", "company_id": 3399, "source": 3, "skills": "Cookie_GRECAPTCHADuration6 monthsDescriptionGoogle Recaptcha service sets this cookie to identify bots to protect the website against malicious spam attacks., Cookierc::aDurationNever ExpiresDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., Cookierc::fDurationNever ExpiresDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., CookiewpEmojiSettingsSupportsDurationsessionDescriptionWordPress sets this cookie when a user interacts with emojis on a WordPress site.  It helps determine if the user's browser can display emojis properly., Cookierc::cDurationsessionDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., Cookierc::bDurationsessionDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., Cookiecookieyes-consentDuration1 yearDescriptionCookieYes sets this cookie to remember users' consent preferences so that their preferences are respected on subsequent visits to this site. It does not collect or store any personal information about the site visitors., Cookie_dc_gtm_UA-*Duration1 minuteDescriptionGoogle Analytics sets this cookie to load the Google Analytics script tag., <PERSON>ie_ga_*Duration1 year 1 month 4 daysDescriptionGoogle Analytics sets this cookie to store and count page views., Cookie_gaDuration1 year 1 month 4 daysDescriptionGoogle Analytics sets this cookie to calculate visitor, session and campaign data and track site usage for the site's analytics report. The cookie stores information anonymously and assigns a randomly generated number to recognise unique visitors., Cookie_gidDuration1 dayDescriptionGoogle Analytics sets this cookie to store information on how visitors use a website while also creating an analytics report of the website's performance. Some of the collected data includes the number of visitors, their source, and the pages they visit anonymously., Cookie_fbpDuration3 monthsDescriptionFacebook sets this cookie to display advertisements when either on Facebook or on a digital platform powered by Facebook advertising after visiting the website., Services\n\n\tTechnical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services\n\n\nExpertise\n\n\tMarketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems\n\n\nInsights\nU-Camp\nCareers\nOur Team, Technical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services, Marketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems, Services\n\n\tTechnical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services\n\n\nExpertise\n\n\tMarketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems\n\n\nInsights\nU-Camp\nCareers\nOur Team, Technical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services, Marketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems, Business Analysis Services\nUI/UX Design Services\nSoftware Engineering Services\nCybersecurity\nTechnical Support\nDigital Marketing, Digital Presence\nBlockchain-based Development\nLoyalty and Multi-level Marketing\nAutomation, Bots and AI\nBusiness Management Systems\nUser Interfaces – Portals and Apps, Digital Wallets\nMarketplaces, Auctions and Crowdfunding, Understanding of software testing processes, types, and methodologies;\nKnowledge of test design techniques and best practices;\nExperience in writing and maintaining test documentation (test plans, test cases, checklists);\nAbility to analyze and clarify software requirements for test coverage;\nBasic experience with API testing (e.g., using Postman, cURL);\nUnderstanding of Client-Server architecture and how web applications function;\nBasic knowledge of SQL;\nStrong verbal and written English communication skills for reporting and collaboration., Experience working with the Linux command line for testing and debugging;\nBasic knowledge of HTML & CSS to verify UI-related test cases;\nFamiliarity with Scrum/Agile methodologies;\nExperience with test automation tools (e.g., Selenium, Cypress, Playwright)., Design, write, and maintain test documentation (test cases, test plans, checklists);\nConduct manual testing of web and/or software applications;\nLog and track bug reports, ensuring issues are properly documented and reproducible;\nEnsure acceptance test cases are regularly updated and aligned with product requirements;\nCollaborate with developers, product managers, and other stakeholders to improve test coverage and software quality., Career and professional growth;\nDecent salary;\nInteresting projects in a professional team;\nTrainings, seminars, meetings and conferences at company expense;\nPleasant and friendly working atmosphere., Business Analysis Services\nUI/UX Design Services\nSoftware Engineering Services\nCybersecurity\nTechnical Support\nDigital Marketing, Digital Presence\nBlockchain-based Development\nLoyalty and Multi-level Marketing\nAutomation, Bots and AI\nBusiness Management Systems\nUser Interfaces – Portals and Apps, Digital Wallets\nMarketplaces, Auctions and Crowdfunding, Privacy Policy\nTerms of use\nCookies Settings", "title": "JOB #: 2\n \nWe are looking for a Junior QA Engineer for a full-time position in the office.\n \n\nRequirements:\n\nUnderstanding of software testing processes, types, and methodologies;\nKnowledge of test design techniques and best practices;\nExperience in writing and maintaining test documentation (test plans, test cases, checklists);\nAbility to analyze and clarify software requirements for test coverage;\nBasic experience with API testing (e.g., using <PERSON>man, cURL);\nUnderstanding of Client-Server architecture and how web applications function;\nBasic knowledge of SQL;\nStrong verbal and written English communication skills for reporting and collaboration.\n\n \n\nWill be a plus:\n\nExperience working with the Linux command line for testing and debugging;\nBasic knowledge of HTML & CSS to verify UI-related test cases;\nFamiliarity with Scrum/Agile methodologies;\nExperience with test automation tools (e.g., Selenium, Cypress, Playwright).\n\n \n\nResponsibilities:\n\nDesign, write, and maintain test documentation (test cases, test plans, checklists);\nConduct manual testing of web and/or software applications;\nLog and track bug reports, ensuring issues are properly documented and reproducible;\nEnsure acceptance test cases are regularly updated and aligned with product requirements;\nCollaborate with developers, product managers, and other stakeholders to improve test coverage and software quality.\n\n \n\nWe offer:\n\nCareer and professional growth;\nDecent salary;\nInteresting projects in a professional team;\nTrainings, seminars, meetings and conferences at company expense;\nPleasant and friendly working atmosphere.\n\n        \n      \n              \n          Other vacancies\n          \n            \n              Back-end Developer\n              \n                View and Apply\n                \n\n\n\n\n\n\n\n\n\n              \n            \n\n          \n            \n              Frontend Developer\n              \n                View and Apply\n                \n\n\n\n\n\n\n\n\n\n              \n            \n\n          \n            \n              WordPress Developer\n              \n                View and Apply", "location": "", "location_type": null, "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://uapp.group/vacancies/qa-engineer/", "description": "JOB #: 2 We are looking for a Junior QA Engineer for a full-time position in the office. Requirements: Understanding of software testing processes, types, and methodologies; Knowledge of test design techniques and best practices; Experience in writing and maintaining test documentation (test plans, test cases, checklists); Ability to analyze and clarify software requirements for test coverage; Basic experience with API testing (e. g. , using <PERSON><PERSON>, cURL); Understanding of Client-Server architecture and how web applications function; Basic knowledge of SQL; Strong verbal and written English communication skills for reporting and collaboration. Will be a plus: Experience working with the Linux command line for testing and debugging; Basic knowledge of HTML & CSS to verify UI-related test cases; Familiarity with Scrum/Agile methodologies; Experience with test automation tools (e. g. , Selenium, Cypress, Playwright). Responsibilities: Design, write, and maintain test documentation (test cases, test plans, checklists); Conduct manual testing of web and/or software applications; Log and track bug reports, ensuring issues are properly documented and reproducible; Ensure acceptance test cases are regularly updated and aligned with product requirements; Collaborate with developers, product managers, and other stakeholders to improve test coverage and software quality. We offer: Career and professional growth; Decent salary; Interesting projects in a professional team; Trainings, seminars, meetings and conferences at company expense; Pleasant and friendly working atmosphere.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://uapp.group/vacancies/wordpress-developer/", "company_id": 3399, "source": 3, "skills": "Cookie_GRECAPTCHADuration6 monthsDescriptionGoogle Recaptcha service sets this cookie to identify bots to protect the website against malicious spam attacks., Cookierc::aDurationNever ExpiresDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., Cookierc::fDurationNever ExpiresDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., CookiewpEmojiSettingsSupportsDurationsessionDescriptionWordPress sets this cookie when a user interacts with emojis on a WordPress site.  It helps determine if the user's browser can display emojis properly., Cookierc::cDurationsessionDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., Cookierc::bDurationsessionDescriptionThis cookie is set by the Google recaptcha service to identify bots to protect the website against malicious spam attacks., Cookiecookieyes-consentDuration1 yearDescriptionCookieYes sets this cookie to remember users' consent preferences so that their preferences are respected on subsequent visits to this site. It does not collect or store any personal information about the site visitors., Cookie_dc_gtm_UA-*Duration1 minuteDescriptionGoogle Analytics sets this cookie to load the Google Analytics script tag., <PERSON>ie_ga_*Duration1 year 1 month 4 daysDescriptionGoogle Analytics sets this cookie to store and count page views., Cookie_gaDuration1 year 1 month 4 daysDescriptionGoogle Analytics sets this cookie to calculate visitor, session and campaign data and track site usage for the site's analytics report. The cookie stores information anonymously and assigns a randomly generated number to recognise unique visitors., Cookie_gidDuration1 dayDescriptionGoogle Analytics sets this cookie to store information on how visitors use a website while also creating an analytics report of the website's performance. Some of the collected data includes the number of visitors, their source, and the pages they visit anonymously., Cookie_fbpDuration3 monthsDescriptionFacebook sets this cookie to display advertisements when either on Facebook or on a digital platform powered by Facebook advertising after visiting the website., Services\n\n\tTechnical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services\n\n\nExpertise\n\n\tMarketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems\n\n\nInsights\nU-Camp\nCareers\nOur Team, Technical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services, Marketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems, Services\n\n\tTechnical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services\n\n\nExpertise\n\n\tMarketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems\n\n\nInsights\nU-Camp\nCareers\nOur Team, Technical Support\n\tUI/UX Design Services\n\tDigital marketing\n\tSoftware Engineering Services\n\tBusiness Analysis Services, Marketplaces, Auctions and Сrowdfunding\n\tBlockchain-based Development\n\tAutomation, Bots and AI\n\tDigital Presence\n\tUser Interfaces, Digital Wallets\n\tLoyalty and Multi-level Marketing\n\tBusiness Management Systems, Business Analysis Services\nUI/UX Design Services\nSoftware Engineering Services\nCybersecurity\nTechnical Support\nDigital Marketing, Digital Presence\nBlockchain-based Development\nLoyalty and Multi-level Marketing\nAutomation, Bots and AI\nBusiness Management Systems\nUser Interfaces – Portals and Apps, Digital Wallets\nMarketplaces, Auctions and Crowdfunding, Experience as a WordPress Developer;\nStrong knowledge of PHP, HTML, CSS, JavaScript, and MySQL;\nExperience with custom themes and plugins development;\nUnderstanding of website optimization and security best practices;\nFamiliarity with REST APIs and integrations;\nEnglish level: B1 or higher., Experience with WooCommerce;\nKnowledge of Elementor, ACF, or other WordPress builders;\nUnderstanding of SEO best practices;\nBasic knowledge of Git and version control., Develop, customize, and maintain WordPress websites;\nWork with themes and plugins to enhance website functionality;\nOptimize website performance and security;\nTroubleshoot and debug WordPress-related issues;\nEnsure cross-browser and mobile responsiveness., Career and professional growth;\nDecent salary;\nInteresting projects in a professional team;\nTrainings, seminars, meetings and conferences at company expense;\nPleasant and friendly working atmosphere., Business Analysis Services\nUI/UX Design Services\nSoftware Engineering Services\nCybersecurity\nTechnical Support\nDigital Marketing, Digital Presence\nBlockchain-based Development\nLoyalty and Multi-level Marketing\nAutomation, Bots and AI\nBusiness Management Systems\nUser Interfaces – Portals and Apps, Digital Wallets\nMarketplaces, Auctions and Crowdfunding, Privacy Policy\nTerms of use\nCookies Settings", "title": "JOB #: 4\n \nWe are looking for a WordPress Developer (Strong Junior/Middle with 2+ year of experience) for a full-time position in the office.\n \n\nRequirements:\n\nExperience as a WordPress Developer;\nStrong knowledge of PHP, HTML, CSS, JavaScript, and MySQL;\nExperience with custom themes and plugins development;\nUnderstanding of website optimization and security best practices;\nFamiliarity with REST APIs and integrations;\nEnglish level: B1 or higher.\n\n \n\nWill be a plus:\n\nExperience with WooCommerce;\nKnowledge of Elementor, ACF, or other WordPress builders;\nUnderstanding of SEO best practices;\nBasic knowledge of Git and version control.\n\n\nResponsibilities:\n\nDevelop, customize, and maintain WordPress websites;\nWork with themes and plugins to enhance website functionality;\nOptimize website performance and security;\nTroubleshoot and debug WordPress-related issues;\nEnsure cross-browser and mobile responsiveness.\n\n\nWe offer:\n\nCareer and professional growth;\nDecent salary;\nInteresting projects in a professional team;\nTrainings, seminars, meetings and conferences at company expense;\nPleasant and friendly working atmosphere.\n\n        \n      \n              \n          Other vacancies\n          \n            \n              Back-end Developer\n              \n                View and Apply\n                \n\n\n\n\n\n\n\n\n\n              \n            \n\n          \n            \n              QA Engineer\n              \n                View and Apply\n                \n\n\n\n\n\n\n\n\n\n              \n            \n\n          \n            \n              Frontend Developer\n              \n                View and Apply", "location": "", "location_type": null, "job_type": "full_time", "min_experience": 2, "max_experience": null, "apply_link": "https://uapp.group/vacancies/wordpress-developer/", "description": "JOB #: 4 We are looking for a WordPress Developer (Strong Junior/Middle with 2+ year of experience) for a full-time position in the office. Requirements: Experience as a WordPress Developer; Strong knowledge of PHP, HTML, CSS, JavaScript, and MySQL; Experience with custom themes and plugins development; Understanding of website optimization and security best practices; Familiarity with REST APIs and integrations; English level: B1 or higher. Will be a plus: Experience with WooCommerce; Knowledge of Elementor, ACF, or other WordPress builders; Understanding of SEO best practices; Basic knowledge of Git and version control. Responsibilities: Develop, customize, and maintain WordPress websites; Work with themes and plugins to enhance website functionality; Optimize website performance and security; Troubleshoot and debug WordPress-related issues; Ensure cross-browser and mobile responsiveness. We offer: Career and professional growth; Decent salary; Interesting projects in a professional team; Trainings, seminars, meetings and conferences at company expense; Pleasant and friendly working atmosphere.", "ctc": null, "currency": null, "meta": {}}]