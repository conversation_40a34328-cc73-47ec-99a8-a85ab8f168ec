[{"jd_link": "https://www.level12.io/careers/account-executive/", "company_id": 3400, "source": 3, "skills": "Outreach to leads and prospects by phone, email, and text.\nSchedule and lead discovery calls with prospects. \nPresent and demonstrate the value of products and services to prospects and partners.\nFollow up with prospects several times throughout the sales cycle to ensure needs are being met.\nEngage and build rapport with prospects and partners at meetings, conferences, and networking events.\nAssist in development of sales strategies and quotas.\nStay current on company offerings and industry trends.\nMaintain a database of contact information.\nBuild long-lasting, mutually beneficial relationships with external contacts and internal teams., Excellent communication/presentation skills and ability to build relationships\nOrganizational and time management skills\nEnthusiastic and passionate\nAbility to travel approximately 25% of the time\nProficiency in MS Office, Google Workspace, & similar, Proven experience as an Account Executive, or other sales/customer service role\nKnowledge of CRM software (e.g. Salesforce)\nBasic copywriting and/or graphic design skills\nBased within 100 miles of Louisville, KY, Our Team\n                                    \n                                \n                                    \n                                        Our Values\n                                    \n                                \n                                    \n                                        Consultants First\n                                    \n                                \n                                    \n                                        Challenge\n                                    \n                                \n                                    \n                                        Contact Us, Test Driven Development\n                                    \n                                \n                                    \n                                        Agile\n                                    \n                                \n                                    \n                                        Let's Build a Tank\n                                    \n                                \n                                    \n                                        Iterative Software Development, Services\n                                    \n                                \n                                    \n                                        Case Studies\n                                    \n                                \n                                    \n                                        Technologies\n                                    \n                                \n                                    \n                                        Industries, Schedule a Call\n                                    \n                                \n                                    \n                                        Blog\n                                    \n                                \n                                    \n                                        How much does it cost?\n                                    \n                                \n                                    \n                                        60/6K Guarantee\n                                    \n                                \n                                    \n                                        Careers", "title": "Account Executive", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "mailto:<EMAIL>", "description": "Thank you for your interest in Level 12; while we are currently not actively hiring for these specific positions, we encourage you to submit an application for consideration in the future should opportunities arise that align with your skills and experience. Overview Pylons Cloud Innovations, a fast-growing Salesforce consulting partner based in the Louisville, KY, metro area, is looking for a high-energy account executive who can recognize opportunities and turn leads into long-lasting partnerships. As an account executive you will communicate directly with clients and prospects, understand their individual needs, and recommend products or services that maximize value. You will also assist in developing sales strategies and establishing quotas. You should be an adaptable, knowledgeable multi-tasker with strong computer and communication skills. Responsibilities Outreach to leads and prospects by phone, email, and text. Schedule and lead discovery calls with prospects. Present and demonstrate the value of products and services to prospects and partners. Follow up with prospects several times throughout the sales cycle to ensure needs are being met. Engage and build rapport with prospects and partners at meetings, conferences, and networking events. Assist in development of sales strategies and quotas. Stay current on company offerings and industry trends. Maintain a database of contact information. Build long-lasting, mutually beneficial relationships with external contacts and internal teams. Qualifications Required: Excellent communication/presentation skills and ability to build relationships Organizational and time management skills Enthusiastic and passionate Ability to travel approximately 25% of the time Proficiency in MS Office, Google Workspace, & similar Preferred: Proven experience as an Account Executive, or other sales/customer service role Knowledge of CRM software (e. g. Salesforce) Basic copywriting and/or graphic design skills Based within 100 miles of Louisville, KY About Us At our company, we maintain a team-focused and team-driven environment. We believe that our people working together with the spirit of “we over me” is what drives the success and growth of our company and creates positive outcomes for our clients and community. While basic sales skills and a positive attitude are required, we are happy to train and mentor an account executive in the beginning stages of their career as we hope to retain our team members for the long term. Pylons Cloud Innovations is a fast-growing company with opportunities for growth for the right fit team members. Are you the right fit? More Details This is   nm,.     a full time and fully remote position. TO BE CONSIDERED, please submit a COVER LETTER with your resume explaining why you think you would be a great fit for our team and company to hrteam@level12. io.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.level12.io/careers/full-stack-web-app-developer-senior/", "company_id": 3400, "source": 3, "skills": "We have a commitment to transparency and offer a \"no surprises experience\" throughout the\ninterview and hiring process.  We value candor...as evidenced by the length of this job\ndescription. :)\n\nSpeaking of job descriptions, we don't let HR people write ours.  They are written by software\ndevs for software devs.\n\nOur CEO prefers the title <PERSON><PERSON>, Chief Executive Developer.  Engineering and operational concerns\ndon't take a back seat when potential sales come to the front door.\n\nWe practice and preach sound development practices. You are likely to learn and grow as a\ndeveloper while working here.\n\nWe aren't trying to compete with FAANG compensation.  But outside that bubble, we are competitive.\nSee below for specifics on salary and benefits.\n\nYou will be working remote with a team that is all working remote and has been for years.\nLet's make the best use of our time by not commuting.\n\nWe have a no-drama office policy. We value and cultivate enjoyable working relationships among\nteam members.\n\nWe emphasize work/life balance and adopt policies that make sure our people don't get burnt out.\nFor instance, our PTO/Vacation policies are designed so that you actually use them.\n\nA commitment to Agile Principles while not\nbeing enslaved to any particular methodology.\n\nYou like building full stack web applications with technologies like Python, React, SQL, etc.\n\nYou are committed to automated testing of all the software you write (our apps typically have\n92%+ test coverage).\n\nYou recognize that there is a lot of idealism in the software development community and are not\ndisenchanted with the the day-to-day realities of programming.\n\nYou like working independently but can contribute to a team as needed\n\nIf you apply, we guarantee that we will give you a response, whether \"yay\" or \"nay\". No black\nholes here!\n\nHard requirement: At this time we are only considering candidates who can work in the U.S.\nwithout sponsorship and can fit their workday into the 8am-7pm ET range. We are open to international candidates working in similar time zones, so long as  your country's laws permit you to work for a U.S. based company on a contract basis., Keep our dev teams relatively small\nEmphasize a 40 hour work week\nAre candid with our clients and set realistic expectations for them\nAre relatively slow adopters of new technologies (so we can see if there is actual value\nthere...not just hype and thereby avoid needless churn)\nPromote professionalism, mitigate drama, and cultivate reasonable relationships with each other\nRegularly seek input from the team on what isn't working for them or where improvements can be\nmade, Technical Skills\nAre able to write modular, well-tested, and maintainable code\nKnow at least two software development domains (SQL, Front-End, Back-End) really well and are capable of radiating that knowledge to our team and beyond\nAre able to work on multiple projects as needed\n\n\nLeadership\nLeads the design for customer projects with feedback from other engineers\nProposes new ideas for improving the development team, customer projects, and/or our tech stack\nAdheres to and promotes our development culture and mission\nFor Team Leads (see below): invests in other team members to see that they are growing as individuals and to facilitate productivity within our team.\n\n\nCode quality\nLeaves code in substantially better shape than before\nFixes bugs/regressions quickly\nMonitors overall code quality/build failures\nCreates tests religiously and makes sure the rest of the team is doing the same\nProactively identifies and reduces technical debt\nProactively defines and solves important architectural issues that may hinder development\n\n\nCommunication\nProvides thorough and timely code feedback for peers\nAble to communicate clearly on technical topics\nKeeps issues up-to-date with progress\nHelps guide other merge requests to completion\nHelps with recruiting and interviewing\nIs able to manage conversations directly with customer contacts when needed.\n\n\nPerformance & Scalability\nExcellent at writing production-ready code with little assistance\nAble to write complex code that can scale with a significant number of users\nAvoids premature optimization, Are able to write modular, well-tested, and maintainable code\nKnow at least two software development domains (SQL, Front-End, Back-End) really well and are capable of radiating that knowledge to our team and beyond\nAre able to work on multiple projects as needed, Leads the design for customer projects with feedback from other engineers\nProposes new ideas for improving the development team, customer projects, and/or our tech stack\nAdheres to and promotes our development culture and mission\nFor Team Leads (see below): invests in other team members to see that they are growing as individuals and to facilitate productivity within our team., Leaves code in substantially better shape than before\nFixes bugs/regressions quickly\nMonitors overall code quality/build failures\nCreates tests religiously and makes sure the rest of the team is doing the same\nProactively identifies and reduces technical debt\nProactively defines and solves important architectural issues that may hinder development, Provides thorough and timely code feedback for peers\nAble to communicate clearly on technical topics\nKeeps issues up-to-date with progress\nHelps guide other merge requests to completion\nHelps with recruiting and interviewing\nIs able to manage conversations directly with customer contacts when needed., Excellent at writing production-ready code with little assistance\nAble to write complex code that can scale with a significant number of users\nAvoids premature optimization, General Requirements\n6+ years developing database-driven web applications with Python\nIf you only have 2-3 years of Python experience, we may still consider your application, but we will need to see experience with other dynamic languages: Ruby, PHP, JavaScript/Node; MVC (ish) web architecture; and strong experience/skills in two of the following: SQL, modern front-end JavaScript (React preferred), or automated testing (TDD a plus).\n\n\nYou agree with and would be pleased to work under our development mission statement and guiding principles\nYou can reason about software, algorithms, and performance from a high level\nYou are (or are willing to be) committed to automated testing in your software development process\nStrong oral and written communication skills\nSelf-motivated and have strong organizational skills\nUS citizen or ability to work in the United States without sponsorship. We are also open to international candidates working in similar time zones, so long as your country's laws permit you to work for a U.S. based company on a contract basis.\nBachelor’s Degree in computer science (or similar degree) or ability to demonstrate comparable education & experience\n\n\nLanguages & Tools on a scale of 1 (novice) to 5 (expert)\nProficient (4): Prefer significant experience working with Python in a web development context.\nProficient (4): Database Development (SQL, multiple-joins, views, triggers, stored procedures)\nProficient (4): Common Python libraries: Django, Flask, SQLAlchemy, Celery, Requests, pytest, etc.\nProficient (4): Writing unit and functional tests. Believing in a TDD approach is a big plus.\nCompetent (3): HTML(5), CSS/SASS, web standards\nCompetent (3): JavaScript, Ajax, JSON, React, Vue, etc.\nCompetent (3): Distributed Version Control (Git, Mercurial)\nCompetent (3): Networking fundamentals (HTTP, DNS, TCP/IP, etc.)\nCompetent (3): Linux and related tools, 6+ years developing database-driven web applications with Python\nIf you only have 2-3 years of Python experience, we may still consider your application, but we will need to see experience with other dynamic languages: Ruby, PHP, JavaScript/Node; MVC (ish) web architecture; and strong experience/skills in two of the following: SQL, modern front-end JavaScript (React preferred), or automated testing (TDD a plus).\n\n\nYou agree with and would be pleased to work under our development mission statement and guiding principles\nYou can reason about software, algorithms, and performance from a high level\nYou are (or are willing to be) committed to automated testing in your software development process\nStrong oral and written communication skills\nSelf-motivated and have strong organizational skills\nUS citizen or ability to work in the United States without sponsorship. We are also open to international candidates working in similar time zones, so long as your country's laws permit you to work for a U.S. based company on a contract basis.\nBachelor’s Degree in computer science (or similar degree) or ability to demonstrate comparable education & experience, If you only have 2-3 years of Python experience, we may still consider your application, but we will need to see experience with other dynamic languages: Ruby, PHP, JavaScript/Node; MVC (ish) web architecture; and strong experience/skills in two of the following: SQL, modern front-end JavaScript (React preferred), or automated testing (TDD a plus)., Proficient (4): Prefer significant experience working with Python in a web development context.\nProficient (4): Database Development (SQL, multiple-joins, views, triggers, stored procedures)\nProficient (4): Common Python libraries: Django, Flask, SQLAlchemy, Celery, Requests, pytest, etc.\nProficient (4): Writing unit and functional tests. Believing in a TDD approach is a big plus.\nCompetent (3): HTML(5), CSS/SASS, web standards\nCompetent (3): JavaScript, Ajax, JSON, React, Vue, etc.\nCompetent (3): Distributed Version Control (Git, Mercurial)\nCompetent (3): Networking fundamentals (HTTP, DNS, TCP/IP, etc.)\nCompetent (3): Linux and related tools, Salary: We are targeting $115K-$145K but would consider stretching for the right candidate.\nWe'll talk about salary & benefits in our first Zoom interview to make sure we are mutually\nin the compensation ballpark.\nEveryone wants to be paid as much as possible, but the economics of business usually require\nthat we meet in the middle.\n\n\nProfit Sharing: In addition to a salary, we payout profit sharing bonuses twice a year.\nOne of the best things about working for us is that we really value work/family balance.\nWe rarely work more than 40 hours a week.\nWe know there are devs out there putting in 50-60 hours a week. We can give them 10-20 hours a\nweek of their life back.\n\n\nPTO/Vacation (5+ weeks total):\nEveryone receives 3 weeks of PTO/Vacation per year\n5-12 sick days per year\n9 holidays\n\n\nHealthcare:\nWe provide Health Insurance through an Anthem Silver Blue Access PPO\nWe cover 100% of the premium for employees and 50% for dependents\nThe plan has a broad nationwide network which should give all our employees, regardless of where\nthey live, plenty of options for in-network providers\nThe PPO is matched with an Health Savings Account (HSA) and we match employee contributions to\nthe HSA\nWe unfortunately cannot offer some benefits such as healthcare to international candidates.\n\n\nFlexibility:\nWhile we expect a regular schedule during normal business hours (fitting in the window of 8-7\nET), we are pretty flexible when schedule changes are needed for legitimate reasons.\nIt's not uncommon for our employees to take time off during the week and make it up during the\nevening or weekend so as to avoid needing to use PTO.\n\n\nTelecommuting/Remote:\nWe all work remotely, and have for years.\nThat means our tools (Slack, Zoom, GitHub, etc.) and planning are designed to work for a\ndistributed team.\nWe are open to international candidates working in similar time zones, so long as  your country's laws permit you to work for a U.S. based company on a contract basis.\n\n\nWe Care: It's easy to say, harder to do. We all need to make a living, our company needs to be\nsuccessful in the marketplace, and hard decisions sometimes need to be made. But, at the end of\nthe day, we really care about our employees, their families, their needs, and their desires., We'll talk about salary & benefits in our first Zoom interview to make sure we are mutually\nin the compensation ballpark.\nEveryone wants to be paid as much as possible, but the economics of business usually require\nthat we meet in the middle., We rarely work more than 40 hours a week.\nWe know there are devs out there putting in 50-60 hours a week. We can give them 10-20 hours a\nweek of their life back., Everyone receives 3 weeks of PTO/Vacation per year\n5-12 sick days per year\n9 holidays, We provide Health Insurance through an Anthem Silver Blue Access PPO\nWe cover 100% of the premium for employees and 50% for dependents\nThe plan has a broad nationwide network which should give all our employees, regardless of where\nthey live, plenty of options for in-network providers\nThe PPO is matched with an Health Savings Account (HSA) and we match employee contributions to\nthe HSA\nWe unfortunately cannot offer some benefits such as healthcare to international candidates., While we expect a regular schedule during normal business hours (fitting in the window of 8-7\nET), we are pretty flexible when schedule changes are needed for legitimate reasons.\nIt's not uncommon for our employees to take time off during the week and make it up during the\nevening or weekend so as to avoid needing to use PTO., We all work remotely, and have for years.\nThat means our tools (Slack, Zoom, GitHub, etc.) and planning are designed to work for a\ndistributed team.\nWe are open to international candidates working in similar time zones, so long as  your country's laws permit you to work for a U.S. based company on a contract basis., All projects managed in a Git\nDevelopment workflow using GitHub for pull requests, issue management, code coverage (using\nCodeCov), CI integration (using CircleCi), etc.\nDeveloper friendly project setup: all our projects can be run locally on a developer's machine.\nTest-centric development methodology:\nAsking \"how can we test this\" is ingrained into our engineering and development workflow.\nWe have Continuous Integration (CI) running for all of our projects for at least the backend\ncode (Python mostly) and some of the projects also run the JavaScript tests in CI.\n\n\nA focus on Agile Principles while not being enslaved to them.\nA openness to changing our processes and using new technologies when those changes add value to\nLevel 12 and/or our customers., Asking \"how can we test this\" is ingrained into our engineering and development workflow.\nWe have Continuous Integration (CI) running for all of our projects for at least the backend\ncode (Python mostly) and some of the projects also run the JavaScript tests in CI., Integrity: We are honest, trustworthy, reliable, and ethical. We act the same, regardless of\nwho is watching, and especially when no-one is watching.  If you use pirated software or media,\ndon't mind \"tweaking\" the truth, or think porn == entertainment, this is not the job for you!\nWork ethic: We work hard and like to work hard. We also work smart.  We want to do in 40\nhours a week what other people need 50+ hours to do.\nWork/life balance: All work and no play is a lose-lose situation. We work hard during work\nhours but are committed to protecting our schedules and ensuring adequate time for rest and\nfamily responsibilities.\nFlexibility: Life happens and rarely on schedule or according to plan. Flexibility keeps us\nfrom snapping during the storms of life. When things change, we need to be able to role with the\npunches.\nDedication: We desire to contribute and to create value for our clients and the company. We\ndemonstrate a high level of “ownership” for and initiative with projects assigned to us.\nTeachable: We are open to and desire feedback/correction. We are willing to ask for help even\nif it makes us “look bad.”\nKaizen, continuous improvement: We recognize the value of continually striving to improve\nourselves and the processes around us.\nResourceful: We like problem solving and seek to use all resources at our disposal in an\nefficient manner to troubleshoot. We know when to ask someone for help and when more effort is\nneeded on our part.\nGifting / Core Competency: People are often gifted in ways that make them very good at doing\nsome things and not very good at doing others. Our employees and our company needs to be\noperating in areas we are strongest in, i.e. focusing on our core competencies.\nAttention to detail: Grand visions, out of necessity, are often presented in abbreviated\nform, but making grand visions a reality requires lots of detail.\nProfessional: We take our work seriously, striving for competence in everything we do and\nexcellence in as many things as possible.\nHumility: We often take ourselves too seriously. We are a stunning mix of great potential and\ngreat weakness. We will have the greatest success when we acknowledge both.\nFinitude: We are finite beings, more limited than we often want to admit. But great strength\ncomes from knowing and admitting where you are weak and compensating for it.\nBalance: Most of life requires trade-offs, we can not be excellent in everything we do.\nPatience & Longevity: We often overestimate what we can do in five years and\nunderestimate what we can do in 20.\nGenerosity: It is better to give than to receive., Send an email to start the process:\nTo: <EMAIL>\nSubject: Web Developer Application\nBody:\nInclude a bit about yourself. We hire humans, not robots. 🙂\nInclude answers to the \"Application Questions\" below.\n\n\nAttach: resume, preferably in PDF\nDon't fret about your resume content or formatting.  We are looking almost exclusively for\nrelevant work history.\nWe will send out a questionnaire for more info, so no need for you to guess at what we want to know.\n\n\n\n\nNo cookie-cutter cover letters please!\nYou should hear back from us in a week or so.  You are welcome to email us to check on the status\nof your application: <EMAIL>, To: <EMAIL>\nSubject: Web Developer Application\nBody:\nInclude a bit about yourself. We hire humans, not robots. 🙂\nInclude answers to the \"Application Questions\" below.\n\n\nAttach: resume, preferably in PDF\nDon't fret about your resume content or formatting.  We are looking almost exclusively for\nrelevant work history.\nWe will send out a questionnaire for more info, so no need for you to guess at what we want to know., Include a bit about yourself. We hire humans, not robots. 🙂\nInclude answers to the \"Application Questions\" below., Don't fret about your resume content or formatting.  We are looking almost exclusively for\nrelevant work history.\nWe will send out a questionnaire for more info, so no need for you to guess at what we want to know., We typically evaluate candidates to join our team as full-time W2 employees.\nHowever, if your current job situation is such that you'd be interested in working full-time (or\nclose to FT) for 30-90 days while we got to know you better and vice-versa, please answer in the\naffirmative.  It's not a guarantee we'd offer or even consider you for this arrangement, just\ngood to know for those who are interested.\nAnswering \"no\" to this question will not have any adverse effect on your application., As close to a typical work day as we can get. We just want to see what it's like to work with each other.\nWe'll assign you work based on a previous real-world project we performed This is a sample project, we're not using candidates for free or cheap labor.\nWe will be available via Slack or Zoom throughout the day to talk through the work and assist you as needed., Our Team\n                                    \n                                \n                                    \n                                        Our Values\n                                    \n                                \n                                    \n                                        Consultants First\n                                    \n                                \n                                    \n                                        Challenge\n                                    \n                                \n                                    \n                                        Contact Us, Test Driven Development\n                                    \n                                \n                                    \n                                        Agile\n                                    \n                                \n                                    \n                                        Let's Build a Tank\n                                    \n                                \n                                    \n                                        Iterative Software Development, Services\n                                    \n                                \n                                    \n                                        Case Studies\n                                    \n                                \n                                    \n                                        Technologies\n                                    \n                                \n                                    \n                                        Industries, Schedule a Call\n                                    \n                                \n                                    \n                                        Blog\n                                    \n                                \n                                    \n                                        How much does it cost?\n                                    \n                                \n                                    \n                                        60/6K Guarantee\n                                    \n                                \n                                    \n                                        Careers", "title": "Full Stack Web App Developer – Senior", "location": "", "location_type": "remote", "job_type": "part_time", "min_experience": 1, "max_experience": 5, "apply_link": "mailto:<EMAIL>", "description": "Thank you for your interest in Level 12; while we are currently not actively hiring for these specific positions, we encourage you to submit an application for consideration in the future should opportunities arise that align with your skills and experience. TL;DR: Why Consider Us? We have a commitment to transparency and offer a \"no surprises experience\" throughout the interview and hiring process. We value candor. . . as evidenced by the length of this job description. :) Speaking of job descriptions, we don't let HR people write ours. They are written by software devs for software devs. Our CEO prefers the title CED, Chief Executive Developer. Engineering and operational concerns don't take a back seat when potential sales come to the front door. We practice and preach sound development practices. You are likely to learn and grow as a developer while working here. We aren't trying to compete with FAANG compensation. But outside that bubble, we are competitive. See below for specifics on salary and benefits. You will be working remote with a team that is all working remote and has been for years. Let's make the best use of our time by not commuting. We have a no-drama office policy. We value and cultivate enjoyable working relationships among team members. We emphasize work/life balance and adopt policies that make sure our people don't get burnt out. For instance, our PTO/Vacation policies are designed so that you actually use them. A commitment to Agile Principles while not being enslaved to any particular methodology. You like building full stack web applications with technologies like Python, React, SQL, etc. You are committed to automated testing of all the software you write (our apps typically have 92%+ test coverage). You recognize that there is a lot of idealism in the software development community and are not disenchanted with the the day-to-day realities of programming. You like working independently but can contribute to a team as needed If you apply, we guarantee that we will give you a response, whether \"yay\" or \"nay\". No black holes here! Hard requirement: At this time we are only considering candidates who can work in the U. S. without sponsorship and can fit their workday into the 8am-7pm ET range. We are open to international candidates working in similar time zones, so long as your country's laws permit you to work for a U. S. based company on a contract basis. Some Quick Advice Our CEO was asked, \"What advice do you have for an engineer looking to get hired on your team? \" His advice, \"The hiring process is difficult. . . don't fear rejection. . . the problem might be us. \" The full answer given in a podcast is queued up for you in the player below: ⇊ Expand ⇊ The Job - Full Stack Web Developer Daily responsibilities primarily consist of coding database-driven web applications and other web-related development work. In a typical day, you will likely work mostly with Python based web applications using frameworks like Django or Flask that interact heavily with a database (usually PostgreSQL). In the course of that work, you will have to interact with related technologies like JavaScript, React/Vue, HTML, and CSS/SASS. There's also opportunities for you to be involved in devops work if interested. This is not a design job. It will require a lot of in-depth programming and database work including the ability to architect maintainable code that gets the job done. While we do prefer to do most of our back-end work in Python, we will occasionally take projects in other languages if they seem to be a good fit for us and the customer. Some days you will jump from project to project as issues come up. At other times you may work primarily on one project for weeks, months, or occasionally years. Our customers have varied needs and so we tend to have varied responsibilities and projects. We have a focus on serving the customer and making software conform to their company rather than making the company conform to software. Management Your immediate manager will be a \"Team Lead. \" This is a senior developer, project manager, and team captain all rolled into one. It's also someone whose proven to be talented with both code and people, especially communication. We don't believe non-technical managers bring a lot of value to a development team, so we don't use them. Ditto non-technical project managers. We keep our dev teams small and they usually have a small number of projects so that our team leads don't get overwhelmed. Communication with clients usually involves the team lead and the developers working on the project. We try to avoid the \"phone game\" whenever possible. The ability to take ownership for a project, engineer a sound technical solution, and \"drive\" a project to completion is essential. Your team lead will be a ready and willing resource to help you architect solutions and/or solve problems, but won't micro-manage. If you aren't asking them for help or indicating you have a problem, they will assume you are making good progress on the issues assigned to you during sprint planning, which happens every two weeks. Our CEO/CED and CTO are both software engineers and heavily involved in the engineering operations of the company. They oversee the health of the development team and projects, help our sales team evaluate opportunities for technical and capacity fit, gather feedback from the devs on what is and isn't working in our tech stack and/or processes, and consider the value that emerging technologies might bring to our organization. Sustainability \"Agile processes promote sustainable development. The sponsors, developers, and users should be able to maintain a constant pace indefinitely\" (Agile Manifesto principle). We actively manage every part of our organization through the lens of what is reasonably sustainable. For example, it's why we: Keep our dev teams relatively small Emphasize a 40 hour work week Are candid with our clients and set realistic expectations for them Are relatively slow adopters of new technologies (so we can see if there is actual value there. . . not just hype and thereby avoid needless churn) Promote professionalism, mitigate drama, and cultivate reasonable relationships with each other Regularly seek input from the team on what isn't working for them or where improvements can be made We don't want anyone to burn out. We'd prefer our team to be here and fully engaged for years to come. Senior Developer The position we have open is for a Senior Developer. Senior Developers usually have 6-10+ years relevant experience and meet the following requirements: Technical Skills Are able to write modular, well-tested, and maintainable code Know at least two software development domains (SQL, Front-End, Back-End) really well and are capable of radiating that knowledge to our team and beyond Are able to work on multiple projects as needed Leadership Leads the design for customer projects with feedback from other engineers Proposes new ideas for improving the development team, customer projects, and/or our tech stack Adheres to and promotes our development culture and mission For Team Leads (see below): invests in other team members to see that they are growing as individuals and to facilitate productivity within our team. Code quality Leaves code in substantially better shape than before Fixes bugs/regressions quickly Monitors overall code quality/build failures Creates tests religiously and makes sure the rest of the team is doing the same Proactively identifies and reduces technical debt Proactively defines and solves important architectural issues that may hinder development Communication Provides thorough and timely code feedback for peers Able to communicate clearly on technical topics Keeps issues up-to-date with progress Helps guide other merge requests to completion Helps with recruiting and interviewing Is able to manage conversations directly with customer contacts when needed. Performance & Scalability Excellent at writing production-ready code with little assistance Able to write complex code that can scale with a significant number of users Avoids premature optimization Leadership In addition to the leadership expectations noted above, some of our senior developers will also be involved in leading other developers and interacting with customers. We realize not every senior developer is as good with people as they are with code and that's ok. We need and value individual contributors. But if you like and are good at interacting with people, we will have opportunities for you to lead teams and/or projects. Everything Else General Requirements 6+ years developing database-driven web applications with Python If you only have 2-3 years of Python experience, we may still consider your application, but we will need to see experience with other dynamic languages: Ruby, PHP, JavaScript/Node; MVC (ish) web architecture; and strong experience/skills in two of the following: SQL, modern front-end JavaScript (React preferred), or automated testing (TDD a plus). You agree with and would be pleased to work under our development mission statement and guiding principles You can reason about software, algorithms, and performance from a high level You are (or are willing to be) committed to automated testing in your software development process Strong oral and written communication skills Self-motivated and have strong organizational skills US citizen or ability to work in the United States without sponsorship. We are also open to international candidates working in similar time zones, so long as your country's laws permit you to work for a U. S. based company on a contract basis. Bachelor’s Degree in computer science (or similar degree) or ability to demonstrate comparable education & experience Languages & Tools on a scale of 1 (novice) to 5 (expert) Proficient (4): Prefer significant experience working with Python in a web development context. Proficient (4): Database Development (SQL, multiple-joins, views, triggers, stored procedures) Proficient (4): Common Python libraries: Django, Flask, SQLAlchemy, Celery, Requests, pytest, etc. Proficient (4): Writing unit and functional tests. Believing in a TDD approach is a big plus. Competent (3): HTML(5), CSS/SASS, web standards Competent (3): JavaScript, Ajax, JSON, React, Vue, etc. Competent (3): Distributed Version Control (Git, Mercurial) Competent (3): Networking fundamentals (HTTP, DNS, TCP/IP, etc. ) Competent (3): Linux and related tools We Are Offering Salary: We are targeting $115K-$145K but would consider stretching for the right candidate. We'll talk about salary & benefits in our first Zoom interview to make sure we are mutually in the compensation ballpark. Everyone wants to be paid as much as possible, but the economics of business usually require that we meet in the middle. Profit Sharing: In addition to a salary, we payout profit sharing bonuses twice a year. One of the best things about working for us is that we really value work/family balance. We rarely work more than 40 hours a week. We know there are devs out there putting in 50-60 hours a week. We can give them 10-20 hours a week of their life back. PTO/Vacation (5+ weeks total): Everyone receives 3 weeks of PTO/Vacation per year 5-12 sick days per year 9 holidays Healthcare: We provide Health Insurance through an Anthem Silver Blue Access PPO We cover 100% of the premium for employees and 50% for dependents The plan has a broad nationwide network which should give all our employees, regardless of where they live, plenty of options for in-network providers The PPO is matched with an Health Savings Account (HSA) and we match employee contributions to the HSA We unfortunately cannot offer some benefits such as healthcare to international candidates. Flexibility: While we expect a regular schedule during normal business hours (fitting in the window of 8-7 ET), we are pretty flexible when schedule changes are needed for legitimate reasons. It's not uncommon for our employees to take time off during the week and make it up during the evening or weekend so as to avoid needing to use PTO. Telecommuting/Remote: We all work remotely, and have for years. That means our tools (Slack, Zoom, GitHub, etc. ) and planning are designed to work for a distributed team. We are open to international candidates working in similar time zones, so long as your country's laws permit you to work for a U. S. based company on a contract basis. We Care: It's easy to say, harder to do. We all need to make a living, our company needs to be successful in the marketplace, and hard decisions sometimes need to be made. But, at the end of the day, we really care about our employees, their families, their needs, and their desires. The Company - Level 12 Who Are We? Level 12 is development firm that specializes in building custom software for our customers. We manage the entire process including planning, managing, and building software exactly as the customer needs it. We serve customers in diverse industries including: non-profit, automotive, manufacturing, logistics, and financial. Our customers range in size from small startups to Fortune 500. We set ourselves apart by digging into the customer's problem until we can own it and then delivering highly functional software that brings significant value to the customer's organization. That may not sound unique, but the longer we do this, the more we encounter people who have been burned by bad development experiences with other firms. We look for long-term relationships with our customers and have some that go back to 2005. We may not be changing the world, but we are impacting the lives of our customers and their employees. You don't have to take our word for it, checkout some of our reviews by our customers. This is a video we often use with our prospective customers to explain our agile/iterative development process (< 2 mins): ⇊ Expand ⇊ On Development Best Practices Behind the scenes, we are using software development best practices to ensure that the code we deliver is flexible, maintainable, and works well for the user for years to come. We know what it's like to work in a substandard development environment or culture that accepts or maybe even promotes practices that kill developer productivity and enjoyment. We work hard at promoting development best practices and ingraining them into our culture. If you come work for us, you can look forward to: All projects managed in a Git Development workflow using GitHub for pull requests, issue management, code coverage (using CodeCov), CI integration (using CircleCi), etc. Developer friendly project setup: all our projects can be run locally on a developer's machine. Test-centric development methodology: Asking \"how can we test this\" is ingrained into our engineering and development workflow. We have Continuous Integration (CI) running for all of our projects for at least the backend code (Python mostly) and some of the projects also run the JavaScript tests in CI. A focus on Agile Principles while not being enslaved to them. A openness to changing our processes and using new technologies when those changes add value to Level 12 and/or our customers. If you'd like to learn more about what makes us tick, our Chief Executive Developer (CED) gave a talk on the importance of CI and automation in an agile software development process (embedded video) and has given other talks and presentations on various topics. ⇊ Expand ⇊ Character First Level 12 was founded on biblical principles and has biblically informed Values. See the about page for our Mission, Values, Vision, and Purpose statements. Due to differing or a lack of religious convictions, we respect that employees may not be able to identify with our faith-based Vision and Purpose. Applicants are not asked questions regarding nor expected to discuss their religious beliefs during the application process or during employment. But, we do expect all applicants and employees to share our Values. Our Values describe not only who we are as an organization, but also who we are not. They encompass our ethics, principles, and beliefs about our organization and its relationship to the world. They communicate to employees the behaviors that are approved (and rewarded), and by extension those that are not. Integrity: We are honest, trustworthy, reliable, and ethical. We act the same, regardless of who is watching, and especially when no-one is watching. If you use pirated software or media, don't mind \"tweaking\" the truth, or think porn == entertainment, this is not the job for you! Work ethic: We work hard and like to work hard. We also work smart. We want to do in 40 hours a week what other people need 50+ hours to do. Work/life balance: All work and no play is a lose-lose situation. We work hard during work hours but are committed to protecting our schedules and ensuring adequate time for rest and family responsibilities. Flexibility: Life happens and rarely on schedule or according to plan. Flexibility keeps us from snapping during the storms of life. When things change, we need to be able to role with the punches. Dedication: We desire to contribute and to create value for our clients and the company. We demonstrate a high level of “ownership” for and initiative with projects assigned to us. Teachable: We are open to and desire feedback/correction. We are willing to ask for help even if it makes us “look bad. ” Kaizen, continuous improvement: We recognize the value of continually striving to improve ourselves and the processes around us. Resourceful: We like problem solving and seek to use all resources at our disposal in an efficient manner to troubleshoot. We know when to ask someone for help and when more effort is needed on our part. Gifting / Core Competency: People are often gifted in ways that make them very good at doing some things and not very good at doing others. Our employees and our company needs to be operating in areas we are strongest in, i. e. focusing on our core competencies. Attention to detail: Grand visions, out of necessity, are often presented in abbreviated form, but making grand visions a reality requires lots of detail. Professional: We take our work seriously, striving for competence in everything we do and excellence in as many things as possible. Humility: We often take ourselves too seriously. We are a stunning mix of great potential and great weakness. We will have the greatest success when we acknowledge both. Finitude: We are finite beings, more limited than we often want to admit. But great strength comes from knowing and admitting where you are weak and compensating for it. Balance: Most of life requires trade-offs, we can not be excellent in everything we do. Patience & Longevity: We often overestimate what we can do in five years and underestimate what we can do in 20. Generosity: It is better to give than to receive. ⇊ Expand ⇊ Next Step - Ready To Apply? Have Questions? Despite the length of this post, we may have missed something. If you have a question that the job description didn't answer, and you would like it answered before applying, please email us: hrteam@level12. io. We usually respond the same or next business day. Our Promise If You Apply Ever send a resume in only to never. . . hear. . . anything? We promise we won't do that. If you follow the directions above, you will receive a response from us within a week or so. If you don't hear anything back from us, double check whether you followed the instructions above (🙂) and then email us for a status update. Application Instructions If you have read all of the above, carefully, and are interested and qualified, then here are your next steps: Send an email to start the process: To: apply-senior-dev@level12. io Subject: Web Developer Application Body: Include a bit about yourself. We hire humans, not robots. 🙂 Include answers to the \"Application Questions\" below. Attach: resume, preferably in PDF Don't fret about your resume content or formatting. We are looking almost exclusively for relevant work history. We will send out a questionnaire for more info, so no need for you to guess at what we want to know. No cookie-cutter cover letters please! You should hear back from us in a week or so. You are welcome to email us to check on the status of your application: hrteam@level12. io Application Questions Any feedback on the job description? Too long? Too short? Find a typo? No. . . that's ok too. This isn't a test. :) Based on your experience, how do you grade a developer? That is, what makes the difference between a mediocre developer (1 of 5) and stellar developer (5 of 5). Where do you fit in? What excites you about programming? What discourages you? What makes a company's culture enjoyable to you? Not enjoyable? Based on this job description what do you find most attractive about this position? What do you find less attractive? Based on our company website and anything else you may know about us, what most attracts you to our company? What concerns you? Where did you originally find this job posting? Where do you live (city/state)? Do you have a comfortable, distraction-free environment setup for remote work? Are you a US citizen or otherwise eligible to work in the United States without sponsorship? Note: as stated above, US Citizenship or ability to work in the US without sponsorship is a hard requirement as is a willingness to work generally between 8am - 7pm Eastern. We are open to international candidates working in similar time zones, so long as your country's laws permit you to work for a US based company on a contract basis. Are you potentially interested in a 30-90 day contract-to-hire position? We typically evaluate candidates to join our team as full-time W2 employees. However, if your current job situation is such that you'd be interested in working full-time (or close to FT) for 30-90 days while we got to know you better and vice-versa, please answer in the affirmative. It's not a guarantee we'd offer or even consider you for this arrangement, just good to know for those who are interested. Answering \"no\" to this question will not have any adverse effect on your application. Regarding Authenticity: as you answer these questions, please keep in mind that we prefer candor over flattery and we are under no delusion that this is the perfect position or we are the perfect company. Likewise, we don't believe there are any perfect candidates. We understand that companies and people are made up of both strengths and weaknesses and the better we understand both your strengths and weaknesses the more likely we are to make a wise hiring decision. Please take time to answer the questions thoughtfully and accurately. Check your SPAM! ! ! If you don't hear from us, check your SPAM folder. All emails that we send will come from the domain \"level12. io\". The Rest of the Process Our application process is outlined roughly below. But, before you get there, we want to apologize in advance if this process seems. . . imposing. We have put considerable thought and refinement into each one of these steps in an effort to make sure our hiring process is as well crafted as our software. And just like software, hiring is a lot more complex than it might seem on the surface. Our hiring process is far from perfect (like our software), we are still learning and tweaking, but we want to assure you that each of these steps gives us crucial information regarding you and your development abilities that is essential for helping us to determine if this is a good match. Consider this: our entire process is less than a week's worth of effort to make sure that where you spend the next 1-5 years of your life is a good fit. Isn't that worth it? Keep in mind that we have deliberately structured our process so that the earlier stages require less effort. Our hope is that if you make it to the later stages of our process, where the time commitment increases, you will have had a chance to get to know us a bit better so you can decide if the time investment on your part is worth it. We care about your time (and ours) and do our best not to waste it! Application Steps Evaluate resume and initial email correspondence Technical skills questionnaire Skills evaluation: 60-90 minute work simulation exercise Zoom interview(s): 45-90 minutes in one or two interviews to get to know you & your technical abilities Skills tests - phase I: three real-world programming challenges, no trick questions here (paid) Skills tests - phase II: project-based skills test: we give you a small project description and you build the best app you can (paid) Skills tests review interview: 2-3 hours on a Zoom meeting with our dev leadership team to get to know you and review your skills test results Collaborative work day: As close to a typical work day as we can get. We just want to see what it's like to work with each other. We'll assign you work based on a previous real-world project we performed This is a sample project, we're not using candidates for free or cheap labor. We will be available via Slack or Zoom throughout the day to talk through the work and assist you as needed. If at any step we don't feel like it's a good match, we'll let you know promptly. We ask that you do the same for us. ⇊ Expand ⇊", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.level12.io/careers/full-stack-web-app-developer-mid/", "company_id": 3400, "source": 3, "skills": "We have a commitment to transparency and offer a \"no surprises experience\" throughout the\ninterview and hiring process.  We value candor...as evidenced by the length of this job\ndescription. :)\n\nSpeaking of job descriptions, we don't let HR people write ours.  They are written by software\ndevs for software devs.\n\nOur CEO prefers the title <PERSON><PERSON>, Chief Executive Developer.  Engineering and operational concerns\ndon't take a back seat when potential sales come to the front door.\n\nWe practice and preach sound development practices. You are likely to learn and grow as a\ndeveloper while working here.\n\nWe aren't trying to compete with FAANG compensation.  But outside that bubble, we are competitive.\nSee below for specifics on salary and benefits.\n\nYou will be working remote with a team that is all working remote and has been for years.\nLet's make the best use of our time by not commuting.\n\nWe have a no-drama office policy. We value and cultivate enjoyable working relationships among\nteam members.\n\nWe emphasize work/life balance and adopt policies that make sure our people don't get burnt out.\nFor instance, our PTO/Vacation policies are designed so that you actually use them.\n\nA commitment to Agile Principles while not\nbeing enslaved to any particular methodology.\n\nYou like building full stack web applications with technologies like Python, React, SQL, etc.\n\nYou are committed to automated testing of all the software you write (our apps typically have\n92%+ test coverage).\n\nYou recognize that there is a lot of idealism in the software development community and are not\ndisenchanted with the the day-to-day realities of programming.\n\nYou like working independently but can contribute to a team as needed\n\nIf you apply, we guarantee that we will give you a response, whether \"yay\" or \"nay\". No black\nholes here!\n\nHard requirement: At this time we are only considering candidates who can work in the U.S.\nwithout sponsorship and can fit their workday into the 8am-7pm ET range. We are open to international candidates working in similar time zones, so long as  your country's laws permit you to work for a U.S. based company on a contract basis., Keep our dev teams relatively small\nEmphasize a 40 hour work week\nAre candid with our clients and set realistic expectations for them\nAre relatively slow adopters of new technologies (so we can see if there is actual value\nthere...not just hype and thereby avoid needless churn)\nPromote professionalism, mitigate drama, and cultivate reasonable relationships with each other\nRegularly seek input from the team on what isn't working for them or where improvements can be\nmade, Technical Skills\nAre able to write modular, well-tested, and maintainable code\nKnow at least one software development domain (SQL, Front-End, Back-End) really well\nAre able to work on multiple projects as needed\n\n\nLeadership\nContributes to the design of customer projects with direction from the team lead and feedback\nfrom other engineers\nProposes new ideas for improving the development team, customer projects, and/or our tech stack\nAdheres to and promotes our development culture\nand mission\n\n\nCode quality\nLeaves code in substantially better shape than before\nFixes bugs/regressions quickly\nMonitors overall code quality/build failures\nCreates tests religiously and encourages the team to do the same\nProactively identifies and reduces technical debt\nIdentifies architectural issues that may hinder development, may need assistance from the team\nsolving such issues\n\n\nCommunication\nContributes to the code review process\nAble to communicate clearly on technical topics\nKeeps issues up-to-date with progress\nHelps guide other merge requests to completion\nIs able to participate in conversations with customer contacts when needed\n\n\nPerformance & Scalability\nCompetent at writing production-ready code with little assistance\nIs able to identify potential performance bottlenecks\nAvoids premature optimization, Are able to write modular, well-tested, and maintainable code\nKnow at least one software development domain (SQL, Front-End, Back-End) really well\nAre able to work on multiple projects as needed, Contributes to the design of customer projects with direction from the team lead and feedback\nfrom other engineers\nProposes new ideas for improving the development team, customer projects, and/or our tech stack\nAdheres to and promotes our development culture\nand mission, Leaves code in substantially better shape than before\nFixes bugs/regressions quickly\nMonitors overall code quality/build failures\nCreates tests religiously and encourages the team to do the same\nProactively identifies and reduces technical debt\nIdentifies architectural issues that may hinder development, may need assistance from the team\nsolving such issues, Contributes to the code review process\nAble to communicate clearly on technical topics\nKeeps issues up-to-date with progress\nHelps guide other merge requests to completion\nIs able to participate in conversations with customer contacts when needed, Competent at writing production-ready code with little assistance\nIs able to identify potential performance bottlenecks\nAvoids premature optimization, General Requirements\n3-6 years developing database-driven web applications with (preferably) Python\nCandidates without Python experience may still be considered but we will need to see experience with other dynamic languages: Ruby, PHP, JavaScript (Node); MVC (ish) web architecture, and experience in two of the following: SQL, modern front-end JavaScript (React preferred), or automated testing (TDD a plus).\n\n\nYou agree with and would be pleased to work under our development mission statement and guiding principles\nYou can reason about software, algorithms, and performance from a high level\nYou are (or are willing to be) committed to automated testing in your software development process\nStrong oral and written communication skills\nSelf-motivated and have strong organizational skills\nBachelor’s Degree in computer science (or similar degree) or ability to demonstrate comparable education & experience\n\n\nLanguages & Tools on a scale of 1 (novice) to 5 (expert)\nCompetent (3): Have significant experience working with dynamic languages (Python, PHP, Ruby, Node) in a web context.  Experience working with Python preferred.\nCompetent (3): Database Development (SQL, multiple-joins, aggregates)\nCompetent (3): HTML(5), CSS/SASS, web standards\nCompetent (3): JavaScript, Ajax, JSON, React, Vue, etc.\nCompetent (3): Writing unit and functional tests. Believing in a TDD approach is a big plus.\nCompetent (3): Version Control (Git, Mercurial)\nCompetent (3): Networking fundamentals (HTTP, DNS, TCP/IP, etc.)\nFamiliar (2): Linux and related tools\nFamiliar (2): Common Python libraries: Django, Flask, SQLAlchemy, Celery, Requests, pytest, etc., 3-6 years developing database-driven web applications with (preferably) Python\nCandidates without Python experience may still be considered but we will need to see experience with other dynamic languages: Ruby, PHP, JavaScript (Node); MVC (ish) web architecture, and experience in two of the following: SQL, modern front-end JavaScript (React preferred), or automated testing (TDD a plus).\n\n\nYou agree with and would be pleased to work under our development mission statement and guiding principles\nYou can reason about software, algorithms, and performance from a high level\nYou are (or are willing to be) committed to automated testing in your software development process\nStrong oral and written communication skills\nSelf-motivated and have strong organizational skills\nBachelor’s Degree in computer science (or similar degree) or ability to demonstrate comparable education & experience, Candidates without Python experience may still be considered but we will need to see experience with other dynamic languages: Ruby, PHP, JavaScript (Node); MVC (ish) web architecture, and experience in two of the following: SQL, modern front-end JavaScript (React preferred), or automated testing (TDD a plus)., Competent (3): Have significant experience working with dynamic languages (Python, PHP, Ruby, Node) in a web context.  Experience working with Python preferred.\nCompetent (3): Database Development (SQL, multiple-joins, aggregates)\nCompetent (3): HTML(5), CSS/SASS, web standards\nCompetent (3): JavaScript, Ajax, JSON, React, Vue, etc.\nCompetent (3): Writing unit and functional tests. Believing in a TDD approach is a big plus.\nCompetent (3): Version Control (Git, Mercurial)\nCompetent (3): Networking fundamentals (HTTP, DNS, TCP/IP, etc.)\nFamiliar (2): Linux and related tools\nFamiliar (2): Common Python libraries: Django, Flask, SQLAlchemy, Celery, Requests, pytest, etc., Salary: We are targeting $90K-$115K but would consider stretching for the right candidate.\nWe'll talk about salary & benefits in our first Zoom interview to make sure we are mutually\nin the compensation ballpark.\nEveryone wants to be paid as much as possible, but the economics of business usually require\nthat we meet in the middle.\n\n\nProfit Sharing: In addition to a salary, we payout profit sharing bonuses twice a year.\nOne of the best things about working for us is that we really value work/family balance.\nWe rarely work more than 40 hours a week.\nWe know there are devs out there putting in 50-60 hours a week. We can give them 10-20 hours a\nweek of their life back.\n\n\nPTO/Vacation (5+ weeks total):\nEveryone receives 3 weeks of PTO/Vacation per year\n5-12 sick days per year\n9 holidays\n\n\nHealthcare:\nWe provide Health Insurance through an Anthem Silver Blue Access PPO\nWe cover 100% of the premium for employees and 50% for dependents\nThe plan has a broad nationwide network which should give all our employees, regardless of where\nthey live, plenty of options for in-network providers\nThe PPO is matched with an Health Savings Account (HSA) and we match employee contributions to\nthe HSA\nWe unfortunately cannot offer some benefits such as healthcare to international candidates.\n\n\nFlexibility:\nWhile we expect a regular schedule during normal business hours (fitting in the window of 8-7\nET), we are pretty flexible when schedule changes are needed for legitimate reasons.\nIt's not uncommon for our employees to take time off during the week and make it up during the\nevening or weekend so as to avoid needing to use PTO.\n\n\nTelecommuting/Remote:\nWe all work remotely, and have for years.\nThat means our tools (Slack, Zoom, GitHub, etc.) and planning are designed to work for a\ndistributed team.\nWe are open to international candidates working in similar time zones, so long as  your country's laws permit you to work for a U.S. based company on a contract basis.\n\n\nWe Care: It's easy to say, harder to do. We all need to make a living, our company needs to be\nsuccessful in the marketplace, and hard decisions sometimes need to be made. But, at the end of\nthe day, we really care about our employees, their families, their needs, and their desires., We'll talk about salary & benefits in our first Zoom interview to make sure we are mutually\nin the compensation ballpark.\nEveryone wants to be paid as much as possible, but the economics of business usually require\nthat we meet in the middle., We rarely work more than 40 hours a week.\nWe know there are devs out there putting in 50-60 hours a week. We can give them 10-20 hours a\nweek of their life back., Everyone receives 3 weeks of PTO/Vacation per year\n5-12 sick days per year\n9 holidays, We provide Health Insurance through an Anthem Silver Blue Access PPO\nWe cover 100% of the premium for employees and 50% for dependents\nThe plan has a broad nationwide network which should give all our employees, regardless of where\nthey live, plenty of options for in-network providers\nThe PPO is matched with an Health Savings Account (HSA) and we match employee contributions to\nthe HSA\nWe unfortunately cannot offer some benefits such as healthcare to international candidates., While we expect a regular schedule during normal business hours (fitting in the window of 8-7\nET), we are pretty flexible when schedule changes are needed for legitimate reasons.\nIt's not uncommon for our employees to take time off during the week and make it up during the\nevening or weekend so as to avoid needing to use PTO., We all work remotely, and have for years.\nThat means our tools (Slack, Zoom, GitHub, etc.) and planning are designed to work for a\ndistributed team.\nWe are open to international candidates working in similar time zones, so long as  your country's laws permit you to work for a U.S. based company on a contract basis., All projects managed in a Git\nDevelopment workflow using GitHub for pull requests, issue management, code coverage (using\nCodeCov), CI integration (using CircleCi), etc.\nDeveloper friendly project setup: all our projects can be run locally on a developer's machine.\nTest-centric development methodology:\nAsking \"how can we test this\" is ingrained into our engineering and development workflow.\nWe have Continuous Integration (CI) running for all of our projects for at least the backend\ncode (Python mostly) and some of the projects also run the JavaScript tests in CI.\n\n\nA focus on Agile Principles while not being enslaved to them.\nA openness to changing our processes and using new technologies when those changes add value to\nLevel 12 and/or our customers., Asking \"how can we test this\" is ingrained into our engineering and development workflow.\nWe have Continuous Integration (CI) running for all of our projects for at least the backend\ncode (Python mostly) and some of the projects also run the JavaScript tests in CI., Integrity: We are honest, trustworthy, reliable, and ethical. We act the same, regardless of\nwho is watching, and especially when no-one is watching.  If you use pirated software or media,\ndon't mind \"tweaking\" the truth, or think porn == entertainment, this is not the job for you!\nWork ethic: We work hard and like to work hard. We also work smart.  We want to do in 40\nhours a week what other people need 50+ hours to do.\nWork/life balance: All work and no play is a lose-lose situation. We work hard during work\nhours but are committed to protecting our schedules and ensuring adequate time for rest and\nfamily responsibilities.\nFlexibility: Life happens and rarely on schedule or according to plan. Flexibility keeps us\nfrom snapping during the storms of life. When things change, we need to be able to role with the\npunches.\nDedication: We desire to contribute and to create value for our clients and the company. We\ndemonstrate a high level of “ownership” for and initiative with projects assigned to us.\nTeachable: We are open to and desire feedback/correction. We are willing to ask for help even\nif it makes us “look bad.”\nKaizen, continuous improvement: We recognize the value of continually striving to improve\nourselves and the processes around us.\nResourceful: We like problem solving and seek to use all resources at our disposal in an\nefficient manner to troubleshoot. We know when to ask someone for help and when more effort is\nneeded on our part.\nGifting / Core Competency: People are often gifted in ways that make them very good at doing\nsome things and not very good at doing others. Our employees and our company needs to be\noperating in areas we are strongest in, i.e. focusing on our core competencies.\nAttention to detail: Grand visions, out of necessity, are often presented in abbreviated\nform, but making grand visions a reality requires lots of detail.\nProfessional: We take our work seriously, striving for competence in everything we do and\nexcellence in as many things as possible.\nHumility: We often take ourselves too seriously. We are a stunning mix of great potential and\ngreat weakness. We will have the greatest success when we acknowledge both.\nFinitude: We are finite beings, more limited than we often want to admit. But great strength\ncomes from knowing and admitting where you are weak and compensating for it.\nBalance: Most of life requires trade-offs, we can not be excellent in everything we do.\nPatience & Longevity: We often overestimate what we can do in five years and\nunderestimate what we can do in 20.\nGenerosity: It is better to give than to receive., Send an email to start the process:\nTo: <EMAIL>\nSubject: Web Developer Application\nBody:\nInclude a bit about yourself. We hire humans, not robots. 🙂\nInclude answers to the \"Application Questions\" below.\n\n\nAttach: resume, preferably in PDF\nDon't fret about your resume content or formatting.  We are looking almost exclusively for\nrelevant work history.\nWe will send out a questionnaire for more info, so no need for you to guess at what we want to know.\n\n\n\n\nNo cookie-cutter cover letters please!\nYou should hear back from us in a week or so.  You are welcome to email us to check on the status\nof your application: <EMAIL>, To: <EMAIL>\nSubject: Web Developer Application\nBody:\nInclude a bit about yourself. We hire humans, not robots. 🙂\nInclude answers to the \"Application Questions\" below.\n\n\nAttach: resume, preferably in PDF\nDon't fret about your resume content or formatting.  We are looking almost exclusively for\nrelevant work history.\nWe will send out a questionnaire for more info, so no need for you to guess at what we want to know., Include a bit about yourself. We hire humans, not robots. 🙂\nInclude answers to the \"Application Questions\" below., Don't fret about your resume content or formatting.  We are looking almost exclusively for\nrelevant work history.\nWe will send out a questionnaire for more info, so no need for you to guess at what we want to know., We typically evaluate candidates to join our team as full-time W2 employees.\nHowever, if your current job situation is such that you'd be interested in working full-time (or\nclose to FT) for 30-90 days while we got to know you better and vice-versa, please answer in the\naffirmative.  It's not a guarantee we'd offer or even consider you for this arrangement, just\ngood to know for those who are interested.\nAnswering \"no\" to this question will not have any adverse effect on your application., As close to a typical work day as we can get. We just want to see what it's like to work with each other.\nWe'll assign you work based on a previous real-world project we performed This is a sample project, we're not using candidates for free or cheap labor.\nWe will be available via Slack or Zoom throughout the day to talk through the work and assist you as needed., Our Team\n                                    \n                                \n                                    \n                                        Our Values\n                                    \n                                \n                                    \n                                        Consultants First\n                                    \n                                \n                                    \n                                        Challenge\n                                    \n                                \n                                    \n                                        Contact Us, Test Driven Development\n                                    \n                                \n                                    \n                                        Agile\n                                    \n                                \n                                    \n                                        Let's Build a Tank\n                                    \n                                \n                                    \n                                        Iterative Software Development, Services\n                                    \n                                \n                                    \n                                        Case Studies\n                                    \n                                \n                                    \n                                        Technologies\n                                    \n                                \n                                    \n                                        Industries, Schedule a Call\n                                    \n                                \n                                    \n                                        Blog\n                                    \n                                \n                                    \n                                        How much does it cost?\n                                    \n                                \n                                    \n                                        60/6K Guarantee\n                                    \n                                \n                                    \n                                        Careers", "title": "Full Stack Web App Developer – Mid-Level", "location": "", "location_type": "remote", "job_type": "part_time", "min_experience": 1, "max_experience": 6, "apply_link": "mailto:<EMAIL>", "description": "Thank you for your interest in Level 12; while we are currently not actively hiring for these specific positions, we encourage you to submit an application for consideration in the future should opportunities arise that align with your skills and experience. TL;DR: Why Consider Us? We have a commitment to transparency and offer a \"no surprises experience\" throughout the interview and hiring process. We value candor. . . as evidenced by the length of this job description. :) Speaking of job descriptions, we don't let HR people write ours. They are written by software devs for software devs. Our CEO prefers the title CED, Chief Executive Developer. Engineering and operational concerns don't take a back seat when potential sales come to the front door. We practice and preach sound development practices. You are likely to learn and grow as a developer while working here. We aren't trying to compete with FAANG compensation. But outside that bubble, we are competitive. See below for specifics on salary and benefits. You will be working remote with a team that is all working remote and has been for years. Let's make the best use of our time by not commuting. We have a no-drama office policy. We value and cultivate enjoyable working relationships among team members. We emphasize work/life balance and adopt policies that make sure our people don't get burnt out. For instance, our PTO/Vacation policies are designed so that you actually use them. A commitment to Agile Principles while not being enslaved to any particular methodology. You like building full stack web applications with technologies like Python, React, SQL, etc. You are committed to automated testing of all the software you write (our apps typically have 92%+ test coverage). You recognize that there is a lot of idealism in the software development community and are not disenchanted with the the day-to-day realities of programming. You like working independently but can contribute to a team as needed If you apply, we guarantee that we will give you a response, whether \"yay\" or \"nay\". No black holes here! Hard requirement: At this time we are only considering candidates who can work in the U. S. without sponsorship and can fit their workday into the 8am-7pm ET range. We are open to international candidates working in similar time zones, so long as your country's laws permit you to work for a U. S. based company on a contract basis. Some Quick Advice Our CEO was asked, \"What advice do you have for an engineer looking to get hired on your team? \" His advice, \"The hiring process is difficult. . . don't fear rejection. . . the problem might be us. \" The full answer given in a podcast is queued up for you in the player below: ⇊ Expand ⇊ The Job - Full Stack Web Developer Daily responsibilities primarily consist of coding database-driven web applications and other web-related development work. In a typical day, you will likely work mostly with Python based web applications using frameworks like Django or Flask that interact heavily with a database (usually PostgreSQL). In the course of that work, you will have to interact with related technologies like JavaScript, React/Vue, HTML, and CSS/SASS. There's also opportunities for you to be involved in devops work if interested. This is not a design job. It will require a lot of in-depth programming and database work including the ability to architect maintainable code that gets the job done. While we do prefer to do most of our back-end work in Python, we will occasionally take projects in other languages if they seem to be a good fit for us and the customer. Some days you will jump from project to project as issues come up. At other times you may work primarily on one project for weeks, months, or occasionally years. Our customers have varied needs and so we tend to have varied responsibilities and projects. We have a focus on serving the customer and making software conform to their company rather than making the company conform to software. Management Your immediate manager will be a \"Team Lead. \" This is a senior developer, project manager, and team captain all rolled into one. It's also someone whose proven to be talented with both code and people, especially communication. We don't believe non-technical managers bring a lot of value to a development team, so we don't use them. Ditto non-technical project managers. We keep our dev teams small and they usually have a small number of projects so that our team leads don't get overwhelmed. Communication with clients usually involves the team lead and the developers working on the project. We try to avoid the \"phone game\" whenever possible. The ability to take ownership for a project, engineer a sound technical solution, and \"drive\" a project to completion is essential. Your team lead will be a ready and willing resource to help you architect solutions and/or solve problems, but won't micro-manage. If you aren't asking them for help or indicating you have a problem, they will assume you are making good progress on the issues assigned to you during sprint planning, which happens every two weeks. Our CEO/CED and CTO are both software engineers and heavily involved in the engineering operations of the company. They oversee the health of the development team and projects, help our sales team evaluate opportunities for technical and capacity fit, gather feedback from the devs on what is and isn't working in our tech stack and/or processes, and consider the value that emerging technologies might bring to our organization. Sustainability \"Agile processes promote sustainable development. The sponsors, developers, and users should be able to maintain a constant pace indefinitely\" (Agile Manifesto principle). We actively manage every part of our organization through the lens of what is reasonably sustainable. For example, it's why we: Keep our dev teams relatively small Emphasize a 40 hour work week Are candid with our clients and set realistic expectations for them Are relatively slow adopters of new technologies (so we can see if there is actual value there. . . not just hype and thereby avoid needless churn) Promote professionalism, mitigate drama, and cultivate reasonable relationships with each other Regularly seek input from the team on what isn't working for them or where improvements can be made We don't want anyone to burn out. We'd prefer our team to be here and fully engaged for years to come. Mid-Level Developer The position we have open is for a Mid-Level Developer. Mid-Level Developers usually have 3-6 years relevant experience and meet most of the following requirements: Technical Skills Are able to write modular, well-tested, and maintainable code Know at least one software development domain (SQL, Front-End, Back-End) really well Are able to work on multiple projects as needed Leadership Contributes to the design of customer projects with direction from the team lead and feedback from other engineers Proposes new ideas for improving the development team, customer projects, and/or our tech stack Adheres to and promotes our development culture and mission Code quality Leaves code in substantially better shape than before Fixes bugs/regressions quickly Monitors overall code quality/build failures Creates tests religiously and encourages the team to do the same Proactively identifies and reduces technical debt Identifies architectural issues that may hinder development, may need assistance from the team solving such issues Communication Contributes to the code review process Able to communicate clearly on technical topics Keeps issues up-to-date with progress Helps guide other merge requests to completion Is able to participate in conversations with customer contacts when needed Performance & Scalability Competent at writing production-ready code with little assistance Is able to identify potential performance bottlenecks Avoids premature optimization We appreciate GitLab letting us copy some of their content for the above requirements. We Are Looking For General Requirements 3-6 years developing database-driven web applications with (preferably) Python Candidates without Python experience may still be considered but we will need to see experience with other dynamic languages: Ruby, PHP, JavaScript (Node); MVC (ish) web architecture, and experience in two of the following: SQL, modern front-end JavaScript (React preferred), or automated testing (TDD a plus). You agree with and would be pleased to work under our development mission statement and guiding principles You can reason about software, algorithms, and performance from a high level You are (or are willing to be) committed to automated testing in your software development process Strong oral and written communication skills Self-motivated and have strong organizational skills Bachelor’s Degree in computer science (or similar degree) or ability to demonstrate comparable education & experience Languages & Tools on a scale of 1 (novice) to 5 (expert) Competent (3): Have significant experience working with dynamic languages (Python, PHP, Ruby, Node) in a web context. Experience working with Python preferred. Competent (3): Database Development (SQL, multiple-joins, aggregates) Competent (3): HTML(5), CSS/SASS, web standards Competent (3): JavaScript, Ajax, JSON, React, Vue, etc. Competent (3): Writing unit and functional tests. Believing in a TDD approach is a big plus. Competent (3): Version Control (Git, Mercurial) Competent (3): Networking fundamentals (HTTP, DNS, TCP/IP, etc. ) Familiar (2): Linux and related tools Familiar (2): Common Python libraries: Django, Flask, SQLAlchemy, Celery, Requests, pytest, etc. We Are Offering Salary: We are targeting $90K-$115K but would consider stretching for the right candidate. We'll talk about salary & benefits in our first Zoom interview to make sure we are mutually in the compensation ballpark. Everyone wants to be paid as much as possible, but the economics of business usually require that we meet in the middle. Profit Sharing: In addition to a salary, we payout profit sharing bonuses twice a year. One of the best things about working for us is that we really value work/family balance. We rarely work more than 40 hours a week. We know there are devs out there putting in 50-60 hours a week. We can give them 10-20 hours a week of their life back. PTO/Vacation (5+ weeks total): Everyone receives 3 weeks of PTO/Vacation per year 5-12 sick days per year 9 holidays Healthcare: We provide Health Insurance through an Anthem Silver Blue Access PPO We cover 100% of the premium for employees and 50% for dependents The plan has a broad nationwide network which should give all our employees, regardless of where they live, plenty of options for in-network providers The PPO is matched with an Health Savings Account (HSA) and we match employee contributions to the HSA We unfortunately cannot offer some benefits such as healthcare to international candidates. Flexibility: While we expect a regular schedule during normal business hours (fitting in the window of 8-7 ET), we are pretty flexible when schedule changes are needed for legitimate reasons. It's not uncommon for our employees to take time off during the week and make it up during the evening or weekend so as to avoid needing to use PTO. Telecommuting/Remote: We all work remotely, and have for years. That means our tools (Slack, Zoom, GitHub, etc. ) and planning are designed to work for a distributed team. We are open to international candidates working in similar time zones, so long as your country's laws permit you to work for a U. S. based company on a contract basis. We Care: It's easy to say, harder to do. We all need to make a living, our company needs to be successful in the marketplace, and hard decisions sometimes need to be made. But, at the end of the day, we really care about our employees, their families, their needs, and their desires. The Company - Level 12 Who Are We? Level 12 is development firm that specializes in building custom software for our customers. We manage the entire process including planning, managing, and building software exactly as the customer needs it. We serve customers in diverse industries including: non-profit, automotive, manufacturing, logistics, and financial. Our customers range in size from small startups to Fortune 500. We set ourselves apart by digging into the customer's problem until we can own it and then delivering highly functional software that brings significant value to the customer's organization. That may not sound unique, but the longer we do this, the more we encounter people who have been burned by bad development experiences with other firms. We look for long-term relationships with our customers and have some that go back to 2005. We may not be changing the world, but we are impacting the lives of our customers and their employees. You don't have to take our word for it, checkout some of our reviews by our customers. This is a video we often use with our prospective customers to explain our agile/iterative development process (< 2 mins): ⇊ Expand ⇊ On Development Best Practices Behind the scenes, we are using software development best practices to ensure that the code we deliver is flexible, maintainable, and works well for the user for years to come. We know what it's like to work in a substandard development environment or culture that accepts or maybe even promotes practices that kill developer productivity and enjoyment. We work hard at promoting development best practices and ingraining them into our culture. If you come work for us, you can look forward to: All projects managed in a Git Development workflow using GitHub for pull requests, issue management, code coverage (using CodeCov), CI integration (using CircleCi), etc. Developer friendly project setup: all our projects can be run locally on a developer's machine. Test-centric development methodology: Asking \"how can we test this\" is ingrained into our engineering and development workflow. We have Continuous Integration (CI) running for all of our projects for at least the backend code (Python mostly) and some of the projects also run the JavaScript tests in CI. A focus on Agile Principles while not being enslaved to them. A openness to changing our processes and using new technologies when those changes add value to Level 12 and/or our customers. If you'd like to learn more about what makes us tick, our Chief Executive Developer (CED) gave a talk on the importance of CI and automation in an agile software development process (embedded video) and has given other talks and presentations on various topics. ⇊ Expand ⇊ Character First Level 12 was founded on biblical principles and has biblically informed Values. See the about page for our Mission, Values, Vision, and Purpose statements. Due to differing or a lack of religious convictions, we respect that employees may not be able to identify with our faith-based Vision and Purpose. Applicants are not asked questions regarding nor expected to discuss their religious beliefs during the application process or during employment. But, we do expect all applicants and employees to share our Values. Our Values describe not only who we are as an organization, but also who we are not. They encompass our ethics, principles, and beliefs about our organization and its relationship to the world. They communicate to employees the behaviors that are approved (and rewarded), and by extension those that are not. Integrity: We are honest, trustworthy, reliable, and ethical. We act the same, regardless of who is watching, and especially when no-one is watching. If you use pirated software or media, don't mind \"tweaking\" the truth, or think porn == entertainment, this is not the job for you! Work ethic: We work hard and like to work hard. We also work smart. We want to do in 40 hours a week what other people need 50+ hours to do. Work/life balance: All work and no play is a lose-lose situation. We work hard during work hours but are committed to protecting our schedules and ensuring adequate time for rest and family responsibilities. Flexibility: Life happens and rarely on schedule or according to plan. Flexibility keeps us from snapping during the storms of life. When things change, we need to be able to role with the punches. Dedication: We desire to contribute and to create value for our clients and the company. We demonstrate a high level of “ownership” for and initiative with projects assigned to us. Teachable: We are open to and desire feedback/correction. We are willing to ask for help even if it makes us “look bad. ” Kaizen, continuous improvement: We recognize the value of continually striving to improve ourselves and the processes around us. Resourceful: We like problem solving and seek to use all resources at our disposal in an efficient manner to troubleshoot. We know when to ask someone for help and when more effort is needed on our part. Gifting / Core Competency: People are often gifted in ways that make them very good at doing some things and not very good at doing others. Our employees and our company needs to be operating in areas we are strongest in, i. e. focusing on our core competencies. Attention to detail: Grand visions, out of necessity, are often presented in abbreviated form, but making grand visions a reality requires lots of detail. Professional: We take our work seriously, striving for competence in everything we do and excellence in as many things as possible. Humility: We often take ourselves too seriously. We are a stunning mix of great potential and great weakness. We will have the greatest success when we acknowledge both. Finitude: We are finite beings, more limited than we often want to admit. But great strength comes from knowing and admitting where you are weak and compensating for it. Balance: Most of life requires trade-offs, we can not be excellent in everything we do. Patience & Longevity: We often overestimate what we can do in five years and underestimate what we can do in 20. Generosity: It is better to give than to receive. ⇊ Expand ⇊ Next Step - Ready To Apply? Have Questions? Despite the length of this post, we may have missed something. If you have a question that the job description didn't answer, and you would like it answered before applying, please email us: hrteam@level12. io. We usually respond the same or next business day. Our Promise If You Apply Ever send a resume in only to never. . . hear. . . anything? We promise we won't do that. If you follow the directions above, you will receive a response from us within a week or so. If you don't hear anything back from us, double check whether you followed the instructions above (🙂) and then email us for a status update. Application Instructions If you have read all of the above, carefully, and are interested and qualified, then here are your next steps: Send an email to start the process: To: apply-mid-level-dev@level12. io Subject: Web Developer Application Body: Include a bit about yourself. We hire humans, not robots. 🙂 Include answers to the \"Application Questions\" below. Attach: resume, preferably in PDF Don't fret about your resume content or formatting. We are looking almost exclusively for relevant work history. We will send out a questionnaire for more info, so no need for you to guess at what we want to know. No cookie-cutter cover letters please! You should hear back from us in a week or so. You are welcome to email us to check on the status of your application: hrteam@level12. io Application Questions Any feedback on the job description? Too long? Too short? Find a typo? No. . . that's ok too. This isn't a test. :) Based on your experience, how do you grade a developer? That is, what makes the difference between a mediocre developer (1 of 5) and stellar developer (5 of 5). Where do you fit in? What excites you about programming? What discourages you? What makes a company's culture enjoyable to you? Not enjoyable? Based on this job description what do you find most attractive about this position? What do you find less attractive? Based on our company website and anything else you may know about us, what most attracts you to our company? What concerns you? Where did you originally find this job posting? Where do you live (city/state)? Do you have a comfortable, distraction-free environment setup for remote work? Are you a US citizen or otherwise eligible to work in the United States without sponsorship? Note: as stated above, US Citizenship or ability to work in the US without sponsorship is a hard requirement as is a willingness to work generally between 8am - 7pm Eastern. We are open to international candidates working in similar time zones, so long as your country's laws permit you to work for a US based company on a contract basis. Are you potentially interested in a 30-90 day contract-to-hire position? We typically evaluate candidates to join our team as full-time W2 employees. However, if your current job situation is such that you'd be interested in working full-time (or close to FT) for 30-90 days while we got to know you better and vice-versa, please answer in the affirmative. It's not a guarantee we'd offer or even consider you for this arrangement, just good to know for those who are interested. Answering \"no\" to this question will not have any adverse effect on your application. Regarding Authenticity: as you answer these questions, please keep in mind that we prefer candor over flattery and we are under no delusion that this is the perfect position or we are the perfect company. Likewise, we don't believe there are any perfect candidates. We understand that companies and people are made up of both strengths and weaknesses and the better we understand both your strengths and weaknesses the more likely we are to make a wise hiring decision. Please take time to answer the questions thoughtfully and accurately. Check your SPAM! ! ! If you don't hear from us, check your SPAM folder. All emails that we send will come from the domain \"level12. io\". The Rest of the Process Our application process is outlined roughly below. But, before you get there, we want to apologize in advance if this process seems. . . imposing. We have put considerable thought and refinement into each one of these steps in an effort to make sure our hiring process is as well crafted as our software. And just like software, hiring is a lot more complex than it might seem on the surface. Our hiring process is far from perfect (like our software), we are still learning and tweaking, but we want to assure you that each of these steps gives us crucial information regarding you and your development abilities that is essential for helping us to determine if this is a good match. Consider this: our entire process is less than a week's worth of effort to make sure that where you spend the next 1-5 years of your life is a good fit. Isn't that worth it? Keep in mind that we have deliberately structured our process so that the earlier stages require less effort. Our hope is that if you make it to the later stages of our process, where the time commitment increases, you will have had a chance to get to know us a bit better so you can decide if the time investment on your part is worth it. We care about your time (and ours) and do our best not to waste it! Application Steps Evaluate resume and initial email correspondence Technical skills questionnaire Skills evaluation: 60-90 minute work simulation exercise Zoom interview(s): 45-90 minutes in one or two interviews to get to know you & your technical abilities Skills tests - phase I: three real-world programming challenges, no trick questions here (paid) Skills tests - phase II: project-based skills test: we give you a small project description and you build the best app you can (paid) Skills tests review interview: 2-3 hours on a Zoom meeting with our dev leadership team to get to know you and review your skills test results Collaborative work day: As close to a typical work day as we can get. We just want to see what it's like to work with each other. We'll assign you work based on a previous real-world project we performed This is a sample project, we're not using candidates for free or cheap labor. We will be available via Slack or Zoom throughout the day to talk through the work and assist you as needed. If at any step we don't feel like it's a good match, we'll let you know promptly. We ask that you do the same for us. ⇊ Expand ⇊", "ctc": null, "currency": null, "meta": {}}]