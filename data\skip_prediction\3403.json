[{"jd_link": "https://www.icefyresolutions.com/careers/net-developer", "company_id": 3403, "source": 3, "skills": "3+ years of solid web development experience in C#IT diploma or related degree is a plusProficiency with Git, OOP, and good English communication skillsDelivery-focused with a collaborative and proactive approachCreative problem-solving skills and attention to detailAbility to work independently and in team environmentsPrior experience with Agile methodologies (Scrum)Familiarity with popular testing tools and cloud services (AWS and Azure)Ability to write clean, maintainable code and meet project deadlines, Develop robust C# .NET solutions aligned with project specificationsMaintain, debug, and enhance existing software productsIdentify and resolve technical risks and issues in .NET projectsConduct unit testing and integration testing to ensure qualityWrite technical documentation to share knowledge effectivelySetup and maintain infrastructure environments for client solutionsUtilize MS SQL, GraphQL, PostgreSQL, MongoDB, Redis, JSON, Linq2DB, Fluent MigratorImplement microservices in .NET Core and Web APIsWork with RabbitMQ, Entity Framework, and design patternsApply containers and orchestration basics(Docker, Docker Compose, K8S), Flexible working hours  Fully remote, hybrid, or in-office work  Mentorship and career development Non-formal education and certifications  Paid lunch and transport (in the office)  Performance bonuses  Paid onboarding expenses  Private health insurance  Team buildings", "title": ".NET Developer", "location": "", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://www.icefyresolutions.com/contact", "description": "Are you passionate about developing cutting-edge C# . NET solutions? Then we have an exciting opportunity for you. Check it out and become part of the IceFyre Solutions team! What we are looking for3+ years of solid web development experience in C#IT diploma or related degree is a plusProficiency with Git, OOP, and good English communication skillsDelivery-focused with a collaborative and proactive approachCreative problem-solving skills and attention to detailAbility to work independently and in team environmentsPrior experience with Agile methodologies (Scrum)Familiarity with popular testing tools and cloud services (AWS and Azure)Ability to write clean, maintainable code and meet project deadlinesWhat you’ll doDevelop robust C# . NET solutions aligned with project specificationsMaintain, debug, and enhance existing software productsIdentify and resolve technical risks and issues in . NET projectsConduct unit testing and integration testing to ensure qualityWrite technical documentation to share knowledge effectivelySetup and maintain infrastructure environments for client solutionsUtilize MS SQL, GraphQL, PostgreSQL, MongoDB, Redis, JSON, Linq2DB, Fluent MigratorImplement microservices in . NET Core and Web APIsWork with RabbitMQ, Entity Framework, and design patternsApply containers and orchestration basics(<PERSON><PERSON>, <PERSON><PERSON> Compose, K8S)What’s in it for youFlexible working hours Fully remote, hybrid, or in-office work Mentorship and career development Non-formal education and certifications Paid lunch and transport (in the office) Performance bonuses Paid onboarding expenses Private health insurance Team buildings", "ctc": null, "currency": null, "meta": {}}]