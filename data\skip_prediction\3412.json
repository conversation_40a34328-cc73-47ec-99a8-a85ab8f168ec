[{"jd_link": "https://www.velvetech.com/careers/android-developer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Development and support applications using Kotlin, Java, RxJava, Android DevTools, and web services (JSON REST)\n    Continuous integration with Jenkins\n    Applications development for new projects using Scrum and Agile (in collaboration with the design team and iOS developers)\n    Documentation of planning and implementation processes in Jira and Confluence, Android development with a strong knowledge of Android , and deep expertise in Java\nConsuming RESTful JSON APIs and WebSockets\nUsing existing frameworks and APIs effectively, with in-depth knowledge of device and Android version interaction\nProducing clear and concise technical documentation for both internal and external use\nTechniques to achieve high-quality user experiences, adhering to Google’s Android design principles and interface guidelines\nWriting efficient, maintainable, and reusable code\nDesign, data structures, problem-solving, and debugging skills\nGit for version control\nStrong focus on usability in app design and development, Proficiency in AI-driven development tools such as GitHub Copilot or similar is required to assist in mobile application development\n\tStrong prompt engineering skills to optimize the outputs of AI tools effectively\n\tCritical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking\n\tUtilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability, Experience with Kotlin, Android’s preferred language for new development\nFamiliarity with Jetpack Compose for modern UI development\nExperience with modern Android app architectures such as MVVM or MVI\nExposure to Android Jetpack libraries (e.g., LiveData, ViewModel)\nUse of Firebase or other cloud-based services for app development, Velvetech is in the TOP 5 development companies in Illinois, USA\n    You have FLEXIBLE working conditions and a COOPERATIVE environment\n    Competitive salary\n    Many CHALLENGING and exciting projects with new opportunities and learning\n    GROWTH opportunities, skills and competencies improvement, and professional certification\n    In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Android Developer (remote)", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 20, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/android-developer/", "description": "Android Developer (remote) About us Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Responsibilities Absolutely. You would be responsible for developing applications for devices powered by the Android operating system. A very competitive income is waiting for you, by building a very satisfying career as an Android developer, working fully remotely from the coziness of your home. Did you know that Android is still the most used mobile operating system in the world? Development and support applications using Kotlin, Java, RxJava, Android DevTools, and web services (JSON REST) Continuous integration with Jenkins Applications development for new projects using Scrum and Agile (in collaboration with the design team and iOS developers) Documentation of planning and implementation processes in Jira and Confluence Requirements Android development with a strong knowledge of Android , and deep expertise in Java Consuming RESTful JSON APIs and WebSockets Using existing frameworks and APIs effectively, with in-depth knowledge of device and Android version interaction Producing clear and concise technical documentation for both internal and external use Techniques to achieve high-quality user experiences, adhering to Google’s Android design principles and interface guidelines Writing efficient, maintainable, and reusable code Design, data structures, problem-solving, and debugging skills Git for version control Strong focus on usability in app design and development AI related requirements Proficiency in AI-driven development tools such as GitHub Copilot or similar is required to assist in mobile application development Strong prompt engineering skills to optimize the outputs of AI tools effectively Critical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking Utilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability Considered an Advantage: Experience with Kotlin, Android’s preferred language for new development Familiarity with Jetpack Compose for modern UI development Experience with modern Android app architectures such as MVVM or MVI Exposure to Android Jetpack libraries (e. g. , LiveData, ViewModel) Use of Firebase or other cloud-based services for app development Benefits Velvetech is in the TOP 5 development companies in Illinois, USA You have FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) Our team We are a friendly team of 150+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/creatio-certified-business-analyst/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Gather and analyze requirements from multiple stakeholders to identify business needs\n    Create detailed business requirement documents, including concept, technical design, use cases, and testing protocols\n    Customize and optimize Creatio systems using low-code tools\n    Develop visual workflows, process maps, and user interfaces to depict automated processes\n    Prepare comprehensive test cases and conduct thorough testing to ensure the quality and reliability of no-code solutions\n\tProvide training sessions for clients on using Creatio’s functionalities\n\tCollaborate with cross-functional teams, including developers and business stakeholders, to ensure smooth project execution, Creatio certification is required\n    At least 2+ years of experience implementing IT solutions, preferably with CRM or BPM platforms\n    Strong understanding of industry models and data exchange technologies\n    Familiarity with business process modeling and IT solutions architecture\n    Excellent communication skills, with fluency in English\n    Ability to analyze complex business processes and identify opportunities for automation\n    Experience in creating and managing automated solutions using no-code tools, Velvetech is in the TOP 5 development companies in the USA\n    You have FLEXIBLE working conditions and a COOPERATIVE environment\n    A COMPETITIVE and performance-based salary, fair compensation and benefits\n    Many CHALLENGING and exciting projects with new opportunities and learning\n    GROWTH opportunities, skills and competencies improvement, and professional certification\n    In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Creatio Certified Business Analyst", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 2, "max_experience": 6, "apply_link": "https://www.velvetech.com/careers/creatio-certified-business-analyst/", "description": "Creatio Certified Business Analyst 2-6 years of experience About us Velvetech, is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. We are seeking a skilled and proactive Business Analyst to join our team. The ideal candidate will have a strong background in analyzing and optimizing business processes using Creatio’s no-code platform. You will play a key role in understanding client needs, translating them into functional requirements, and ensuring successful implementation Responsibilities Gather and analyze requirements from multiple stakeholders to identify business needs Create detailed business requirement documents, including concept, technical design, use cases, and testing protocols Customize and optimize Creatio systems using low-code tools Develop visual workflows, process maps, and user interfaces to depict automated processes Prepare comprehensive test cases and conduct thorough testing to ensure the quality and reliability of no-code solutions Provide training sessions for clients on using Creatio’s functionalities Collaborate with cross-functional teams, including developers and business stakeholders, to ensure smooth project execution Requirements Creatio certification is required At least 2+ years of experience implementing IT solutions, preferably with CRM or BPM platforms Strong understanding of industry models and data exchange technologies Familiarity with business process modeling and IT solutions architecture Excellent communication skills, with fluency in English Ability to analyze complex business processes and identify opportunities for automation Experience in creating and managing automated solutions using no-code tools Benefits Velvetech is in the TOP 5 development companies in the USA You have FLEXIBLE working conditions and a COOPERATIVE environment A COMPETITIVE and performance-based salary, fair compensation and benefits Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) Our team We are a friendly team of 150+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/business-analyst/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Evaluating business processes, anticipating requirements, uncovering areas for improvement, and developing and implementing solutions\n    Eliciting business needs and collaborating with the clients\n    Communicating with project stakeholders and managing the requirements lifecycle\n    Analyzing requirements and elaborating technical design specifications\n    Evaluating solutions: creating test plans, preparing test data, use cases description\n    Managing communication between the client and the development team: preparing meeting agenda, conducting online meetings with the client, producing meeting notes, creating the list of action points and controlling their implementation, collecting and processing feedback\n    Producing and delivering weekly and monthly reports, release notes\n    Monitoring deliverables and ensuring timely completion of projects, English level: Upper-Intermediate or Advanced (preferable to have a certificate)\n    Bachelor’s or Master’s degree\n    1-3 years of experience in business analysis\n    Good knowledge of business analysis and ability to apply appropriate methodologies\n    Ability to define the best business solution among available options\n    Ability to effectively communicate with clients and stakeholders\n    Natural analytical way of thinking and be able to explain difficult concepts to non-technical users\n    Excellent planning, organizational, and time management skills\n    Good experience of teamwork\n\tFamiliarity with AI-driven analysis tools such as ChatGPT or similar is required to facilitate advanced data analysis\n\tPrompt engineering skills to optimize AI-generated insights effectively\n\tCritical understanding of AI tool boundaries and the ability to apply sound judgment and critical thinking in analyzing data is essential\n\tUtilize AI for enhancing data interpretation, report generation, and decision-making processes to improve workflow reliability, Velvetech is in the TOP 5 development companies in Illinois, USA\n    You have FLEXIBLE working conditions and a COOPERATIVE environment\n    Competitive salary\n    Many CHALLENGING and exciting projects with new opportunities and learning\n    GROWTH opportunities, skills and competencies improvement, and professional certification\n    In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Business Analyst (remote)", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 1, "max_experience": 3, "apply_link": "https://www.velvetech.com/careers/business-analyst/", "description": "Business Analyst (remote) About us Velvetech is an international company, based in the US, with 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Responsibilities Would you like to improve our processes and systems? If your answer is YES, then we are offering to start conducting research and analysis in our company, to come up with solutions for business issues and to introduce these systems to our clients. By working fully remotely from the coziness of your home, you will be able to organize your schedule and have a performance-based salary with many benefits from our company. You would be acting as the key interface between the users and the project managers to gather information, document processes, and confirm the final documents with users. Evaluating business processes, anticipating requirements, uncovering areas for improvement, and developing and implementing solutions Eliciting business needs and collaborating with the clients Communicating with project stakeholders and managing the requirements lifecycle Analyzing requirements and elaborating technical design specifications Evaluating solutions: creating test plans, preparing test data, use cases description Managing communication between the client and the development team: preparing meeting agenda, conducting online meetings with the client, producing meeting notes, creating the list of action points and controlling their implementation, collecting and processing feedback Producing and delivering weekly and monthly reports, release notes Monitoring deliverables and ensuring timely completion of projects Project Types Future projects as highly loaded SaaS web-based services for automating business processes in the field of medicine, insurance, finance, energy, including modules for processing large data arrays, integration with external data sources, mobile applications, and others. Requirements English level: Upper-Intermediate or Advanced (preferable to have a certificate) Bachelor’s or Master’s degree 1-3 years of experience in business analysis Good knowledge of business analysis and ability to apply appropriate methodologies Ability to define the best business solution among available options Ability to effectively communicate with clients and stakeholders Natural analytical way of thinking and be able to explain difficult concepts to non-technical users Excellent planning, organizational, and time management skills Good experience of teamwork Familiarity with AI-driven analysis tools such as ChatGPT or similar is required to facilitate advanced data analysis Prompt engineering skills to optimize AI-generated insights effectively Critical understanding of AI tool boundaries and the ability to apply sound judgment and critical thinking in analyzing data is essential Utilize AI for enhancing data interpretation, report generation, and decision-making processes to improve workflow reliability Benefits Velvetech is in the TOP 5 development companies in Illinois, USA You have FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) Our team We are a friendly team of 130+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/it-recruiter/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Understand all factors relevant to hiring managers and interpret business needs to develop an optimal hiring plan\n    Maintain HuntFlow database and available data and statistics to support hiring plan\n    Coordinate all the stages of the interview for a hiring team and a candidate\n    Search for potential candidates on LinkedIn, GitHub, Amazing Hiring, Monster Jobs, Indeed, etc.\n    Understand talent markets and complex candidate profiles to identify potential candidates\n    Leverage tools and valuation frameworks to assess relevant candidates\n    Conduct interviews and prepare interview selection report for each candidate\n    Perform onboarding of a new employee\n    Work with candidates.\n    Actively participate in HR process development (recruitment, team building, capability development, training, onboarding, remote events organization)\n    Participate in the organization of external events (career days in universities, meetups, hackathons, etc.)\n    Job occupancy: Recruitment 80% – Other 20%s, Bachelor degree in Human Resource, Psychology or relevant field\n    Strong expertise in all phases of IT recruiting and sourcing processes\n    Excellent written and communication skills\n    Basic IT knowledge required to understand the business need\n    English at the intermediate level \n    Experience in international recruiting will be an advantage, Velvetech is in the TOP 5 development companies in Illinois, USA\n    You have FLEXIBLE working conditions and a COOPERATIVE environment\n    Competitive salary\n    Many CHALLENGING and exciting projects with new opportunities and learning\n    GROWTH opportunities, skills and competencies improvement, and professional certification\n    In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "IT Recruiter (remote)", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 20, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/it-recruiter/", "description": "IT Recruiter (remote) About us Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Responsibilities Understand all factors relevant to hiring managers and interpret business needs to develop an optimal hiring plan Maintain HuntFlow database and available data and statistics to support hiring plan Coordinate all the stages of the interview for a hiring team and a candidate Search for potential candidates on LinkedIn, GitHub, Amazing Hiring, Monster Jobs, Indeed, etc. Understand talent markets and complex candidate profiles to identify potential candidates Leverage tools and valuation frameworks to assess relevant candidates Conduct interviews and prepare interview selection report for each candidate Perform onboarding of a new employee Work with candidates. Actively participate in HR process development (recruitment, team building, capability development, training, onboarding, remote events organization) Participate in the organization of external events (career days in universities, meetups, hackathons, etc. ) Job occupancy: Recruitment 80% – Other 20%s Requirements Bachelor degree in Human Resource, Psychology or relevant field Strong expertise in all phases of IT recruiting and sourcing processes Excellent written and communication skills Basic IT knowledge required to understand the business need English at the intermediate level Experience in international recruiting will be an advantage Benefits Velvetech is in the TOP 5 development companies in Illinois, USA You have FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) Our team We are a friendly team of 170+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/creatio-sertified-implementation-specialist/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Work with clients to gather and analyze their business requirements\n    Design and configure Creatio solutions using low-code tools\n    Develop and customize workflows, user interfaces, and business logic within Creatio\n    Conduct thorough testing to ensure the quality and functionality of implemented solutions\n    Provide training and support to clients during and after the developing and  implementation process\n\tCreate comprehensive documentation for implemented solutions and processes\n\tCollaborate with business analysts, developers, and other stakeholders to ensure successful project delivery, Creatio certification is required\n    At least 2+ years of experience in developing and implementing IT solutions, preferably with CRM or BPM platforms\n    Strong understanding of business process modeling and data integration methods\n    Proficiency in using no-code development tools for system customization\n\tDeep knowledge of Creatio architecture, C#, JS, Angular\n    Excellent problem-solving skills and attention to detail\n    Strong communication and interpersonal skills\n    Ability to work independently and manage multiple projects simultaneously\n\tEnglish knowledge at Intermediate level or higher, Velvetech is in the TOP 5 development companies in the USA\n    You have FLEXIBLE working conditions and a COOPERATIVE environment\n    A COMPETITIVE and performance-based salary, fair compensation and benefits\n    Many CHALLENGING and exciting projects with new opportunities and learning\n    GROWTH opportunities, skills and competencies improvement, and professional certification\n    In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Creatio Certified Developer and Implementation Specialist", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 2, "max_experience": 6, "apply_link": "https://www.velvetech.com/careers/creatio-sertified-implementation-specialist/", "description": "Creatio Certified Developer and Implementation Specialist 2-6 years of experience About us Velvetech, is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Responsibilities Work with clients to gather and analyze their business requirements Design and configure Creatio solutions using low-code tools Develop and customize workflows, user interfaces, and business logic within Creatio Conduct thorough testing to ensure the quality and functionality of implemented solutions Provide training and support to clients during and after the developing and implementation process Create comprehensive documentation for implemented solutions and processes Collaborate with business analysts, developers, and other stakeholders to ensure successful project delivery Requirements Creatio certification is required At least 2+ years of experience in developing and implementing IT solutions, preferably with CRM or BPM platforms Strong understanding of business process modeling and data integration methods Proficiency in using no-code development tools for system customization Deep knowledge of Creatio architecture, C#, JS, Angular Excellent problem-solving skills and attention to detail Strong communication and interpersonal skills Ability to work independently and manage multiple projects simultaneously English knowledge at Intermediate level or higher Benefits Velvetech is in the TOP 5 development companies in the USA You have FLEXIBLE working conditions and a COOPERATIVE environment A COMPETITIVE and performance-based salary, fair compensation and benefits Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) Our team We are a friendly team of 170+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/ios-developer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Design and build applications for the iOS platform\n        Ensure the performance, quality, and responsiveness of applications\n        Collaborate with a team to define, design, and ship new features\n        Identify and correct bottlenecks and fix bugs\n        Help maintain code quality, organization, and automatization, Object-oriented programming, design patterns, data structures, and algorithms\niOS application development with a strong command of Swift\nCore Data and SQLite for data persistence\nGit and platforms like GitHub or GitLab for version control\nExcellent coding and debugging skills\nDeveloping and deploying applications using Apple’s ecosystem, including familiarity with Xcode\nAgile and Scrum methodologies\nContinuous integration and continuous deployment (CI/CD) practices, Proficiency in AI-driven development tools such as GitHub Copilot or similar is required to assist in mobile application development\n\tStrong prompt engineering skills to optimize the outputs of AI tools effectively\n\tCritical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking\n\tUtilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability, Experience with SwiftUI for modern iOS user interface development\nFamiliarity with Combine for handling asynchronous events\nExposure to Apple’s new frameworks like ARKit or CoreML\nExperience with mobile application architectures like MVVM or VIPER\nUse of cloud services for mobile applications, such as Firebase or AWS Mobile Services, Velvetech is in the TOP 5 development companies in Illinois, USA\n        You have FLEXIBLE working conditions and a COOPERATIVE environment\n        Competitive salary\n        Many CHALLENGING and exciting projects with new opportunities and learning\n        GROWTH opportunities, skills and competencies improvement, and professional certification\n        In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "iOS Developer (remote)", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 20, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/ios-developer/", "description": "iOS Developer (remote) About us Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Responsibilities If you are interested in being responsible for developing applications for mobile devices powered by Apple’s iOS operating system, then you are on our APPLE tree. With a chance to develop your skills more while working fully remotely by having a performance-based salary and many benefits here, you will be responsible for creating and architecting new mobile applications, maintaining and improving existing features on existing applications, and working with our teams to develop innovative solutions that meet our clients’ business needs. Design and build applications for the iOS platform Ensure the performance, quality, and responsiveness of applications Collaborate with a team to define, design, and ship new features Identify and correct bottlenecks and fix bugs Help maintain code quality, organization, and automatization Requirements Object-oriented programming, design patterns, data structures, and algorithms iOS application development with a strong command of Swift Core Data and SQLite for data persistence Git and platforms like GitHub or GitLab for version control Excellent coding and debugging skills Developing and deploying applications using Apple’s ecosystem, including familiarity with Xcode Agile and Scrum methodologies Continuous integration and continuous deployment (CI/CD) practices AI related requirements Proficiency in AI-driven development tools such as GitHub Copilot or similar is required to assist in mobile application development Strong prompt engineering skills to optimize the outputs of AI tools effectively Critical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking Utilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability Considered an Advantage: Experience with SwiftUI for modern iOS user interface development Familiarity with Combine for handling asynchronous events Exposure to Apple’s new frameworks like ARKit or CoreML Experience with mobile application architectures like MVVM or VIPER Use of cloud services for mobile applications, such as Firebase or AWS Mobile Services Benefits Velvetech is in the TOP 5 development companies in Illinois, USA You have FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) Our team We are a friendly team of 170+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/net-developer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Developing front-end website architecture and back-end website applications\n\tDesigning user interactions on web pages and ensuring responsiveness of applications\n\tDeveloping a project from conception to finished product while meeting both technical and client needs\n\tStaying updated with all trends in developing web applications and programming languages, .NET Core\n\tSQL databases (MS SQL, MySQL, PostgreSQL)\n\tORM: EF, NHibernate\n\tUI: Vue.js / React / Angular\n\tSOLID principles and best practices in software design\n\tEnglish level: Intermediate and higher, Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required\n\tStrong prompt engineering skills to optimize the outputs of AI tools effectively\n\tCritical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking\n\tUtilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability, Familiarity with containerization and orchestration tools like Docker and Kubernetes\n\t\tExperience working in Linux environments\n\tKnowledge of NoSQL databases such as MongoDB or Cassandra\n\tUnderstanding of functional programming languages such as Python or F#\n\t\tStrong organizational and project management skills\n\t\t\tExcellent problem-solving skills and attention to detail\n\t\t\t\tRelevant Certifications: Microsoft Certified: Azure Developer Associate, AWS Certified Developer – Associate, Working in the TOP 5 development companies in Illinois, USA\n\tFLEXIBLE working conditions and a COOPERATIVE environment\n\tCompetitive salary\n\tMany CHALLENGING and exciting projects with new opportunities and learning\n\tGROWTH opportunities, skills and competencies improvement, and professional certification\n\tIn-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": ".NET Developer", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 2, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/net-developer/", "description": ". NET Developer Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Responsibilities Fully remote position that gives the possibility to work from the coziness of your home with many benefits, and a competitive performance-based salary. As a new team member, you will design web applications for business systems, use various programming languages to write code, apply knowledge to adapt and test applications, maintain coding documentation, project progress reports and application maintenance logs. Are you in? Developing front-end website architecture and back-end website applications Designing user interactions on web pages and ensuring responsiveness of applications Developing a project from conception to finished product while meeting both technical and client needs Staying updated with all trends in developing web applications and programming languages Requirements 2+ years of experience in the following technologies: . NET Core SQL databases (MS SQL, MySQL, PostgreSQL) ORM: EF, NHibernate UI: Vue. js / React / Angular SOLID principles and best practices in software design English level: Intermediate and higher AI related requirements Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required Strong prompt engineering skills to optimize the outputs of AI tools effectively Critical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking Utilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability The following will be considered as an advantage: Familiarity with containerization and orchestration tools like Docker and Kubernetes Experience working in Linux environments Knowledge of NoSQL databases such as MongoDB or Cassandra Understanding of functional programming languages such as Python or F# Strong organizational and project management skills Excellent problem-solving skills and attention to detail Relevant Certifications: Microsoft Certified: Azure Developer Associate, AWS Certified Developer – Associate Benefits Working in the TOP 5 development companies in Illinois, USA FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) What we offer Future projects as highly loaded SaaS web-based services for automating business processes in the field of medicine, insurance, finance, energy, including modules for processing large data arrays, billing subsystems, integration with external data sources, mobile applications, and IP telephony systems. Our team We are a friendly team of 170+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients. Start your journey today!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/machine-learning-engineer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Automate tasks related to product acceptance, warehouse operations (working with the catalog -10-15 million product units from different retailers, processing products in warehouses, making automated decisions on what is more profitable to do with this product based on the analysis of various factors (seasonality, etc.) – sell or return to the manufacturer, etc.Categorize incoming products by UPC (Universal Product Code). If no matching is found, create a new product card and fill in all the data (name, description, attributes, cost). Currently, this is done manually and prone to errors. The goal is to fully automate these tasks using AI., Proven experience in AI/ML and NLPExperience with Time Series analysis and forecasting (Trends, seasonality and price opt.)Experience in creating custom workspaces for Chat GPTExperience of building data processing automation on top of GPT4Experience with Prompt EngineeringExperience with TensorFlow and PyTorch (desired)Programming skills: Python, C#, or JavascriptUnderstanding how REST API is built, knowledge of SQLAbility to connect external data sources for model trainingStrong problem-solving skillsExcellent communication skillsExperience working in Agile/Scrum environment, Working in the TOP 5 development companies in Illinois, USAFLEXIBLE working conditions and a COOPERATIVE environmentCompetitive salaryMany CHALLENGING and exciting projects with new opportunities and learningGROWTH opportunities, skills and competencies improvement, and professional certificationIn-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Machine Learning Engineer", "location": "", "location_type": null, "job_type": "full_time", "min_experience": 20, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/machine-learning-engineer/", "description": "Machine Learning EngineerVelvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. ResponsibilitiesWe are seeking an experienced AI/ML Automation Engineer to join our team. The successful candidate will have a strong background in working with AI models, creating custom workspaces for ChatGPT, and connecting external data sources for model training. This role will involve building of data processing with the use of GPT4 and developing AI-based agents to automate various business tasks. Key Responsibilities: Develop AI-based agents to automate business tasks. Automate tasks related to product acceptance, warehouse operations (working with the catalog -10-15 million product units from different retailers, processing products in warehouses, making automated decisions on what is more profitable to do with this product based on the analysis of various factors (seasonality, etc. ) – sell or return to the manufacturer, etc. Categorize incoming products by UPC (Universal Product Code). If no matching is found, create a new product card and fill in all the data (name, description, attributes, cost). Currently, this is done manually and prone to errors. The goal is to fully automate these tasks using AI. RequirementsProven experience in AI/ML and NLPExperience with Time Series analysis and forecasting (Trends, seasonality and price opt. )Experience in creating custom workspaces for Chat GPTExperience of building data processing automation on top of GPT4Experience with Prompt EngineeringExperience with TensorFlow and PyTorch (desired)Programming skills: Python, C#, or JavascriptUnderstanding how REST API is built, knowledge of SQLAbility to connect external data sources for model trainingStrong problem-solving skillsExcellent communication skillsExperience working in Agile/Scrum environmentBenefitsWorking in the TOP 5 development companies in Illinois, USAFLEXIBLE working conditions and a COOPERATIVE environmentCompetitive salaryMany CHALLENGING and exciting projects with new opportunities and learningGROWTH opportunities, skills and competencies improvement, and professional certificationIn-company TRAINING (English, Software / DevOps / Project management / Design / Business)What we offerFuture projects as highly loaded SaaS web-based services for automating business processes in the field of medicine, insurance, finance, energy, including modules for processing large data arrays, billing subsystems, integration with external data sources, mobile applications, and IP telephony systems. Our teamWe are a friendly team of 150+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients. Start your journey today!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/net-backend-developer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Developing and improving an application software and designing technical architecture supported by writing technical and functional documentation\n    Implementing software tests and debugging code while producing the design schema of a database\n    Support in making regular modifications to existing software for error correction, adaptation to new hardware and improving overall function and performance\n    Analyzing customer needs and keeping up with changes in technologies, .NET Core\n\tSQL databases (MS SQL, MySQL, PostgreSQL)\n\tORM: EF, NHibernate\n\tSOLID principles and best practices in software design\n\tEnglish level: Intermediate and higher, Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required\n\tStrong prompt engineering skills to optimize the outputs of AI tools effectively\n\tCritical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking\n\tUtilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability, Familiarity with containerization and orchestration tools like Docker and Kubernetes\n\tExperience working in Linux environments\n\tKnowledge of NoSQL databases such as MongoDB or Cassandra\n\tUnderstanding of functional programming languages such as Python or F#\n\tExcellent problem-solving skills and attention to detail\n\tRelevant Certifications: Microsoft Certified: Azure Developer Associate, AWS Certified Developer – Associate, Velvetech is in the TOP 5 development companies in Illinois, USA \n    You have FLEXIBLE working conditions and a COOPERATIVE environment\n    Competitive salary\n    Many CHALLENGING and exciting projects with new opportunities and learning\n    GROWTH opportunities, skills and competencies improvement, and professional certification\n    In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": ".NET Backend Developer", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 20, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/net-backend-developer/", "description": ". NET Backend Developer Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Responsibilities A new team member will work fully remotely from the coziness of its home with many benefits and a competitive performance-based salary. With the ability to grow with us, you will be building software using the languages and technologies of the . NET framework. By working on our projects, you will be able to upgrade your skills in designing, developing, coding, customizing, configuring, testing, and deploying in support of enterprise packaged solutions. Developing and improving an application software and designing technical architecture supported by writing technical and functional documentation Implementing software tests and debugging code while producing the design schema of a database Support in making regular modifications to existing software for error correction, adaptation to new hardware and improving overall function and performance Analyzing customer needs and keeping up with changes in technologies Requirements Experience in the following technologies: . NET Core SQL databases (MS SQL, MySQL, PostgreSQL) ORM: EF, NHibernate SOLID principles and best practices in software design English level: Intermediate and higher AI related requirements Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required Strong prompt engineering skills to optimize the outputs of AI tools effectively Critical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking Utilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability The following will be considered as an advantage: Familiarity with containerization and orchestration tools like Docker and Kubernetes Experience working in Linux environments Knowledge of NoSQL databases such as MongoDB or Cassandra Understanding of functional programming languages such as Python or F# Excellent problem-solving skills and attention to detail Relevant Certifications: Microsoft Certified: Azure Developer Associate, AWS Certified Developer – Associate Benefits Velvetech is in the TOP 5 development companies in Illinois, USA You have FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) What we offer Future projects as highly loaded SaaS web-based services for automating business processes in the field of medicine, insurance, finance, energy, including modules for processing large data arrays, integration with external data sources, mobile applications, and others. Our team We are a friendly team of 170+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients. Let’s fly together!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/nodejs-react-developer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Strong experience with React\n\tExperience with Node.js / NestJS\nSQL databases such as PostgreSQL, MySQL, or MS SQL\n\tFamiliarity with modern front-end build pipelines and tools\nProficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar\n\tStrong prompt engineering skills to optimize the outputs of AI tools effectively\nAbility to understand business requirements and translate them into technical requirements\nExcellent written and verbal communication skills (English), Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required\nStrong prompt engineering skills to optimize the outputs of AI tools effectively\nCritical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking\nUtilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability, Understanding the principles of OOP/FP/SOLID\n\tExperience in mobile API implementation and MBaaS solutions\n\tInfrastructure experience with Kubernetes, AWS (RDS, SES, SQS, SNS, etc.)\n\tUnderstanding of microservice architecture\n\tExperience with TypeScript\n\tExperience with RabbitMQ, Velvetech is in the TOP 5 development companies in Illinois, USA\n    You have FLEXIBLE working conditions and a COOPERATIVE environment\n    Competitive salary\n    Many CHALLENGING and exciting projects with new opportunities and learning\n    GROWTH opportunities, skills and competencies improvement, and professional certification\n    In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Fullstack NodeJS React developer", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 20, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/nodejs-react-developer/", "description": "Fullstack NodeJS React developer About us Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Requirements Experience that you need to have for this role: Strong experience with React Experience with Node. js / NestJS SQL databases such as PostgreSQL, MySQL, or MS SQL Familiarity with modern front-end build pipelines and tools Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar Strong prompt engineering skills to optimize the outputs of AI tools effectively Ability to understand business requirements and translate them into technical requirements Excellent written and verbal communication skills (English) Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required Strong prompt engineering skills to optimize the outputs of AI tools effectively Critical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking Utilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability Additional experience will be considered as an advantage: Understanding the principles of OOP/FP/SOLID Experience in mobile API implementation and MBaaS solutions Infrastructure experience with Kubernetes, AWS (RDS, SES, SQS, SNS, etc. ) Understanding of microservice architecture Experience with TypeScript Experience with RabbitMQ AI related requirements Benefits Velvetech is in the TOP 5 development companies in Illinois, USA You have FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) What we offer Future projects as highly loaded SaaS web-based services for automating business processes in the field of medicine, insurance, finance, energy, including modules for processing large data arrays, billing subsystems, integration with external data sources, mobile applications, and IP telephony systems. Our team We are a friendly team of 150+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/node-js-developer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Designing customer-facing UI and back-end services for various business processes\n    Implementing effective security protocols, data protection measures, and storage solutions\n    Running diagnostic tests, repairing defects, and providing technical support\n    Documenting Node.js processes, including database schemas, as well as preparing reports\n    Recommending and implementing improvements to processes and technologies, Experience with Node.js/NestJS\n    Process stack – NestJs, microservices, MySql, Mongo, Redis, Elasticsearch;\n    Gitlab, CI/CD.\n    Ability to understand business requirements and translate them into technical requirements, Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required\n\tStrong prompt engineering skills to optimize the outputs of AI tools effectively\n\tCritical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking\n\tUtilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability, Understanding the principles of OOP/FP/SOLID\n\tExperience in mobile API implementation and MBaaS solutions will be a good advantage\n\tInfrastructure – Kubernetes, AWS (RDS, SES, SQS, SNS, etc.) \n    Experience with TypeScript\n    RabbitMQ, Velvetech is in the TOP 5 development companies in Illinois, USA\n    You have FLEXIBLE working conditions and a COOPERATIVE environment\n    Competitive salary\n    Many CHALLENGING and exciting projects with new opportunities and learning\n    GROWTH opportunities, skills and competencies improvement, and professional certification\n    In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Node.js Developer (remote)", "location": "", "location_type": "remote", "job_type": null, "min_experience": 20, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/node-js-developer/", "description": "Node. js Developer (remote) About us Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation and support of software solutions with modern microservices backends (dotnet core, Node. js, Java, SQL Server, Postgres, Mongo, Redis, RabbitMQ), web front-ends (React, Angular), and mobile development (Swift, Kotlin, Java). Most of the systems are prepared for cloud infrastructures (AWS, Azure, GCP), and heavily rely on Docker, Kubernetes while modern CI/CD is made using Gitlab CI or Teamcity. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in Gitlab or Github are built with multiple coordinated teams, collaborating via JIRA, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with start-ups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Responsibilities Designing customer-facing UI and back-end services for various business processes Implementing effective security protocols, data protection measures, and storage solutions Running diagnostic tests, repairing defects, and providing technical support Documenting Node. js processes, including database schemas, as well as preparing reports Recommending and implementing improvements to processes and technologies Requirements Experience with Node. js/NestJS Process stack – NestJs, microservices, MySql, Mongo, Redis, Elasticsearch; Gitlab, CI/CD. Ability to understand business requirements and translate them into technical requirements AI related requirements Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required Strong prompt engineering skills to optimize the outputs of AI tools effectively Critical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking Utilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability Additional experience will be considered as an advantage: Understanding the principles of OOP/FP/SOLID Experience in mobile API implementation and MBaaS solutions will be a good advantage Infrastructure – Kubernetes, AWS (RDS, SES, SQS, SNS, etc. ) Experience with TypeScript RabbitMQ Benefits Velvetech is in the TOP 5 development companies in Illinois, USA You have FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) Our team We are a friendly team of 170+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/outbound-lead-generator/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Prospect Identification & Research: Actively research and identify potential clients within target industries and personas that align with Velvetech’s Ideal Customer Profile (ICP).\nPersonalized Outreach Execution:\nCraft and send highly personalized, compelling emails and LinkedIn messages based on prospect research and strategic campaign guidance.\nConduct targeted cold calls to key decision-makers to introduce Velvetech and uncover potential needs.\nLead Qualification: Engage prospects in meaningful conversations to understand their business challenges, determine if Velvetech’s solutions are a potential fit, and qualify their interest.\nMeeting Scheduling: Secure introductory meetings or calls between qualified prospects and Velvetech’s Account Executives/Sales Team.\nActivity Tracking & Reporting: Meticulously log all outreach activities, prospect interactions, and lead information in the CRM system (e.g., HubSpot, Creatio). Track personal performance against KPIs.\nTool Utilization: Effectively use sales tools including CRM, email automation platforms (e.g., SmartLead, Apollo), LinkedIn Sales Navigator, and other prospecting tools provided.\nCollaboration & Feedback: Work closely with the Outbound Marketing Strategist, providing feedback on campaign messaging effectiveness, market response, and prospect objections. Collaborate with the Sales team for smooth lead handoffs.\nContinuous Learning: Stay informed about Velvetech’s services, case studies, target industries, and best practices in sales development and outbound prospecting., Experience: 2+ years of experience in a B2B Sales Development, Business Development, Lead Generation, or Inside Sales role, preferably within the software/technology industry.\n\t\n\tSkills:\n\t\n\tProven ability to generate leads and schedule meetings through outbound prospecting (cold calling, email, social selling).\n\n\tExceptional verbal and written communication skills, with the ability to craft clear, concise, and persuasive outreach.\n\n\tStrong research skills to effectively personalize outreach.\n\n\tResilience, persistence, and a positive attitude in handling rejection.\n\nExcellent organizational and time management skills to handle high-volume outreach across multiple channels.\nProficiency using CRM software (e.g., HubSpot, Salesforce, Creatio) and sales engagement tools (e.g., SalesLoft, Outreach, Apollo, SmartLead).\nComfortable and proficient with LinkedIn Sales Navigator for prospecting and outreach.\nGoal-oriented mindset with a drive to meet and exceed targets.\nAbility to quickly learn and articulate technical concepts and business value.\n\n\t\nPreferred Qualifications\n\n\tExperience prospecting into mid-market or enterprise accounts.\nExperience targeting industries such as Financial Services, Healthcare, Logistics, or Online Retail.\nFamiliarity with custom software development services or related B2B technology solutions.\nBachelor’s degree in Business, Marketing, Communications, or a related field.\nProven track record of consistently meeting or exceeding SDR quotas.\n\nBenefits\n\n    Velvetech is in the TOP 5 development companies in Illinois, USA\n    You have FLEXIBLE working conditions and a COOPERATIVE environment\n    Competitive salary\n    Many CHALLENGING and exciting projects with new opportunities and learning\n    GROWTH opportunities, skills and competencies improvement, and professional certification\n    In-company TRAINING (English, Software / DevOps / Project management / Design / Business)\n\nOur team\nWe are a friendly team of 170+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you!\nPeople work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other.\nYou have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients., Proven ability to generate leads and schedule meetings through outbound prospecting (cold calling, email, social selling).\n\n\tExceptional verbal and written communication skills, with the ability to craft clear, concise, and persuasive outreach.\n\n\tStrong research skills to effectively personalize outreach.\n\n\tResilience, persistence, and a positive attitude in handling rejection.\n\nExcellent organizational and time management skills to handle high-volume outreach across multiple channels.\nProficiency using CRM software (e.g., HubSpot, Salesforce, Creatio) and sales engagement tools (e.g., SalesLoft, Outreach, Apollo, SmartLead).\nComfortable and proficient with LinkedIn Sales Navigator for prospecting and outreach.\nGoal-oriented mindset with a drive to meet and exceed targets.\nAbility to quickly learn and articulate technical concepts and business value., Experience prospecting into mid-market or enterprise accounts.\nExperience targeting industries such as Financial Services, Healthcare, Logistics, or Online Retail.\nFamiliarity with custom software development services or related B2B technology solutions.\nBachelor’s degree in Business, Marketing, Communications, or a related field.\nProven track record of consistently meeting or exceeding SDR quotas., Velvetech is in the TOP 5 development companies in Illinois, USA\n    You have FLEXIBLE working conditions and a COOPERATIVE environment\n    Competitive salary\n    Many CHALLENGING and exciting projects with new opportunities and learning\n    GROWTH opportunities, skills and competencies improvement, and professional certification\n    In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Outbound Lead Generator", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 2, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/outbound-lead-generator/", "description": "Outbound Lead Generator About us Velvetech is an international company, based in the US, with 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. About the Role: Velvetech is seeking two ambitious and results-oriented Outbound Lead Generation Specialists to join our growing team and play a crucial role in driving our new business pipeline. As a LeadGen, you will be on the front lines of our lead generation efforts, responsible for identifying, engaging, and qualifying potential clients who fit our Ideal Customer Profile (ICP). You will utilize a multi-channel approach, including strategic cold calling, highly personalized emails, and targeted LinkedIn outreach, to initiate conversations and schedule introductory meetings for our Sales team. This role requires excellent communication skills, persistence, a knack for research, and the ability to articulate the value of complex custom software solutions to decision-makers in industries like FinTech, Healthcare, Logistics, and e-commerce. You will work closely with our Outbound Marketing Strategist and Sales team to execute targeted campaigns and contribute directly to Velvetech’s growth. Key Responsibilities: Prospect Identification & Research: Actively research and identify potential clients within target industries and personas that align with Velvetech’s Ideal Customer Profile (ICP). Personalized Outreach Execution: Craft and send highly personalized, compelling emails and LinkedIn messages based on prospect research and strategic campaign guidance. Conduct targeted cold calls to key decision-makers to introduce Velvetech and uncover potential needs. Lead Qualification: Engage prospects in meaningful conversations to understand their business challenges, determine if Velvetech’s solutions are a potential fit, and qualify their interest. Meeting Scheduling: Secure introductory meetings or calls between qualified prospects and Velvetech’s Account Executives/Sales Team. Activity Tracking & Reporting: Meticulously log all outreach activities, prospect interactions, and lead information in the CRM system (e. g. , HubSpot, Creatio). Track personal performance against KPIs. Tool Utilization: Effectively use sales tools including CRM, email automation platforms (e. g. , SmartLead, Apollo), LinkedIn Sales Navigator, and other prospecting tools provided. Collaboration & Feedback: Work closely with the Outbound Marketing Strategist, providing feedback on campaign messaging effectiveness, market response, and prospect objections. Collaborate with the Sales team for smooth lead handoffs. Continuous Learning: Stay informed about Velvetech’s services, case studies, target industries, and best practices in sales development and outbound prospecting. Qualifications: Experience: 2+ years of experience in a B2B Sales Development, Business Development, Lead Generation, or Inside Sales role, preferably within the software/technology industry. Skills: Proven ability to generate leads and schedule meetings through outbound prospecting (cold calling, email, social selling). Exceptional verbal and written communication skills, with the ability to craft clear, concise, and persuasive outreach. Strong research skills to effectively personalize outreach. Resilience, persistence, and a positive attitude in handling rejection. Excellent organizational and time management skills to handle high-volume outreach across multiple channels. Proficiency using CRM software (e. g. , HubSpot, Salesforce, Creatio) and sales engagement tools (e. g. , SalesLoft, Outreach, Apollo, SmartLead). Comfortable and proficient with LinkedIn Sales Navigator for prospecting and outreach. Goal-oriented mindset with a drive to meet and exceed targets. Ability to quickly learn and articulate technical concepts and business value. Preferred Qualifications Experience prospecting into mid-market or enterprise accounts. Experience targeting industries such as Financial Services, Healthcare, Logistics, or Online Retail. Familiarity with custom software development services or related B2B technology solutions. Bachelor’s degree in Business, Marketing, Communications, or a related field. Proven track record of consistently meeting or exceeding SDR quotas. Benefits Velvetech is in the TOP 5 development companies in Illinois, USA You have FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) Our team We are a friendly team of 170+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/power-bi-developer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Use advanced data modeling to build PowerBI-based dashboards (and related infrastructure) to solve real-world issues\n    Collaborate and engage with Subject Matter Experts (SMEs) and Domain experts to gain operational insights\n    Educate users and stakeholders on self-service solutions\n    Effectively contract and communicate with clients, partners, and internal analysts to define deliverables, timelines, and responsibilities\n    Design, develop and support data analytics using Power BI service, self-service models, and visualization tools\n    Develop, publish, schedule Power BI reports, enhance data models and dashboards to meet business requirements\n    Integrate Power BI reports into other applications using embedded services or API automation\n    Work with other analysts and data engineers to identify and understand source data systems and business requirements for solutions delivery\n    Identify KPIs with clear objectives and monitor them consistently\n    Analyze data and present it through reports that can help in decision-making\n    Understand business requirements and choose a data model that best fits user needs, customizing when needed, DAX experience – strong understanding and experience of Power BI application layer models\n    Bachelor’s Degree in Computer Science, Computer Engineering, or a closely related field\n    Excellent analytical and problem-solving skills for translating data into meaningful visualizations\n    Working knowledge of Power Queries; MS Power BI suite (Power Query/M/DAX), R language, MS PowerApps\n    Knowledge of SQL queries, SQL Server Reporting Services (SSRS)\n    2+ years of experience writing DAX, defining, building, releasing, and evangelizing Power BI-based dashboards\n    2-4 years information system experience or equivalent working experience\n    2+ years of experience delivering complex reports and dashboards\n    Ability to multitask and meet constant deadlines in a fast-paced environment\n    Intermediate and above English skills (not ideal, but spoken, as the client will be verbally defining tasks and meeting with the person), Velvetech is in the TOP 5 development companies in Illinois, USA\n    You have FLEXIBLE working conditions and a COOPERATIVE environment\n    Competitive salary\n    Many CHALLENGING and exciting projects with new opportunities and learning\n    GROWTH opportunities, skills and competencies improvement, and professional certification\n    In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Power BI Developer", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 2, "max_experience": 4, "apply_link": "https://www.velvetech.com/careers/power-bi-developer/", "description": "Power BI Developer About us Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Responsibilities The Power BI Developer partners with business stakeholders and analysts to understand and prioritize data and information requirements. Focuses on design and development of business intelligence (“BI”) applications and architecture. Solves technical problems and optimizes the performance of business intelligence tools. Use advanced data modeling to build PowerBI-based dashboards (and related infrastructure) to solve real-world issues Collaborate and engage with Subject Matter Experts (SMEs) and Domain experts to gain operational insights Educate users and stakeholders on self-service solutions Effectively contract and communicate with clients, partners, and internal analysts to define deliverables, timelines, and responsibilities Design, develop and support data analytics using Power BI service, self-service models, and visualization tools Develop, publish, schedule Power BI reports, enhance data models and dashboards to meet business requirements Integrate Power BI reports into other applications using embedded services or API automation Work with other analysts and data engineers to identify and understand source data systems and business requirements for solutions delivery Identify KPIs with clear objectives and monitor them consistently Analyze data and present it through reports that can help in decision-making Understand business requirements and choose a data model that best fits user needs, customizing when needed Requirements Experience that you need to have for this role: DAX experience – strong understanding and experience of Power BI application layer models Bachelor’s Degree in Computer Science, Computer Engineering, or a closely related field Excellent analytical and problem-solving skills for translating data into meaningful visualizations Working knowledge of Power Queries; MS Power BI suite (Power Query/M/DAX), R language, MS PowerApps Knowledge of SQL queries, SQL Server Reporting Services (SSRS) 2+ years of experience writing DAX, defining, building, releasing, and evangelizing Power BI-based dashboards 2-4 years information system experience or equivalent working experience 2+ years of experience delivering complex reports and dashboards Ability to multitask and meet constant deadlines in a fast-paced environment Intermediate and above English skills (not ideal, but spoken, as the client will be verbally defining tasks and meeting with the person) The following will be considered as an advantage: Experience with AI tools Experience with Azure AI Experience with differen large language models Benefits Velvetech is in the TOP 5 development companies in Illinois, USA You have FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) Our team We are a friendly team of 170+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/project-manager/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Working with technologies that we use in business while supporting architecture design and all technical requirements for the projects\n    Defining of project scope and objectives, involving all relevant stakeholders, and ensuring technical feasibility\n    Leading the team and setting up a workflow for them (development, testing, working with requirements)\n    Doing HRM activities such as staff recruiting, selecting, orienting, and training them while maintaining a safe and secure work environment\n    Motivating staff and developing their personal growth opportunities as well as leading their career path within the projects\n    Recommending strategies, policies, and procedures by evaluating organization outcomes, identifying issues, and evaluating trends\n    Tracking project performance, specifically to analyze the successful completion of short and long-term goals\n    Creating a transparent communication environment between all participants in the process in order to motivate them, tracking the satisfaction of the project from the side of the team\n    Communicating with the customers, managing their expectations, presenting ready-made solutions, demo versions, and prototypes\n    Participating in the selection and approval of the project team and in the development of information systems architecture\n    Meeting budgetary objectives and making adjustments to project constraints based on financial analysis, Software Development Service (Developing custom software solutions and platforms, etc.)\n    Web and Mobile Applications (Development and Integration, iOS, Android, etc.)\n    IT Services (Data and System Migration, Cloud, IT Security, etc.)\n    Process Automation (Smart Technology implementation, Problem-solving methodologies, etc.), Knowledge and experience in applying Agile methodologies, including <PERSON><PERSON>, Ka<PERSON>ban or Scaled Agile (LeSS, SAFe)\n    Experience in the role of developer, architect of information systems or business analyst\n    3+ years of successful experience in a similar position\n    English level: Upper-Intermediate and higher\n    Solid organizational skills including attention to detail and multitasking skills\n    Excellent written and verbal communication skills, Experience in the development of systems with high load\n    Understanding of the principles of automation and optimization of business processes\n    Project Management certification is a plus\n\tFamiliarity with AI-driven analysis tools such as ChatGPT or similar is required to facilitate advanced data analysis\n\tPrompt engineering skills to optimize AI-generated insights effectively\n\tCritical understanding of AI tool boundaries and the ability to apply sound judgment and critical thinking in analyzing data is essential\n\tUtilize AI for enhancing data interpretation, report generation, and decision-making processes to improve workflow reliability, Velvetech is in the TOP 5 development companies in Illinois, USA\n    You have FLEXIBLE working conditions and a COOPERATIVE environment\n    Competitive salary\n    Many CHALLENGING and exciting projects with new opportunities and learning\n    GROWTH opportunities, skills and competencies improvement, and professional certification\n    In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Project Manager (remote)", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 3, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/project-manager/", "description": "Project Manager (remote) About us Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Responsibilities If you are a specialist capable of managing the project from design and prioritization, scheduling tasks, control, communications towards prompt problem-solving, we are inviting you to join us. By working fully remotely from the coziness of your home, you will be able to organize your schedule and have a performance-based salary with many benefits from our company. Your main target would be to bring the customer’s idea to implementation on time, using available resources with managing remote teams. As part of this task, the Project Manager needs to build a development plan, organize a team, set up a process for working on a project, provide feedback between teams and a customer, eliminate interference for teams, control quality and deliver a product on time. If you have experience in the full cycle of Project Management, you are certainly at the right place! Working with technologies that we use in business while supporting architecture design and all technical requirements for the projects Defining of project scope and objectives, involving all relevant stakeholders, and ensuring technical feasibility Leading the team and setting up a workflow for them (development, testing, working with requirements) Doing HRM activities such as staff recruiting, selecting, orienting, and training them while maintaining a safe and secure work environment Motivating staff and developing their personal growth opportunities as well as leading their career path within the projects Recommending strategies, policies, and procedures by evaluating organization outcomes, identifying issues, and evaluating trends Tracking project performance, specifically to analyze the successful completion of short and long-term goals Creating a transparent communication environment between all participants in the process in order to motivate them, tracking the satisfaction of the project from the side of the team Communicating with the customers, managing their expectations, presenting ready-made solutions, demo versions, and prototypes Participating in the selection and approval of the project team and in the development of information systems architecture Meeting budgetary objectives and making adjustments to project constraints based on financial analysis Project Types Future projects as highly loaded SaaS web-based services for automating business processes in the field of medicine, insurance, finance, energy, including modules for processing large data arrays, integration with external data sources, mobile applications, and others. Software Development Service (Developing custom software solutions and platforms, etc. ) Web and Mobile Applications (Development and Integration, iOS, Android, etc. ) IT Services (Data and System Migration, Cloud, IT Security, etc. ) Process Automation (Smart Technology implementation, Problem-solving methodologies, etc. ) Requirements Experience that you need to have for this role: Knowledge and experience in applying Agile methodologies, including Scrum, Kanban or Scaled Agile (LeSS, SAFe) Experience in the role of developer, architect of information systems or business analyst 3+ years of successful experience in a similar position English level: Upper-Intermediate and higher Solid organizational skills including attention to detail and multitasking skills Excellent written and verbal communication skills Additional experience will be considered as an advantage: Experience in the development of systems with high load Understanding of the principles of automation and optimization of business processes Project Management certification is a plus Familiarity with AI-driven analysis tools such as ChatGPT or similar is required to facilitate advanced data analysis Prompt engineering skills to optimize AI-generated insights effectively Critical understanding of AI tool boundaries and the ability to apply sound judgment and critical thinking in analyzing data is essential Utilize AI for enhancing data interpretation, report generation, and decision-making processes to improve workflow reliability Benefits Velvetech is in the TOP 5 development companies in Illinois, USA You have FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) Our team We are a friendly team of 150+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/python-developer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Develop, test, and maintain high-quality Python applications\n        Collaborate with frontend developers to ensure seamless integration with the backend\n        Write clean, maintainable, and efficient code\n        Implement and manage API integrations\n        Develop and integrate high-quality visualizations using Highcharts\n        Work on trading algorithms and strategies\n\t\tDevelop integration with APEX custodian/back-end system via XML data exchange\n\t\tImplement and manage functionalities related to options trading\n\t\tParticipate in code reviews to maintain code quality and share knowledge\n\t\tDebug and resolve technical issues as they arise\n\t\tDesign and build application layers\n\t\tAnalyze requirements and design new functionality\n\t\tSupport and fix existing functionality\n\t\tProvide technical support during the project lifecycle\n\t\tEnsure proper documentation of all development activities, Proven experience as a Python Developer\n        Strong knowledge of Python frameworks (e.g., Django, Flask, Tornado)\n        Experience with RESTful APIs and integration with third-party services\n        Familiarity with front-end technologies (React, Angular, Vue) is a plus\n        Proficiency in using Git and GitHub for version control\n        Relational databases (such as PostgreSQL or MySQL) and SQL\n        Understanding of software development principles and best practices\n        Proficiency with Highcharts or similar data visualization libraries like Plotly or D3.js\n        Experience in the development of high-load systems\n        Knowledge of Kubernetes and Docker\n        Experience with microservices architecture\n\t\tKnowledge of English at least at the Intermediate level, Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required\n\tStrong prompt engineering skills to optimize the outputs of AI tools effectively\n\tCritical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking\n\tUtilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability, Challenging and exciting projects\n        Flexible schedule\n        Young and friendly team\n        Bonuses per project results, Velvetech is in the TOP 5 development companies in Illinois, USA\n        You have FLEXIBLE working conditions and a COOPERATIVE environment\n        Competitive salary\n        Many CHALLENGING and exciting projects with new opportunities and learning\n        GROWTH opportunities, skills and competencies improvement, and professional certification\n        In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Python Full-Stack Developer", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 20, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/python-developer/", "description": "Python Full-Stack Developer About us Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Velvetech is seeking a skilled and motivated Python Full-Stack Developer to join our dynamic team. You will be part of a new project, collaborating with other talented developers to deliver high-quality solutions. This project involves creating advanced financial visualizations using Highcharts and integrating them into our website Responsibilities Develop, test, and maintain high-quality Python applications Collaborate with frontend developers to ensure seamless integration with the backend Write clean, maintainable, and efficient code Implement and manage API integrations Develop and integrate high-quality visualizations using Highcharts Work on trading algorithms and strategies Develop integration with APEX custodian/back-end system via XML data exchange Implement and manage functionalities related to options trading Participate in code reviews to maintain code quality and share knowledge Debug and resolve technical issues as they arise Design and build application layers Analyze requirements and design new functionality Support and fix existing functionality Provide technical support during the project lifecycle Ensure proper documentation of all development activities Requirements Technical Skills: Proven experience as a Python Developer Strong knowledge of Python frameworks (e. g. , Django, Flask, Tornado) Experience with RESTful APIs and integration with third-party services Familiarity with front-end technologies (React, Angular, Vue) is a plus Proficiency in using Git and GitHub for version control Relational databases (such as PostgreSQL or MySQL) and SQL Understanding of software development principles and best practices Proficiency with Highcharts or similar data visualization libraries like Plotly or D3. js Experience in the development of high-load systems Knowledge of Kubernetes and Docker Experience with microservices architecture Knowledge of English at least at the Intermediate level AI related requirements Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required Strong prompt engineering skills to optimize the outputs of AI tools effectively Critical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking Utilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability Preferred Qualifications Bachelor’s degree in Computer Science, Engineering, or a related field Experience with cloud services (e. g. , AWS, Azure) Familiarity with Agile methodologies and project management tools Offer Challenging and exciting projects Flexible schedule Young and friendly team Bonuses per project results Benefits Velvetech is in the TOP 5 development companies in Illinois, USA You have FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) Our team We are a friendly team of 150+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/qa-automation-engineer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Do manual testing of the application\n        Develop automated test cases using Cypress\n        Testing frontend, database and API of application\n        Implement and document best practices for test automation and assist the development teams in improving code quality\n        Ability to translate product requirements into test plans and test cases for test execution. Prioritizing what needs to be automated\n        Participate in scrum meetings and estimating efforts with English-speaking team members\n        Work closely with product managers and engineering team to release high quality applications, Experience in Katalon studio and Postman – nice to have skill\n        Experience in both: manual and automation testing\n        Expertise in test automation tools such Cypress, Selenium or other frameworks\n        Experience in testing all three layers of the application – frontend, database and API\n        Experience in using Project Management tool – JIRA\n        Experience using Version control system (GitHub, Bitbucket…)\n        Experience in Agile project methodologies (Kanban or Scrum)\n        Good English knowledge – reading, writing, speaking\n        Ability to create test plans, strategies and identify roadblocks during sprints\n        Cross teams communication with engineering, product, and client success\n\t\tProficiency in AI-driven QA tools for test case generation and analysis is required to support quality assurance processes\n\t\tCandi<PERSON> should have strong prompt engineering skills to optimize AI-driven testing outputs effectively\n\t\tUnderstanding of AI tool boundaries and the ability to apply sound judgment and critical thinking in QA activities\n\t\tUtilize AI to debug, refine quality assurance processes, and improve documentation for enhanced reliability and productivity, Velvetech is in the TOP 5 development companies in Illinois, USA\n        You have FLEXIBLE working conditions and a COOPERATIVE environment\n        Competitive salary\n        Many CHALLENGING and exciting projects with new opportunities and learning\n        GROWTH opportunities, skills and competencies improvement, and professional certification\n        In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "QA Automation Engineer", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 20, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/qa-automation-engineer/", "description": "QA Automation Engineer About us Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Responsibilities We have one ticket for this race, working for us fully remotely from the coziness of your home with many benefits, and a competitive performance-based salary. We’re currently seeking a QA Automation Engineer to serve as a key member of our quality assurance team. We’re looking for someone who is a self starter with experience building automation from the ground up. Added automation tests in the CI/CD pipeline is a plus. This role will work within the Agile framework of continuous delivery. You should have in-depth experience with Cypress, Selenium or other testing frameworks. Do manual testing of the application Develop automated test cases using Cypress Testing frontend, database and API of application Implement and document best practices for test automation and assist the development teams in improving code quality Ability to translate product requirements into test plans and test cases for test execution. Prioritizing what needs to be automated Participate in scrum meetings and estimating efforts with English-speaking team members Work closely with product managers and engineering team to release high quality applications Requirements Experience in Katalon studio and Postman – nice to have skill Experience in both: manual and automation testing Expertise in test automation tools such Cypress, Selenium or other frameworks Experience in testing all three layers of the application – frontend, database and API Experience in using Project Management tool – JIRA Experience using Version control system (GitHub, Bitbucket…) Experience in Agile project methodologies (Kanban or Scrum) Good English knowledge – reading, writing, speaking Ability to create test plans, strategies and identify roadblocks during sprints Cross teams communication with engineering, product, and client success Proficiency in AI-driven QA tools for test case generation and analysis is required to support quality assurance processes Candidates should have strong prompt engineering skills to optimize AI-driven testing outputs effectively Understanding of AI tool boundaries and the ability to apply sound judgment and critical thinking in QA activities Utilize AI to debug, refine quality assurance processes, and improve documentation for enhanced reliability and productivity Benefits Velvetech is in the TOP 5 development companies in Illinois, USA You have FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) What we offer Future projects as highly loaded SaaS web-based services for automating business processes in the field of medicine, insurance, finance, energy, including modules for processing large data arrays, billing subsystems, integration with external data sources, mobile applications, and IP telephony systems. Our team We are a friendly team of 150+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients. Let’s fly together!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/qa-engineer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Testing current products and identifying deficiencies while suggesting the solutions\n        Investigating product quality in order to make improvements to achieve better customer satisfaction\n        Planing, creating and managing the overall quality planning strategy with the team\n        Collaborate with the product development team to ensure consistent project execution\n        Identify quality assurance process bottleneck and suggest actions for improvement\n        Participating in daily and other meetings\n        Tracking all bugs and communicate issues to ensure their resolution before the release, Growth and development on a dynamic project\n        Opportunity to cooperate with American designers, skill up UX/UI\n        Migration between projects\n        Possibility to work remotely, Understanding of the software development life cycle (SDLC)\n\tKnowledge of fundamental testing principles, methodologies, and testing types (e.g., functional and regression testing)\n\n\tHands-on experience in creating and maintaining test plans, bug tracking, and monitoring fixes\n\n\t1+ years of manual testing experience\n\n\tProficiency with issue tracking systems (e.g., Jira)\n\n\tFamiliarity with working in Agile environments and understanding specific features of testing for Agile projects\n\n\tExperience using test management tools.\n\n\tStrong analytical, communication, and team collaboration skills.\n\n\tAttention to detail, accuracy, and ability to learn and adapt quickly., Proficiency in AI-driven QA tools for test case generation and analysis is required to support quality assurance processes\n\t\tCandidates should have strong prompt engineering skills to optimize AI-driven testing outputs effectively\n\t\tUnderstanding of AI tool boundaries and the ability to apply sound judgment and critical thinking in QA activities\n\t\tUtilize AI to debug, refine quality assurance processes, and improve documentation for enhanced reliability and productivity, Knowledge of SQL, mainly ability to write simple queries\n        Experience with Swagger\n        Understanding of working principles of applications on microservice architecture\n        Experience in web and mobile testing\n\tISTQB Foundation Level Certification or equivalent., Velvetech is in the TOP 5 development companies in Illinois, USA\n        You have FLEXIBLE working conditions and a COOPERATIVE environment\n        Competitive salary\n        Many CHALLENGING and exciting projects with new opportunities and learning\n        GROWTH opportunities, skills and competencies improvement, and professional certification\n        In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "QA Engineer (remote)", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 1, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/qa-engineer/", "description": "QA Engineer (remote) About us Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Responsibilities If you like assessing the quality of specifications and technical design documents in order to ensure timely, relevant, and meaningful feedback, we can make your dream come true – you will be our QA Engineer, working fully remotely from your home. You will be working with a WMS module within which the rules/policies for item processing will be created. In particular, creating and testing rules for item management. We are ready to provide the templates for creating consistent and clear documentation. Testing current products and identifying deficiencies while suggesting the solutions Investigating product quality in order to make improvements to achieve better customer satisfaction Planing, creating and managing the overall quality planning strategy with the team Collaborate with the product development team to ensure consistent project execution Identify quality assurance process bottleneck and suggest actions for improvement Participating in daily and other meetings Tracking all bugs and communicate issues to ensure their resolution before the release Additional possibilities: Growth and development on a dynamic project Opportunity to cooperate with American designers, skill up UX/UI Migration between projects Possibility to work remotely Requirements Understanding of the software development life cycle (SDLC) Knowledge of fundamental testing principles, methodologies, and testing types (e. g. , functional and regression testing) Hands-on experience in creating and maintaining test plans, bug tracking, and monitoring fixes 1+ years of manual testing experience Proficiency with issue tracking systems (e. g. , Jira) Familiarity with working in Agile environments and understanding specific features of testing for Agile projects Experience using test management tools. Strong analytical, communication, and team collaboration skills. Attention to detail, accuracy, and ability to learn and adapt quickly. AI related requirements Proficiency in AI-driven QA tools for test case generation and analysis is required to support quality assurance processes Candidates should have strong prompt engineering skills to optimize AI-driven testing outputs effectively Understanding of AI tool boundaries and the ability to apply sound judgment and critical thinking in QA activities Utilize AI to debug, refine quality assurance processes, and improve documentation for enhanced reliability and productivity Additional experience will be considered as an advantage: Knowledge of SQL, mainly ability to write simple queries Experience with Swagger Understanding of working principles of applications on microservice architecture Experience in web and mobile testing ISTQB Foundation Level Certification or equivalent. Benefits Velvetech is in the TOP 5 development companies in Illinois, USA You have FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) Our team We are a friendly team of 150+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/react-developer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Actively involved from conception to completion with projects that are technologically sound and aesthetically impressiveBuilding reusable components and front-end libraries for future use and translating designs and wireframes into high-quality codeOptimizing components for maximum performance across a vast array of web-capable devices and browsers and coordinating with various teams working on distinct layersRedesigning and rewriting the product functionality, developing features to enhance the user experience, and determining the structure and design of the productEvaluating emerging technologies and proposing strategic, innovative, and cost-effective solutions that increase the efficiency, reliability, and integration of the business softwareDeveloping the shared component library (currently converting our component library from Flow to TypeScript, migrating from version 9 to 10 of styled components or CSS models)Helping transition our product from the old UI to ASP.NET and transition the scripts in jQuery+React, which inject components in our legacy UI, to a full SPA application (React ^16.13.x)Communicate frontend best practices to the team, enhancing efficiency and performance across the department, Continuously learning as a front-end developer and improving SPA development and other skillsWeb and Mobile Applications (Development and Integration, iOS, Android, etc.)IT Services (Data and System Migration, Cloud, IT Security, etc.)Process Automation (Smart Technology implementation, Problem-solving methodologies, etc.), Software Development Service (Developing custom software solutions and platforms, etc.)Performing code review and improving CI/CD processes and automationParticipating in bi-weekly inter-team demos via Zoom, sharing results, and receiving input from other teammatesWorking in short 5-day sprints with small batches of clear tasks, Thorough understanding of React and its core principles, including React 18 or later\nModern JavaScript features (ES2015+), with an understanding of recent ECMAScript updates\nDOM, HTML5 API, CSS3, and HTML5\nBuilding SPAs (Single Page Applications) and writing tests using Jest + React Testing Library\nRESTful architecture and API, Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required\nStrong prompt engineering skills to optimize the outputs of AI tools effectively\nCritical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking\nUtilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability, Experience in independently implementing product features\nWorking in a cross-functional team environment (e.g., with designers, UX researchers, BAs, product managers, and developers)\nProficiency in TypeScript\nInterest in the latest ECMAScript features, React, and client-side development innovations\nFamiliarity with Testing Library and its extensions for hooks and React\nExperience with introducing new tools and refining configurations for ESLint and Prettier\nCustomization of GitLab CI/CD pipelines, Docker, or modern build tools like Vite and ESBuild\nKnowledge of Nginx configuration\nIntermediate English proficiency or higher, Velvetech is in the TOP 5 development companies in Illinois, USAYou have FLEXIBLE working conditions and a COOPERATIVE environmentCompetitive salaryMany CHALLENGING and exciting projects with new opportunities and learningGROWTH opportunities, skills and competencies improvement, and professional certificationIn-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "React Developer (remote)", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 20, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/react-developer/", "description": "React Developer (remote)About usVelvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. ResponsibilitiesIf you would like to share your knowledge and experience in designing and implementing user interface components for JavaScript-based web and mobile applications using the React open-source library ecosystem with our friendly team, we invite you to join us. As a new team member, working fully remotely from the coziness of your home, you would be responsible for leading the team while developing the latest user-facing features using React and building a modern highly responsive web-based user interface. In a friendly environment with the possibility of growing your business and technical skills further along with various certifications and career upgrades, you would be able to have many benefits and a competitive performance-based salary that are waiting for you. Actively involved from conception to completion with projects that are technologically sound and aesthetically impressiveBuilding reusable components and front-end libraries for future use and translating designs and wireframes into high-quality codeOptimizing components for maximum performance across a vast array of web-capable devices and browsers and coordinating with various teams working on distinct layersRedesigning and rewriting the product functionality, developing features to enhance the user experience, and determining the structure and design of the productEvaluating emerging technologies and proposing strategic, innovative, and cost-effective solutions that increase the efficiency, reliability, and integration of the business softwareDeveloping the shared component library (currently converting our component library from Flow to TypeScript, migrating from version 9 to 10 of styled components or CSS models)Helping transition our product from the old UI to ASP. NET and transition the scripts in jQuery+React, which inject components in our legacy UI, to a full SPA application (React ^16. 13. x)Communicate frontend best practices to the team, enhancing efficiency and performance across the departmentAdditional activities and benefits:Continuously learning as a front-end developer and improving SPA development and other skillsWeb and Mobile Applications (Development and Integration, iOS, Android, etc. )IT Services (Data and System Migration, Cloud, IT Security, etc. )Process Automation (Smart Technology implementation, Problem-solving methodologies, etc. )Project TypesFuture projects as highly loaded SaaS web-based services for automating business processes in the field of medicine, insurance, finance, energy, including modules for processing large data arrays, integration with external data sources, mobile applications, and others. Software Development Service (Developing custom software solutions and platforms, etc. )Performing code review and improving CI/CD processes and automationParticipating in bi-weekly inter-team demos via Zoom, sharing results, and receiving input from other teammatesWorking in short 5-day sprints with small batches of clear tasks Requirements Thorough understanding of React and its core principles, including React 18 or later Modern JavaScript features (ES2015+), with an understanding of recent ECMAScript updates DOM, HTML5 API, CSS3, and HTML5 Building SPAs (Single Page Applications) and writing tests using Jest + React Testing Library RESTful architecture and API AI related requirements Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required Strong prompt engineering skills to optimize the outputs of AI tools effectively Critical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking Utilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability The following experience will be considered as an advantage: Experience in independently implementing product features Working in a cross-functional team environment (e. g. , with designers, UX researchers, BAs, product managers, and developers) Proficiency in TypeScript Interest in the latest ECMAScript features, React, and client-side development innovations Familiarity with Testing Library and its extensions for hooks and React Experience with introducing new tools and refining configurations for ESLint and Prettier Customization of GitLab CI/CD pipelines, Docker, or modern build tools like Vite and ESBuild Knowledge of Nginx configuration Intermediate English proficiency or higher BenefitsVelvetech is in the TOP 5 development companies in Illinois, USAYou have FLEXIBLE working conditions and a COOPERATIVE environmentCompetitive salaryMany CHALLENGING and exciting projects with new opportunities and learningGROWTH opportunities, skills and competencies improvement, and professional certificationIn-company TRAINING (English, Software / DevOps / Project management / Design / Business)What we offerOur employee has an opportunity for continuous learning as a front-end developer with improving your SPA development and other skills while performing code review and improving CI/CD processes and automation. Besides that, you will be participating in bi-weekly inter-team demos via Zoom, sharing results and receiving input from other teammates. By working in short 5-day sprints with small batches of clear tasks, we offer many flexible benefits that make us different from other companies. Our teamWe are a friendly team of 170+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/senior-generative-ai-engineer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Design and implement advanced generative AI models to address key project challenges\n\tDevelop generative AI models for enhancing customer experience, optimizing cross-channel sales, order fulfillment, and logistics\n\tIntegrate generative AI solutions with various sales and logistics platforms\n\tInnovate in AI-driven customer experience management\n\tArchitect and develop advanced AI models using TensorFlow, PyTorch, GANs, VAEs, and Transformers\n\tImplement computer vision algorithms for product assessment and NLP for processing textual data\n\tDesign dynamic pricing models that adapt to real-time data\n\tIntegrate the AI engine with existing systems via RESTful APIs\n\tImplement a continuous improvement culture through a feedback loop mechanism\n\tStay abreast of the latest AI research and technologies to continuously improve our solutions\n\tCollaborate with cross-functional teams to align AI solutions with business objectives, Expertise in AI/ML, NLP, and computer vision, and prompt engineering\n\tExperience with cloud services, big data technologies\n\tExpertise in GANs, VAEs, TensorFlow, PyTorch, and deep learning architectures\n\tProven experience in deploying scalable AI models in cloud environments\n\tStrong programming skills in Python and advanced experience with AI development tools\n\tAdvanced analytics and excellent problem-solving abilities\n\tStrong foundation in mathematics, statistics, and data preprocessing, Bachelor or advanced degree in Computer Science, AI, or related field\n\tSignificant experience in generative AI model development, training, fine-tuning, and deploying AI models\n\tPortfolio of projects demonstrating expertise in generative AI, Groundbreaking AI project work\n\tCompetitive salary\n\tCareer growth opportunities in a fast-paced tech-driven company\n\tResult oriented culture, collaborative and highly innovative work environment\nYou have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients. Start your journey today!, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Senior Generative AI Engineer", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/senior-generative-ai-engineer/", "description": "Senior Generative AI Engineer At Velvetech we’re innovating at the intersection of retail operations, sales, logistics, and technology. Our latest initiative, the Cross-Channel Commerce Integration Engine, aims to redefine how retail businesses work with their vendors, serve their customers, manage sales, order fulfillment, and logistics across all channels. Role overview We are seeking a Senior Generative AI Engineer with a strong background in developing and deploying sophisticated generative AI solutions. The ideal candidate will possess deep expertise in generative AI technologies, including GANs, VAEs, and NLP, and will play a pivotal role in driving our project from conception to deployment. As a Senior Generative AI Engineer, you will lead the development of our AI-powered engine, using advanced AI to enhance back-office operational efficiencies and customer experiences across online, in-store, and mobile platforms. Responsibilities Design and implement advanced generative AI models to address key project challenges Develop generative AI models for enhancing customer experience, optimizing cross-channel sales, order fulfillment, and logistics Integrate generative AI solutions with various sales and logistics platforms Innovate in AI-driven customer experience management Architect and develop advanced AI models using TensorFlow, PyTorch, GANs, VAEs, and Transformers Implement computer vision algorithms for product assessment and NLP for processing textual data Design dynamic pricing models that adapt to real-time data Integrate the AI engine with existing systems via RESTful APIs Implement a continuous improvement culture through a feedback loop mechanism Stay abreast of the latest AI research and technologies to continuously improve our solutions Collaborate with cross-functional teams to align AI solutions with business objectives Requirements Expertise in AI/ML, NLP, and computer vision, and prompt engineering Experience with cloud services, big data technologies Expertise in GANs, VAEs, TensorFlow, PyTorch, and deep learning architectures Proven experience in deploying scalable AI models in cloud environments Strong programming skills in Python and advanced experience with AI development tools Advanced analytics and excellent problem-solving abilities Strong foundation in mathematics, statistics, and data preprocessing The following will be considered as an advantage: Bachelor or advanced degree in Computer Science, AI, or related field Significant experience in generative AI model development, training, fine-tuning, and deploying AI models Portfolio of projects demonstrating expertise in generative AI What we offer Groundbreaking AI project work Competitive salary Career growth opportunities in a fast-paced tech-driven company Result oriented culture, collaborative and highly innovative work environment You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients. Start your journey today!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/senior-data-engineer-analytic/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Architect and implement ETL workflows using Microsoft Fabric, including Data Pipelines, Dataflows, and Notebooks.\n\tIntegrate Azure Blob Storage as the primary data staging area, ensuring seamless compatibility with Microsoft Fabric.\n\tDesign template mapping systems with versioning to support dynamic file processing and metadata management.\n\tBuild and optimize data processing pipelines for various source file formats and carrier templates.\n    Ensure the system adheres to high availability, scalability, and performance standards.\n\tCollaborate with Power Apps and Power Automate developers for smooth workflow integration.\n\tDevelop and implement comprehensive error handling and logging mechanisms., 5+ years of experience in data engineering, with strong expertise in Microsoft Fabric or Azure Data Factory, Azure Synapse Analytics and Azure ecosystems.\n\tPractical knowledge of Python, SQL, Javascript. Experience with C# and Java can be helpful.\n    Proficiency in building and maintaining Azure Blob Storage solutions.\n    Experience with Power BI for data visualization and integration.\n    Advanced programming skills in Python and DAX.\n    Familiarity with BPMN and data governance frameworks.\n    Microsoft certifications in relevant tools (e.g., PL-600, PL-400, DP-500).\n    Excellent problem-solving and communication skills., Velvetech is in the TOP 5 development companies in Illinois,  USA\n    You have FLEXIBLE working conditions and a COOPERATIVE environment\n    Competitive salary\n    Many CHALLENGING and exciting projects with new opportunities and learning\n    GROWTH opportunities, skills and competencies improvement, and professional certification\n    In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Senior Data Engineer / Analytic Engineer (Microsoft Fabric)", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/senior-data-engineer-analytic/", "description": "Senior Data Engineer / Analytic Engineer (Microsoft Fabric) About us Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Position overview As a Senior Data Engineer / Analytic Engineer (Microsoft Fabric), you will lead the design, implementation, and optimization of data engineering pipelines and ETL processes. You will focus on leveraging Microsoft Fabric, Azure Blob Storage, and Power Platform tools to build scalable and efficient systems for data transformation and analytics. This is a fully remote position, allowing you to work from the comfort of your home with flexible working conditions. Responsibilities Architect and implement ETL workflows using Microsoft Fabric, including Data Pipelines, Dataflows, and Notebooks. Integrate Azure Blob Storage as the primary data staging area, ensuring seamless compatibility with Microsoft Fabric. Design template mapping systems with versioning to support dynamic file processing and metadata management. Build and optimize data processing pipelines for various source file formats and carrier templates. Ensure the system adheres to high availability, scalability, and performance standards. Collaborate with Power Apps and Power Automate developers for smooth workflow integration. Develop and implement comprehensive error handling and logging mechanisms. Requirements 5+ years of experience in data engineering, with strong expertise in Microsoft Fabric or Azure Data Factory, Azure Synapse Analytics and Azure ecosystems. Practical knowledge of Python, SQL, Javascript. Experience with C# and Java can be helpful. Proficiency in building and maintaining Azure Blob Storage solutions. Experience with Power BI for data visualization and integration. Advanced programming skills in Python and DAX. Familiarity with BPMN and data governance frameworks. Microsoft certifications in relevant tools (e. g. , PL-600, PL-400, DP-500). Excellent problem-solving and communication skills. Benefits Velvetech is in the TOP 5 development companies in Illinois, USA You have FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) Our team We are a friendly team of 150+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/senior-data-engineer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Develop and optimize ETL workflows using Python and SQL.\n\tDesign and implement scalable data pipelines for processing various data sources.\n\tWork with relational databases (e.g., PostgreSQL, Oracle) to ensure efficient data storage and retrieval.\n\tOptimize SQL queries and Python scripts for performance and maintainability.\n    Implement robust error handling, logging, and monitoring for data processing pipelines.\n\tCollaborate with cross-functional teams to integrate and manage data sources., 5+ years of experience in Data Engineering.\n\tExpert-level Python and SQL skills – ability to write efficient, scalable, and maintainable code.\n    Strong experience in designing ETL processes and working with large datasets.\n    Solid understanding of database performance tuning and query optimization.\n    Experience with data modeling and schema design.\n    Familiarity with cloud storage solutions (Azure, AWS, or GCP).\n    Strong problem-solving skills and ability to work independently.\n    Nice to have: Experience with PySpark, orchestrators and knowledge of CI/CD for deployment process automation., Velvetech is in the TOP 5 development companies in Illinois,  USA\n    You have FLEXIBLE working conditions and a COOPERATIVE environment\n    Competitive salary\n    Many CHALLENGING and exciting projects with new opportunities and learning\n    GROWTH opportunities, skills and competencies improvement, and professional certification\n    In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Senior Data Engineer", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/senior-data-engineer/", "description": "Senior Data Engineer About us Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Position overview We are looking for a Senior Data Engineer with deep expertise in Python and SQL. You will design, implement, and optimize data pipelines and ETL processes, ensuring high performance, scalability, and reliability. This is a fully remote position with flexible working conditions. Responsibilities Develop and optimize ETL workflows using Python and SQL. Design and implement scalable data pipelines for processing various data sources. Work with relational databases (e. g. , PostgreSQL, Oracle) to ensure efficient data storage and retrieval. Optimize SQL queries and Python scripts for performance and maintainability. Implement robust error handling, logging, and monitoring for data processing pipelines. Collaborate with cross-functional teams to integrate and manage data sources. Requirements 5+ years of experience in Data Engineering. Expert-level Python and SQL skills – ability to write efficient, scalable, and maintainable code. Strong experience in designing ETL processes and working with large datasets. Solid understanding of database performance tuning and query optimization. Experience with data modeling and schema design. Familiarity with cloud storage solutions (Azure, AWS, or GCP). Strong problem-solving skills and ability to work independently. Nice to have: Experience with PySpark, orchestrators and knowledge of CI/CD for deployment process automation. Benefits Velvetech is in the TOP 5 development companies in Illinois, USA You have FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) Our team We are a friendly team of 150+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/senior-voip-engineer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Development of the new features on ErlangSystem configuring on the basis of KAZOO, FreeSWITCH, Kamailio, malfunction analysisTechnical support of users, consulting, maintaining documentationEnsure trouble-free work of PBXMonitoring and analyzing of events (detected on our side or reported by client)Participating in projects related to VoIP as a consultant, Experience in developing on ErlangHTTP (used for KAZOO configuring) and understanding of JSON formatKnowledge and administration experience with OS Linux (CentOS/Debian), Experience with KAZOOAbility to tune Linux to PBX tasksDeep knowledge of SIP/RTP/WebRTC/STUN protocolsAdministration of IP-telephony, mainly KAZOO, FreeSWITCH, KamailioGitLab CI/CDUnderstanding of SIPp or any other tool used for testing (including load testing)Experience with Zabbix, VoIP monitor Experience in developing on Python, other programming languages will be a plus, Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required\n\tStrong prompt engineering skills to optimize the outputs of AI tools effectively\n\tCritical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking\n\tUtilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability, Velvetech is in the TOP 5 development companies in Illinois, USAYou have FLEXIBLE working conditions and a COOPERATIVE environmentCompetitive salaryMany CHALLENGING and exciting projects with new opportunities and learningGROWTH opportunities, skills and competencies improvement, and professional certificationIn-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Erlang developer", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 20, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/senior-voip-engineer/", "description": "Erlang developerAbout usVelvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. ResponsibilitiesWe’re developing a system for a call center that provides a full spectrum of telephony services, and also statistics for call center work control – online and real-time speech recognition, maintenance of statistics, etc. One of our projects is a kazoo-based multi-tenant PBX with CRM systems integrations, speech recognition, call queues, etc. Therefore, we are seeking an expert for implementation, maintaining and integrating current products related to SIP/VoIP, who will work fully remotely from the coziness of its home with a performance-based salary and many benefits. Development of the new features on ErlangSystem configuring on the basis of KAZOO, FreeSWITCH, Kamailio, malfunction analysisTechnical support of users, consulting, maintaining documentationEnsure trouble-free work of PBXMonitoring and analyzing of events (detected on our side or reported by client)Participating in projects related to VoIP as a consultantRequirementsExperience in developing on ErlangHTTP (used for KAZOO configuring) and understanding of JSON formatKnowledge and administration experience with OS Linux (CentOS/Debian)The following experience will be considered as an advantageExperience with KAZOOAbility to tune Linux to PBX tasksDeep knowledge of SIP/RTP/WebRTC/STUN protocolsAdministration of IP-telephony, mainly KAZOO, FreeSWITCH, KamailioGitLab CI/CDUnderstanding of SIPp or any other tool used for testing (including load testing)Experience with Zabbix, VoIP monitor Experience in developing on Python, other programming languages will be a plus AI related requirements Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required Strong prompt engineering skills to optimize the outputs of AI tools effectively Critical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking Utilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability BenefitsVelvetech is in the TOP 5 development companies in Illinois, USAYou have FLEXIBLE working conditions and a COOPERATIVE environmentCompetitive salaryMany CHALLENGING and exciting projects with new opportunities and learningGROWTH opportunities, skills and competencies improvement, and professional certificationIn-company TRAINING (English, Software / DevOps / Project management / Design / Business)Our teamWe are a friendly team of 170+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/ui-ux-designer/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Gather and evaluate user requirements in collaboration with product managers, stakeholders, subject matter experts and engineers.\n    Illustrate design ideas using storyboards, process flows and sitemaps\n    Design graphic user interface elements, like menus, tabs and widgets that are aligned with our UI Toolkit and pattern library.\n    Develop UI mockups and prototypes that clearly illustrate how sites function and look and clearly communicate the business logic.\n    Create original graphic designs (e.g. images, sketches and tables)\n    Prepare and present rough drafts/functional wireframes to internal teams and key stakeholders\n    Identify and troubleshoot UX problems (e.g. responsiveness)\n    Conduct layout adjustments based on user feedback, Proven work experience as a UI/UX Designer or similar role\n    Portfolio of design projects (heavy data/grid cases are a plus)\n    Knowledge of wireframe tools (e.g. Zeppelin, Balsamig)\n    Up-to-date knowledge of design software like Figma, Adobe Illustrator and Photoshop\n    Knowledge of Prototyping, Motion Design, User Research, Information Architecture\n    Proficient with Microsoft Office Suite or related software\n    English level: Upper-Intermediate or Advanced\n\tExperience with confluence\n\tCreation of guidelines, Proficiency in AI-driven design tools such as Figma with AI plugins, Adobe XD with Sensei features, or similar is required\nStrong skills in crafting effective prompts and inputs to optimize AI tool outputs for design\nA critical understanding of AI tool limitations and the ability to apply sound judgment and critical thinking in design processes\nExperience utilizing artificial intelligence to enhance design quality, streamline design workflows, and improve documentation for reliability and efficiency, Strong communication skills to collaborate with various stakeholders\n    Good time-management skills\n    Self-motivated in achieving goals and completing routine tasks\n    Excellent planning, organizational, and time management skills, Working in the TOP 5 development companies in Illinois, USA\n    FLEXIBLE working conditions and a COOPERATIVE environment\n    Competitive salary\n    Many CHALLENGING and exciting projects with new opportunities and learning\n    GROWTH opportunities, skills and competencies improvement, and professional certification\n    In-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "UI/UX Designer", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 20, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/ui-ux-designer/", "description": "UI/UX Designer Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for , or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Responsibilities We are looking for a result driven, team and detail oriented, organized UI/UX Designer. UI/UX Designer responsibilities include gathering user requirements, designing graphic elements and building navigation components. We follow a design first development practice that will integrate our collective business knowledge into a SaaS platform that must be intuitive to use. To be successful in this role, you should have experience with design software and wireframe tools. You should also have a portfolio of professional design projects that includes work with web/mobile applications. Ultimately, you’ll create both functional and appealing features that address our clients’ needs and help us grow our customer base. Gather and evaluate user requirements in collaboration with product managers, stakeholders, subject matter experts and engineers. Illustrate design ideas using storyboards, process flows and sitemaps Design graphic user interface elements, like menus, tabs and widgets that are aligned with our UI Toolkit and pattern library. Develop UI mockups and prototypes that clearly illustrate how sites function and look and clearly communicate the business logic. Create original graphic designs (e. g. images, sketches and tables) Prepare and present rough drafts/functional wireframes to internal teams and key stakeholders Identify and troubleshoot UX problems (e. g. responsiveness) Conduct layout adjustments based on user feedback Requirements Proven work experience as a UI/UX Designer or similar role Portfolio of design projects (heavy data/grid cases are a plus) Knowledge of wireframe tools (e. g. Zeppelin, Balsamig) Up-to-date knowledge of design software like Figma, Adobe Illustrator and Photoshop Knowledge of Prototyping, Motion Design, User Research, Information Architecture Proficient with Microsoft Office Suite or related software English level: Upper-Intermediate or Advanced Experience with confluence Creation of guidelines AI related requirements Proficiency in AI-driven design tools such as Figma with AI plugins, Adobe XD with Sensei features, or similar is required Strong skills in crafting effective prompts and inputs to optimize AI tool outputs for design A critical understanding of AI tool limitations and the ability to apply sound judgment and critical thinking in design processes Experience utilizing artificial intelligence to enhance design quality, streamline design workflows, and improve documentation for reliability and efficiency The following will be considered as an advantage: Strong communication skills to collaborate with various stakeholders Good time-management skills Self-motivated in achieving goals and completing routine tasks Excellent planning, organizational, and time management skills Benefits Working in the TOP 5 development companies in Illinois, USA FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) Our team We are a friendly team of 170+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients. Start your journey today!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/technical-team-lead/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Lead a cross-functional project team that may include backend, frontend, QA engineers, and business analysts.\n    Oversee the end-to-end technical execution of projects, ensuring timely and high-quality delivery.\n    Participate in hands-on development, primarily using .NET Core and React, contributing to both backend and frontend codebases.\n    Design system architecture and development plans, balancing scalability, maintainability, and performance.\n    Serve as the primary communication link between the development team and the client, ensuring alignment on goals, priorities, and deliverables.\n    Introduce and promote the use of AI-powered development tools (e.g., GitHub Copilot, ChatGPT, Tabnine) to improve team productivity.\n    Foster a collaborative, proactive, and results-driven team culture.\n    Drive process improvements, encourage best practices, and ensure Agile delivery through Scrum or Kanban methodologies.\n    Monitor project progress, conduct regular team syncs, and provide technical guidance and mentoring.\n    Stay current with industry trends, emerging technologies, and best practices in software development and AI tools., Experience in full stack development, with strong skills in:\n        \n            .NET Core\n            React (Vue.js or Angular experience is a plus)\n            SQL databases: MS SQL, MySQL, PostgreSQL\n            ORM: Entity Framework, NHibernate\n        \n    \n    Strong understanding of SOLID principles and modern software design practices.\n    Experience working with AI-assisted development tools like GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar.\n    Strong prompt engineering skills to leverage AI tools effectively.\n    Familiarity with containerization (<PERSON><PERSON>, <PERSON>bernetes) is a plus.\n    English proficiency at Upper-Intermediate level or higher.\n\tProficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required\n\tStrong prompt engineering skills to optimize the outputs of AI tools effectively\n\tCritical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking\n\tUtilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability, .NET Core\n            React (Vue.js or Angular experience is a plus)\n            SQL databases: MS SQL, MySQL, PostgreSQL\n            ORM: Entity Framework, NHibernate, Proven experience in a team leadership role, managing cross-functional teams (developers, QA, BAs).\n    Strong ability to organize team work, mentor engineers, and resolve technical or process-related issues.\n    Excellent communication and interpersonal skills, able to interact effectively with both technical teams and business stakeholders.\n    Strong organizational skills with the ability to manage multiple priorities in a fast-paced environment.\n    Adaptability and readiness to quickly adjust to changing project scopes, priorities, and technologies., Experience with functional programming languages (Python, F#).\n    Experience working in cloud environments: AWS, Azure, GCP.\n    Microsoft Certified: Azure Developer Associate or AWS Certified Developer – Associate.\n    Previous experience leading teams in client-facing, dynamic, and Agile environments.\n\tExperience in Linux environments and with NoSQL databases (MongoDB, Cassandra) is an advantage., Working in the TOP 5 development companies in Illinois, USA\n\tFLEXIBLE working conditions and a COOPERATIVE environment\n\tCompetitive salary\n\tMany CHALLENGING and exciting projects with new opportunities and learning\n\tGROWTH opportunities, skills and competencies improvement, and professional certification\n\tIn-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "Technical Team Lead", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": 20, "max_experience": null, "apply_link": "https://www.velvetech.com/careers/technical-team-lead/", "description": "Technical Team Lead Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. Position overview We are looking for a Technical Team Lead to join our fully remote, globally distributed team. This is a leadership role that combines hands-on development with managing a cross-functional team, which may include backend developers, frontend developers, QA engineers, and business analysts. You will be responsible for the technical direction, team coordination, mentoring, and delivery of high-quality solutions that meet both client and business needs. You will also actively participate in the development process and facilitate the effective use of modern AI-powered development tools. Responsibilities Lead a cross-functional project team that may include backend, frontend, QA engineers, and business analysts. Oversee the end-to-end technical execution of projects, ensuring timely and high-quality delivery. Participate in hands-on development, primarily using . NET Core and React, contributing to both backend and frontend codebases. Design system architecture and development plans, balancing scalability, maintainability, and performance. Serve as the primary communication link between the development team and the client, ensuring alignment on goals, priorities, and deliverables. Introduce and promote the use of AI-powered development tools (e. g. , GitHub Copilot, ChatGPT, Tabnine) to improve team productivity. Foster a collaborative, proactive, and results-driven team culture. Drive process improvements, encourage best practices, and ensure Agile delivery through Scrum or Kanban methodologies. Monitor project progress, conduct regular team syncs, and provide technical guidance and mentoring. Stay current with industry trends, emerging technologies, and best practices in software development and AI tools. Requirements Technical Skills: Experience in full stack development, with strong skills in: . NET Core React (Vue. js or Angular experience is a plus) SQL databases: MS SQL, MySQL, PostgreSQL ORM: Entity Framework, NHibernate Strong understanding of SOLID principles and modern software design practices. Experience working with AI-assisted development tools like GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar. Strong prompt engineering skills to leverage AI tools effectively. Familiarity with containerization (Docker, Kubernetes) is a plus. English proficiency at Upper-Intermediate level or higher. Proficiency in AI-driven development tools such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar is required Strong prompt engineering skills to optimize the outputs of AI tools effectively Critical understanding of the boundaries of AI tools and the ability to apply sound judgment and critical thinking Utilizing artificial intelligence to debug, refine code quality, and improve documentation for enhanced productivity and workflow reliability Leadership & Communication: Proven experience in a team leadership role, managing cross-functional teams (developers, QA, BAs). Strong ability to organize team work, mentor engineers, and resolve technical or process-related issues. Excellent communication and interpersonal skills, able to interact effectively with both technical teams and business stakeholders. Strong organizational skills with the ability to manage multiple priorities in a fast-paced environment. Adaptability and readiness to quickly adjust to changing project scopes, priorities, and technologies. Preferred Qualifications Experience with functional programming languages (Python, F#). Experience working in cloud environments: AWS, Azure, GCP. Microsoft Certified: Azure Developer Associate or AWS Certified Developer – Associate. Previous experience leading teams in client-facing, dynamic, and Agile environments. Experience in Linux environments and with NoSQL databases (MongoDB, Cassandra) is an advantage. Benefits Working in the TOP 5 development companies in Illinois, USA FLEXIBLE working conditions and a COOPERATIVE environment Competitive salary Many CHALLENGING and exciting projects with new opportunities and learning GROWTH opportunities, skills and competencies improvement, and professional certification In-company TRAINING (English, Software / DevOps / Project management / Design / Business) What we offer Future projects as highly loaded SaaS web-based services for automating business processes in the field of medicine, insurance, finance, energy, including modules for processing large data arrays, billing subsystems, integration with external data sources, mobile applications, and IP telephony systems. Our team We are a friendly team of 170+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients. Start your journey today!", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.velvetech.com/careers/system-administrator-devops-specialist/", "company_id": 3412, "source": 3, "skills": "Expertise\n          \n            \n          \n        \n        Work\n          \n            \n          \n        \n        Blog\n          \n            \n          \n        \n        Company, Industries\n            \n              \n            \n        Services\n            \n              \n            \n        Technologies, Administration of corporate servers: Linux and Windows, configuration, troubleshootingNetwork configuration, troubleshootingAdministration of virtualization systems (Hyper-V, ESXi)Building fault-tolerant systems. Scaling under loadApplication protection, query optimization, load balancingCI/CD configuring, Successful work experience as a system administrator from 2 years and / or DevOpsStrong knowledge of Linux CentOS/DebianStrong knowledge of WindowsStrong knowledge of network equipment (routers, firewalls, switches)Cloud Work Experience (Azure, AWS, etc)Experience in administering Hyper-V and/or ESXi virtualization systemsExperience in administering web servers IIS, Apache, nginx, proxy, HTTP, postmanCI/CD – GitLab or equivalents, Administering and configuring Cisco equipment, SonicWall firewallsIntroduction to Github, GitLab, TeamCity, CI/CD, Dockers, Kubernetes, ELK, Prometheus, ZabbixAutomation of deployment (docker, kubernetes, etc)Experience with automation using Bash/CMD/Powershell/Python.Understanding the basic principles of information security.Experience with TerraformExperience with AnsibleEnglish level: Intermediate and higherFamiliarity with AI-assisted development tools, such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar, is advantageousBasic skills in prompt engineering to leverage AI tools effectively for DevOps workflowsAwareness of the limitations of AI tools and the capability to apply critical thinking when integrating them into DevOps processesAbility to utilize AI technologies to enhance code debugging, optimize infrastructure automation, and improve documentation for increased efficiency and reliability, Challenging and exciting projectsFlexible scheduleYoung and friendly teamBonuses per project results, Velvetech is in the TOP 5 development companies in Illinois, USAYou have FLEXIBLE working conditions and a COOPERATIVE environmentCompetitive salaryMany CHALLENGING and exciting projects with new opportunities and learningGROWTH opportunities, skills and competencies improvement, and professional certificationIn-company TRAINING (English, Software / DevOps / Project management / Design / Business), Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact, Industries \n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, HealthcareInsuranceTransportation and LogisticsManufacturingRetail and eCommerceEducationReal EstateEnergyOil&GasFinTech, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, Custom Software DevelopmentWeb DevelopmentIoT Development ServicesMobile DevelopmentData AnalyticsDesign & Creative UX/UIIT ConsultingQA & Software TestingBusiness Process AutomationMLAIBlockchainStaff Augmentation, Industries\n              \n                \n              \n            \n            Services\n              \n                \n              \n            \n            Technologies, .NETFPGABlockchainAzureAWSGCPJavaPHPPythonC++GolangReact NativeNode.jsCreatio, AboutCareersEngagement ModelsContact", "title": "System Administrator / DevOps Specialist", "location": "", "location_type": "flexible", "job_type": "full_time", "min_experience": 2, "max_experience": 2, "apply_link": "https://www.velvetech.com/careers/system-administrator-devops-specialist/", "description": "System Administrator / DevOps Specialist Velvetech is an American global software development company headquartered in Miami, FL, and with an office in Chicago, IL. Velvetech has its developers strategically distributed in 10+ global technical job markets to deliver the best in class software engineering and exceptional client services. We have 20+ years of experience in software and hardware development for clients all around the world, especially for US clients. As an official Microsoft Gold Partner, our company is listed in the top 5 software development companies in Illinois, USA. US clients are leading trading and financial organizations, insurance companies, large healthcare associations, pharmaceutical and energy companies, equipment manufacturers, hi-tech startups, etc. Areas of expertise are consulting, development, implementation, and support of software solutions. We implement modern backend solutions that support microservices architecture with the following technologies: NET Core, Node. js, Java, SQL Server, PostgreSQL, MongoDB, Redis, RabbitMQ. For the web UI front-end development, we opt for React, or Angular, and for mobile development — Swift, Kotlin, Java. Most of the systems are designed for cloud infrastructures like AWS, Azure, GCP, and heavily rely on Docker, Kubernetes while modern CI/CD is made using GitLab CI or TeamCity. In September 2021, Velvetech LLC received the status of the official Training Provider of the International Institute of Business Analysis. This status gives us the right to conduct regular quality trainings for Business Analysts and anyone performing BA activities regardless their job title. Principles are based on Agile Methodology, Scrum and Kanban. Various projects coded in GitLab or GitHub are built with multiple coordinated teams, collaborating via Jira, Confluence, MS Teams, and Slack. Velvetech constantly follows the Tech trends and actively cooperates with startups in such breakthrough areas as Machine Learning, the Internet of Things, Blockchain, FPGA, and AI. ResponsibilitiesNow we are looking for a System Administrator/ DevOps Specialist to our DevOps team. Administration of corporate servers: Linux and Windows, configuration, troubleshootingNetwork configuration, troubleshootingAdministration of virtualization systems (Hyper-V, ESXi)Building fault-tolerant systems. Scaling under loadApplication protection, query optimization, load balancingCI/CD configuringRequirementsExperience in the following technologies:Successful work experience as a system administrator from 2 years and / or DevOpsStrong knowledge of Linux CentOS/DebianStrong knowledge of WindowsStrong knowledge of network equipment (routers, firewalls, switches)Cloud Work Experience (Azure, AWS, etc)Experience in administering Hyper-V and/or ESXi virtualization systemsExperience in administering web servers IIS, Apache, nginx, proxy, HTTP, postmanCI/CD – GitLab or equivalentsThe following experience will be considered as an advantage:Administering and configuring Cisco equipment, SonicWall firewallsIntroduction to Github, GitLab, TeamCity, CI/CD, Dockers, Kubernetes, ELK, Prometheus, ZabbixAutomation of deployment (docker, kubernetes, etc)Experience with automation using Bash/CMD/Powershell/Python. Understanding the basic principles of information security. Experience with TerraformExperience with AnsibleEnglish level: Intermediate and higherFamiliarity with AI-assisted development tools, such as GitHub Copilot, ChatGPT, Cursor IDE, Tabnine, or similar, is advantageousBasic skills in prompt engineering to leverage AI tools effectively for DevOps workflowsAwareness of the limitations of AI tools and the capability to apply critical thinking when integrating them into DevOps processesAbility to utilize AI technologies to enhance code debugging, optimize infrastructure automation, and improve documentation for increased efficiency and reliabilityOfferChallenging and exciting projectsFlexible scheduleYoung and friendly teamBonuses per project resultsBenefitsVelvetech is in the TOP 5 development companies in Illinois, USAYou have FLEXIBLE working conditions and a COOPERATIVE environmentCompetitive salaryMany CHALLENGING and exciting projects with new opportunities and learningGROWTH opportunities, skills and competencies improvement, and professional certificationIn-company TRAINING (English, Software / DevOps / Project management / Design / Business)Our teamWe are a friendly team of 150+ professionals where everyone is able to achieve high results and grows professionally not only due to their own knowledge and skills but also with the support of colleagues. Values that we highly appreciate are high performance, responsibility, respect, and loyalty. In order to have a highly motivated team, Velvetech invests in their people, additional education, certification, and others. You choose your career path and we are here to support you! People work together in harmony, encouraging each other to continuously learn, progress, and pursue perfection. Our employees enjoy working as we turn challenges into advantages to share success with each other. You have a chance to become a part of a friendly and professional team that creates robust software solutions to deliver maximum value to our international clients.", "ctc": null, "currency": null, "meta": {}}]