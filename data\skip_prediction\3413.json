[{"jd_link": "https://grinteq.com/open-positions/back-end-developer", "company_id": 3413, "source": 3, "skills": "Qualifications and skillsMinimum 6+ years of experience in development in total.Highest PHP version (8.X+).Highest Symfony version (5.X+).Experience with REST-API.Fluent in English, both written and spoken.Experience in website design and implementation with PHP8 or higher version, the Symfony Framework 5.4 (or similar PHP MVC Framework), Twig, Doctrine, MySQL or MariaDB), and JavaScript.Confident use of the Linux shell.Desirable skills :Experience in server administration (Apache, MariaDB) / DevOps.Experience in server virtualization (VMware ESXi / Hyper-V).Experience in the development of mobile applications.‍", "title": "PHP Symfony Developer", "location": "", "location_type": "remote", "job_type": null, "min_experience": 6, "max_experience": null, "apply_link": "https://grinteq.com/open-positions/back-end-developer", "description": "PositionWe're looking for a Senior PHP Symfony Developer to join us on our roadmap to architect, engineer, deploy, scale and maintain a whole new generation of marketplace technologies in addition to supporting our current ecosystem of custom technologies. You'll play a significant role in augmenting our legacy of customer trust and technical pioneering. Responsibilities and dutiesQualifications and skillsMinimum 6+ years of experience in development in total. Highest PHP version (8. X+). Highest Symfony version (5. X+). Experience with REST-API. Fluent in English, both written and spoken. Experience in website design and implementation with PHP8 or higher version, the Symfony Framework 5. 4 (or similar PHP MVC Framework), Twig, Doctrine, MySQL or MariaDB), and JavaScript. Confident use of the Linux shell. Desirable skills :Experience in server administration (Apache, MariaDB) / DevOps. Experience in server virtualization (VMware ESXi / Hyper-V). Experience in the development of mobile applications. ‍What we offerA decent salary level, which allows you to think about our mutual success and not about tomorrow. Flexible working hours. You create your own schedule. Possibility to work remotely. You prefer home office or traveling around? Easy, that's exactly how we operate. ‍", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/back-end-shopify-developer", "company_id": 3413, "source": 3, "skills": "Qualifications and skills3+ years of experience with Shopify/Shopify+ integrationsShopify App Development (Private Apps, Custom Apps, Shopify Scripts)Understanding of modern web development using  HTML, CSS, JavaScriptExperience with Ruby and Node.jsExperience with Python, NoSQL, MySQL, and Relational Databases (DynamoDB, Firestore)Experience with Serverless frameworksExperience with APIs, REST, and/or GraphQLExperience with AWS and/or Google CloudUnderstanding of Git Version Control (BitBucket or GitHub) Project Management Tools (JIRA)Excellent written and oral communication skills with team members and clients", "title": "Shopify Back-end Developer", "location": "", "location_type": null, "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://grinteq.com/open-positions/back-end-shopify-developer", "description": "PositionWe are currently seeking a strong Shopify+ Developer who enjoys interacting with both customers and peers. The developer will be dealing with large, high-profile clients and will be required to configure and customize enterprise solutions to meet the client’s complex needs. The ideal candidate will have strong communication skills and a willingness to provide support, along with strong technical knowledge. Responsibilities and dutiesThe Back-End Developer is primarily responsible for producing quality, on-budget, and on-schedule solutions on projects. Maintain and develop new features on existing appsDevelop new appsMigrate apps between platformsContribute to internal initiatives to improve development processes and app integrationsDevelop reusable tools for app integration and implementationQualifications and skills3+ years of experience with Shopify/Shopify+ integrationsShopify App Development (Private Apps, Custom Apps, Shopify Scripts)Understanding of modern web development using HTML, CSS, JavaScriptExperience with Ruby and Node. jsExperience with Python, NoSQL, MySQL, and Relational Databases (DynamoDB, Firestore)Experience with Serverless frameworksExperience with APIs, REST, and/or GraphQLExperience with AWS and/or Google CloudUnderstanding of Git Version Control (BitBucket or GitHub) Project Management Tools (JIRA)Excellent written and oral communication skills with team members and clientsWhat we offer", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/devops-engineer", "company_id": 3413, "source": 3, "skills": "Qualifications and skillsAt least 3- 5 years of hands-on experience in developing data ingestion, data processing and analytical pipelines for big data, relational databases, NoSQL, and data warehouse solutions on AWS Cloud Platform.AWS data services experience in particular AWS Glue, Lambda, RDS PostgreSQL, S3, Redshift, Athena, and other data infrastructure automation services.Experience with scripting in a serverless architecture using Python.Experience with Data modeling, querying, and optimization for relational, NoSQL, data warehouses and data lakes.Experience working with data lake and federated data architectures.Nice to Have: Knowledge of AWS step functions or similar orchestration tool. Experience with Visual Studio Code, Docker and Git.‍", "title": "AWS Data Engineer", "location": "", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": 5, "apply_link": "https://grinteq.com/open-positions/devops-engineer", "description": "PositionWe are looking for an experienced AWS Data Engineer to join the team that develop a data-centric platform that empowers multiple data sources about consumers behaviors across the global for actionable insights discovery. Responsibilities and dutiesDesign, implement, and support a platform providing secured access to large datasets. Develop and maintain end to end scalable data infrastructure and data pipelines. Design and implement routines to extract, transform, and load data from a wide variety of data sources using Python, SQL, scripting, and AWS big data technologies. Design and implement complex ETL pipelines and other BI solutions. Work closely with product owners, developers, and data strategists to explore new data sources and deliver the data. ‍Qualifications and skillsAt least 3- 5 years of hands-on experience in developing data ingestion, data processing and analytical pipelines for big data, relational databases, NoSQL, and data warehouse solutions on AWS Cloud Platform. AWS data services experience in particular AWS Glue, Lambda, RDS PostgreSQL, S3, Redshift, Athena, and other data infrastructure automation services. Experience with scripting in a serverless architecture using Python. Experience with Data modeling, querying, and optimization for relational, NoSQL, data warehouses and data lakes. Experience working with data lake and federated data architectures. Nice to Have: Knowledge of AWS step functions or similar orchestration tool. Experience with Visual Studio Code, Docker and Git. ‍What we offerA decent salary level, which allows you to think about our mutual success and not about tomorrow. Flexible working hours. You create your own schedule. Possibility to work remotely. You prefer home office or traveling around? Easy, that's exactly how we operate.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/bigcommerce-developer", "company_id": 3413, "source": 3, "skills": "Qualifications and skillsPossesses a wide breadth of knowledge regarding standards-compliant HTML, CSS & Javascript, including responsive design techniques, performance implications of CSS / CSS Animations & JavaScript.Has done stencil template development on 3 or more prior projects and can submit a small portfolio of recent work and/or a code sample as proof.Knowledgeable of modern CSS concepts such as Grid CSS and Flexbox. Experience with Vue.js or React.js is a plus.Has an understanding of web standards and accessibilityExperienced in integrating external web services, such as SOAP, REST, or Graph QL APIs, is recommended.Comfortable using software development management tools like the Atlassian suite (JIRA, Confluence, etc.) or a similar set of applications.Familiarity with using Git to create branches and push new code changes and can create pull requests for the technical lead to code review.Demonstrates a proven track record of following through with deadlines and keeping on top of new tasks.", "title": "BigCommerce Developer", "location": "", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://grinteq.com/open-positions/bigcommerce-developer", "description": "PositionOur Front-End Engineers help build and maintain interactive ecommerce stores for our clients and their customers. They work with our Project Managers and other engineers to deliver quality websites on the BigCommerce platform. They bring their platform expertise to solve challenging client challenges on a daily basis. We’re looking for individuals who thrive in dynamic environments, embrace processes and organization, are detail-oriented, and enjoy using data to inform their decision-making. Responsibilities and dutiesProperly submits pull requests with code fulfilling a ticket’s acceptance criteria. Communicate any blockers, progress, and statuses regarding a ticket. Uses best practices regarding accessibility, site performance, & SEO. Uses HTML, CSS, JS to create BigCommerce web elements matching client-approved designs and functionality. Creates test cases for completed work to pass on to QA. Qualifications and skillsPossesses a wide breadth of knowledge regarding standards-compliant HTML, CSS & Javascript, including responsive design techniques, performance implications of CSS / CSS Animations & JavaScript. Has done stencil template development on 3 or more prior projects and can submit a small portfolio of recent work and/or a code sample as proof. Knowledgeable of modern CSS concepts such as Grid CSS and Flexbox. Experience with Vue. js or React. js is a plus. Has an understanding of web standards and accessibilityExperienced in integrating external web services, such as SOAP, REST, or Graph QL APIs, is recommended. Comfortable using software development management tools like the Atlassian suite (JIRA, Confluence, etc. ) or a similar set of applications. Familiarity with using Git to create branches and push new code changes and can create pull requests for the technical lead to code review. Demonstrates a proven track record of following through with deadlines and keeping on top of new tasks. What we offerA decent salary level which allows you to think about our mutual success and not about tomorrow. Flexible working hours. You create your own schedule. Important part though: most of our clients are from US, so be ready for occasional evening calls. Possibility to work remotely. You prefer home office or traveling around? Easy, that's exactly how we operate.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/delivery", "company_id": 3413, "source": 3, "skills": "Qualifications and skillsPhotoshop — ability to create raster graphics, experience with using masks, blending modes, smart objects, adjustment layers.Figma — experience with auto layouts, components, and options.Knowledge of Adobe Illustrator (or Corel/Affinity).Experience with 3D programs like blender, 3d max, or Cinema4d.Understanding of typography, ability to create readable and juicy layouts.Feeling of composition and color theory.Basic understanding of HTML and CSS.‍", "title": "Graphic Designer", "location": "", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://grinteq.com/open-positions/delivery", "description": "PositionWe are looking for a Graphic Designer to join our skillful Grinteq Design Unit. You will report directly to Head of Delivery while working alongside three seasoned UX/UI, Web Experience and Graphic Designers. Responsibilities and dutiesWorking with both internal tasks of our company and with clients’ tasks. 3D modeling: stretching textures and rendering objects. Working with Figma (our main work tool), Photoshop. Vector graphics creation. Drawing website headers, blog covers, drawing icons and illustrations. Our main goal is to provide clients with neat design solutions. That means we have to go deep into the origin of the client’s request, figure out the real problem, and solve it together. Working with us, you also need to have:Ability to adapt to different tasks and projects. Competence to estimate tasks, present your solutions and work with comments. Talent to find a compromise. ‍Qualifications and skillsPhotoshop — ability to create raster graphics, experience with using masks, blending modes, smart objects, adjustment layers. Figma — experience with auto layouts, components, and options. Knowledge of Adobe Illustrator (or Corel/Affinity). Experience with 3D programs like blender, 3d max, or Cinema4d. Understanding of typography, ability to create readable and juicy layouts. Feeling of composition and color theory. Basic understanding of HTML and CSS. ‍What we offerA decent salary level, which allows you to think about our mutual success and not about tomorrow. Flexible working hours. You create your own schedule. Possibility to work remotely. You prefer home office or traveling around? Easy, that's exactly how we operate.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/java-engineer", "company_id": 3413, "source": 3, "skills": "Qualifications and skills4+ years of experience in Java development (Java 8+).3+ years of hands-on experience with Liferay DXP (7.x mandatory).Strong knowledge of Liferay APIs, Service Builder, and content management (Journal Articles).Experience with custom portlet development, dynamic navigation structures, and theme customization.Ability to manage and resolve complex nested macro or referencing logic.Experience with Git, Maven/Gradle, and working in agile environments.Experience with Docker, CI/CD pipelines, or headless CMS architectures (nice to have).", "title": "Java Liferay Developer", "location": "", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://grinteq.com/open-positions/java-engineer", "description": "PositionWe are looking for an Experienced Java Back-End Developer to join our team. Responsibilities and dutiesQuickly gain an up-to-date and comprehensive understanding of the code base and technology stack within weeks. Support and extend a custom Liferay Customer Portal used for documentation and internal tools. Maintain and enhance custom Liferay portlets, themes, and workflows. Manage and evolve internal link mapping between legacy and new content systems. Develop and maintain Java backend components, REST APIs, and data handling logic. Implement UI improvements using JavaScript, CSS, and Liferay frontend tools. Ensure the content structure remains navigable, performant, and maintainable. Qualifications and skills4+ years of experience in Java development (Java 8+). 3+ years of hands-on experience with Liferay DXP (7. x mandatory). Strong knowledge of Liferay APIs, Service Builder, and content management (Journal Articles). Experience with custom portlet development, dynamic navigation structures, and theme customization. Ability to manage and resolve complex nested macro or referencing logic. Experience with Git, Maven/Gradle, and working in agile environments. Experience with Docker, CI/CD pipelines, or headless CMS architectures (nice to have). What we offerA decent salary level, which allows you to think about our mutual success and not about tomorrow. Flexible working hours. You create your own schedule. Possibility to work remotely. You prefer home office or traveling around? Easy, that's exactly how we operate.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/front-end-developer", "company_id": 3413, "source": 3, "skills": "Qualifications and skillsA minimum of 7 years of hands-on experience in frontend development, with a strong focus on React.Proficiency in building complex, modern web applications using the latest version of React.Ideally, some experience with Next.js and a willingness to quickly adapt to new technologies.Excellent communication skills, able to effectively convey technical concepts to both technical and non-technical stakeholders.Knowledge of GraphQL and its implementation in frontend applications.Familiarity with commerce tools and their integration in web applications.A passion for frontend development and a track record of successful project delivery.Strong problem-solving skills and a proactive approach to finding innovative solutions.", "title": "React Developer", "location": "", "location_type": "remote", "job_type": null, "min_experience": 7, "max_experience": 7, "apply_link": "https://grinteq.com/open-positions/front-end-developer", "description": "PositionWe are looking for a Senior Front-End Developer who will be responsible for developing Next Generation of ecommerce frontend. You will work in the scrum team that develops new functions for the eCommerce platform. You will participate in the implementation of technical and process innovations in frontend development, help in solving any emerging challenges and technical problems. You will support the skill community, share knowledge and experience with other frontend experts. Responsibilities and dutiesLead and mentor a team of frontend developers, fostering collaboration and ensuring high-quality code delivery. Collaborate with Project Managers and Business Analysts to understand requirements and translate them into functional and visually appealing web applications. Implement responsive user interfaces and ensure cross-browser compatibility for a flawless user experience. Utilize your expertise in React to develop and maintain robust, scalable, and performant frontend applications. Effectively communicate progress, challenges, and technical solutions to the team and stakeholders. Conduct code reviews and provide constructive feedback to ensure code quality and adherence to standards. Take ownership of projects and drive them to successful completion, meeting deadlines and exceeding expectations. Qualifications and skillsA minimum of 7 years of hands-on experience in frontend development, with a strong focus on React. Proficiency in building complex, modern web applications using the latest version of React. Ideally, some experience with Next. js and a willingness to quickly adapt to new technologies. Excellent communication skills, able to effectively convey technical concepts to both technical and non-technical stakeholders. Knowledge of GraphQL and its implementation in frontend applications. Familiarity with commerce tools and their integration in web applications. A passion for frontend development and a track record of successful project delivery. Strong problem-solving skills and a proactive approach to finding innovative solutions. What we offerA decent salary level which allows you to think about our mutual success and not about tomorrow. Flexible working hours. You create your own schedule. Possibility to work remotely. You prefer home office or traveling around? Easy, that's exactly how we operate. ‍", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/marketing", "company_id": 3413, "source": 3, "skills": "Qualifications and skillsExcellent command of English (minimum Upper-Intermediate) and storytelling;Minimum 1 year of experience of copywriting in B2B tech + a matching portfolio, ideally covering different content types (any experience in writing within ecommerce segment is a fat benefit);Self-organized and motivated, autonomous, attentive to details.", "title": "Technical Copywriter", "location": "", "location_type": "remote", "job_type": "contract", "min_experience": 1, "max_experience": 1, "apply_link": "https://grinteq.com/open-positions/marketing", "description": "PositionA boutique digital agency with deep tech expertise, growing at stellar velocity, is looking for a copywriter on freelance basis to boost our media presence via content. Grinteq helps businesses implement ecommerce solutions with a pool of senior SFCC, Magento, Shopify software engineers either by boosting their existing teams or providing end-to-end development support. We are fully remote with flexible working hours. We believe that all efforts should be decently rewarded. We have excellent sense of humour and comfortable working environment (Oh, you’ve never had so much fun as when working with us :)Responsibilities and dutiesWriting clear and grammatically correct copy with a distinctive brand tone of voice including blog articles, company news updates and press releases, texts for landing pages, video scripts, and other marketing materials;Conducting quality research and interviews (when required) to produce original industry-leading copy;Optimizing content according to best SEO practices by effective keyword placement, while keeping the copy attractive and informative. Qualifications and skillsExcellent command of English (minimum Upper-Intermediate) and storytelling;Minimum 1 year of experience of copywriting in B2B tech + a matching portfolio, ideally covering different content types (any experience in writing within ecommerce segment is a fat benefit);Self-organized and motivated, autonomous, attentive to details. What we offerA decent salary level which allows you to think about our mutual success and not about tomorrow. Flexible working hours. You create your own schedule. Possibility to work remotely. You prefer home office or traveling around? Easy, that's exactly how we operate. ‍", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/react-native-developer", "company_id": 3413, "source": 3, "skills": "Qualifications and skillsReact Native development in general.Software library development (do not directly develop a React Native application, but an SDK).React Native App / SDK architecture.React Native updates.Testing (e.g. UI tests).Debugging / Profiling of React Native projects (memory management, threading, etc.).", "title": "React Native Developer", "location": "", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://grinteq.com/open-positions/react-native-developer", "description": "PositionWe're looking for a React Native Engineer to join our growing team. If you're motivated by collaboration and shipping apps that raise the bar, this role is for you. You'll join an empowered product development team that collectively owns its process, deliverables, and results. Responsibilities and dutiesYou will refactor / improve existing codebase. You will develop further features together with other developers on the project. You will coach the team. Qualifications and skillsReact Native development in general. Software library development (do not directly develop a React Native application, but an SDK). React Native App / SDK architecture. React Native updates. Testing (e. g. UI tests). Debugging / Profiling of React Native projects (memory management, threading, etc. ). What we offerA decent salary level which allows you to think about our mutual success and not about tomorrow. Flexible working hours. You create your own schedule. Possibility to work remotely. You prefer home office or traveling around? Easy, that's exactly how we operate.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/python-developer", "company_id": 3413, "source": 3, "skills": "Qualifications and skillsAt least 3 years of experience with full-stack development in Python.Python proficiency and working knowledge of a Python framework is a must, familiarity with Django is a plus.Strong understanding of API architecture, frontend JS frameworks, and frontend template development.Detail-oriented and can translate product and design requirements into a working application.Familiarity with AWS, Linux, Celery, Redis, Elasticsearch is a plus.", "title": "Python Developer", "location": "", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": 3, "apply_link": "https://grinteq.com/open-positions/python-developer", "description": "PositionWe are looking for Python/Django Full-Stack Engineer with a focus on the backend. The person will be a meaningful part of a small team (5 Engineers). You are going to work directly with the product and design teams to lead the development of user-facing products. As a full-stack engineer, you will get to work on transforming large data sets in the backend to easy-to-use products and analytics on the frontend. Responsibilities and dutiesTake ownership of backend to frontend stack and develop products you are proud to highlight. Work with existing large scale data sets to build backend services. Integrate services and data into front end frameworks and templates to deliver working products. Collaborate with data and other backend engineers on large scale data projects. Collaborate with product and design to understand and implement new features. Qualifications and skillsAt least 3 years of experience with full-stack development in Python. Python proficiency and working knowledge of a Python framework is a must, familiarity with Django is a plus. Strong understanding of API architecture, frontend JS frameworks, and frontend template development. Detail-oriented and can translate product and design requirements into a working application. Familiarity with AWS, Linux, Celery, Redis, Elasticsearch is a plus. What we offerA decent salary level which allows you to think about our mutual success and not about tomorrow. Flexible working hours. You create your own schedule. Important part though: many of our clients are from US, so be ready for occasional evening calls. Possibility to work remotely. You prefer home office or traveling around? Easy, that's exactly how we operate.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/recruiting", "company_id": 3413, "source": 3, "skills": "Qualifications and skillsSolid experience in IT recruiting, knowledge of ecommerce domain — our focus technologies are Salesforce Commerce Cloud, Salesforce, Magento, Shopify, Ruby on Rails and related.Strong English skills in both verbal and written form.Developed communication skills and strong professional etiquette.Analytical mindset, attention to details.High result orientation.", "title": "Rec<PERSON>er", "location": "", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://grinteq.com/open-positions/recruiting", "description": "PositionWe are currently looking for a Recruiter to join our growing department. Grineq provides dedicated teams of senior developers who have strong expertise with a particular focus on ecommerce (Salesforce CommerceCloud, Magento, Shopify, Ruby on Rails). Responsibilities and dutiesWork with current vacancies, searching for candidates for various IT tech and non-tech positions. Hire worldwide, with main focus on LATAM and European countries. Understanding search requirements. Arranging and conducting online meetings and interviews. Working with hiring managers. Maintaining and expanding your professional contact base. Writing & updating CVs of employees. Qualifications and skillsSolid experience in IT recruiting, knowledge of ecommerce domain — our focus technologies are Salesforce Commerce Cloud, Salesforce, Magento, Shopify, Ruby on Rails and related. Strong English skills in both verbal and written form. Developed communication skills and strong professional etiquette. Analytical mindset, attention to details. High result orientation. What we offerA decent salary level which allows you to think about our mutual success and not about tomorrow. Flexible working hours. You create your own schedule. Possibility to work remotely. You prefer home office or traveling around? Easy, that's exactly how we operate. ‍", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/resources", "company_id": 3413, "source": 3, "skills": "Qualifications and skillsHave 5 years of business/systems experienceDemonstrate 3 years of subject matter expertise in at least one focus area (business analysis, systems analysis, product ownership)Possess 5 years of digital project oversight experienceHave 3 years of Agile experience in digital experience solution buildsHave 3 years of experience with practice platforms, CMS/DXP concepts, and modern Martech solutionsStay up to date on practice platform offerings, CMS/DXP concepts, and modern Martech trendsUse task management tools such as JIRA (preferred)Convey advanced business requirements through clear documentationDemonstrate excellent verbal and written communication skillsInteract professionally with executives, managers, technical experts, and subject matter expertsHold a high school diploma or equivalent (required)‍", "title": "Technical Business Analyst", "location": "", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": 5, "apply_link": "https://grinteq.com/open-positions/resources", "description": "PositionWe are looking for a solution-oriented Senior Business Analyst to gather and translate business needs into actionable insights. You’ll define goals, support strategy, and bridge tech and business. Strong communication and ownership are key. Responsibilities and dutiesDevelop relationships with clients at the lead or manager levelParticipate in client meetings with clear objectives, content, and desired outcomesManage expectations with clients and internal teamsWork closely with clients to gather technical requirements and understand key project details and objectivesPresent development deliverables to clients and educate them on best practices and methodologiesParticipate in new business pitchesUnderstand client business challenges and long-term objectives in the context of broader business goalsGather and document business requirements in artifacts like business requirements documentsEvaluate business processes and map them to the digital experience platformDistill requirements into themes, epics, stories, and tasksAssist in building strategy, vision, and roadmap for the clientPrioritize work across implementation workstreams in collaboration with the project teamBuild and present project-level reporting and dashboards to client stakeholdersApprove sprint deliverables against stated requirementsNegotiate with client stakeholders on complex decisions affecting project and business outcomesFollow and improve upon repeatable methodologies for digital experience deliveryManage, prioritize, and maintain backlogParticipate in workshops and brainstorming sessionsContribute to the creation and maintenance of sprint plans, themes, and epics with PM, PO, and other teamsGuide the work of others and provide on-the-job training‍Qualifications and skillsHave 5 years of business/systems experienceDemonstrate 3 years of subject matter expertise in at least one focus area (business analysis, systems analysis, product ownership)Possess 5 years of digital project oversight experienceHave 3 years of Agile experience in digital experience solution buildsHave 3 years of experience with practice platforms, CMS/DXP concepts, and modern Martech solutionsStay up to date on practice platform offerings, CMS/DXP concepts, and modern Martech trendsUse task management tools such as JIRA (preferred)Convey advanced business requirements through clear documentationDemonstrate excellent verbal and written communication skillsInteract professionally with executives, managers, technical experts, and subject matter expertsHold a high school diploma or equivalent (required)‍What we offerA decent salary level, which allows you to think about our mutual success and not about tomorrow. Flexible working hours. You create your own schedule. Possibility to work remotely. You prefer home office or traveling around? Easy, that's exactly how we operate. ‍", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/ruby-on-rails-developer", "company_id": 3413, "source": 3, "skills": "Qualifications and skillsThis job is tailored for you if:Minimum 4 years of experience in Ruby on Rails.Engineering background, Dev and/or Archi.Excellent management and communication skills.Leadership and excellent relationship (with other teams).World class organizational skills adapted to a startup.Technical stack:Ruby On RailsMySQLLinuxGit", "title": "Ruby on Rails Developer", "location": "", "location_type": "remote", "job_type": null, "min_experience": 4, "max_experience": 4, "apply_link": "https://grinteq.com/open-positions/ruby-on-rails-developer", "description": "PositionWe're looking for a Ruby on Rails Developer to join our growing team. If you're motivated by collaboration and shipping experiences that raise the bar, this role is for you. You'll join an empowered development team that collectively owns its process, deliverables, and results. Responsibilities and dutiesWithin the development team and in strong coordination with the product , your job will consist of:Developing new features from end to end. Handling the technical integration. Fixing production bugs. ‍Qualifications and skillsThis job is tailored for you if:Minimum 4 years of experience in Ruby on Rails. Engineering background, Dev and/or Archi. Excellent management and communication skills. Leadership and excellent relationship (with other teams). World class organizational skills adapted to a startup. Technical stack:Ruby On RailsMySQLLinuxGitWhat we offerA decent salary level which allows you to think about our mutual success and not about tomorrow. Flexible working hours. You create your own schedule. Possibility to work remotely. You prefer home office or traveling around? Easy, that's exactly how we operate.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/sales", "company_id": 3413, "source": 3, "skills": "Qualifications and skillsMultitasking and fast learning.The desire to learn and develop your skills.Ability to work with MS Office documents, Google Docs.English - level B2 or higher (written and spoken).Initiative and suggestion of ideas/hypotheses.Understanding the IT sphere, E-commerce domain, technologies/programming languages.It will be a plus, but not necessarily:Work experience in IT/ or lead generation for at least 3 months.Experience working with Linked In, Sales Navigator, Apollo, Hubspot, Lemlist (uploading contacts).", "title": "Lead Generation Specialist / Sales Assistant", "location": "", "location_type": "remote", "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://grinteq.com/open-positions/sales", "description": "PositionWe are looking for a Sales Assistant on a full-time basis to boost Grinteq sales activities and onboard new clients. Grinteq helps businesses implement ecommerce solutions with a pool of senior SFCC, Magento, Shopify software engineers either by boosting their existing teams or providing full-cycle development support. Responsibilities and dutiesCollecting a database of potential customers, finding information in LinkedIn and on other resources (1500-2000 contacts / month). Finding email, linked in profiles and organising information in a structured way in Excel tables. Analysis and processing of large amounts of information. The ability to effectively use lead generation tools such as Sales Navigator, Email finders, Google search. If there is no such experience - we will show how! Qualifications and skillsMultitasking and fast learning. The desire to learn and develop your skills. Ability to work with MS Office documents, Google Docs. English - level B2 or higher (written and spoken). Initiative and suggestion of ideas/hypotheses. Understanding the IT sphere, E-commerce domain, technologies/programming languages. It will be a plus, but not necessarily:Work experience in IT/ or lead generation for at least 3 months. Experience working with Linked In, Sales Navigator, Apollo, Hubspot, Lemlist (uploading contacts). What we offerFully remote work with flexible working hours. Cool Sales bonus structure. Excellent sense of humour and comfortable working environment. Great prospects for your professional growth.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/sfcc-front-end-developer", "company_id": 3413, "source": 3, "skills": "Qualifications and skillsThe ideal candidate will have prior experience in a similar role at a software house and/or an eCommerce company and has the following skills:Mastery of HTML5, CSS3 and JavaScript (ES6) adhering to industry defined coding standards.Commercial experience of unit testing / integration / end-to-end testing.Experience of using CSS pre-processors, CSS-in-JS (we use SASS and styled components).Solid understanding of the Agile methodology i.e. story point estimation, refinement, sprint planning, retrospective, sprint demos.Commercial experience of using version control tooling (GIT) in a large development team performing.Good understanding / awareness of Accessibility, SEO Principles, Security, Performance best practices.Debugging and troubleshooting skills.A desire to write readable, maintainable, modular and extendable code.Experience in bringing multiple viewport responsive and adaptive web designs to life.The candidate has to have good communication skills and capability to interact directly with the wider team (shows and tells / requirement analysis sessions / retrospectives etc).If you know any of this even better!SFCC Certified Developer or SFCC Certified Architect.Software development experience with direct Salesforce Commerce Cloud experience (Commerce Cloud certification preferred) and eCommerce feeds and integrations.Additional Commercial ecommerce platform experience – Magento / Hybris / Shopify.Knowledge of jQuery. Experience with any popular jQuery plug-in (jQuery UI, jQuery tools, jQuery Mobile, etc.) is a plus.Knowledge of popular CSS grid systems (Bootstrap, 960gs, Skeleton, etc.).", "title": "SFCC Front-End Developer", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://grinteq.com/open-positions/sfcc-front-end-developer", "description": "PositionWe're looking for a Middle+ / Senior SFCC Front-End Developer to join us on our roadmap to architect, engineer, deploy, scale and maintain a whole new generation of marketplace technologies in addition to supporting our current ecosystem of custom technologies. You'll play a significant role in augmenting our legacy of customer trust and technical pioneering. Responsibilities and dutiesProducing quality, on-budget and on-schedule solutions on projects. Creating prototypes and production-ready XHTML / CSS / JS for eCommerce websites. Building responsive layouts, reusable code and libraries for future use. Focusing on user needs and optimum user experience. Working within an agile team, contributing to daily stand-ups, sprint planning and estimations. Forward thinking, focused on performance initiatives and driven to implement front-end solutions. Contributing to the team's focus to maintain Front-End Quality. Staying current with evolving standards and technologies, learning new skills as needed. Qualifications and skillsThe ideal candidate will have prior experience in a similar role at a software house and/or an eCommerce company and has the following skills:Mastery of HTML5, CSS3 and JavaScript (ES6) adhering to industry defined coding standards. Commercial experience of unit testing / integration / end-to-end testing. Experience of using CSS pre-processors, CSS-in-JS (we use SASS and styled components). Solid understanding of the Agile methodology i. e. story point estimation, refinement, sprint planning, retrospective, sprint demos. Commercial experience of using version control tooling (GIT) in a large development team performing. Good understanding / awareness of Accessibility, SEO Principles, Security, Performance best practices. Debugging and troubleshooting skills. A desire to write readable, maintainable, modular and extendable code. Experience in bringing multiple viewport responsive and adaptive web designs to life. The candidate has to have good communication skills and capability to interact directly with the wider team (shows and tells / requirement analysis sessions / retrospectives etc). If you know any of this even better! SFCC Certified Developer or SFCC Certified Architect. Software development experience with direct Salesforce Commerce Cloud experience (Commerce Cloud certification preferred) and eCommerce feeds and integrations. Additional Commercial ecommerce platform experience – Magento / Hybris / Shopify. Knowledge of jQuery. Experience with any popular jQuery plug-in (jQuery UI, jQuery tools, jQuery Mobile, etc. ) is a plus. Knowledge of popular CSS grid systems (Bootstrap, 960gs, Skeleton, etc. ). What we offer", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/sfcc-back-end-developer", "company_id": 3413, "source": 3, "skills": "Qualifications and skills10+ years of experience with software development.5+ years of experience with Salesforce Commerce Cloud (ex Demandware), ideally holding a leadership role on at least 2 full-scale projects.Strong knowledge and experience with integrations to back-end systems, in particular other systems in the Salesforce landscape.Ability to come up with accurate development estimates based on high-level business and/or technical requirements.A keen interest in emerging technologies and how they might impact future design decisions and strategic directions.Excellent knowledge of design patterns, OOP, coding standards, algorithm performance & optimization.Good understanding of data structures, JavaScript, RESTful JSON, browser-based DOM manipulation.Extensive experience with debugging, reuse, source code, management strategies (e.g. forking, branching), and release management.Knowledge of interactions with enterprise 3PL solutions (ERP, CRM, OMS, PIM) using web services & job.Experience with production launch readiness and cloud-based deployment models.Excellent knowledge of performance optimization techniques.SFCC Architect Certification is a strong plus.Excellent time management and strong multi-tasking skills.E-commerce or similar web experience is strongly preferred.Prior experience in helping clients through the decision-making process.Must be fluent in English, written and verbal communications.", "title": "SFCC Technical Lead", "location": "", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://grinteq.com/open-positions/sfcc-back-end-developer", "description": "PositionThe SFCC Technical Lead is primarily responsible for producing quality, on-budget, and on-schedule solutions on projects. Responsibilities and dutiesWork with client’s IT organization to establish technology strategy at an application level. Facilitate group discussions and lead client requirement activities; able to translate user requirements into functional specifications for development teams. Establish high, mid and micro level plans and set technical direction for a small team; lead the estimation effort for projects; work to identify and manage risk and control scope. Strong knowledge and expertise regarding SFCC Platform gained in direct interaction with our projects. Leads teams of 2- 5 members to deliver to the highest quality, exceeding customer expectations. Work closely with a local team to create high quality e-commerce sites built on the SFCC platform. Analyze client business needs and recommend innovative solutions that leverage technology to provide market differentiation, efficiency improvements, and better user experiences. Collaborate with an international team of Project Managers and Architects to understand client needs and communicate project progress. Mentor more junior staff members and be a role model for best practices in technical development. Write great code! Collaborate with more experienced colleagues to determine the best approach to solving complex business problems with technology. Qualifications and skills10+ years of experience with software development. 5+ years of experience with Salesforce Commerce Cloud (ex Demandware), ideally holding a leadership role on at least 2 full-scale projects. Strong knowledge and experience with integrations to back-end systems, in particular other systems in the Salesforce landscape. Ability to come up with accurate development estimates based on high-level business and/or technical requirements. A keen interest in emerging technologies and how they might impact future design decisions and strategic directions. Excellent knowledge of design patterns, OOP, coding standards, algorithm performance & optimization. Good understanding of data structures, JavaScript, RESTful JSON, browser-based DOM manipulation. Extensive experience with debugging, reuse, source code, management strategies (e. g. forking, branching), and release management. Knowledge of interactions with enterprise 3PL solutions (ERP, CRM, OMS, PIM) using web services & job. Experience with production launch readiness and cloud-based deployment models. Excellent knowledge of performance optimization techniques. SFCC Architect Certification is a strong plus. Excellent time management and strong multi-tasking skills. E-commerce or similar web experience is strongly preferred. Prior experience in helping clients through the decision-making process. Must be fluent in English, written and verbal communications. What we offerA decent salary level which allows you to think about our mutual success and not about tomorrow. Flexible working hours. You create your own schedule. Important part though: most of our clients are from US, so be ready for occasional evening calls. Possibility to work remotely. You prefer home office or traveling around? Easy, that's exactly how we operate.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/sfcc-full-stack-developer", "company_id": 3413, "source": 3, "skills": "Qualifications and skillsProven experience working in an SFCC / Demandware Platform as a Senior Backend developer.Previous experience on Salesforce Commerce Cloud (Demandware) SiteGenesis/SFRA framework, Services framework and other platform capabilities.Good grasp of data structures and algorithms.Experience with front-end application architecture and development.Experience with coding modular object-oriented JavaScript.Knowledge and understanding about JavaScript design patterns (Factory Pattern, Strategy).<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>aca<PERSON>tern, Asynchronous Module Definition etc.).Well versed with jQuery framework and patterns used. Should be able to write custom plugins for jQuery.Knowledge about JavaScript MV* frameworks.Hands-on experience with AngularJS framework and its constructs like custom directives, services etc.Hands-on experience with CSS preprocessors (SASS, LESS, STYLUS).Understanding and hand-on experience with writing modular CSS using SMACSS and Object Oriented CSS methodologies.Understanding of front-end frameworks like Bootstrap, Semantic-UI and Foundation.Experience with RESTful APIs.Experience and understanding with writing JavaScript unit tests (Jasmine + Karma).Experienced with front-end tooling able to write custom automation tasks for Grunt.Experienced with architecture and development of front-end tailored for various Content Management Systems in the likes of Sharepoint, Umbraco, Episerver, Sitefinity and Sitecore.Should maintain and extend back-end development Guidelines and boilerplate that is currently being used as a starting point for projects executed by both internal and offshore development teams.Should be able to incorporate and understand HTML5 semantic elements and understand the SEO benefits of Microdata and Google Rich snippets for in page SEO.‍", "title": "SFCC Full-Stack Developer", "location": "", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://grinteq.com/open-positions/sfcc-full-stack-developer", "description": "PositionWe're looking for a Middle+ / Senior SFCC Full-Stack Developer to join us on our roadmap to architect, engineer, deploy, scale and maintain a whole new generation of marketplace technologies in addition to supporting our current ecosystem of custom technologies. You'll play a significant role in augmenting our legacy of customer trust and technical pioneering. Responsibilities and dutiesWork on flagship websites for globally recognised brands, whilst meeting the demanding challenges on usability, resilience, performance, scalability and security. Build cross-browser, cross-device compatible pages adhering to industry best practices. Develop templates and content slots using ISML, JavaScript/jQuery, HTML, XML, CSS, AJAX and integrate with Pipelines/Controllers on Salesforce Commerce Cloud platform. Responsible for architecting and defining the Front-end framework to solve complex designs and interactions that reflect the user experience and creative proposition. Mentoring and guiding all project activities on multiple small to medium sized projects or one large project. Shift between a creative and a technical focus depending on the project need and/or the type of project. Participate in developing supporting proposal materials for projects. Participate in scoping and planning work. Write technical documentation (admin guides), white papers, presentations, contributes to determining internal processes. ‍Qualifications and skillsProven experience working in an SFCC / Demandware Platform as a Senior Backend developer. Previous experience on Salesforce Commerce Cloud (Demandware) SiteGenesis/SFRA framework, Services framework and other platform capabilities. Good grasp of data structures and algorithms. Experience with front-end application architecture and development. Experience with coding modular object-oriented JavaScript. Knowledge and understanding about JavaScript design patterns (Factory Pattern, Strategy). Pattern, Module Pattern, Reveal Pattern, Facade Pattern, Asynchronous Module Definition etc. ). Well versed with jQuery framework and patterns used. Should be able to write custom plugins for jQuery. Knowledge about JavaScript MV* frameworks. Hands-on experience with AngularJS framework and its constructs like custom directives, services etc. Hands-on experience with CSS preprocessors (SASS, LESS, STYLUS). Understanding and hand-on experience with writing modular CSS using SMACSS and Object Oriented CSS methodologies. Understanding of front-end frameworks like Bootstrap, Semantic-UI and Foundation. Experience with RESTful APIs. Experience and understanding with writing JavaScript unit tests (Jasmine + Karma). Experienced with front-end tooling able to write custom automation tasks for Grunt. Experienced with architecture and development of front-end tailored for various Content Management Systems in the likes of Sharepoint, Umbraco, Episerver, Sitefinity and Sitecore. Should maintain and extend back-end development Guidelines and boilerplate that is currently being used as a starting point for projects executed by both internal and offshore development teams. Should be able to incorporate and understand HTML5 semantic elements and understand the SEO benefits of Microdata and Google Rich snippets for in page SEO. ‍What we offerFlexible working hours. You create your own schedule. Possibility to work remotely: you prefer home office or traveling around? Easy, that's exactly how we operate. A decent salary level which allows you to think about our mutual success and not about tomorrow. ‍", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://grinteq.com/open-positions/shopify-developer", "company_id": 3413, "source": 3, "skills": "Qualifications and skills3 years of experience as a React Developer.6 months of hands-on experience in a project with Remix.1 year experience working in Node.js + TypeScript, Shopify.Experience with GraphQL.6 months of hands-on experience in a project with Hydrogen.Knowledge of good programming practices SOLID, OOP, functional programming.Experience in Unit testing, integration testing and frontend testing.Knowledge of GitHub and GitHub Actions.Fluency in English.", "title": "Shopify Developer", "location": "", "location_type": "remote", "job_type": null, "min_experience": 1, "max_experience": 3, "apply_link": "https://grinteq.com/open-positions/shopify-developer", "description": "PositionWe are looking for a Shopify Developer to join our team. We are looking for someone who is professional, reliable, and pays attention to detail. Most importantly, we seek a team player with the ability to accomplish results as a team leader and as an individual contributor, managing multiple projects in a fast-paced environment. Responsibilities and dutiesWorking closely with the Designers, Backend Developers, and Testers. Designing and implementing eCommerce projects. Creating dedicated functionalities in JS that meet the business needs of customers. Delivering high-quality code. Working closely with the Designers, Backend/Frontend Developers, and Testers. Working on a Cloud environment with Cloud-native services. Qualifications and skills3 years of experience as a React Developer. 6 months of hands-on experience in a project with Remix. 1 year experience working in Node. js + TypeScript, Shopify. Experience with GraphQL. 6 months of hands-on experience in a project with Hydrogen. Knowledge of good programming practices SOLID, OOP, functional programming. Experience in Unit testing, integration testing and frontend testing. Knowledge of GitHub and GitHub Actions. Fluency in English. What we offerA decent salary level which allows you to think about our mutual success and not about tomorrow. Flexible working hours. You create your own schedule. Important part though: most of our clients are from US, so be ready for occasional evening calls. Possibility to work remotely. You prefer home office or traveling around? Easy, that's exactly how we operate.", "ctc": null, "currency": null, "meta": {}}]