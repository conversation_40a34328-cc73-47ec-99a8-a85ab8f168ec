[{"jd_link": "https://aleph1.io/join-us/full-stack-js-engineer/", "company_id": 3416, "source": 3, "skills": "Kiev", "title": "Full Stack JS Engineer", "location": "", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://aleph1.io/join-us/full-stack-js-engineer/", "description": "Привіт! Наша компанія в пошуках Full Stack JS Developer для роботи на новому FinTech проекті. Детальніше про технології: * Node. js / Express* React. js / Redux* AWS Cloud* TypeScript* MongoDB, PostgreSQL* Docker infrastructure* CI (Travis, CircleCI)* GitHub* Jira Про культуру в компанії — у нас мало бюрократії та основний фокус на результат. Вільний робочий графік. Можливо працювати вдома або в нашому офісі коворкінгу Creative States of Arsenal. Пропонуємо * Цікаві та складні проекти* Лояльний, відкритий менеджмент* Можливість впливати на кінцевий результат* Професійний та фінансовий розвиток* Гнучкий робочий графік, що дозволяє планувати свій робочий день самостійно* Привітні колеги, готові завжди допомогти* Відпустка — 20 робочих днів Обов’язки Написання підтримувального розширюваного коду;Робота з високонавантаженими проектами;Якісно та відповільно виконувати свою роботу.", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://aleph1.io/join-us/middle-manual-qa-engineer/", "company_id": 3416, "source": 3, "skills": "Kiev", "title": "Middle Manual QA Engineer", "location": "", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://aleph1.io/join-us/middle-manual-qa-engineer/", "description": "We are looking for a Middle Manual QA Engineer to provide QA expertise as a part of the development process for our client’s projects. Hard skills & Experience: — 2-years experience as a Manual QA Engineer — Experience in creating test plans and estimating — Experience with different DB’s — Understanding of HTTP, REST — Understanding and knowledge of software testing methodologies, approaches and techniques — Experience with Functional Testing, UI/UX Testing, System Testing, and User Acceptance Testing phases — Knowledge of Agile/Scrum methodologies Soft skills: — Great attention to details, analytical thinking, responsibility — Ability to work in a team and independently — Strong will and ability to learn — Intermediate English level Буде плюсом — Knowledge of any programming language (for example JavaScript) Пропонуємо — Interesting and challenging FinTech, E-commerce, Social/Media, Blockchain projects — Flexible working schedule — An opportunity to work from home — Great office location (Coworking Creative State of Arsenal) — Transparent business processes (no bureaucracy) — Great opportunity for professional growth Обов’язки Provide QA expertise as a part of the development process for our client’s projects.", "ctc": null, "currency": null, "meta": {}}]