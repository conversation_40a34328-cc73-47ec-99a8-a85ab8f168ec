x-airflow-common: &airflow-common
  build:
    context: .
    dockerfile: Dockerfile
  image: airflow-custom:latest
  env_file:
    - .env
  user: "50000:0"
  volumes:
    # Mount code and config directories for local development
    - ./dags:/opt/airflow/dags:rw
    - ./scripts:/opt/airflow/scripts:rw
    - ./config:/opt/airflow/config:rw
    - ./etl:/opt/airflow/etl:rw
    # Mount data and logs directories for persistence and access
    - ./data:/opt/airflow/data:rw
    - ./logs:/opt/airflow/logs:rw
  networks:
    - airflow-network
    - db_network
  

services:
  postgres:
    image: postgres:13
    container_name: airflow-postgres
    environment:
      - POSTGRES_USER=airflow
      - POSTGRES_PASSWORD=airflow
      - POSTGRES_DB=airflow
    ports:
      - "5433:5432" # external:internal
    volumes:
      - postgres-db-volume:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U airflow"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: always
    networks:
      - airflow-network # Only needs to be on the internal network

  # Airflow Broker (Internal)
  redis:
    image: redis:latest
    container_name: airflow-redis
    ports:
      - "6380:6379"
    volumes:
      - redis-db-volume:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 30s
      retries: 5
    restart: always
    networks:
      - airflow-network

  # Initialize Airflow Database
  airflow-init:
    <<: *airflow-common
    container_name: airflow-init
    entrypoint: /bin/bash
    command: -c "airflow db migrate"
    restart: on-failure
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - airflow-network
      - db_network

  airflow-webserver:
    <<: *airflow-common
    container_name: airflow-server
    command: api-server
    ports:
      - "8081:8080"
    depends_on:
      - airflow-init # Ensure DB is migrated
    healthcheck: # Health check for the webserver
      test: ["CMD-SHELL", "curl --fail http://localhost:8080/api/v2/monitor/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    restart: always
    networks:
      - airflow-network

  # Airflow DAG Processor
  airflow-dag-processor:
    <<: *airflow-common
    container_name: airflow-dag-processor
    command: dag-processor
    depends_on:
      - airflow-webserver
    restart: always
    networks:
      - airflow-network
      - db_network

  # Airflow Scheduler
  airflow-scheduler:
    <<: *airflow-common
    container_name: airflow-scheduler
    command: scheduler
    depends_on:
      - airflow-webserver # Depends on webserver for API access if configured
      - postgres 
      - redis
    restart: always
    networks:
      - airflow-network
      - db_network

  # Airflow Celery Worker
  airflow-worker:
    <<: *airflow-common
    # container_name: airflow-worker # Let Docker assign name for replicas
    command: celery worker
    depends_on:
      - airflow-scheduler
      - postgres 
      - redis 
    restart: always
    networks:
      - airflow-network
      - db_network
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
      replicas: 2 # Example: Run 2 workers

  # Celery Flower Monitor
  flower:
    <<: *airflow-common
    container_name: flower
    command: celery flower
    ports:
      - "5556:5555" # external:internal
    depends_on:
      - airflow-worker # Flower monitors workers
      - redis # Flower needs Redis access
    restart: always
    networks:
      - airflow-network

networks:
  airflow-network:
    driver: bridge
  db_network:
    external: true
    name: db_network

volumes:
  postgres-db-volume:
  redis-db-volume: