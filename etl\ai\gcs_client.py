import os
from google.cloud import storage
from google.oauth2 import service_account
from google.cloud.storage.blob import Blob
from google.oauth2 import service_account
import json
from etl.ai import logger
from config.core.settings import get_settings

class GCSClient:
    def __init__(self, config_path: str|None=None, processing_type: str = "embedding"):
        self.settings = get_settings()
        if not config_path:
            config_path = self.settings.GCP_CONFIG_PATH
        self.config = self._load_credentials(config_path)
        self.client = storage.Client(credentials=self.config)
        self.bucket_name =self.settings.GCS_BUCKET
        self.gs_input_path = self.settings.gs_input_path(processing_type)
        self.gs_output_path = self.settings.gs_output_path(processing_type)
        
        # Verify credentials on initialization
        self._verify_credentials()

    def _load_credentials(self, config_path: str) -> service_account.Credentials:
        """Load GCP credentials from config file"""
        try:
            config = json.load(open(config_path, 'r'))
            credentials = service_account.Credentials.from_service_account_info(config)
            logger.info(f"Loaded credentials for service account: {credentials.service_account_email}")
            return credentials
        except Exception as e:
            logger.error(f"Error loading credentials from {config_path}: {e}")
            raise

    def _verify_credentials(self):
        """Verify the credentials have proper access"""
        try:
            # Test that we can list buckets (requires project-level access)
            buckets = list(self.client.list_buckets(max_results=1))
            logger.info(f"Successfully verified credentials by listing buckets: {len(buckets)} buckets found")
            try:
                bucket = self.client.bucket(self.bucket_name)
                bucket.reload()  # failed, if bucket doesn't exist or no access
                logger.info(f"Successfully verified access to bucket: {self.bucket_name}")
            except Exception as e:
                logger.error(f"No access to bucket {self.bucket_name} despite having project-level access")
                
        except Exception as e:
            logger.warning(f"Could not fully verify credentials: {e}")

    def upload_file(self, local_file_path: str, blob_name: str) -> str | None:
        """Upload a file to GCS and return the GCS URI"""
        try:
            bucket = self.client.bucket(self.bucket_name)  # Use bucket() instead of get_bucket()
            blob = bucket.blob(blob_name)
            blob.upload_from_filename(local_file_path)

            gcs_uri = f"{self.settings.gs_base_path}/{blob_name}"
            logger.info(f"File {local_file_path} uploaded to {gcs_uri}")
            return gcs_uri
        except Exception as e:
            logger.error(f"Error uploading to GCS: {e}")
            return None

    def move_blob(self, source_blob_name: Blob, destination_blob_name: str) -> str:
        bucket = self.client.bucket(self.bucket_name)
        new_blob = bucket.rename_blob(source_blob_name, destination_blob_name)
        logger.info(f"Moved: {destination_blob_name} -> {new_blob.name}")
        return new_blob.name

    def download_files_from_gcs(self, prefix: str, destination_folder: str, file_ext: str = ".jsonl",archive_des:str="archive") -> list[str]:
        
        """
        Download files from a Google Cloud Storage (GCS) bucket to a local directory.

        Args:
            prefix (str): The prefix to filter files in the GCS bucket.
            destination_folder (str): The local directory to download files to.
            file_ext (str, optional): The file extension to filter files for downloading. Defaults to ".jsonl".
            archive_des (str, optional): The folder name in GCS where downloaded files will be archived. Defaults to "archive".

        Returns:
            list[str]: A list of local file paths of the downloaded files.

        This method downloads files from the specified GCS bucket that match the given prefix and file extension.
        It saves them to the specified local directory, maintaining the directory structure. After downloading,
        the files are moved to an archive location in GCS.
        """

        try:
            bucket = self.client.bucket(self.bucket_name)
            
            # List blobs with prefix
            logger.info(f"Listing blobs with prefix '{prefix}' in bucket '{self.bucket_name}'")
            blobs: list[Blob] = list(bucket.list_blobs(prefix=prefix))
            logger.info(f"Found {len(blobs)} total blobs")
            
            # Create destination folder
            os.makedirs(destination_folder,mode=0o775, exist_ok=True)
            
            # Filter and download files
            downloaded_files = []
            
            for counter,blob in enumerate(blobs):
                # Skip if not matching extension
                if not str(blob.name).endswith(file_ext) or archive_des in str(blob.name) or "incremental_predictions" in str(blob.name):
                    logger.info(f"Deleting: {blob.name}")
                    blob.reload()  # Fetch blob metadata to use in generation_match_precondition.
                    generation_match_precondition = blob.generation
                    blob.delete(if_generation_match=generation_match_precondition)
                    continue
                    
                # Create relative path structure
                rel_path:str = blob.name
                if prefix and rel_path.startswith(prefix):
                    rel_path = blob.name[len(prefix):]
                if rel_path.startswith('/'):
                    rel_path = f"{counter}_{rel_path.split('/')[-1]}"
                # Construct local file path
                local_file_path = os.path.join(destination_folder, rel_path)
                
                # Ensure directory exists
                os.makedirs(os.path.dirname(local_file_path),mode=0o755, exist_ok=True)
                
                # Download file
                blob.download_to_filename(local_file_path)
                # move blob to archive folder
                des_blob = str(blob.name).replace(prefix,"")
                logger.info(f"Desired blob: {des_blob}")
                if "/" in des_blob:
                    des_blob=des_blob.replace("/","_")
                self.move_blob(blob,f"{self.settings.GCS_BASE_PATH}/{archive_des}/{des_blob}")
                downloaded_files.append(local_file_path)
                logger.info(f"Downloaded: {blob.name} -> {local_file_path}")
            
            logger.info(f"Downloaded {len(downloaded_files)} files")

            return downloaded_files
        except Exception as e:
            logger.error(f"Error downloading from GCS: {e}")
 

 