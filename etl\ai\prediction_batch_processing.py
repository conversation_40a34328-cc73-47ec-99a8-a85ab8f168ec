import asyncio
import glob
import os
import json
from pathlib import Path
import re
from datetime import datetime
from typing import Any, <PERSON><PERSON>, Optional
from google import genai
from google.genai.types import CreateBatchJobConfig, HttpOptions,BatchJob
from google.oauth2 import service_account
from etl.extractor.jd_content_extractor import  ExtractionConfig
from .gcs_client import GCSClient
from etl.ai import logger
from config.core.settings import get_settings

class GenAIPrediction:
    def __init__(self):
        self.settings = get_settings()
        self.client = self._setup_genai_client()
        

    def _setup_genai_client(self):
        credentials = service_account.Credentials.from_service_account_file(
            self.settings.GCP_CONFIG_PATH,
            scopes=["https://www.googleapis.com/auth/cloud-platform"],
        )
        return genai.Client(
            http_options=HttpOptions(api_version="v1"),
            project=self.settings.GCP_PROJECT_ID,
            location=self.settings.GCP_PROJECT_LOCATION,
            credentials=credentials,
            vertexai=True,
        )

    async def run_prediction_batch(self, input_gcs_uri: str, output_gcs_uri: str) -> BatchJob:
        job = self.client.batches.create(
            model="gemini-2.0-flash-lite-001",
            src=input_gcs_uri,
            config=CreateBatchJobConfig(dest=output_gcs_uri),
        )
        logger.info(f"Job name: {job.name}")
        logger.debug(f"Job details: {job.__dict__}")
        return job


class BatchProcessor:
    def __init__(self, gcp_config_path: str | None = None):
        self.settings = get_settings()
        self.prediction_folder = "prediction"
        if not gcp_config_path:
            gcp_config_path = self.settings.GCP_CONFIG_PATH
        self.gcs_client = GCSClient(gcp_config_path,self.prediction_folder) 
        self.gen_ai_client = GenAIPrediction()
        self.config = ExtractionConfig.default()
        
        self.prediction_batch_size = int(self.settings.VERTEX_PREDICTION_BATCH_SIZE)

    def create_jd_enrichment_prompt(self, jd_text: str) -> str:
        return f"""
        Given the following job description text:

        ---
        {jd_text}
        ---

        Extract the following fields using only the information present in the text. Use comma-separated values where needed. Be concise and accurate.

        - Skills (extract skills for this job from the job text)
        - Preferred Location (location)

        Return the output as valid JSON like:
        {{
            "skills": "...",
            "location": "..."
        }}
        """

    def extract_ctc_and_currency(self, jd_text: str) -> Tuple[Optional[int], Optional[str]]:
        """
        Extract CTC (Cost to Company) and currency from job description using regex
        Returns:
            Tuple containing (ctc, currency) where:
                - ctc is an integer value (in the original currency)
                - currency is a string like USD, EUR, etc.
        """
        # Define regex patterns
        # Currency symbols and codes
        
        currency_pattern = r'(\$|€|£|¥|₹|USD|EUR|GBP|JPY|INR|AUD|CAD|CHF|CNY|HKD|NZD)'
        # Salary patterns
        # Pattern 1: Currency symbol/code followed by number (with optional comma/decimals)
        # Examples: $50,000, $50K, $50-60K, €50,000-€60,000
        salary_pattern1 = rf'{currency_pattern}\s*(\d+(?:,\d+)*(?:\.\d+)?(?:\s*[kK])?(?:\s*-\s*{currency_pattern}?\s*\d+(?:,\d+)*(?:\.\d+)?(?:\s*[kK])?)?)'
        # Pattern 2: Number followed by currency code -> 50,000 USD, 50K EUR, 50-60K USD
        salary_pattern2 = r'(\d+(?:,\d+)*(?:\.\d+)?(?:\s*[kK])?(?:\s*-\s*\d+(?:,\d+)*(?:\.\d+)?(?:\s*[kK])?)?\s*(?:' + currency_pattern + r'))'
        # Pattern 3: Just numbers with K suffix (assume local currency) 50K, 50-60K, 50,000-60,000
        salary_pattern3 = r'(\d+(?:,\d+)*(?:\.\d+)?\s*[kK](?:\s*-\s*\d+(?:,\d+)*(?:\.\d+)?\s*[kK])?)'

        # Try all patterns for salary
        ctc = None
        currency = None

        match = re.search(salary_pattern1, jd_text, re.IGNORECASE)
        if match:
            currency_match = match.group(1)
            salary_text = match.group(2)
            currency = self._normalize_currency(currency_match)
            ctc = self._normalize_salary(salary_text)
            return ctc, currency

        match = re.search(salary_pattern2, jd_text, re.IGNORECASE)
        if match:
            salary_text = match.group(1)
            currency_match = re.search(currency_pattern, salary_text, re.IGNORECASE)
            if currency_match:
                currency = self._normalize_currency(currency_match.group(0))
                salary_part = re.sub(currency_pattern, '', salary_text, flags=re.IGNORECASE).strip()
                ctc = self._normalize_salary(salary_part)
                return ctc, currency

        match = re.search(salary_pattern3, jd_text, re.IGNORECASE)
        if match:
            salary_text = match.group(1)
            ctc = self._normalize_salary(salary_text)

        return ctc, currency

    def _normalize_currency(self, currency_str: str) -> str:
        currency_map = {
            '$': 'USD', '€': 'EUR', '£': 'GBP', '¥': 'JPY', '₹': 'INR'
        }
        return currency_map.get(currency_str, currency_str.upper())

    def _normalize_salary(self, salary_str: str) -> int:
        """
        Convert salary string to integer
        - Handles K/k suffix
        - Handles ranges by taking average
        - Removes commas and other non-numeric characters
        """
        # Check if it's a range
        if '-' in salary_str:
            parts = salary_str.split('-')
            low = self._convert_salary_to_number(parts[0])
            high = self._convert_salary_to_number(parts[1])
            return (low + high) // 2
        return self._convert_salary_to_number(salary_str)

    def _convert_salary_to_number(self, salary_part: str) -> int:
        """Convert single salary string to number"""
        # Remove non-numeric characters except K/k and decimal pointlist
        salary_part = re.sub(r'[^\d\.kK]', '', salary_part)
        # Check for K/k suffix
        has_k = salary_part.lower().endswith('k')
        if has_k:
        # Convert to float first to handle decimals
            salary_part = salary_part[:-1]
        try:
            value = float(salary_part)

            # Apply K multiplier if present
            if has_k:
                value *= 1000
            return int(value)
        except ValueError:
            return 0

    def prepare_batch_input_file(self, jd_files_path: list[str]) -> str:
        """Prepare JSONL input files for batch processing (1500 records per file)"""
        try:
            output_dir = self.settings.VERTEX_PREDICTION_BATCH_INPUT_DIR
            Path(output_dir).mkdir(parents=True, exist_ok=True)

            file_index = 0
            record_count = 0
            # dynamic file name and based on the record count
            current_file = open(os.path.join(output_dir, f"batch_{file_index}.jsonl"), "w", encoding="utf-8")

            for jd_file in jd_files_path:
                try:
                    with open(jd_file, 'r', encoding='utf-8') as jd_f:
                        job_descriptions: list[dict] = json.load(jd_f)

                    for jd in job_descriptions:
                        jd_text = jd.get('description', '')
                        if not jd_text or not isinstance(jd_text, str):
                            continue
                        jd["ctc"], jd["currency"] = self.extract_ctc_and_currency(jd_text)
                        jd.pop("description", None)
                        jd = {k: v for k, v in jd.items() if v is not None and v}

                        prompt = self.create_jd_enrichment_prompt(
                            f'<description>{jd_text}</description> <jd_data>{jd}</jd_data>'
                        )

                        request_json = {
                            "request": {
                                "contents": [
                                    {
                                        "role": "user",
                                        "parts": [{"text": prompt}],
                                    },
                                ],
                            },
                        }

                        current_file.write(json.dumps(request_json) + '\n')
                        record_count += 1

                        if record_count >= self.prediction_batch_size:
                            current_file.close()
                            file_index += 1
                            record_count = 0
                            current_file = open(os.path.join(output_dir, f"batch_{file_index}.jsonl"), "w", encoding="utf-8")

                except Exception as e:
                    logger.error(f"Error processing file {jd_file} for batch: {e}")

            if not current_file.closed:
                current_file.close()

            logger.info(f"Generated batch input files in directory: {output_dir}")
            return output_dir

        except Exception as e:
            logger.error(f"Error creating batch input files: {e}")
            return None

    async def process_jds(self, jd_files: list[str]) -> Any | None:
        if not jd_files:
            logger.info("No JD files found for GenAI processing")
            return None
        vertex_local_input_dir = self.prepare_batch_input_file(jd_files)
        if not vertex_local_input_dir:
            return
        vertex_jobs = []
        for input_jsonl_path in glob.glob(f"{vertex_local_input_dir}/*.jsonl"):
            batch_timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            gcs_blob_name = f"{self.settings.GCS_INPUT_PREFIX}/{self.prediction_folder}/{batch_timestamp}_batch_input.jsonl"
            print(gcs_blob_name)
            blob_input_uri = self.gcs_client.upload_file(input_jsonl_path, gcs_blob_name)
            #must remove local file
            logger.info(f"Uploaded batch input file to {blob_input_uri}")
            #remove local file
            logger.info(f"Removing local batch input file {input_jsonl_path}")
            os.remove(input_jsonl_path)
            if not blob_input_uri:
                return None

            output_gcs_uri = f"{self.settings.gs_base_path}/{self.settings.GCS_OUTPUT_PREDICTION_PREFIX}"
            vertex_jobs.append(self.gen_ai_client.run_prediction_batch(blob_input_uri, output_gcs_uri))
        if not vertex_jobs:
            logger.warning("No vertex jobs to run")
            return None
        await asyncio.gather(*vertex_jobs)
        self._remove_processed_files(jd_files)

    def _remove_processed_files(self, jd_files: list[str]) -> None:
        """Remove processed files"""
        for file_path in jd_files:
            try:
                os.remove(file_path)
            except Exception as e:
                logger.error(f"Error removing processed file {file_path}: {e}")

    def collect_jd_files(self) -> list[str]:
        """Collect all JD files in the output directory"""
        jd_files = []
        try:
            for file in os.listdir(self.config.jd_content_needs_prediction_dir):
                if file.endswith(".json"):
                    jd_files.append(os.path.join(self.config.jd_content_needs_prediction_dir, file))
        except Exception as e:
            logger.error(f"Error collecting JD files: {e}")

        return jd_files

async def async_main():
    processor = BatchProcessor()
    jds = processor.collect_jd_files()
    await processor.process_jds(jds)

    
def main():
    asyncio.run(async_main())
    
if __name__ == "__main__":
    main()