import asyncio
import csv
import json
import os
from datetime import datetime
from pathlib import Path
import shutil
from urllib.parse import urljoin
from typing import Any
from dataclasses import dataclass
import re
from pydantic import BaseModel, Field
import aiofiles
from playwright.async_api import async_playwright, <PERSON>, TimeoutError as PlaywrightTimeoutError
from config.core.settings import get_settings
from etl.extractor import logger
import random

from etl.extractor.models import ScrappedJDLinkModel
from etl.extractor.models.enum import JobSource

@dataclass
class ExtractionConfig:
    """Configuration for job extraction"""
    jd_links_input_dir: str
    jd_content_needs_prediction_dir: str
    jd_content_skip_prediction_dir:str
    batch_size: int
    timeout: int
    concurrent_scrapes: int
    max_retries: int = 2
    retry_delay: int = 2
    backoff_factor: float = 2.0

    @classmethod
    def default(cls) -> 'ExtractionConfig':
        """Create default configuration"""
        settings = get_settings()
        return cls(
            jd_links_input_dir=settings.LOCAL_SCRAPED_LINKS_DIR,
            jd_content_needs_prediction_dir=settings.LOCAL_JD_NEEDS_PREDICTION_DIR,
            jd_content_skip_prediction_dir=settings.LOCAL_JD_SKIP_PREDICTION_DIR,
            batch_size=50,
            timeout=60000,  # 60 seconds
            concurrent_scrapes=3,  # Reduced from 5 to prevent overwhelming servers
            max_retries=3,
            retry_delay=2,
            backoff_factor=2.0
        )

class JobModel(BaseModel):
    jd_link: str = Field(..., description="Job description link")
    company_id: int = Field(..., description="Company ID")
    content_selectors: dict = Field(..., description="Content selectors")
    source: int = Field(..., description="Job source")

class JDRecord:
    """Class to hold job description data"""

    # Enhanced regex patterns for job type extraction
    JOB_TYPE_PATTERNS = {
        'full_time': r'\b(?:full[- ]?time|permanent|regular|full[- ]?time[- ]?position|full[- ]?time[- ]?role)\b',
        'part_time': r'\b(?:part[- ]?time|temporary|contract|part[- ]?time[- ]?position|part[- ]?time[- ]?role)\b',
        'contract': r'\b(?:contract|freelance|consultant|contract[- ]?position|contract[- ]?role|contract[- ]?based)\b',
        'internship': r'\b(?:intern|internship|trainee|intern[- ]?position|intern[- ]?role|internship[- ]?program)\b',
        'freelance': r'\b(?:freelance|gig|project[- ]?based|freelance[- ]?position|freelance[- ]?role|independent[- ]?contractor)\b',
        'temporary': r'\b(?:temporary|temp|seasonal|temporary[- ]?position|temporary[- ]?role|temp[- ]?position)\b'
    }

    # Enhanced regex patterns for location type extraction
    LOCATION_TYPE_PATTERNS = {
        'remote': r'\b(?:remote|work[- ]?from[- ]?home|wfh|virtual|remote[- ]?position|remote[- ]?work|work[- ]?remotely)\b',
        'hybrid': r'\b(?:hybrid|flexible[- ]?remote|part[- ]?remote|hybrid[- ]?work|hybrid[- ]?position|part[- ]?time[- ]?remote)\b',
        'onsite': r'\b(?:onsite|in[- ]?office|in[- ]?person|at[- ]?location|office[- ]?based|work[- ]?from[- ]?office)\b',
        'flexible': r'\b(?:flexible|any[- ]?location|location[- ]?flexible|flexible[- ]?location|work[- ]?from[- ]?anywhere)\b'
    }

    def __init__(
        self,
        jd_link: str,
        company_id: int,
        source: int
    ):
        self.jd_link = jd_link
        self.company_id = company_id
        self.source = source
        self.apply_link: str = jd_link
        self.skills:str | None = None # it should be comma separated
        self.posted_at: str | None = None
        self.description: str | None = None
        self.title: None | str = None
        self.location: None | str = None
        self.location_type: None | str = None
        self.job_type: None | str = None
        self.min_experience: None | int = None
        self.max_experience: None | int = None
        self.ctc: None | str = None
        self.currency: None | str = None
        self.company_dir: None | str = None
        self.meta: dict[str, Any] = {}

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary"""
        return {
            "jd_link": self.jd_link,
            "company_id": self.company_id,
            "source": self.source,
            "skills": self.skills,
            "title": self.title,
            "location": self.location,
            "location_type": self.location_type,
            "job_type": self.job_type,
            "min_experience": self.min_experience,
            "max_experience": self.max_experience,
            "apply_link": self.apply_link,
            "description": self.description,
            "ctc": self.ctc,
            "currency": self.currency,
            "meta": self.meta
        }

    def process_description(self) -> None:
        """Process job description to extract structured information"""
        if not self.description:
            return

        self.min_experience, self.max_experience = self.extract_experience(self.description)
        self.job_type = self.extract_job_type(self.description)
        self.location_type = self.extract_location_type(self.description)

        logger.info(f"Extracted from job description: "
                   f"Experience: {self.min_experience}-{self.max_experience} years, "
                   f"Job Type: {self.job_type}, "
                   f"Location Type: {self.location_type}")

    @classmethod
    def extract_experience(cls, text: str) -> tuple[None | int, None | int]:
        """Extract minimum and maximum experience from text"""
        if not text:
            return None, None

        normalized_text = ' '.join(text.lower().split())
        min_exp = None
        max_exp = None
        year_mentions = []

        # Pattern for X+ years (minimum requirement)
        plus_matches = re.findall(r'(\d+)\+\s*(?:years?|yrs?)', normalized_text)
        for match in plus_matches:
            year_mentions.append(('min', int(match)))

        # Pattern for range X-Y years
        range_matches = re.findall(r'(\d+)[-–](\d+)\s*(?:years?|yrs?)', normalized_text)
        for min_val, max_val in range_matches:
            year_mentions.append(('range', int(min_val), int(max_val)))

        # Pattern for "must have X years"
        must_have_matches = re.findall(r'must\s*have\s*(\d+)(?:\+)?\s*(?:years?|yrs?)', normalized_text)
        for match in must_have_matches:
            if '+' in normalized_text[normalized_text.find(match):normalized_text.find(match)+10]:
                year_mentions.append(('min', int(match)))
            else:
                year_mentions.append(('exact', int(match)))

        # Pattern for exact X years
        exact_matches = re.findall(r'(?<![0-9+-])(\d+)\s*(?:years?|yrs?)(?!\+)', normalized_text)
        for match in exact_matches:
            year_mentions.append(('exact', int(match)))

        # Pattern for minimum/at least X years
        min_matches = re.findall(r'(?:minimum|min|at\s*least)\s*(\d+)\s*(?:years?|yrs?)', normalized_text)
        for match in min_matches:
            year_mentions.append(('min', int(match)))

        # Process all year mentions
        for mention in year_mentions:
            if mention[0] == 'min':
                if min_exp is None or mention[1] < min_exp:
                    min_exp = mention[1]
            elif mention[0] == 'exact':
                if min_exp is None or mention[1] < min_exp:
                    min_exp = mention[1]
                if max_exp is None or mention[1] > max_exp:
                    max_exp = mention[1]
            elif mention[0] == 'range':
                if min_exp is None or mention[1] < min_exp:
                    min_exp = mention[1]
                if max_exp is None or mention[2] > max_exp:
                    max_exp = mention[2]

        return min_exp, max_exp

    @classmethod
    def extract_job_type(cls, text: str) -> None | str:
        """Extract job type from text"""
        if not text:
            return None

        text = text.lower()
        job_type_matches = {}
        for job_type, pattern in cls.JOB_TYPE_PATTERNS.items():
            matches = len(re.findall(pattern, text))
            if matches > 0:
                job_type_matches[job_type] = matches

        if job_type_matches:
            return max(job_type_matches.items(), key=lambda x: x[1])[0]
        return None

    @classmethod
    def extract_location_type(cls, text: str) -> None | str:
        """Extract location type from text"""
        if not text:
            return None

        text = text.lower()
        location_matches = {}
        for loc_type, pattern in cls.LOCATION_TYPE_PATTERNS.items():
            matches = len(re.findall(pattern, text))
            if matches > 0:
                location_matches[loc_type] = matches

        if location_matches:
            return max(location_matches.items(), key=lambda x: x[1])[0]
        return None

class JobExtractor:
    """Main class for job extraction with enhanced error handling"""
    
    def __init__(self, config: None | ExtractionConfig = None):
        self.config = config or ExtractionConfig.default()
        self._setup_directories()

    def _cleanup(self) -> None:
        """Cleanup directory contents"""
        if os.path.exists(self.config.jd_links_input_dir):
            shutil.rmtree(self.config.jd_links_input_dir)
        os.makedirs(self.config.jd_links_input_dir, mode=0o775, exist_ok=True)
        
    def _setup_directories(self) -> None:
        """Create necessary directories"""
        for directory in [self.config.jd_content_needs_prediction_dir]:
            os.makedirs(directory, mode=0o775, exist_ok=True)

    async def _create_browser_context(self, playwright):
        """Create browser with enhanced settings for network stability"""
        browser = await playwright.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-extensions',
                '--no-first-run',
                '--disable-default-apps',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-field-trial-config'
            ]
        )
        
        context = await browser.new_context(
            viewport={'width': 1280, 'height': 1024},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        )
        
        return browser, context

    async def _retry_with_backoff(self, func, *args, **kwargs):
        """Retry function with exponential backoff"""
        for attempt in range(self.config.max_retries):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                if attempt == self.config.max_retries - 1:
                    raise e
                
                # Calculate delay with jitter
                delay = self.config.retry_delay * (self.config.backoff_factor ** attempt)
                jitter = random.uniform(0.1, 0.5) * delay
                total_delay = delay + jitter
                
                logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {total_delay:.2f}s")
                await asyncio.sleep(total_delay)
        
        raise Exception(f"All {self.config.max_retries} attempts failed")

    async def process_job_batch(self, batch: list[JobModel]) -> list[JDRecord]:
        """Process a batch of jobs concurrently with enhanced error handling"""
        async with async_playwright() as playwright:
            browser, context = await self._create_browser_context(playwright)
            
            job_records: list[JDRecord] = []
            semaphore = asyncio.Semaphore(self.config.concurrent_scrapes)

            async def process_job_with_retry(job_model: JobModel):
                async with semaphore:
                    page = None
                    try:
                        page = await context.new_page()
                        
                        # Set additional page configurations
                        await page.set_extra_http_headers({
                            'Accept-Language': 'en-US,en;q=0.9',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
                        })
                        
                        jd = JDRecord(job_model.jd_link, job_model.company_id,source=job_model.source)
                        
                        # Use retry mechanism for scraping
                        jd = await self._retry_with_backoff(
                            self._scrape_job, 
                            page, 
                            jd, 
                            job_model.content_selectors
                        )
                        
                        if jd and jd.description:
                            job_records.append(jd)
                            logger.info(f"Processed: {jd.jd_link} - SUCCESS")
                        else:
                            logger.warning(f"Processed: {job_model.jd_link} - NO CONTENT")
                    
                    except Exception as e:
                        logger.error(f"Error processing job {job_model.jd_link}: {str(e)}")
                        # Still create a record for tracking
                        failed_jd = JDRecord(job_model.jd_link, job_model.company_id,source=job_model.source)
                        failed_jd.meta['error'] = str(e)
                        job_records.append(failed_jd)
                    
                    finally:
                        if page:
                            try:
                                await page.close()
                            except Exception:
                                pass

            # Process jobs with controlled concurrency
            tasks = [process_job_with_retry(job) for job in batch]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            # Cleanup
            try:
                await context.close()
                await browser.close()
            except Exception as e:
                logger.warning(f"Error closing browser: {e}")
            
            logger.info(f"Processed {len(batch)} jobs, {len([j for j in job_records if j.description])} successful")
            await self._save_jd_to_json(job_records)
            
            return job_records

    def _clean_job_description(self, text):
        """Clean and format job description text"""
        if isinstance(text, bytes):
            text = text.decode('utf-8')
        
        # Remove excess whitespace
        text = re.sub(r'\s+', ' ', text)
        # Clean up newlines
        text = re.sub(r'\n\s*\n', '\n\n', text)
        # Fix bullet points
        text = re.sub(r'\n\s*-\s*', '\n• ', text)
        # Ensure proper spacing after punctuation
        text = re.sub(r'([.!?])\s*', r'\1 ', text)
        # Normalize section headers
        text = re.sub(r'(What you will be doing|What you will have):\s*', r'\n\n## \1:\n\n', text)
        # Clean up multiple bullet points
        text = re.sub(r'\n•\s*\n•', '\n•', text)
        # Limit consecutive newlines
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        return text.strip()

    @staticmethod
    async def _extract_skills(page: Page, selector: None | str, job_url: str) -> str:
        """Extract skills with special handling for Dice.com"""
        if not selector:
            return ""

        try:
            # Special handling for Dice.com skills extraction
            if "dice.com" in job_url:
                logger.info(f"Extracting skills from Dice job: {job_url}")
                try:
                    # First, try to click the "Show More" button if it exists
                    show_more_button = page.locator('button[data-cy="moreSkills"]').first
                    if await show_more_button.is_visible(timeout=3000):
                        logger.info("Found 'Show More Skills' button, clicking...")

                        # Scroll into view
                        await show_more_button.scroll_into_view_if_needed()
                        await page.wait_for_timeout(1000)

                        # Click with force to bypass any overlays
                        await show_more_button.click(force=True)
                        await page.wait_for_timeout(2000)
                        logger.info("Successfully clicked 'Show More Skills' button")
                    else:
                        logger.info("No 'Show More Skills' button found")

                except Exception as e:
                    logger.debug(f"Could not click 'Show More Skills' button: {e}")

                # Extract all skills after potentially expanding the list
                try:
                    skills_elements = await page.query_selector_all('div[data-cy="skillsList"] span[id^="skillChip:"]')
                    if skills_elements:
                        skills_list = []
                        for element in skills_elements:
                            skill_text = await element.text_content()
                            if skill_text and skill_text.strip():
                                skills_list.append(skill_text.strip())

                        if skills_list:
                            skills_str = ", ".join(skills_list)
                            logger.info(f"Extracted {len(skills_list)} skills from Dice: {skills_str[:100]}...")
                            return skills_str
                        else:
                            logger.info("No skills found in skillsList")
                    else:
                        logger.info("No skills elements found with selector: div[data-cy=\"skillsList\"] span[id^=\"skillChip:\"]")

                except Exception as e:
                    logger.warning(f"Error extracting Dice skills: {e}")

            # Fallback to generic skills extraction for non-Dice sites or if Dice-specific extraction fails
            logger.info(f"Using generic skills extraction with selector: {selector}")
            elements = await page.query_selector_all(selector)
            if not elements:
                logger.info("No elements found with generic selector")
                return ""

            skills_list = []
            for element in elements:
                content = await element.text_content()
                if content:
                    skills_list.append(content.strip())

            result = ", ".join(skills_list) if skills_list else ""
            logger.info(f"Generic skills extraction result: {result[:100]}...")
            return result

        except Exception as e:
            logger.warning(f"Error extracting skills with selector {selector}: {e}")
            return ""
    
    @staticmethod
    def _clean_posted_date(posted_date: str) -> str:
        """Convert relative posted date to UTC timestamp."""
        if not posted_date:
            return ""

        from datetime import datetime, timedelta, timezone
        import re

        # Regex to extract number and time unit from "Posted X days/hours/weeks ago"
        POSTED_DATE_PATTERN = re.compile(r'Posted\s+(\d+)\s+(day|days|hour|hours|week|weeks|month|months|year|years)\s+ago', re.IGNORECASE)
        match = POSTED_DATE_PATTERN.search(posted_date)

        if match:
            number = int(match.group(1))
            unit = match.group(2).lower()

            # Get current UTC time
            now_utc = datetime.now(timezone.utc)

            # Calculate the posted date by subtracting the time difference
            if unit in ['hour', 'hours']:
                posted_datetime = now_utc - timedelta(hours=number)
            elif unit in ['day', 'days']:
                posted_datetime = now_utc - timedelta(days=number)
            elif unit in ['week', 'weeks']:
                posted_datetime = now_utc - timedelta(weeks=number)
            elif unit in ['month', 'months']:
                # Approximate: 1 month = 30 days
                posted_datetime = now_utc - timedelta(days=number * 30)
            elif unit in ['year', 'years']:
                # Approximate: 1 year = 365 days
                posted_datetime = now_utc - timedelta(days=number * 365)
            else:
                # If unit not recognized, return original text
                return posted_date.strip()

            # Return UTC timestamp in the desired format with microseconds and timezone
            return posted_datetime.isoformat()

        # If no match found, return the original text
        return posted_date.strip()

    async def _scrape_job(self, page: Page, jd: JDRecord, selectors: dict[str, str]) -> JDRecord | None:
        """Scrape job details with enhanced error handling"""
        try:
            # Navigate with retry on network errors
            await page.goto(
                jd.jd_link, 
                timeout=self.config.timeout,
                wait_until='domcontentloaded'
            )
            
            # Add random delay to avoid being detected as bot
            await asyncio.sleep(random.uniform(1, 3))

            # Wait for main content with shorter timeout
            main_selector = selectors.get("job_description") or selectors.get("title")
            if main_selector:
                try:
                    await page.wait_for_selector(main_selector, timeout=10000)
                except PlaywrightTimeoutError:
                    logger.warning(f"Timeout waiting for selector {main_selector} on {jd.jd_link}")

            # Extract job information
            description = await self._safe_extract(page, selectors.get("job_description"))
            apply_link = await self._get_apply_link(page, selectors.get("apply_link"), jd.jd_link)
            if not description or not apply_link:
                logger.warning(f"Missing content for {jd.jd_link}: description={bool(description)}, apply_link={bool(apply_link)}")
                return jd
            jd.title = await self._safe_extract(page, selectors.get("title"))
            jd.location = await self._safe_extract(page, selectors.get("location"))
            posted_at = await self._safe_extract(page, selectors.get("posted_at"))
            
            jd.posted_at = self._clean_posted_date(posted_at) if posted_at else datetime.now().isoformat()
            if selectors.get("skills"):
                jd.skills = await self._extract_skills(page, selectors.get("skills"), jd.jd_link)
            jd.apply_link = apply_link
            jd.description = self._clean_job_description(description)
            jd.process_description()

            return jd
            
        except Exception as e:
            logger.error(f"Error scraping job {jd.jd_link}: {e}")
            # Re-raise to trigger retry mechanism
            raise e

    @staticmethod
    async def _safe_extract(page: Page, selector: None | str) -> str:
        """Safely extract text content from a selector"""
        if not selector:
            return ""

        try:
            elements = await page.query_selector_all(selector)
            if not elements:
                return ""

            contents = []
            for element in elements:
                content = await element.text_content()
                if content:
                    contents.append(content.strip())

            return "\n".join(contents) if contents else ""
        except Exception as e:
            logger.warning(f"Error extracting content with selector {selector}: {e}")
            return ""

    @staticmethod
    async def _get_apply_link(page: Page, selector: None | str, base_url: str) -> str:
        """Extract apply link from page"""
        if not selector:
            return base_url

        try:
            element = await page.query_selector(selector)
            if element:
                href = await element.get_attribute("href")
                if href:
                    return urljoin(base_url, href)
        except Exception as e:
            logger.warning(f"Error extracting apply link: {e}")

        return base_url

    async def _get_save_jd_file_path(self, jd: JDRecord):
        file_path = None
        if not jd.skills:
            file_path = Path(f"{self.config.jd_content_needs_prediction_dir}/{jd.company_id}.json")
        else:
            file_path = Path(f"{self.config.jd_content_skip_prediction_dir}/{jd.company_id}.json")
        file_path.parent.mkdir(parents=True, exist_ok=True,mode=0o775)
        return file_path
        
    async def _save_jd_to_json(self, jds: list[JDRecord]) -> None:
        """Save scraped job descriptions to JSON files"""
        try:
            for jd in jds:
                if not jd.description:  # Skip failed extractions
                    continue
                    
                file_path = await self._get_save_jd_file_path(jd)

                # Update existing file or create new one
                if file_path.exists():
                    async with aiofiles.open(file_path, 'r', encoding='utf-8') as file:
                        content = await file.read()
                        existing_jds: list[dict] = json.loads(content)
                    existing_jds.append(jd.to_dict())
                else:
                    existing_jds = [jd.to_dict()]
                
                async with aiofiles.open(file_path, 'w', encoding='utf-8') as file:
                    await file.write(json.dumps(existing_jds, indent=4, ensure_ascii=False))
                    
        except Exception as e:
            logger.error(f"Error saving JDs to file: {e}")

    async def load_jobs_from_csv_folder(self, folder_path: str) -> list[JobModel]:
        """Load jobs from all CSV files in a folder"""
        all_jobs: list[JobModel] = []
        
        try:
            folder = Path(folder_path)
            if not folder.exists():
                logger.error(f"Folder does not exist: {folder_path}")
                return all_jobs
                
            csv_files = list(folder.glob("*.csv"))
            if not csv_files:
                logger.warning(f"No CSV files found in folder: {folder_path}")
                return all_jobs
                
            logger.info(f"Found {len(csv_files)} CSV files in {folder_path}")
            
            # Process CSV files with limited concurrency
            semaphore = asyncio.Semaphore(3)  # Limit concurrent file reads
            
            async def process_csv_file(csv_file):
                async with semaphore:
                    return await self.load_jobs_from_csv_file(csv_file)
            
            tasks = [process_csv_file(csv_file) for csv_file in csv_files]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Error processing {csv_files[i]}: {result}")
                else:
                    all_jobs.extend(result)
                    logger.info(f"Loaded {len(result)} jobs from {csv_files[i].name}")
            
            logger.info(f"Total jobs loaded: {len(all_jobs)}")
            
        except Exception as e:
            logger.error(f"Error loading jobs from folder {folder_path}: {e}")
        
        return all_jobs

    @staticmethod
    async def load_jobs_from_csv_file(csv_dir: str) -> list[JobModel]:
        """Load jobs from CSV file with better error handling"""
        jobs: list[JobModel] = []
        try:
            async with aiofiles.open(csv_dir, 'r', encoding='utf-8') as file:
                content = await file.read()

            company_id = 0
            content_selectors = None

            from io import StringIO
            reader = csv.DictReader(StringIO(content))

            for row in reader:
                try:
                    parsed_row = ScrappedJDLinkModel(**row)
                    jd_link = parsed_row.job_url
                    if parsed_row.company_id:
                        company_id = int(parsed_row.company_id)
                    
                    if parsed_row.content_selectors:
                        content_selectors = parsed_row.content_selectors
                    
                    if not content_selectors or not jd_link:
                        logger.warning(f"Missing data in row: company_id={company_id}, jd_link={bool(jd_link)}, selectors={bool(content_selectors)}")
                        continue

                    # Handle JSON parsing with better error handling
                    try:
                        selectors = json.loads(content_selectors)
                    except json.JSONDecodeError:
                        # Try with single quotes converted to double quotes
                        try:
                            fixed_json = content_selectors.replace("'", '"')
                            selectors = json.loads(fixed_json)
                        except json.JSONDecodeError as e:
                            logger.error(f"JSON decode error for {jd_link}: {e}")
                            continue
                    
                    job = JobModel(
                        company_id=company_id,
                        jd_link=jd_link,
                        content_selectors=selectors,
                        source=parsed_row.source
                    )
                    jobs.append(job)
                    
                except Exception as e:
                    logger.error(f"Error processing row: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error loading CSV file {csv_dir}: {e}")

        return jobs

    async def extract_job_descriptions(self) -> list[JDRecord]:
        """Main extraction method with enhanced logging and error handling"""
        logger.info("Starting job description extraction")
        
        jobs = await self.load_jobs_from_csv_folder(self.config.jd_links_input_dir)
        logger.info(f"Loaded {len(jobs)} jobs from CSV files")

        if not jobs:
            logger.warning("No jobs found to process")
            return []

        all_results: list[JDRecord] = []
        total_batches = (len(jobs) - 1) // self.config.batch_size + 1

        # Process in batches with progress tracking
        for i in range(0, len(jobs), self.config.batch_size):
            batch_num = i // self.config.batch_size + 1
            batch = jobs[i:min(i + self.config.batch_size, len(jobs))]
            
            logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} jobs)")
            
            try:
                batch_results = await self.process_job_batch(batch)
                all_results.extend(batch_results)
                
                successful_in_batch = len([jd for jd in batch_results if jd.description])
                logger.info(f"Batch {batch_num} complete: {successful_in_batch}/{len(batch)} successful")
                
            except Exception as e:
                logger.error(f"Error processing batch {batch_num}: {e}")
                continue

        # Generate final statistics
        successful = len([jd for jd in all_results if jd.description])
        failed = len(all_results) - successful
        
        logger.info(f"Extraction complete: {successful} successful, {failed} failed out of {len(jobs)} total jobs")

        # Generate summary report
        report_data = {
            "total_jobs": len(jobs),
            "processed": len(all_results),
            "successful": successful,
            "failed": failed,
            "success_rate": f"{(successful/len(jobs)*100):.1f}%" if jobs else "0%",
            "timestamp": datetime.now().isoformat(),
        }
        
        logger.info(f"Final report: {report_data}")
        
        # Cleanup input files
        try:
            self._cleanup()
        except Exception as e:
            logger.warning(f"Error during cleanup: {e}")

        return all_results

    async def process_job_links(self):
        """Main entry point for processing job links"""
        all_results = await self.extract_job_descriptions()
        return all_results

async def amain():
    """Main function to run the extractor"""
    extractor = JobExtractor()
    jd_records = await extractor.process_job_links()
    return len(jd_records) if jd_records else 0
   
def main():
    return asyncio.run(amain())

if __name__ == "__main__":
    main()