# import asyncio
import asyncio
from etl.extractor import logger
from etl.extractor.regional_extractors.uk.career_page_uk_extractor import main as uk_career_page_extractor
from etl.extractor.regional_extractors.new_usa.usa_career_page_extractor import main as usa_career_page_extractor
from etl.extractor.regional_extractors.us.usa_career_page_extractor import main as usa_career_page_extractor1
from etl.extractor.job_provider.dice_jd_link_extractor import main as dice_extractor

import asyncio

async def main():
    logger.info("Starting job link extraction (2+2 batch concurrency)")

    # Batch 1: Run 2 extractors concurrently (Dice + UK)
    logger.info("Starting Batch 1: Dice + UK extractors")
    batch1_tasks = [
        asyncio.create_task(run_dice_extractor()),
        asyncio.create_task(run_uk_career_page_extractor())
    ]
    batch1_results = await asyncio.gather(*batch1_tasks, return_exceptions=True)

    # Log batch 1 results
    batch1_extractors = ["Dice", "UK Career Page"]
    for i, result in enumerate(batch1_results):
        if isinstance(result, Exception):
            logger.error(f"{batch1_extractors[i]} extractor failed: {result}")
        else:
            logger.info(f"{batch1_extractors[i]} extractor completed successfully")

    # Batch 2: Run remaining 2 extractors concurrently (USA + USA1)
    logger.info("Starting Batch 2: USA extractors")
    batch2_tasks = [
        asyncio.create_task(run_usa_career_page_extractor()),
        asyncio.create_task(run_usa_career_page_extractor1())
    ]
    batch2_results = await asyncio.gather(*batch2_tasks, return_exceptions=True)

    # Log batch 2 results
    batch2_extractors = ["USA Career Page", "USA Career Page 1"]
    for i, result in enumerate(batch2_results):
        if isinstance(result, Exception):
            logger.error(f"{batch2_extractors[i]} extractor failed: {result}")
        else:
            logger.info(f"{batch2_extractors[i]} extractor completed successfully")

    logger.info("All job link extractors completed!")

async def run_dice_extractor():
    """Async wrapper for dice_extractor"""
    logger.info("Starting Dice extraction")
    await dice_extractor()
    logger.info("Dice extraction completed")

async def run_uk_career_page_extractor():
    """Async wrapper for uk_career_page_extractor"""
    logger.info("Starting UK career page extraction")
    await uk_career_page_extractor()
    logger.info("UK career page extraction completed")

async def run_usa_career_page_extractor():
    """Async wrapper for usa_career_page_extractor"""
    logger.info("Starting USA career page extraction")
    await usa_career_page_extractor()
    logger.info("USA career page extraction completed")

async def run_usa_career_page_extractor1():
    """Async wrapper for usa_career_page_extractor1"""
    logger.info("Starting USA career page extraction 1")
    await usa_career_page_extractor1()
    logger.info("USA career page extraction 1 completed")

if __name__ == "__main__":
    asyncio.run(main())