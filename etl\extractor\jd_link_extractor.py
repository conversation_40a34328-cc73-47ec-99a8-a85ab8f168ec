from etl.extractor import logger
from etl.extractor.regional_extractors.uk.career_page_uk_extractor import main as uk_career_page_extractor
from etl.extractor.regional_extractors.new_usa.usa_career_page_extractor import main as usa_career_page_extractor
from etl.extractor.regional_extractors.us.usa_career_page_extractor import main as usa_career_page_extractor1
from etl.extractor.job_provider.dice_jd_link_extractor import main as dice_extractor

def main():
    logger.info("Starting job link extraction")
    dice_extractor()
    logger.info("Starting UK job link extraction. dice is done")
    uk_career_page_extractor()
    logger.info("Starting US job link extraction. UK is done")
    usa_career_page_extractor()
    logger.info("Starting US job link extraction. US1 is done")
    usa_career_page_extractor1()
    logger.info("All job links extracted. Done!")

if __name__ == "__main__":
    main()