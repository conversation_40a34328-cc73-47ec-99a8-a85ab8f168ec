import asyncio
import csv
import requests
from config.core.settings import get_settings
from playwright.async_api import async_playwright
from etl.loader.load_to_postgres import PostgresLoader
import os
from config.core import logger
from dotenv import load_dotenv
load_dotenv()
settings = get_settings()


HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json, text/plain, */*",
    "User-Agent": "Mozilla/5.0",
    "Origin": "https://hiring.cafe",
    "Referer": "https://hiring.cafe/",
}

FACETS_URL = settings.HIRING_CAFE_FACETS_URL
BASE_URL = settings.HIRING_CAFE_BASE_URL
OUTPUT_CSV = settings.HIRING_CAFE_OUTPUT_CSV
def fetch_all_companies():
    payload = {
        "facetType": "company_name",
        "query": "",
        "searchState": {
            "locations": [
                {
                    "id": "FxY1yZQBoEtHp_8UEq7V",
                    "types": ["country"],
                    "address_components": [
                        {
                            "long_name": "United States",
                            "short_name": "US",
                            "types": ["country"]
                        }
                    ],
                    "formatted_address": "United States",
                    "population": *********,
                    "workplace_types": [],
                    "options": {
                        "flexible_regions": ["anywhere_in_continent", "anywhere_in_world"]
                    }
                }
            ]
        }
    }

    res = requests.post(FACETS_URL, json=payload, headers=HEADERS)
    res.raise_for_status()
    data = res.json()
    logger.debug("Facets response: %s", data)  # See the full response

    companies = []
    companies = data.get("suggestions", [])
    # The company list is usually inside data['facets'], find the right facet
    for facet in data.get("facets", []):
        if facet.get("facetType") == "company_name":
            # company names are usually inside facet['values']
            companies = [item["label"] for item in facet.get("values", [])]
            break

    return companies


async def collect_view_all_links(page, company_names):
    view_all_records = []
    for company in company_names:
        logger.info("Searching company: %s", company)
        try:
            await page.goto("https://hiring.cafe/?searchState=%7B%22locations%22%3A%5B%7B%22id%22%3A%22FxY1yZQBoEtHp_8UEq7V%22%2C%22types%22%3A%5B%22country%22%5D%2C%22address_components%22%3A%5B%7B%22long_name%22%3A%22United+States%22%2C%22short_name%22%3A%22US%22%2C%22types%22%3A%5B%22country%22%5D%7D%5D%2C%22formatted_address%22%3A%22United+States%22%2C%22population%22%3A*********%2C%22workplace_types%22%3A%5B%5D%2C%22options%22%3A%7B%22flexible_regions%22%3A%5B%22anywhere_in_continent%22%2C%22anywhere_in_world%22%5D%7D%7D%5D%7D", timeout=60000)
            await page.wait_for_timeout(3000)

            await page.click("//button[contains(text(), 'Company')]")
            await page.wait_for_selector(".ant-modal-content", timeout=10000)

            input_box = await page.wait_for_selector("div.css-19bb58m input")
            await input_box.click()
            await input_box.fill(company)
            await page.wait_for_timeout(2000)
            await page.keyboard.press("Enter")
            await page.wait_for_timeout(1000)

            apply_btn = await page.wait_for_selector("button.w-1\/2.bg-pink-500.text-white.rounded.py-2", timeout=10000)
            await apply_btn.scroll_into_view_if_needed()
            await page.wait_for_timeout(500)
            await apply_btn.click(force=True)
            await page.wait_for_timeout(3000)

            if await page.locator("text=You're all caught up!").is_visible():
                logger.info("No jobs found for %s", company)
                continue

            links = await page.locator("//a[contains(text(), 'View all')]").all()
            for link in links:
                href = await link.get_attribute('href')
                if href:
                    view_all_records.append((company, BASE_URL + href))
                    logger.info("View all link: %s", href)

        except Exception as e:
            logger.error("Error processing company '%s': %s", company, e)
    return view_all_records


async def extract_job_links(page, url):
    full_view_links = []
    await page.goto(url)
    await page.wait_for_timeout(3000)

    last_count = -1
    tries = 0
    while tries < 10:
        cards = await page.locator("div.relative.xl\\:z-10").all()
        count = len(cards)
        if count == last_count:
            tries += 1
        else:
            tries = 0
            last_count = count
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight);")
        await page.wait_for_timeout(2000)

    visited = set()
    cards = await page.locator("div.relative.xl\\:z-10").all()
    for card in cards:
        try:
            text = await card.inner_text()
            if not text.strip() or text in visited:
                continue
            visited.add(text)

            try:
                await page.click("header div.flex.items-center.space-x-2 > button")
                await page.wait_for_timeout(1000)
            except Exception:
                pass

            await card.scroll_into_view_if_needed()
            await page.wait_for_timeout(500)
            await card.click()
            await page.wait_for_timeout(300)
            await card.click()
            await page.wait_for_timeout(1000)

            job_url = None
            try:
                link_el = await page.wait_for_selector("a.text-xs.flex.items-center.space-x-2.p-1\\.5", timeout=5000)
                job_url = await link_el.get_attribute("href")
            except Exception as e:
                logger.error("Could not extract job link: %s", e)

            website_url = None
            try:
                button = await page.query_selector("button.flex.items-center.space-x-1.rounded.border.border-violet-300.text-violet-500.px-2.text-xs.font-bold.w-fit")
                if button:
                    async with page.context.expect_page() as new_tab:
                        await button.click()
                    website_page = await new_tab.value
                    await website_page.wait_for_load_state()
                    website_url = website_page.url
                    await website_page.close()
            except Exception as we:
                logger.error("Failed to extract company website URL: %s", we)

            if job_url:
                full_view_links.append((job_url, website_url))
                logger.info("Job: %s", job_url)

            try:
                await page.click("header div.flex.items-center.space-x-2 > button")
                await page.wait_for_timeout(500)
            except Exception:
                pass

        except Exception as e:
            logger.error("Error processing card: %s", e)

    return full_view_links


def save_jobs_to_csv_and_db(db: PostgresLoader, output_csv, company_name, job_links):
    existing_job_urls = set()

    if not os.path.exists(output_csv):
        os.makedirs(os.path.dirname(output_csv), exist_ok=True)

    if os.path.exists(output_csv):
        with open(output_csv, "r", encoding="utf-8") as f:
            reader = csv.DictReader(f)
            for row in reader:
                existing_job_urls.add(row["job_url"])

    # Find the first real company website (not hiring.cafe) from job_links
    real_urls = [url for _, url in job_links if url and 'hiring.cafe' not in url]
    if not real_urls:
        logger.warning("No real company website found for %s. Skipping DB insertion.", company_name)
        return
    company_url = real_urls[0]
    company_id, _ = db.get_or_create_company(company_name, company_url, {})

    with open(output_csv, "a", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        if os.stat(output_csv).st_size == 0:
            writer.writerow(["company_id", "company_url", "job_url", "content_selectors"])
        content_selectors_written = False
        job_urls_to_save = set()
        for job_url, job_website_url in job_links:
            if job_url in existing_job_urls:
                continue
            content_selectors = (
                '{"title": "h2.font-extrabold.text-3xl.text-gray-800.mb-4",'
                '"location": "div.flex.space-x-2 > span",'
                '"job_description": "div.flex.flex-col",'
                '"job_posted_date": "span.text-xs.text-cyan-700.font-bold.flex-none",'
                '"Skills": "div.flex.flex-col.space-y-3 > span:nth-of-type(2)"}'
                if not content_selectors_written else ""
            )
            content_selectors_written = True
            writer.writerow([company_id, job_website_url or "", job_url, content_selectors])
            existing_job_urls.add(job_url)
            job_urls_to_save.add(job_url)
        # Update jd_links in the company table
        if job_urls_to_save:
            db.process_new_links(job_urls_to_save, company_id)


async def main():
    db = PostgresLoader()
    db.connect()

    companies = fetch_all_companies()
    if not companies:
        logger.error("No companies found.")
        return

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()

        view_all_links = await collect_view_all_links(page, companies)

        for company, url in view_all_links:
            logger.info("Extracting jobs for %s", company)
            try:
                job_links = await extract_job_links(page, url)
                save_jobs_to_csv_and_db(db, OUTPUT_CSV, company, job_links)
            except Exception as e:
                logger.error("Failed to extract jobs for %s: %s", company, e)

        await browser.close()
    logger.info("Done! Extracted jobs saved to %s", OUTPUT_CSV)


if __name__ == "__main__":
    asyncio.run(main())
