import asyncio
import csv
import re
import json
import os
import sys
import traceback
import argparse
from urllib.parse import urljoin, urlparse
from config.core.settings import get_settings
from etl.extractor.models.jd_schema import ScrappedJDLinkModel
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError

from etl.loader.load_to_postgres import PostgresLoader
from etl import logger


def safe_int(value, default=0):
    """Safely convert a value to integer with fallback."""
    if not value or value == '':
        return default
    try:
        return int(str(value).strip())
    except (ValueError, TypeError):
        logger.warning(f"Could not convert '{value}' to integer, using default {default}")
        return default

def load_configs_csv(filename: str):
    """
    Loads site configurations from a CSV file.
    It checks for the file in the current working directory first,
    then falls back to the script's directory. This makes it more flexible.
    """
    filepath = None
    # Path 1: Check relative to the current working directory (or if an absolute path is given)
    if os.path.exists(filename):
        filepath = filename
    else:
        # Path 2: Fallback to the directory where the script is located
        script_dir = os.path.dirname(os.path.abspath(__file__))
        path_in_script_dir = os.path.join(script_dir, filename)
        if os.path.exists(path_in_script_dir):
            filepath = path_in_script_dir

    if not filepath:
        # If the file is still not found, raise a clear error and exit.
        script_dir = os.path.dirname(os.path.abspath(__file__))
        error_msg = (
            f"Configuration file '{filename}' not found. "
            f"Please ensure it exists in the current working directory OR "
            f"in the script's directory ({script_dir})."
        )
        logger.error(error_msg)
        # Raising the error provides a clearer traceback and stops the script.
        raise FileNotFoundError(error_msg)

    configs = {}
    logger.info(f"Attempting to load configurations from {filepath}")
    try:
        with open(filepath, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for i, row in enumerate(reader):
                try:
                    def safe_strip(value, default=''):
                        return value.strip() if value else default

                    company_name = safe_strip(row.get('company_name', ''))
                    if company_name:
                        config = {
                            'start_url': safe_strip(row.get('start_url', '')),
                            'job_link_locator': {
                                'strategy': safe_strip(row.get('job_link_locator_strategy', '')),
                                'value': safe_strip(row.get('job_link_locator_value', ''))
                            },
                            'pagination_type': safe_strip(row.get('pagination_type', 'none')),
                            'pagination_locator': {
                                'strategy': safe_strip(row.get('pagination_locator_strategy', '')),
                                'value': safe_strip(row.get('pagination_locator_value', ''))
                            },
                            'base_url': safe_strip(row.get('base_url', '')),
                            'link_filter_keyword': safe_strip(row.get('link_filter_keyword', '')),
                            'link_extraction_method': safe_strip(row.get('link_extraction_method', 'href')),
                            'link_attribute': safe_strip(row.get('link_attribute', '')),
                            'onclick_regex': safe_strip(row.get('onclick_regex', '')),
                            'initial_wait_time': safe_int(row.get('initial_wait_time', '3'), 3),
                            'page_load_timeout': safe_int(row.get('page_load_timeout', '60'), 60),
                            'pagination_wait_time': safe_int(row.get('pagination_wait_time', '5'), 5),
                            'max_pages': safe_int(row.get('max_pages', '1'), 1),
                            'content_selectors': safe_strip(row.get('content_selectors', '{}')),
                            'company_url': safe_strip(row.get('company_url', ''))
                        }

                        # Fix onclick_regex if embedded in link_attribute
                        if config['link_extraction_method'] == 'onclick' and not config.get('onclick_regex', '').strip():
                            link_attr = config.get('link_attribute', '').strip()
                            if link_attr and '(' in link_attr and ')' in link_attr:
                                config['onclick_regex'] = link_attr
                                logger.info(f"     Fixed onclick_regex for {company_name}: moved from link_attribute")

                        # Fix max_pages if misplaced in initial_wait_timeout
                        if config.get('pagination_type') in ['next_button', 'scroll', 'load_more'] and config.get('max_pages', 1) <= 1:
                            initial_wait_timeout = safe_int(row.get('initial_wait_timeout', '1'), 1)
                            if initial_wait_timeout > 10:
                                config['max_pages'] = initial_wait_timeout
                                logger.info(f"     Fixed max_pages for {company_name}: {initial_wait_timeout} (moved from initial_wait_timeout)")

                        # Fix escaped characters in XPath
                        if config['pagination_locator'].get('strategy') == 'xpath':
                            xpath_value = config['pagination_locator'].get('value', '')
                            if '\\,' in xpath_value or '\\.' in xpath_value:
                                fixed_xpath = xpath_value.replace('\\,', ',').replace('\\.', '.')
                                config['pagination_locator']['value'] = fixed_xpath
                                logger.info(f"     Fixed escaped characters in pagination XPath for {company_name}: '{fixed_xpath}'")

                        if config['job_link_locator'].get('strategy') == 'xpath':
                            xpath_value = config['job_link_locator'].get('value', '')
                            if '\\,' in xpath_value or '\\.' in xpath_value:
                                fixed_xpath = xpath_value.replace('\\,', ',').replace('\\.', '.')
                                config['job_link_locator']['value'] = fixed_xpath
                                logger.info(f"     Fixed escaped characters in job link XPath for {company_name}: '{fixed_xpath}'")

                        configs[company_name] = config
                except Exception as row_error:
                    logger.warning(f"Error processing row {i+1}: {row_error}")
                    continue
        
        logger.info(f"Successfully loaded {len(configs)} configurations from {filepath}")
        return configs
        
    except Exception as e:
        # The initial FileNotFoundError is handled above, this catches other file reading errors
        logger.error(f"Error reading or parsing configurations from {filepath}: {e}")
        return {}

def get_playwright_selector(locator_config):
    """Convert locator configuration to Playwright selector."""
    if not locator_config or not locator_config.get('value'):
        return None
    
    strategy = locator_config.get('strategy', 'css').lower()
    value = locator_config['value']
    
    if strategy == 'css':
        return f"css={value}"
    elif strategy == 'xpath':
        return f"xpath={value}"
    elif strategy == 'text':
        return f"text={value}"
    else:
        return f"css={value}"  # Default to CSS

def is_career_page_url(url):
    """Filter out career page URLs that are not actual job links."""
    if not url:
        return True  # Filter out empty URLs

    # Hardcoded patterns to filter out career pages (not actual job links)
    career_page_patterns = [
        '/careers/careers/',  # Generic duplicate careers path
        '/careers/',  # Generic careers page ending
        '/career/',   # Generic career page ending
        '/jobs/',     # Generic jobs page ending
        '/opportunities/', # Generic opportunities page ending
        '/openings/', # Generic openings page ending
    ]

    # Check if URL ends with any career page pattern (indicating it's a listing page, not a job)
    for pattern in career_page_patterns:
        if url.endswith(pattern):
            return True

    # Additional specific company filters
    if 'ideamaker.agency' in url:
        # Filter out the main careers page but keep specific job pages
        if url.endswith('/careers/careers/') or url.endswith('/careers/'):
            return True

    # Star.global specific filters
    if 'star.global' in url:
        # Filter out main careers page
        if url.endswith('/careers/'):
            return True
        # Filter out location and team filter pages (these are not job links)
        if '/careers/careers/?location=' in url or '/careers/careers/?team=' in url:
            return True

    # Filter out application portal URLs (not actual job links)
    # Exception: Some companies legitimately use these platforms as their primary job portal
    legitimate_workable_companies = ['azumo']  # Companies that use Workable as primary platform
    legitimate_bamboohr_companies = ['magmalabs']  # Companies that use BambooHR as primary platform

    # Check if this is a legitimate Workable URL for companies that use it as primary platform
    if 'apply.workable.com' in url:
        for company in legitimate_workable_companies:
            if f'/{company}/j/' in url:  # This is a legitimate job link for this company
                return False  # Don't filter out

    # Check if this is a legitimate BambooHR URL for companies that use it as primary platform
    if 'bamboohr.com' in url:
        for company in legitimate_bamboohr_companies:
            if f'{company}.bamboohr.com' in url:  # This is a legitimate job link for this company
                return False  # Don't filter out

    application_portal_patterns = [
        'applytojob.com',
        'apply.workable.com',  # Will be filtered unless in legitimate list above
        'jobs.lever.co',
        'greenhouse.io',
        'workday.com',
        'icims.com',
        'smartrecruiters.com',
        'bamboohr.com',
        'jobvite.com',
        'recruiting.ultipro.com',
        'careers-',  # Generic careers subdomain pattern
    ]

    for pattern in application_portal_patterns:
        if pattern in url:
            return True

    return False  # Keep the URL (it's likely a job link)

def is_base_url_only(url: str) -> bool:
    """
    Filter out base URLs that are just domain names without meaningful paths
    Returns True if it's a base URL that should be filtered out
    """
    if not url or url.strip() == "":
        return True

    from urllib.parse import urlparse

    try:
        parsed = urlparse(url)
        path = parsed.path.rstrip('/')

        # Filter out URLs that are just domain names (no meaningful path)
        if path == '' or path == '/':
            logger.info(f"  FILTERED (base URL): {url}")
            return True

        # Filter out common base paths that aren't job-specific
        base_paths = ['/home', '/index', '/main', '/default']
        if path.lower() in base_paths:
            logger.info(f"  FILTERED (base path): {url}")
            return True

        return False

    except Exception as e:
        logger.debug(f"Error parsing URL {url}: {e}")
        return False

def resolve_url(base_url, relative_url):
    """Resolve relative URL to absolute URL with hardcoded fixes for specific companies."""
    if not relative_url:
        return None

    if relative_url.startswith(('http://', 'https://')):
        return relative_url

    if relative_url.startswith('//'):
        return f"https:{relative_url}"

    # Hardcoded fixes for specific companies
    if base_url:
        # Fix for MEV - prevent duplicate /careers/ path
        if 'mev.com' in base_url and '/careers/' in relative_url:
            if relative_url.startswith('/careers/'):
                return f"https://mev.com{relative_url}"
            else:
                return urljoin(base_url.rstrip('/') + '/', relative_url.lstrip('/'))

        # Fix for Ivanti - prevent duplicate /company/careers/ path
        elif 'ivanti.com' in base_url and '/company/careers/' in relative_url:
            if relative_url.startswith('/company/careers/'):
                return f"https://www.ivanti.com{relative_url}"
            else:
                return urljoin(base_url.rstrip('/') + '/', relative_url.lstrip('/'))

        # Default URL resolution for other companies
        else:
            return urljoin(base_url.rstrip('/') + '/', relative_url.lstrip('/'))

    return relative_url

async def handle_next_button_pagination(page, config, all_job_links):
    """Handle next button pagination with intelligent stopping."""
    max_pages = config.get('max_pages', 100)  # Increased default to handle large sites
    pagination_wait_time = min(config.get('pagination_wait_time', 5), 2) * 1000  
    pagination_selector = get_playwright_selector(config.get('pagination_locator'))

    if not pagination_selector:
        logger.warning("Next button pagination requested but no pagination_locator configured")
        return

    job_link_selector = get_playwright_selector(config["job_link_locator"])
    link_extraction_method = config.get("link_extraction_method", "href")
    link_filter_keyword = config.get("link_filter_keyword")
    base_url_config = config.get("base_url") or config['start_url']

    consecutive_empty_pages = 0
    max_consecutive_empty = 3  # Stop after 3 consecutive pages with no new links

    for page_num in range(2, max_pages + 1):
        try:
            # Check if next button exists and is clickable
            next_button = page.locator(pagination_selector).first

            # Debug: Check how many pagination elements we found
            all_pagination_elements = await page.locator(pagination_selector).all()
            logger.info(f"Found {len(all_pagination_elements)} pagination elements on page {page_num-1}")

            if not await next_button.is_visible():
                logger.info(f"Next button not visible on page {page_num-1}, stopping pagination")
                break

            if not await next_button.is_enabled():
                logger.info(f"Next button not enabled on page {page_num-1}, stopping pagination")
                break

            # Debug: Log what we're about to click
            next_button_text = await next_button.inner_text()
            next_button_href = await next_button.get_attribute('href')
            logger.info(f"About to click next button: text='{next_button_text}', href='{next_button_href}'")
            
            # Handle any popups that might be blocking the click
            await handle_common_popups(page, "pagination")

            # Click next button with force if needed
            try:
                await next_button.click()
            except Exception as click_err:
                logger.warning(f"Normal click failed: {click_err}, trying force click...")
                try:
                    await next_button.click(force=True)
                except Exception as force_err:
                    logger.error(f"Force click also failed: {force_err}")
                    break

            await page.wait_for_timeout(pagination_wait_time)
            
            # Extract job links from new page
            try:
                await page.locator(job_link_selector).first.wait_for(state='attached', timeout=10000)
                job_elements = await page.locator(job_link_selector).all()
                
                page_links = set()
                for element in job_elements:
                    try:
                        if link_extraction_method == "href" or link_extraction_method == "attribute":
                            raw_link = await element.get_attribute("href")
                        elif link_extraction_method == "data-value":
                            raw_link = await element.get_attribute("data-value")
                            if raw_link and base_url_config:
                                raw_link = base_url_config.rstrip('/') + raw_link
                        elif link_extraction_method == "data-jobid":
                            job_id = await element.get_attribute("data-jobid")
                            if job_id and base_url_config:
                                raw_link = f"{base_url_config.rstrip('/')}/job-openings/?jobId={job_id}"
                            elif job_id:
                                # Fallback to current page URL if no base_url
                                current_url = page.url
                                raw_link = f"{current_url.split('?')[0]}?jobId={job_id}"
                        elif link_extraction_method == "onclick":
                            onclick_value = await element.get_attribute("onclick")
                            if onclick_value and config.get("onclick_regex"):
                                import re
                                try:
                                    match = re.search(config["onclick_regex"], onclick_value)
                                    if match:
                                        # Handle multi-group regex patterns generically
                                        if len(match.groups()) >= 3:
                                            # For 3+ groups, check if base_url contains smartrecruiters pattern
                                            if base_url_config and "smartrecruiters.com" in base_url_config:
                                                _, job_id, slug = match.groups()
                                                company_part = base_url_config.split('smartrecruiters.com/')[-1].split('/')[0]
                                                raw_link = f"https://www.smartrecruiters.com/{company_part}/{job_id}/{slug}"
                                            else:
                                                # Generic 3-group handling
                                                raw_link = match.group(1)
                                                if raw_link and base_url_config:
                                                    raw_link = base_url_config.rstrip('/') + '/' + raw_link.lstrip('/')
                                        elif len(match.groups()) >= 1:
                                            # Standard single group extraction
                                            raw_link = match.group(1)
                                            if raw_link and base_url_config:
                                                raw_link = base_url_config.rstrip('/') + '/' + raw_link.lstrip('/')
                                except re.error as regex_err:
                                    logger.warning(f"Invalid onclick regex in pagination: {regex_err}")
                        else:
                            raw_link = await element.get_attribute("href")
                        
                        absolute_link = resolve_url(base_url_config, raw_link)

                        if absolute_link and not is_career_page_url(absolute_link) and not is_base_url_only(absolute_link):
                            if link_filter_keyword:
                                if link_filter_keyword in absolute_link:
                                    page_links.add(absolute_link)
                            else:
                                page_links.add(absolute_link)
                    except Exception as e:
                        logger.debug(f"Error processing element on page {page_num}: {e}")
                
                # Track total before adding new links to detect actual new unique links
                previous_total = len(all_job_links)
                all_job_links.update(page_links)
                new_unique_links = len(all_job_links) - previous_total

                logger.info(f"Page {page_num}: Found {len(page_links)} raw links, {new_unique_links} new unique links (total: {len(all_job_links)})")

                if new_unique_links == 0:
                    consecutive_empty_pages += 1
                    logger.info(f"No new unique links found on page {page_num} (consecutive empty: {consecutive_empty_pages})")
                    if consecutive_empty_pages >= max_consecutive_empty:
                        logger.info(f"Stopping pagination after {consecutive_empty_pages} consecutive pages with no new unique links")
                        break
                else:
                    consecutive_empty_pages = 0  # Reset counter when we find new unique links
                    
            except PlaywrightTimeoutError:
                logger.warning(f"Timeout waiting for job links on page {page_num}")
                break
                
        except Exception as e:
            logger.error(f"Error during pagination on page {page_num}: {e}")
            break

async def handle_next_button_url_pagination(page, config, all_job_links):
    """Handle next button URL-based pagination (navigates to href instead of clicking)."""
    max_pages = config.get('max_pages', 100)
    pagination_wait_time = min(config.get('pagination_wait_time', 5), 2) * 1000 
    pagination_selector = get_playwright_selector(config.get('pagination_locator'))

    if not pagination_selector:
        logger.warning("Next button URL pagination requested but no pagination_locator configured")
        return

    job_link_selector = get_playwright_selector(config["job_link_locator"])
    link_extraction_method = config.get("link_extraction_method", "href")
    link_filter_keyword = config.get("link_filter_keyword")
    base_url_config = config.get("base_url") or config['start_url']

    consecutive_empty_pages = 0
    max_consecutive_empty = 3

    for page_num in range(2, max_pages + 1):
        try:
            # Check if next button exists and get its href
            next_button = page.locator(pagination_selector).first
            if not await next_button.is_visible():
                logger.info(f"Next button not visible on page {page_num-1}, stopping pagination")
                break

            next_url = await next_button.get_attribute('href')
            if not next_url or next_url == "javascript:void(0);" or next_url == "#":
                logger.info(f"Next button has no valid href on page {page_num-1}, stopping pagination")
                break

            # Resolve the URL
            resolved_next_url = resolve_url(page.url, next_url)
            if not resolved_next_url or resolved_next_url == page.url:
                logger.info(f"Next URL is same as current page on page {page_num-1}, stopping pagination")
                break

            # Navigate to next page
            logger.info(f"Navigating to next page URL: {resolved_next_url}")
            await page.goto(resolved_next_url, wait_until='domcontentloaded', timeout=60000)
            await page.wait_for_timeout(pagination_wait_time)

            # Extract job links from new page
            try:
                await page.locator(job_link_selector).first.wait_for(state='attached', timeout=10000)
                job_elements = await page.locator(job_link_selector).all()

                page_links = set()
                for element in job_elements:
                    try:
                        if link_extraction_method == "href" or link_extraction_method == "attribute":
                            raw_link = await element.get_attribute("href")
                        elif link_extraction_method == "get_attribute":
                            raw_link = await element.get_attribute("href")
                        else:
                            raw_link = await element.get_attribute("href")

                        absolute_link = resolve_url(base_url_config, raw_link)

                        if absolute_link and not is_career_page_url(absolute_link) and not is_base_url_only(absolute_link):
                            if link_filter_keyword:
                                if link_filter_keyword in absolute_link:
                                    page_links.add(absolute_link)
                            else:
                                page_links.add(absolute_link)
                    except Exception as e:
                        logger.debug(f"Error processing element on page {page_num}: {e}")

                # Track total before adding new links to detect actual new unique links
                previous_total = len(all_job_links)
                all_job_links.update(page_links)
                new_unique_links = len(all_job_links) - previous_total

                logger.info(f"Page {page_num}: Found {len(page_links)} raw links, {new_unique_links} new unique links (total: {len(all_job_links)})")

                if new_unique_links == 0:
                    consecutive_empty_pages += 1
                    logger.info(f"No new unique links found on page {page_num} (consecutive empty: {consecutive_empty_pages})")
                    if consecutive_empty_pages >= max_consecutive_empty:
                        logger.info(f"Stopping pagination after {consecutive_empty_pages} consecutive pages with no new unique links")
                        break
                else:
                    consecutive_empty_pages = 0  # Reset counter when we find new unique links

            except PlaywrightTimeoutError:
                logger.warning(f"Timeout waiting for job links on page {page_num}")
                break

        except Exception as e:
            logger.error(f"Error during URL pagination on page {page_num}: {e}")
            break

async def handle_scroll_pagination(page, config, all_job_links):
    """Handle scroll-based pagination with enhanced strategies."""
    max_pages = config.get('max_pages', 5)
    pagination_wait_time = config.get('pagination_wait_time', 3) * 1000

    job_link_selector = get_playwright_selector(config["job_link_locator"])
    link_extraction_method = config.get("link_extraction_method", "href")
    link_filter_keyword = config.get("link_filter_keyword")
    base_url_config = config.get("base_url") or config['start_url']

    # Check if there's a specific pagination button (like "Load More" or "Next")
    pagination_selector = get_playwright_selector(config.get('pagination_locator'))

    no_new_content_count = 0

    for scroll_attempt in range(max_pages):
        try:
            logger.info(f"Scroll attempt {scroll_attempt + 1}/{max_pages}")

            # Get current page height before scrolling
            prev_height = await page.evaluate("document.body.scrollHeight")

            # Try multiple scroll strategies
            if pagination_selector:
                # Strategy 1: Look for pagination button first
                try:
                    pagination_button = page.locator(pagination_selector).first
                    if await pagination_button.is_visible(timeout=5000):
                        logger.info("Found pagination button, clicking...")
                        await pagination_button.click()
                        await page.wait_for_timeout(pagination_wait_time)
                    else:
                        logger.info("Pagination button not visible, trying scroll...")
                        await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                        await page.wait_for_timeout(pagination_wait_time)
                except Exception as e:
                    logger.debug(f"Pagination button click failed: {e}, falling back to scroll")
                    await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                    await page.wait_for_timeout(pagination_wait_time)
            else:
                # Strategy 2: Multiple scroll approaches
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await page.wait_for_timeout(pagination_wait_time // 2)

                # Try smooth scrolling
                await page.evaluate("""
                    window.scrollTo({
                        top: document.body.scrollHeight,
                        behavior: 'smooth'
                    });
                """)
                await page.wait_for_timeout(pagination_wait_time // 2)

            # Wait for potential new content to load
            try:
                await page.wait_for_function(
                    f"document.body.scrollHeight > {prev_height}",
                    timeout=pagination_wait_time
                )
                logger.info("New content detected after scroll")
            except Exception:
                logger.debug("No height change detected, checking for new elements...")

            # Extract all job links after scroll
            try:
                job_elements = await page.locator(job_link_selector).all()

                current_links = set()
                for element in job_elements:
                    try:
                        if link_extraction_method == "href" or link_extraction_method == "attribute":
                            raw_link = await element.get_attribute("href")
                        elif link_extraction_method == "data-value":
                            raw_link = await element.get_attribute("data-value")
                            if raw_link and base_url_config:
                                raw_link = base_url_config.rstrip('/') + raw_link
                        elif link_extraction_method == "data-jobid":
                            job_id = await element.get_attribute("data-jobid")
                            if job_id and base_url_config:
                                raw_link = f"{base_url_config.rstrip('/')}/job-openings/?jobId={job_id}"
                            elif job_id:
                                # Fallback to current page URL if no base_url
                                current_url = page.url
                                raw_link = f"{current_url.split('?')[0]}?jobId={job_id}"
                        elif link_extraction_method == "onclick":
                            onclick_value = await element.get_attribute("onclick")
                            if onclick_value and config.get("onclick_regex"):
                                import re
                                try:
                                    match = re.search(config["onclick_regex"], onclick_value)
                                    if match:
                                        # Handle multi-group regex patterns generically
                                        if len(match.groups()) >= 3:
                                            # For 3+ groups, check if base_url contains smartrecruiters pattern
                                            if base_url_config and "smartrecruiters.com" in base_url_config:
                                                _, job_id, slug = match.groups()
                                                company_part = base_url_config.split('smartrecruiters.com/')[-1].split('/')[0]
                                                raw_link = f"https://www.smartrecruiters.com/{company_part}/{job_id}/{slug}"
                                            else:
                                                # Generic 3-group handling
                                                raw_link = match.group(1)
                                                if raw_link and base_url_config:
                                                    raw_link = base_url_config.rstrip('/') + '/' + raw_link.lstrip('/')
                                        elif len(match.groups()) >= 1:
                                            # Standard single group extraction
                                            raw_link = match.group(1)
                                            if raw_link and base_url_config:
                                                raw_link = base_url_config.rstrip('/') + '/' + raw_link.lstrip('/')
                                except re.error as regex_err:
                                    logger.warning(f"Invalid onclick regex in scroll pagination: {regex_err}")
                        else:
                            raw_link = await element.get_attribute("href")

                        absolute_link = resolve_url(base_url_config, raw_link)

                        if absolute_link and not is_career_page_url(absolute_link) and not is_base_url_only(absolute_link):
                            if link_filter_keyword:
                                if link_filter_keyword in absolute_link:
                                    current_links.add(absolute_link)
                            else:
                                current_links.add(absolute_link)
                    except Exception as e:
                        logger.debug(f"Error processing element during scroll {scroll_attempt + 1}: {e}")

                new_links = current_links - all_job_links
                all_job_links.update(new_links)

                logger.info(f"Scroll {scroll_attempt + 1}: Found {len(new_links)} new links (total: {len(all_job_links)})")

                if len(new_links) == 0:
                    no_new_content_count += 1
                    if no_new_content_count >= 2:  # Stop after 2 consecutive attempts with no new content
                        logger.info("No new links found after 2 consecutive scrolls, stopping")
                        break
                else:
                    no_new_content_count = 0  # Reset counter if we found new links

            except Exception as e:
                logger.error(f"Error extracting links during scroll {scroll_attempt + 1}: {e}")
                break

        except Exception as e:
            logger.error(f"Error during scroll pagination attempt {scroll_attempt + 1}: {e}")
            break

async def handle_numbered_pagination(page, config, all_job_links):
    """Handle numbered pagination (1, 2, 3, 4, etc.)."""
    max_pages = config.get('max_pages', 10)
    pagination_wait_time = min(config.get('pagination_wait_time', 5), 2) * 1000 
    pagination_selector = get_playwright_selector(config.get('pagination_locator'))

    if not pagination_selector:
        logger.warning("Numbered pagination requested but no pagination_locator configured")
        return

    job_link_selector = get_playwright_selector(config["job_link_locator"])
    link_extraction_method = config.get("link_extraction_method", "href")
    link_filter_keyword = config.get("link_filter_keyword")
    base_url_config = config.get("base_url") or config['start_url']

    consecutive_empty_pages = 0
    max_consecutive_empty = 3

    for page_num in range(2, max_pages + 1):
        try:
            # Look for the specific page number button
            page_number_selector = f"{pagination_selector}:has-text('{page_num}')"
            page_button = page.locator(page_number_selector).first

            if not await page_button.is_visible():
                logger.info(f"Page {page_num} button not visible, stopping numbered pagination")
                break

            if not await page_button.is_enabled():
                logger.info(f"Page {page_num} button not enabled, stopping numbered pagination")
                break

            # Click the page number
            logger.info(f"Clicking page {page_num} button")
            try:
                await page_button.click()
            except Exception as click_error:
                logger.warning(f"Error clicking page {page_num} button: {click_error}")
                try:
                    await page_button.click(force=True)
                    logger.info(f"Force clicked page {page_num} button")
                except Exception as force_click_error:
                    logger.error(f"Force click also failed for page {page_num}: {force_click_error}")
                    break

            await page.wait_for_timeout(pagination_wait_time)

            # Extract job links from new page
            try:
                await page.locator(job_link_selector).first.wait_for(state='attached', timeout=10000) 
                job_elements = await page.locator(job_link_selector).all()

                page_links = set()
                for element in job_elements:
                    try:
                        if link_extraction_method == "href" or link_extraction_method == "attribute":
                            raw_link = await element.get_attribute("href")
                        elif link_extraction_method == "get_attribute":
                            raw_link = await element.get_attribute("href")
                        else:
                            raw_link = await element.get_attribute("href")

                        absolute_link = resolve_url(base_url_config, raw_link)

                        if absolute_link and not is_career_page_url(absolute_link) and not is_base_url_only(absolute_link):
                            if link_filter_keyword:
                                if link_filter_keyword in absolute_link:
                                    page_links.add(absolute_link)
                            else:
                                page_links.add(absolute_link)
                    except Exception as e:
                        logger.debug(f"Error processing element on page {page_num}: {e}")

                # Track total before adding new links to detect actual new unique links
                previous_total = len(all_job_links)
                all_job_links.update(page_links)
                new_unique_links = len(all_job_links) - previous_total

                logger.info(f"Page {page_num}: Found {len(page_links)} raw links, {new_unique_links} new unique links (total: {len(all_job_links)})")

                if new_unique_links == 0:
                    consecutive_empty_pages += 1
                    logger.info(f"No new unique links found on page {page_num} (consecutive empty: {consecutive_empty_pages})")
                    if consecutive_empty_pages >= max_consecutive_empty:
                        logger.info(f"Stopping numbered pagination after {consecutive_empty_pages} consecutive pages with no new unique links")
                        break
                else:
                    consecutive_empty_pages = 0  # Reset counter when we find new unique links

            except PlaywrightTimeoutError:
                logger.warning(f"Timeout waiting for job links on page {page_num}")
                break

        except Exception as e:
            logger.error(f"Error during numbered pagination on page {page_num}: {e}")
            break

async def handle_url_pagination(page, config, all_job_links):
    """Handle URL-based pagination by constructing URLs with page parameters."""
    max_pages = config.get('max_pages', 10)
    pagination_wait_time = min(config.get('pagination_wait_time', 5), 2) * 1000 
    base_url = config['start_url']

    job_link_selector = get_playwright_selector(config["job_link_locator"])
    link_extraction_method = config.get("link_extraction_method", "href")
    link_filter_keyword = config.get("link_filter_keyword")
    base_url_config = config.get("base_url") or config['start_url']

    consecutive_empty_pages = 0
    max_consecutive_empty = 3

    # Try different URL patterns that Miro might use
    url_patterns = [
        f"{base_url}?page={{page_num}}",
        f"{base_url}?offset={{offset}}",
        f"{base_url}?p={{page_num}}",
        f"{base_url}?pageNumber={{page_num}}",
        f"{base_url}?currentPage={{page_num}}"
    ]

    for page_num in range(2, max_pages + 1):
        page_found = False

        for pattern in url_patterns:
            try:
                if "offset" in pattern:
                    # For offset-based pagination, calculate offset (assuming 12 jobs per page based on what we saw)
                    offset = (page_num - 1) * 12
                    next_url = pattern.format(offset=offset)
                else:
                    next_url = pattern.format(page_num=page_num)

                logger.info(f"Trying URL pattern: {next_url}")

                # Navigate to the constructed URL
                await page.goto(next_url, wait_until='domcontentloaded', timeout=60000)
                await page.wait_for_timeout(pagination_wait_time)

                # Check if we got a valid page (not 404 or error)
                page_title = await page.title()
                if "404" in page_title or "error" in page_title.lower() or "not found" in page_title.lower():
                    logger.debug(f"Page {page_num} returned error page with pattern {pattern}")
                    continue

                # Try to extract job links
                try:
                    await page.locator(job_link_selector).first.wait_for(state='attached', timeout=10000)
                    job_elements = await page.locator(job_link_selector).all()

                    if not job_elements:
                        logger.debug(f"No job elements found on page {page_num} with pattern {pattern}")
                        continue

                    page_links = set()
                    for element in job_elements:
                        try:
                            if link_extraction_method == "href" or link_extraction_method == "attribute":
                                raw_link = await element.get_attribute("href")
                            elif link_extraction_method == "get_attribute":
                                raw_link = await element.get_attribute("href")
                            else:
                                raw_link = await element.get_attribute("href")

                            absolute_link = resolve_url(base_url_config, raw_link)

                            if absolute_link and not is_career_page_url(absolute_link) and not is_base_url_only(absolute_link):
                                if link_filter_keyword:
                                    if link_filter_keyword in absolute_link:
                                        page_links.add(absolute_link)
                                else:
                                    page_links.add(absolute_link)
                        except Exception as e:
                            logger.debug(f"Error processing element on page {page_num}: {e}")

                    # Track total before adding new links to detect actual new unique links
                    previous_total = len(all_job_links)
                    all_job_links.update(page_links)
                    new_unique_links = len(all_job_links) - previous_total

                    logger.info(f"Page {page_num}: Found {len(page_links)} raw links, {new_unique_links} new unique links (total: {len(all_job_links)})")

                    if new_unique_links == 0:
                        consecutive_empty_pages += 1
                        logger.info(f"No new unique links found on page {page_num} (consecutive empty: {consecutive_empty_pages})")
                        if consecutive_empty_pages >= max_consecutive_empty:
                            logger.info(f"Stopping URL pagination after {consecutive_empty_pages} consecutive pages with no new unique links")
                            return
                    else:
                        consecutive_empty_pages = 0  # Reset counter when we find new unique links
                        page_found = True
                        break  

                except PlaywrightTimeoutError:
                    logger.debug(f"Timeout waiting for job links on page {page_num} with pattern {pattern}")
                    continue

            except Exception as e:
                logger.debug(f"Error trying URL pattern {pattern} for page {page_num}: {e}")
                continue

        if not page_found:
            logger.info(f"No working URL pattern found for page {page_num}, stopping URL pagination")
            break

async def handle_hash_pagination(page, config, all_job_links):
    """Handle hash-based pagination (#page=1, #page=2, etc.)."""
    max_pages = config.get('max_pages', 20)
    pagination_wait_time = min(config.get('pagination_wait_time', 5), 2) * 1000  
    base_url = config['start_url'].rstrip('/')

    job_link_selector = get_playwright_selector(config["job_link_locator"])
    link_extraction_method = config.get("link_extraction_method", "href")
    link_filter_keyword = config.get("link_filter_keyword")
    base_url_config = config.get("base_url") or config['start_url']

    consecutive_empty_pages = 0
    max_consecutive_empty = 3

    # Try different hash-based URL patterns
    hash_patterns = [
        f"{base_url}#page={{page_num}}",
        f"{base_url}/#page={{page_num}}",
        f"{base_url}#p={{page_num}}",
        f"{base_url}/#p={{page_num}}",
        f"{base_url}#pageNumber={{page_num}}",
        f"{base_url}/#pageNumber={{page_num}}"
    ]

    for page_num in range(2, max_pages + 1):
        page_found = False

        for pattern in hash_patterns:
            try:
                next_url = pattern.format(page_num=page_num)
                logger.info(f"Trying hash URL pattern: {next_url}")

                # Navigate to the hash URL
                await page.goto(next_url, wait_until='domcontentloaded', timeout=60000)
                await page.wait_for_timeout(pagination_wait_time)

                # Wait for JavaScript to process the hash change (reduced from 2s to 1s)
                await page.wait_for_timeout(1000)

                # Try to extract job links
                try:
                    await page.locator(job_link_selector).first.wait_for(state='attached', timeout=10000)  
                    job_elements = await page.locator(job_link_selector).all()

                    if not job_elements:
                        logger.debug(f"No job elements found on page {page_num} with pattern {pattern}")
                        continue

                    page_links = set()
                    for element in job_elements:
                        try:
                            if link_extraction_method == "href" or link_extraction_method == "attribute":
                                raw_link = await element.get_attribute("href")
                            elif link_extraction_method == "get_attribute":
                                raw_link = await element.get_attribute("href")
                            else:
                                raw_link = await element.get_attribute("href")

                            absolute_link = resolve_url(base_url_config, raw_link)

                            if absolute_link and not is_career_page_url(absolute_link) and not is_base_url_only(absolute_link):
                                if link_filter_keyword:
                                    if link_filter_keyword in absolute_link:
                                        page_links.add(absolute_link)
                                else:
                                    page_links.add(absolute_link)
                        except Exception as e:
                            logger.debug(f"Error processing element on page {page_num}: {e}")

                    # Track total before adding new links to detect actual new unique links
                    previous_total = len(all_job_links)
                    all_job_links.update(page_links)
                    new_unique_links = len(all_job_links) - previous_total

                    logger.info(f"Page {page_num}: Found {len(page_links)} raw links, {new_unique_links} new unique links (total: {len(all_job_links)})")

                    if new_unique_links == 0:
                        consecutive_empty_pages += 1
                        logger.info(f"No new unique links found on page {page_num} (consecutive empty: {consecutive_empty_pages})")
                        if consecutive_empty_pages >= max_consecutive_empty:
                            logger.info(f"Stopping hash pagination after {consecutive_empty_pages} consecutive pages with no new unique links")
                            return
                    else:
                        consecutive_empty_pages = 0  # Reset counter when we find new unique links
                        page_found = True
                        break  # Found working pattern, use it for remaining pages

                except PlaywrightTimeoutError:
                    logger.debug(f"Timeout waiting for job links on page {page_num} with pattern {pattern}")
                    continue

            except Exception as e:
                logger.debug(f"Error trying hash URL pattern {pattern} for page {page_num}: {e}")
                continue

        if not page_found:
            logger.info(f"No working hash URL pattern found for page {page_num}, stopping hash pagination")
            break

async def handle_load_more_pagination(page, config, all_job_links):
    """Handle load more button pagination."""
    max_pages = config.get('max_pages', 10)
    pagination_wait_time = min(config.get('pagination_wait_time', 5), 2) * 1000  
    pagination_selector = get_playwright_selector(config.get('pagination_locator'))
    
    if not pagination_selector:
        logger.warning("Load more pagination requested but no pagination_locator configured")
        return
    
    job_link_selector = get_playwright_selector(config["job_link_locator"])
    link_extraction_method = config.get("link_extraction_method", "href")
    link_filter_keyword = config.get("link_filter_keyword")
    base_url_config = config.get("base_url") or config['start_url']
    
    for load_attempt in range(max_pages):
        try:
            # Check if load more button exists and is clickable
            load_more_button = page.locator(pagination_selector).first
            if not await load_more_button.is_visible():
                logger.info(f"Load more button not visible after attempt {load_attempt}, stopping")
                break
            
            if not await load_more_button.is_enabled():
                logger.info(f"Load more button not enabled after attempt {load_attempt}, stopping")
                break
            
            # Click load more button
            await load_more_button.click()
            await page.wait_for_timeout(pagination_wait_time)
            
            # Extract all job links after loading more
            try:
                job_elements = await page.locator(job_link_selector).all()
                
                current_links = set()
                for element in job_elements:
                    try:
                        if link_extraction_method == "href" or link_extraction_method == "attribute":
                            raw_link = await element.get_attribute("href")
                        elif link_extraction_method == "data-value":
                            raw_link = await element.get_attribute("data-value")
                            if raw_link and base_url_config:
                                raw_link = base_url_config.rstrip('/') + raw_link
                        elif link_extraction_method == "data-jobid":
                            job_id = await element.get_attribute("data-jobid")
                            if job_id and base_url_config:
                                raw_link = f"{base_url_config.rstrip('/')}/job-openings/?jobId={job_id}"
                            elif job_id:
                                # Fallback to current page URL if no base_url
                                current_url = page.url
                                raw_link = f"{current_url.split('?')[0]}?jobId={job_id}"
                        elif link_extraction_method == "onclick":
                            onclick_value = await element.get_attribute("onclick")
                            if onclick_value and config.get("onclick_regex"):
                                import re
                                try:
                                    match = re.search(config["onclick_regex"], onclick_value)
                                    if match:
                                        # Handle multi-group regex patterns generically
                                        if len(match.groups()) >= 3:
                                            # For 3+ groups, check if base_url contains smartrecruiters pattern
                                            if base_url_config and "smartrecruiters.com" in base_url_config:
                                                _, job_id, slug = match.groups()
                                                company_part = base_url_config.split('smartrecruiters.com/')[-1].split('/')[0]
                                                raw_link = f"https://www.smartrecruiters.com/{company_part}/{job_id}/{slug}"
                                            else:
                                                # Generic 3-group handling
                                                raw_link = match.group(1)
                                                if raw_link and base_url_config:
                                                    raw_link = base_url_config.rstrip('/') + '/' + raw_link.lstrip('/')
                                        elif len(match.groups()) >= 1:
                                            # Standard single group extraction
                                            raw_link = match.group(1)
                                            if raw_link and base_url_config:
                                                raw_link = base_url_config.rstrip('/') + '/' + raw_link.lstrip('/')
                                except re.error as regex_err:
                                    logger.warning(f"Invalid onclick regex in load more pagination: {regex_err}")
                        else:
                            raw_link = await element.get_attribute("href")
                        
                        absolute_link = resolve_url(base_url_config, raw_link)

                        if absolute_link and not is_career_page_url(absolute_link) and not is_base_url_only(absolute_link):
                            if link_filter_keyword:
                                if link_filter_keyword in absolute_link:
                                    current_links.add(absolute_link)
                            else:
                                current_links.add(absolute_link)
                    except Exception as e:
                        logger.debug(f"Error processing element during load more {load_attempt + 1}: {e}")
                
                new_links = current_links - all_job_links
                all_job_links.update(new_links)
                
                logger.info(f"Load more {load_attempt + 1}: Found {len(new_links)} new links (total: {len(all_job_links)})")
                
                if len(new_links) == 0:
                    logger.info("No new links found after load more, stopping")
                    break
                    
            except Exception as e:
                logger.error(f"Error extracting links during load more {load_attempt + 1}: {e}")
                break
                
        except Exception as e:
            logger.error(f"Error during load more pagination attempt {load_attempt + 1}: {e}")
            break

async def handle_common_popups(page, site_name):
    """Handle common popups like cookie consent, GDPR, etc."""
    try:
        # Common cookie consent selectors
        cookie_selectors = [
            'button:has-text("Accept")',
            'button:has-text("Accept All")',
            'button:has-text("I Accept")',
            'button:has-text("OK")',
            'button:has-text("Got it")',
            'button:has-text("Continue")',
            '.cc-allow',
            '.cc-dismiss',
            '#cookie-accept',
            '[data-testid="accept-cookies"]',
            '.cookie-accept',
            '.accept-cookies'
        ]

        for selector in cookie_selectors:
            try:
                cookie_button = page.locator(selector).first
                if await cookie_button.is_visible(timeout=2000):  
                    logger.info(f"Found cookie consent button with selector: {selector}")
                    await cookie_button.click()
                    await page.wait_for_timeout(500) 
                    logger.info("Cookie consent handled")
                    break
            except Exception:
                continue

        # Handle GDPR/Privacy popups
        privacy_selectors = [
            'button:has-text("Accept and Close")',
            'button:has-text("I Agree")',
            'button:has-text("Agree")',
            'button:has-text("Accept All Cookies")',
            'button:has-text("Accept Cookies")',
            '.privacy-accept',
            '.gdpr-accept',
            '[data-ph-id*="cookie-popup"] button',
            '[role="region"][key-role="gdpr-regionRole"] button'
        ]

        for selector in privacy_selectors:
            try:
                privacy_button = page.locator(selector).first
                if await privacy_button.is_visible(timeout=2000):
                    logger.info(f"Found privacy popup with selector: {selector}")
                    await privacy_button.click()
                    await page.wait_for_timeout(1000)
                    logger.info("Privacy popup handled")
                    break
            except Exception:
                continue

    except Exception as e:
        logger.debug(f"Error handling popups for {site_name}: {e}")

# ==============================================================================
# CORRECTED FUNCTION
# ==============================================================================
async def scrape_job_links_async(site_name, site_configs):
    """Scrape job links for a single site using Playwright."""
    start_time = asyncio.get_event_loop().time()
    logger.info(f"\n--- Starting scrape for: {site_name} ---")

    config = site_configs.get(site_name)
    if not config:
        logger.error(f"No configuration found for {site_name}")
        return set()

    logger.info(f"Start URL: {config['start_url']}")

    all_job_links = set()
    browser = None
    context = None

    async with async_playwright() as p:
        try:
            browser = await p.chromium.launch(
                headless=True,
                args=[
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-blink-features=AutomationControlled",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                    "--disable-extensions",
                    "--no-first-run",
                    "--disable-default-apps",
                    "--disable-gpu",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding",
                    "--disable-ipc-flooding-protection",
                    "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                ]
            )
        except Exception as launch_err:
            logger.error(f"Error launching browser for {site_name}: {launch_err}")
            logger.error("Ensure browser binaries are installed (run 'playwright install')")
            return set()

        try:
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                accept_downloads=False,
                viewport={'width': 1920, 'height': 1080},
                extra_http_headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                }
            )
            page = await context.new_page()
            current_scope = page

            # Increase timeout significantly for problematic sites
            default_timeout_ms = config.get("page_load_timeout", 120) * 1000 
            page.set_default_timeout(default_timeout_ms)
            page.set_default_navigation_timeout(default_timeout_ms)

            logger.info("Navigating to start URL...")

            navigation_success = False
            for attempt in range(3):
                try:
                    if attempt > 0:
                        logger.info(f"Navigation attempt {attempt + 1} for {site_name}")
                        await asyncio.sleep(1 * attempt)

                    wait_strategies = ['domcontentloaded', 'networkidle', 'load']
                    wait_until = wait_strategies[min(attempt, len(wait_strategies) - 1)]

                    response = await page.goto(
                        config['start_url'],
                        wait_until=wait_until,
                        timeout=default_timeout_ms
                    )

                    if response and not response.ok:
                        logger.warning(f"Received non-OK status code {response.status} for {config['start_url']}")
                        if response.status in [403, 429]:  # Rate limited or forbidden
                            logger.info("Rate limited, waiting before retry...")
                            await asyncio.sleep(3) 
                            continue

                    logger.info("Navigation successful.")
                    navigation_success = True
                    break

                except PlaywrightTimeoutError:
                    logger.warning(f"Timeout on attempt {attempt + 1} navigating to {config['start_url']} for {site_name}")
                    if attempt == 2:
                        logger.error(f"Final timeout navigating to {config['start_url']} for {site_name}")
                        # Don't close browser here - let the finally block handle it
                        return set()
                except Exception as e:
                    logger.warning(f"Navigation error on attempt {attempt + 1} for {site_name}: {e}")
                    if attempt == 2:
                        logger.error(f"Final navigation error for {site_name} to {config['start_url']}: {e}")
                        # **FIX**: Do NOT close the browser here.

            if not navigation_success:
                logger.error(f"Failed to navigate to {config['start_url']} for {site_name} after 3 attempts")
                # **FIX**: Return early, letting the `finally` block handle the cleanup.
                return set()


            initial_wait_ms = config.get("initial_wait_time", 3) * 1000
            if initial_wait_ms > 0:
                logger.info(f"Performing initial wait of {initial_wait_ms / 1000} seconds...")
                await page.wait_for_timeout(initial_wait_ms)

            await handle_common_popups(page, site_name)

            job_link_selector = get_playwright_selector(config["job_link_locator"])
            link_extraction_method = config.get("link_extraction_method", "href")
            link_filter_keyword = config.get("link_filter_keyword")
            base_url_config = config.get("base_url") or config['start_url']

            try:
                await current_scope.locator(job_link_selector).first.wait_for(state='attached', timeout=10000)  # Reduced from 15s to 10s
                job_elements = await current_scope.locator(job_link_selector).all()
                logger.info(f"Found {len(job_elements)} potential link elements.")

                for i, element in enumerate(job_elements):
                    raw_link = None
                    try:
                        if link_extraction_method == "href" or link_extraction_method == "attribute":
                            raw_link = await element.get_attribute("href")
                        elif link_extraction_method == "data-value":
                            raw_link = await element.get_attribute("data-value")
                            if raw_link and base_url_config:
                                raw_link = base_url_config.rstrip('/') + raw_link
                        elif link_extraction_method == "data-jobid":
                            job_id = await element.get_attribute("data-jobid")
                            if job_id and base_url_config:
                                raw_link = f"{base_url_config.rstrip('/')}/job-openings/?jobId={job_id}"
                            elif job_id:
                                current_url = page.url
                                raw_link = f"{current_url.split('?')[0]}?jobId={job_id}"
                        elif link_extraction_method == "onclick":
                            onclick_value = await element.get_attribute("onclick")
                            if onclick_value and config.get("onclick_regex"):
                                import re
                                try:
                                    match = re.search(config["onclick_regex"], onclick_value)
                                    if match:
                                        if len(match.groups()) >= 3:
                                            if base_url_config and "smartrecruiters.com" in base_url_config:
                                                _, job_id, slug = match.groups()
                                                company_part = base_url_config.split('smartrecruiters.com/')[-1].split('/')[0] if 'smartrecruiters.com/' in base_url_config else f"{site_name}Inc"
                                                raw_link = f"https://www.smartrecruiters.com/{company_part}/{job_id}/{slug}"
                                            else:
                                                raw_link = match.group(1)
                                                if raw_link and base_url_config:
                                                    raw_link = base_url_config.rstrip('/') + '/' + raw_link.lstrip('/')
                                        elif len(match.groups()) >= 1:
                                            raw_link = match.group(1)
                                            if raw_link and base_url_config:
                                                raw_link = base_url_config.rstrip('/') + '/' + raw_link.lstrip('/')
                                        else:
                                            logger.warning(f"onclick regex matched but no capturing groups for {site_name}")
                                except re.error as regex_err:
                                    logger.warning(f"Invalid onclick regex for {site_name}: {regex_err}")
                        else:
                            raw_link = await element.get_attribute("href")

                        absolute_link = resolve_url(base_url_config, raw_link)

                        if absolute_link and not is_career_page_url(absolute_link) and not is_base_url_only(absolute_link):
                            if link_filter_keyword:
                                if link_filter_keyword in absolute_link:
                                    all_job_links.add(absolute_link)
                            else:
                                all_job_links.add(absolute_link)
                    except Exception as el_err:
                        logger.warning(f"Error processing a link element {i+1}: {el_err}")

                pagination_type = config.get('pagination_type', 'none')
                if pagination_type != 'none':
                    logger.info(f"Handling {pagination_type} pagination for {site_name}")
                    if pagination_type == "next_button":
                        await handle_next_button_pagination(page, config, all_job_links)
                    elif pagination_type in ["next_button_href", "next_button_url"]:
                        await handle_next_button_url_pagination(page, config, all_job_links)
                    elif pagination_type == "scroll":
                        await handle_scroll_pagination(page, config, all_job_links)
                    elif pagination_type == "load_more":
                        await handle_load_more_pagination(page, config, all_job_links)
                    elif pagination_type == "numbered_pagination":
                        await handle_numbered_pagination(page, config, all_job_links)
                    elif pagination_type == "url_pagination":
                        await handle_url_pagination(page, config, all_job_links)
                    elif pagination_type == "hash_pagination":
                        await handle_hash_pagination(page, config, all_job_links)

            except PlaywrightTimeoutError:
                logger.warning(f"No job links found with selector '{job_link_selector}' for {site_name}.")
            except Exception as e:
                logger.error(f"Error locating or processing job links for {site_name}: {e}")

        except Exception as e_outer:
            logger.error(f"A critical error occurred during the scraping process for {site_name}: {e_outer}")
            logger.error(traceback.format_exc())
        finally:
            # Safely close browser context and browser
            try:
                if context and not context.is_closed():
                    await context.close()
            except Exception as context_close_error:
                logger.debug(f"Error closing context for {site_name}: {context_close_error}")

            try:
                if browser:
                    await browser.close()
            except Exception as browser_close_error:
                logger.debug(f"Error closing browser for {site_name}: {browser_close_error}")

    duration = asyncio.get_event_loop().time() - start_time
    logger.info(f"--- Finished scrape for: {site_name} in {duration:.2f} seconds ---")
    logger.info(f"Found {len(all_job_links)} unique links.")
    return all_job_links

async def process_companies_batch(company_batch, site_configs, semaphore, db):
    """Process a batch of companies concurrently with controlled resource usage."""
    logger.info(f"   Processing batch of {len(company_batch)} companies: {', '.join(company_batch)}")

    # Create tasks for concurrent execution with semaphore control
    async def scrape_with_semaphore(company_name):
        async with semaphore:  # Limit concurrent browser instances
            return await scrape_job_links_async(company_name, site_configs)

    # Create tasks for all companies in the batch
    tasks = []
    for company_name in company_batch:
        task = scrape_with_semaphore(company_name)
        tasks.append((company_name, task))

    # Execute tasks concurrently and collect results
    batch_results = {}
    batch_csv_data = []

    try:
        completed_tasks = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)

        for i, (company_name, _) in enumerate(tasks):
            result = completed_tasks[i]
            if isinstance(result, Exception):
                logger.error(f"  {company_name} failed with exception: {result}")
                batch_results[company_name] = set()
            else:
                job_links = result
                batch_results[company_name] = job_links
                logger.info(f"     {company_name}: {len(job_links)} links")

                # Process results for database and CSV
                if job_links:
                    try:
                        config = site_configs[company_name]
                        content_selectors = config.get('content_selectors', '{}')
                        company_url = config.get('company_url')

                        # Get or create company in database
                        company_id, created = db.get_or_create_company(company_name, company_url or "", config)
                        logger.info(f"Company ID: {company_id}, {'created' if created else 'already exists'}")

                        # Use the same database approach as UK extractor
                        if job_links:
                            # Apply filter logic and process unique links through database
                            links_to_process = set(job_links)
                            # process_new_links handles deduplication AND stores links in database
                            links_to_process = db.process_new_links(links_to_process, company_id)

                            # Add to results for CSV (only unique links after database processing)
                            links_to_process = links_to_process if links_to_process else set()
                            first_link = True
                            for link in sorted(list(links_to_process)):
                                if first_link:
                                    # First row has full details
                                    batch_csv_data.append({
                                        'company_id': company_id,
                                        'job_url': link,
                                        'content_selectors': str(content_selectors) if content_selectors else None,
                                    })
                                    first_link = False
                                else:
                                    # Subsequent rows have empty content_selectors for cleaner CSV
                                    batch_csv_data.append({
                                        'company_id': company_id,
                                        'job_url': link,
                                        'content_selectors': '',
                                    })

                            logger.info(f"     Processed {len(links_to_process)} unique job links for {company_name}")
                        else:
                            logger.info(f"No job links found for {company_name}")

                    except Exception as db_err:
                        logger.error(f"  Database error for {company_name}: {db_err}")
                        # Fallback: still add to CSV even if database fails
                        first_link = True
                        for link in sorted(job_links):
                            batch_csv_data.append({
                                'company_id': company_name,  # Use company name as fallback ID
                                'job_url': link,
                                'content_selectors': config.get('content_selectors', '{}') if first_link else ""
                            })
                            first_link = False

    except Exception as batch_err:
        logger.error(f"  Error in batch processing: {batch_err}")
        # Ensure all companies have results even if batch fails
        for company_name in company_batch:
            if company_name not in batch_results:
                batch_results[company_name] = set()

    return batch_results, batch_csv_data

# --- Main Execution ---
async def amain():

    db = PostgresLoader()
    db.connect()
    config_file = "new_usa_site_configs/site-config-USA_jd_link_extractor_script10.CSV"
    sites = None  # if you wanna specific company
    output_file = f"{get_settings().LOCAL_SCRAPED_LINKS_DIR}/results_for_script10.csv"

    # Load configurations - this part can remain synchronous
    try:
        site_configs = load_configs_csv(config_file)
        if not site_configs:
            # Error messages are printed within load_configs_csv
            sys.exit(1) # Exit if config loading fails
    except Exception:
         sys.exit(1) # Exit on any exception during config loading

    if sites:
        # Filter sites to scrape based on command-line input
        sites_to_scrape = []
        invalid_sites = []
        for site_arg in sites:
            if site_arg in site_configs:
                sites_to_scrape.append(site_arg)
            else:
                invalid_sites.append(site_arg)

        if invalid_sites:
            logger.warning(f"The following specified sites were not found in the config file and will be skipped: {', '.join(invalid_sites)}")
        if not sites_to_scrape:
            logger.error("No valid sites specified to scrape were found in the configuration.")
            sys.exit(1)
        logger.info(f"Scraping specified sites: {', '.join(sites_to_scrape)}")
    else:
        # Scrape all sites defined in the config file
        sites_to_scrape = list(site_configs.keys())
        logger.info(f"Scraping all {len(sites_to_scrape)} sites defined in the configuration file.")

    all_results_for_csv = []
    summary_counts = {}
    total_links_saved_to_csv = 0 # Renamed for clarity, this is the count *in the CSV*

    logger.info("Ensuring Playwright browsers are installed (run 'playwright install' if you encounter issues)...")
    # Attempt a quick check using async playwright
    try:
        async with async_playwright() as p:
            # Use await for launch and close
            browser = await p.chromium.launch(headless=True)
            await browser.close()
        logger.info("Playwright chromium check successful.")
    except Exception as install_err:
        logger.error(f"Playwright check failed: {install_err}. Please run 'playwright install'.")
        # Optionally exit if check fails, or just warn
        # sys.exit(1)

    batch_size = 20
    batch_run = (len(sites_to_scrape) + batch_size-1) // batch_size
    batches = []
    for i in range(0,batch_run):
        batches.append(sites_to_scrape[i*batch_size:min(len(sites_to_scrape),(i+1)*batch_size)])

    results = []
    for batch in batches:
        tasks = [scrape_job_links_async(name, site_configs) for name in batch]
        # Here we also gather exceptions to prevent the whole script from crashing
        # on a single failure.
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        results.extend(batch_results)


    # Iterate through the results and process them
    for i, company_name in enumerate(sites_to_scrape):
        result = results[i] # Get the result for the current company
        config: dict = site_configs[company_name] # Get config for this company

        if isinstance(result, Exception):
            # Handle scraping errors for this specific site
            logger.error(f"Scraping task failed for {company_name}: {result}")
            scraped_links = set() # Treat as 0 links found for summary and CSV
        else:
            # Task succeeded, result is the set of links
            scraped_links = result

        # Capture the number of links found for this company for the summary
        summary_counts[company_name] = len(scraped_links)
        logger.info(f"Links found for {company_name}: {len(scraped_links)}")
        
        # check the company exists or create it 
        company_id, created = db.get_or_create_company(company_name, config.get("company_url",""), config)
        logger.info(f"Company ID: {company_id}, {"created" if created else 'already exists'}")

        # Apply your filter logic here
        links_to_process = set(scraped_links)
        # it should be unique links
        links_to_process = db.process_new_links(links_to_process,company_id)

        # Get config details for CSV output
        content_selectors_for_site = config.get('content_selectors')
        link_count_for_csv = 0
        first_iteration = True
        # Use links_to_process after applying your filter logic
        links_to_process = links_to_process if links_to_process else set()
        for link in sorted(list(links_to_process)):
            if first_iteration:
                # First row has full details
                all_results_for_csv.append(
                    ScrappedJDLinkModel(company_id=company_id,
                                        job_url=link,
                                        content_selectors=str(content_selectors_for_site) if content_selectors_for_site else None,
                                        ).model_dump()
                )
                first_iteration = False
            else:
                # Subsequent rows have empty company_name for cleaner CSV
                all_results_for_csv.append(
                    ScrappedJDLinkModel(company_id=company_id,
                        job_url=link).model_dump())
            link_count_for_csv += 1

        total_links_saved_to_csv += link_count_for_csv # Increment total for CSV count


    logger.info("==============================")
    logger.info(f"Total unique links saved to CSV across {len(sites_to_scrape)} site(s): {total_links_saved_to_csv}")
    logger.info("==============================")

    if all_results_for_csv:
        try:
            # Ensure the output directory exists if a path is specified
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True, mode=0o755)
                logger.info(f"Created output directory: {output_dir}")

            with open(output_file, "w", newline='', encoding='utf-8') as f:
                # Define fieldnames including the potentially empty content_selectors, company_url, company_linkedin
                fieldnames = ['company_id', 'job_url', 'content_selectors','source']
                writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore') # Use extrasaction to ignore fields not in header

                writer.writeheader()
                writer.writerows(all_results_for_csv)
            logger.info(f"Results successfully saved to {output_file}")
        except IOError as e:
            logger.error(f"Could not write to output file '{output_file}'. Check permissions or path. Error: {e}")
            logger.debug("Stack trace:", exc_info=True)
        except Exception as e:
            logger.error(f"Error saving results to CSV file '{output_file}': {e}")
            logger.debug("Stack trace:", exc_info=True)
    else:
        logger.info("No links were scraped successfully. Output file will not be created.")

    logger.info("Script finished.")
    db.close() # Close database connection at the end
async def main():
    await amain()

if __name__ == "__main__":
    asyncio.run(main())