import re
import os
import csv
from urllib.parse import urljoin, urlparse
from etl.extractor.models.jd_schema import ScrappedJ<PERSON><PERSON>inkModel
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError, <PERSON>rro<PERSON> as PlaywrightError
import sys
import traceback
import asyncio
import time # Keep for duration calculation if asyncio.get_event_loop().time() is not preferred
from config.core.settings import get_settings
from etl.loader.load_to_postgres import PostgresLoader
from config.core import logger



# --- Configuration Loading ---
def load_configs_csv(filename: str):
    """Loads site configurations from a CSV file."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    filepath = os.path.join(script_dir, filename)

    configs = {}
    logger.info(f"Attempting to load configurations from {filepath}")
    try:
        with open(filepath, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            if not reader.fieldnames:
                logger.error(f"Error: CSV file '{filepath}' is empty or has no header.")
                return None

            required_headers = ['company_name', 'start_url', 'job_link_locator_strategy', 'job_link_locator_value']
            missing_headers = [h for h in required_headers if h not in reader.fieldnames]
            if missing_headers:
                logger.error(f"CSV file '{filepath}' is missing essential header columns: {', '.join(missing_headers)}. Cannot proceed.")
                return None

            def get_safe_stripped_value(row_dict, key, default_if_missing_or_none=''):
                val = row_dict.get(key)
                if val is None:
                    return default_if_missing_or_none
                return val.strip()

            for i, row in enumerate(reader):
                company_name = get_safe_stripped_value(row, 'company_name')
                if not company_name:
                    logger.warning(f"Skipping row {i+2} in {filepath} due to missing or empty 'company_name'.")
                    continue

                config_entry = {}
                config_entry['start_url'] = get_safe_stripped_value(row, 'start_url')
                if not config_entry['start_url']:
                    logger.warning(f"Skipping site '{company_name}' (row {i+2}) due to missing or empty 'start_url'.")
                    continue

                jl_strategy = get_safe_stripped_value(row, 'job_link_locator_strategy')
                jl_value = get_safe_stripped_value(row, 'job_link_locator_value')
                if jl_strategy and jl_value:
                    config_entry['job_link_locator'] = [jl_strategy, jl_value]
                else:
                    logger.warning(f"Skipping site '{company_name}' (row {i+2}) due to missing 'job_link_locator_strategy' or 'job_link_locator_value'.")
                    continue
                
                # Ensure all expected keys from example CSV are processed
                for loc_key_base in [
                    "initial_wait_locator", "navigation_click_locator", "pagination_locator",
                    "job_link_locator_in_iframe", "category_link_locator", "job_link_in_category_locator",
                    "iframe_selector" # Added iframe_selector here as it has strategy/value
                ]:
                    strategy = get_safe_stripped_value(row, f"{loc_key_base}_strategy")
                    value = get_safe_stripped_value(row, f"{loc_key_base}_value")
                    if strategy and value:
                        config_entry[loc_key_base] = [strategy, value]
                    # For iframe_selector specifically, if only iframe_selector_value is present, treat it as CSS
                    elif loc_key_base == "iframe_selector" and not strategy and value:
                         config_entry[loc_key_base] = ["CSS", value]


                config_entry['pagination_type'] = get_safe_stripped_value(row, 'pagination_type', 'none') or 'none'
                config_entry['base_url'] = get_safe_stripped_value(row, 'base_url') or None
                config_entry['link_filter_keyword'] = get_safe_stripped_value(row, 'link_filter_keyword') or None
                config_entry['link_extraction_method'] = get_safe_stripped_value(row, 'link_extraction_method', 'href') or 'href'
                config_entry['link_attribute'] = get_safe_stripped_value(row, 'link_attribute') or None
                config_entry['onclick_regex'] = get_safe_stripped_value(row, 'onclick_regex') or None
                config_entry['pagination_locator_template'] = get_safe_stripped_value(row, 'pagination_locator_template') or None
                config_entry['content_selectors'] = get_safe_stripped_value(row, 'content_selectors') or None
                config_entry['company_url'] = get_safe_stripped_value(row, 'company_url') or None
                config_entry['company_linkedin'] = get_safe_stripped_value(row, 'company_linkedin') or None
                config_entry['url_param_name'] = get_safe_stripped_value(row, 'url_param_name') or None
                config_entry['no_results_selector'] = get_safe_stripped_value(row, 'no_results_selector') or None
                # config_entry['iframe_selector'] = get_safe_stripped_value(row, 'iframe_selector') or None # Handled above
                config_entry['url_construction_template'] = get_safe_stripped_value(row, 'url_construction_template') or None


                for int_key, default_val in [
                    ('initial_wait_time', 5), ('initial_wait_timeout', 20),
                    ('navigation_wait_time', 5), ('page_load_timeout', 60),
                    ('pagination_wait_time', 5), ('max_pages', 50),
                    ('url_param_start_value', 0), ('url_param_increment', 10), ('url_param_max_value', 1000)
                ]:
                    val_str = get_safe_stripped_value(row, int_key)
                    try:
                        config_entry[int_key] = int(val_str) if val_str else default_val
                    except ValueError:
                        logger.warning(f"Invalid integer value '{val_str}' for '{int_key}' in site '{company_name}' (row {i+2}). Using default {default_val}.")
                        config_entry[int_key] = default_val
                configs[company_name] = config_entry

        if not configs:
            logger.warning(f"No valid configurations were loaded from {filepath}.")
            return None
        logger.info(f"Successfully loaded {len(configs)} configurations from {filepath}")
        return configs
    except FileNotFoundError:
        logger.error(f"Configuration file '{filepath}' not found.")
        raise 
    except Exception as e:
        logger.error(f"Unexpected error while loading CSV configurations from {filepath}: {e}")
        logger.error(traceback.format_exc())
        raise 

# --- Helper Functions ---
def get_playwright_selector(locator_tuple_or_str):
    if isinstance(locator_tuple_or_str, str): # If only value is provided, assume CSS
        return f"css={locator_tuple_or_str}"
    if not isinstance(locator_tuple_or_str, (list, tuple)) or len(locator_tuple_or_str) != 2:
        logger.warning(f"Invalid locator_tuple format: {locator_tuple_or_str}. Expected [strategy, value] or just value. Using value as is.")
        return str(locator_tuple_or_str) # Should not happen if CSV parsing is correct
    
    strategy, value = locator_tuple_or_str
    if not strategy: # If strategy is empty but value exists, assume CSS
        return f"css={value}"

    strategy_lower = strategy.lower().replace(" ", "_").replace("-", "_")

    if strategy_lower in ["css", "css_selector"]: return f"css={value}"
    elif strategy_lower == "xpath": return f"xpath={value}"
    elif strategy_lower == "id": return f"id={value}"
    elif strategy_lower == "name": return f"css=[name='{value}']" # CSS attribute selector
    elif strategy_lower in ["link_text", "text"]: return f"text={value}"
    elif strategy_lower in ["partial_link_text", "text_contains"]: return f"text*={value}" # Playwright's text selector with *
    elif strategy_lower in ["tag_name", "tag"]: return f"css={value}" # e.g., "button" becomes "css=button"
    elif strategy_lower in ["class", "class_name"]:
        # For multiple classes, join with dots: "class1 class2" -> ".class1.class2"
        return f"css=.{value.replace(' ', '.')}"
    else:
        logger.warning(f"Unsupported locator strategy '{strategy}'. Using value as CSS: {value}")
        return f"css={value}"


def resolve_url(base_url, link_url):
    if not link_url: return None
    link_url = link_url.strip()
    if not link_url or link_url.lower().startswith('javascript:') or link_url.lower().startswith('mailto:'):
        return None
    if link_url.startswith("//"):
        parsed_base = urlparse(base_url)
        scheme = parsed_base.scheme if parsed_base.scheme else 'https'
        return f"{scheme}:{link_url}"
    try:
        absolute_url = urljoin(base_url, link_url)
        parsed_abs = urlparse(absolute_url)
        if parsed_abs.scheme in ['http', 'https'] and parsed_abs.netloc:
            return absolute_url
        else:
            logger.warning(f"Resolved URL '{absolute_url}' from base '{base_url}' and link '{link_url}' seems invalid (no scheme/netloc). Skipping.")
            return None
    except Exception as e:
        logger.error(f"Error resolving URL: base='{base_url}', link='{link_url}'. Error: {e}")
        return None

async def extract_links_from_elements_async(elements, config, current_page_url):
    """Async helper to extract and process links from a list of Playwright locators."""
    page_links_found = set()
    link_extraction_method = config.get("link_extraction_method", "href")
    link_attribute = config.get("link_attribute")
    onclick_regex_str = config.get("onclick_regex")
    link_filter_keyword = config.get("link_filter_keyword")
    base_url_for_config = config.get("base_url") or current_page_url
    url_construction_template = config.get("url_construction_template")

    if base_url_for_config and not (base_url_for_config.endswith('/') or '#' in urlparse(base_url_for_config).path or '?' in urlparse(base_url_for_config).path):
        parsed_uri = urlparse(base_url_for_config)
        if parsed_uri.scheme and parsed_uri.netloc and not parsed_uri.path.endswith('/') and not parsed_uri.query and not parsed_uri.fragment:
             base_url_for_config += '/'


    onclick_regex_compiled = None
    if link_extraction_method == "onclick_regex" and onclick_regex_str:
        try:
            onclick_regex_compiled = re.compile(onclick_regex_str)
        except re.error as re_err:
            logger.warning(f"Invalid onclick_regex '{onclick_regex_str}': {re_err}. Defaulting to 'href'.")
            link_extraction_method = "href"

    for element in elements:
        raw_value = None
        try:
            if link_extraction_method == "href":
                raw_value = await element.get_attribute("href")
            elif link_extraction_method == "attribute" and link_attribute:
                raw_value = await element.get_attribute(link_attribute)
            elif link_extraction_method == "onclick_regex" and onclick_regex_compiled:
                onclick_attr = await element.get_attribute("onclick")
                if onclick_attr:
                    match = onclick_regex_compiled.search(onclick_attr)
                    if match: raw_value = match.group(1) if match.groups() else match.group(0)
            elif link_extraction_method == "construct_from_attribute_template" and link_attribute and url_construction_template:
                attr_val = await element.get_attribute(link_attribute)
                if attr_val:
                    raw_value = url_construction_template.replace("{value}", attr_val)
            else: # Default to href
                raw_value = await element.get_attribute("href")
                if link_extraction_method not in ["href", "attribute", "onclick_regex", "construct_from_attribute_template"]:
                    logger.warning(f"Unknown link_extraction_method '{link_extraction_method}'. Defaulting to 'href'.")

            is_pre_resolved = link_extraction_method == "construct_from_attribute_template"
            absolute_link = None
            if is_pre_resolved and raw_value and urlparse(raw_value).scheme and urlparse(raw_value).netloc:
                absolute_link = raw_value
            elif raw_value:
                absolute_link = resolve_url(base_url_for_config, raw_value)
            
            if absolute_link:
                if link_filter_keyword and link_filter_keyword not in absolute_link:
                    # logger.debug(f"Link '{absolute_link}' filtered out by keyword '{link_filter_keyword}'.")
                    continue
                page_links_found.add(absolute_link)
        except PlaywrightError as el_err:
            logger.error(f"  Error processing element for link: {el_err}")
        except Exception as e:
            logger.error(f"  Unexpected error processing element for link: {e}")
    return page_links_found

# --- Core Asynchronous Scraping Function ---
async def scrape_job_links_async(site_name, configs):
    if site_name not in configs:
        logger.error(f"Configuration for site '{site_name}' not found.")
        return set()

    config = configs[site_name]
    all_job_links = set()
    start_time = asyncio.get_event_loop().time()
    logger.info(f"\n--- Starting scrape for: {site_name} ---")
    logger.info(f"Start URL: {config['start_url']}")

    browser = None
    context = None
    page = None

    async with async_playwright() as p:
        try:
            browser = await p.chromium.launch(headless=True, args=['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage'])
        except PlaywrightError as launch_err:
            logger.error(f"Error launching browser for {site_name}: {launch_err}")
            logger.error("Ensure browser binaries are installed (run 'playwright install')")
            return set()

        try:
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36', # Updated UA
                accept_downloads=False,
                # viewport={'width': 1920, 'height': 1080} # Optional: Set viewport
            )
            
            page = await context.new_page()
            default_timeout_ms = config.get("page_load_timeout", 60) * 1000
            page.set_default_timeout(default_timeout_ms)
            page.set_default_navigation_timeout(default_timeout_ms)

            logger.info(f"Navigating to start URL...")
            try:
                response = await page.goto(config['start_url'], wait_until='domcontentloaded', timeout=default_timeout_ms)
                if response and not response.ok:
                    logger.warning(f"Status {response.status} for {config['start_url']}")
            except PlaywrightTimeoutError:
                logger.error(f"Error: Timeout navigating to {config['start_url']}.")
                return set()
            except PlaywrightError as e:
                logger.error(f"Error: Navigation error for {config['start_url']}: {e}")
                return set()

            # --- Initial Actions (Wait/Navigate) ---
            nav_clicked_main_page = False
            if config.get("navigation_click_locator") and config.get("pagination_type") == "click_to_navigate":
                nav_selector = get_playwright_selector(config["navigation_click_locator"])
                initial_wait_timeout_ms = config.get("initial_wait_timeout", 20) * 1000
                nav_wait_ms = config.get("navigation_wait_time", 5) * 1000
                try:
                    logger.info(f"Attempting initial navigation click: {nav_selector}")
                    nav_button = page.locator(nav_selector)
                    await nav_button.wait_for(state="visible", timeout=initial_wait_timeout_ms)
                    await nav_button.click(timeout=initial_wait_timeout_ms // 2)
                    logger.info(f"Navigation element clicked. Waiting {nav_wait_ms / 1000}s...")
                    await page.wait_for_timeout(nav_wait_ms)
                    nav_clicked_main_page = True
                except PlaywrightTimeoutError:
                    logger.warning(f"Timeout finding or clicking initial navigation element {nav_selector} for {site_name}.")
                except PlaywrightError as e:
                    logger.error(f"Error during initial navigation click for {site_name}: {e}")
                    return set()


            if config.get("initial_wait_locator") and not nav_clicked_main_page:
                wait_selector = get_playwright_selector(config["initial_wait_locator"])
                timeout_ms = config.get("initial_wait_timeout", 20) * 1000
                try:
                    logger.info(f"Waiting for initial element: {wait_selector} (timeout: {timeout_ms / 1000}s)")
                    await page.locator(wait_selector).first.wait_for(state='attached', timeout=timeout_ms)
                    logger.info("Initial element found.")
                except PlaywrightTimeoutError:
                    logger.warning(f"Initial element {wait_selector} not found. Continuing...")
                except PlaywrightError as e:
                    logger.error(f"Error waiting for initial element {wait_selector}: {e}. Continuing...")
            elif not config.get("navigation_click_locator") or (config.get("navigation_click_locator") and not nav_clicked_main_page):
                initial_wait_ms = config.get("initial_wait_time", 5) * 1000
                if initial_wait_ms > 0:
                    logger.info(f"Initial wait: {initial_wait_ms / 1000}s")
                    await page.wait_for_timeout(initial_wait_ms)
            
            # --- Main Scraping Logic ---
            pagination_type = config.get("pagination_type", "none")

            if pagination_type == "iframe":
                iframe_locator_config = config.get("iframe_selector") # This is now [strategy, value]
                if not iframe_locator_config:
                    logger.error(f"Error: 'iframe_selector' (strategy and value) missing for iframe type site {site_name}.")
                    return set()
                
                iframe_selector_str = get_playwright_selector(iframe_locator_config)
                logger.info(f"Locating iframe with selector: {iframe_selector_str}")
                try:
                    iframe_element = page.locator(iframe_selector_str)
                    await iframe_element.wait_for(state="visible", timeout=15000) # Wait for iframe element itself
                    
                    # Content Frame Logic for Playwright
                    frame = iframe_element.content_frame()
                    if not frame:
                        logger.error(f"Could not get content frame for iframe {iframe_selector_str} on {site_name}")
                        return set()
                    
                    # Wait for something inside the frame to ensure it's loaded
                    await frame.wait_for_load_state('domcontentloaded', timeout=15000)


                    job_link_locator_iframe_cfg = config.get("job_link_locator_in_iframe")
                    if not job_link_locator_iframe_cfg:
                        logger.error(f"Error: 'job_link_locator_in_iframe' missing for iframe type site {site_name}")
                        return set()
                    job_link_locator_iframe = get_playwright_selector(job_link_locator_iframe_cfg)
                    logger.info(f"Looking for job links in iframe: {job_link_locator_iframe}")
                    
                    await frame.locator(job_link_locator_iframe).first.wait_for(state='attached', timeout=15000)
                    job_elements_in_iframe = await frame.locator(job_link_locator_iframe).all()
                    
                    logger.info(f"Found {len(job_elements_in_iframe)} elements in iframe.")
                    all_job_links.update(await extract_links_from_elements_async(job_elements_in_iframe, config, page.url))
                except PlaywrightTimeoutError:
                    logger.warning(f"Timeout finding iframe or job links within iframe for {site_name}.")
                except Exception as e_iframe:
                    logger.error(f"Error processing iframe for {site_name}: {e_iframe}")
                    logger.error(traceback.format_exc())

            elif pagination_type == "url_param_pagination":
                current_param_val = config.get("url_param_start_value", 0)
                param_increment = config.get("url_param_increment", 10)
                max_param_val = config.get("url_param_max_value", 1000)
                param_name = config.get("url_param_name")
                base_scrape_url = config['start_url'] 
                no_results_sel_str = config.get("no_results_selector")
                no_results_sel = get_playwright_selector(no_results_sel_str) if no_results_sel_str else None


                if not param_name:
                    logger.error(f"Error: 'url_param_name' missing for {site_name}. Cannot paginate.")
                    return set()

                page_count = 0
                max_pages_url_param = config.get("max_pages", 20)

                while current_param_val <= max_param_val and page_count < max_pages_url_param:
                    # Construct URL carefully, avoid double '?' or '&'
                    separator = '&' if '?' in base_scrape_url else '?'
                    paginated_url = f"{base_scrape_url.rstrip('/')}{separator}{param_name}={current_param_val}"
                    logger.info(f"Scraping URL param page: {paginated_url}")
                    try:
                        await page.goto(paginated_url, wait_until='domcontentloaded', timeout=default_timeout_ms)
                    except PlaywrightTimeoutError:
                        logger.warning(f"Timeout loading {paginated_url}. Stopping pagination.")
                        break
                    
                    await page.wait_for_timeout(config.get("pagination_wait_time", 3) * 1000)

                    if no_results_sel:
                        try:
                            if await page.locator(no_results_sel).count() > 0 and await page.locator(no_results_sel).is_visible(timeout=1000):
                                logger.info(f"No results indicator '{no_results_sel}' found. Stopping.")
                                break
                        except PlaywrightTimeoutError: 
                            pass 
                    
                    job_link_selector = get_playwright_selector(config["job_link_locator"])
                    job_elements = []
                    try:
                        await page.locator(job_link_selector).first.wait_for(state='attached', timeout=10000)
                        job_elements = await page.locator(job_link_selector).all()
                    except PlaywrightTimeoutError:
                        logger.warning(f"No job links on {paginated_url} or timeout. Stopping.")
                        break

                    if not job_elements and current_param_val > config.get("url_param_start_value", 0):
                        logger.info("No job elements found on subsequent param page. Stopping.")
                        break
                    
                    links_on_this_page = await extract_links_from_elements_async(job_elements, config, paginated_url)
                    if not links_on_this_page and current_param_val > config.get("url_param_start_value", 0) :
                        logger.info("No new links extracted from this param page. Stopping.")
                        break

                    initial_count = len(all_job_links)
                    all_job_links.update(links_on_this_page)
                    if len(all_job_links) == initial_count and current_param_val > config.get("url_param_start_value", 0):
                        logger.info("No new unique links added from this param page. Stopping.")
                        break

                    current_param_val += param_increment
                    page_count +=1
                    if page_count >= max_pages_url_param:
                        logger.info(f"Reached max_pages_url_param ({max_pages_url_param}) for {site_name}.")
                        break


            elif pagination_type == "custom_multi_page":
                logger.info("Handling custom_multi_page (e.g., Subsplash-like category navigation)")
                category_locator_cfg = config.get("category_link_locator")
                job_in_category_locator_cfg = config.get("job_link_in_category_locator")
                
                if not category_locator_cfg or not job_in_category_locator_cfg:
                    logger.error(f"Error: Missing category or job locators for custom_multi_page site {site_name}.")
                    return set()

                category_locator_str = get_playwright_selector(category_locator_cfg)
                job_in_category_locator_str = get_playwright_selector(job_in_category_locator_cfg)
                
                category_elements_loc = page.locator(category_locator_str)
                count = await category_elements_loc.count()
                category_urls = []

                for i in range(count):
                    cat_el = category_elements_loc.nth(i)
                    href = await cat_el.get_attribute("href")
                    abs_href = resolve_url(page.url, href)
                    if abs_href: category_urls.append(abs_href)
                
                logger.info(f"Found {len(category_urls)} category URLs.")
                for i, cat_url in enumerate(category_urls):
                    if i >= config.get("max_pages", 10): 
                        logger.info(f"Reached max category pages ({config.get('max_pages', 10)}) for {site_name}.")
                        break
                    logger.info(f"Processing category URL: {cat_url}")
                    try:
                        await page.goto(cat_url, wait_until='domcontentloaded', timeout=default_timeout_ms)
                        await page.wait_for_timeout(3000) 

                        job_elements_in_cat = await page.locator(job_in_category_locator_str).all()
                        logger.info(f"  Found {len(job_elements_in_cat)} job elements in category.")
                        all_job_links.update(await extract_links_from_elements_async(job_elements_in_cat, config, cat_url))
                    except Exception as e_cat:
                        logger.error(f"  Error processing category {cat_url}: {e_cat}")
                        logger.error(traceback.format_exc())
            
            else: # Standard pagination (none, next_button, load_more, scroll, etc.)
                current_page_num = 1
                max_pages = config.get("max_pages", 50)
                consecutive_no_new_links = 0
                pagination_wait_ms = config.get("pagination_wait_time", 5) * 1000


                while current_page_num <= max_pages:
                    logger.info(f"Scraping page/view {current_page_num}...")
                    await page.wait_for_timeout(1500) 

                    job_link_selector = get_playwright_selector(config["job_link_locator"])
                    job_elements = []
                    try:
                        await page.locator(job_link_selector).first.wait_for(state='attached', timeout=15000)
                        job_elements = await page.locator(job_link_selector).all()
                        logger.info(f"Found {len(job_elements)} potential link elements on page {current_page_num}.")
                    except PlaywrightTimeoutError:
                        logger.warning(f"Timeout waiting for job links ({job_link_selector}) on page {current_page_num}.")
                        if current_page_num == 1 and not all_job_links: logger.warning(f"Warning: No job links on first page for {site_name}.")
                        if pagination_type != 'scroll': break
                    except PlaywrightError as e:
                        logger.error(f"Error locating job links ({job_link_selector}): {e}")
                        break
                    
                    if not job_elements and pagination_type != 'scroll':
                        if current_page_num > 1: logger.info("No more job link elements found. Ending pagination.")
                        else: logger.info("No job link elements found on the first page/view. Ending pagination for this site.")
                        break
                    
                    page_links_found_this_iteration = await extract_links_from_elements_async(job_elements, config, page.url)
                    
                    newly_added_count = len(page_links_found_this_iteration - all_job_links)
                    logger.info(f"Found {len(page_links_found_this_iteration)} unique links this iteration, {newly_added_count} are new.")
                    all_job_links.update(page_links_found_this_iteration)

                    if pagination_type in ["load_more", "load_more_js", "scroll", "load_more_scroll", "next_button_js_scroll"]:
                        if newly_added_count == 0 and len(job_elements) > 0 : 
                             consecutive_no_new_links += 1
                             logger.info(f"No new links found on page {current_page_num}. Consecutive count: {consecutive_no_new_links}")
                             if consecutive_no_new_links >= 2 : # Allow one retry
                                  logger.info(f"Stopping {pagination_type} pagination for {site_name} after {consecutive_no_new_links} attempts with no new links.")
                                  break
                        else:
                            consecutive_no_new_links = 0
                    
                    pagination_successful = False
                    if pagination_type == "none" or (pagination_type == "click_to_navigate" and nav_clicked_main_page):
                        logger.info(f"Pagination type is '{pagination_type}'. Stopping pagination loop.")
                        break

                    elif pagination_type == "scroll":
                        logger.info("Scrolling down...")
                        last_height = await page.evaluate("document.body.scrollHeight")
                        await page.evaluate("window.scrollTo(0, document.body.scrollHeight);")
                        await page.wait_for_timeout(pagination_wait_ms + 1000) 
                        new_height = await page.evaluate("document.body.scrollHeight")
                        if new_height == last_height:
                            logger.info("Scroll height did not change. Assuming end of content.")
                            break
                        else: 
                            logger.info(f"Scrolled from {last_height} to {new_height}.")
                            pagination_successful = True
                    
                    elif pagination_type in ["next_button", "load_more", "next_button_js", "load_more_js", "next_button_url", "next_button_data_table", "load_more_scroll", "next_button_js_scroll"]:
                        pag_selector_str = None
                        if config.get("pagination_locator"):
                            pag_selector_str = get_playwright_selector(config["pagination_locator"])
                        elif pagination_type == "next_button_data_table" and config.get("pagination_locator_template"):
                            try:
                                template = config["pagination_locator_template"]
                                pag_selector_str = get_playwright_selector(['XPATH', template.format(current_page_num + 1)]) 
                                logger.info(f"Using pagination template for page {current_page_num + 1}: {pag_selector_str}")
                            except Exception as fmt_err:
                                logger.error(f"Error formatting pagination template: {fmt_err}"); break
                        
                        if not pag_selector_str:
                            logger.error(f"Pagination type '{pagination_type}' requires 'pagination_locator' or template. Stopping."); break

                        try:
                            logger.info(f"Looking for pagination element: {pag_selector_str}")
                            pagination_element = page.locator(pag_selector_str)
                            await pagination_element.wait_for(state='attached', timeout=10000)

                            if not await pagination_element.is_visible(timeout=5000):
                                logger.info("Pagination element found but not visible. Assuming end of pages."); break
                            if "button" in pagination_type and not await pagination_element.is_enabled(timeout=5000): # Check if it's a button-like pagination
                                logger.info("Pagination button found but is disabled. Assuming end of pages."); break

                            if pagination_type == "next_button_url":
                                next_url = await pagination_element.get_attribute('href')
                                resolved_next_url = resolve_url(page.url, next_url)
                                if resolved_next_url and resolved_next_url != page.url and resolved_next_url.lower() != "javascript:void(0);":
                                    logger.info(f"Navigating to next page URL: {resolved_next_url}")
                                    await page.goto(resolved_next_url, wait_until='domcontentloaded', timeout=default_timeout_ms)
                                    pagination_successful = True
                                else:
                                    logger.info("Next button found but no valid/different URL. Assuming end."); break
                            else: # Click-based pagination
                                logger.info(f"Clicking pagination element ({pagination_type})...")
                                try:
                                    await pagination_element.scroll_into_view_if_needed(timeout=3000)
                                    await page.wait_for_timeout(500) # Brief pause after scroll
                                    
                                    # Try standard click first
                                    click_error = None
                                    try:
                                        await pagination_element.click(timeout=10000, force=("js" in pagination_type)) # Force for JS types
                                    except PlaywrightError as click_err_detail:
                                        click_error = click_err_detail
                                        logger.warning(f"  Standard click failed: {click_err_detail}. Trying JS click if not already forced.")

                                    if click_error or "js" in pagination_type: # If standard failed or it's a JS type, ensure JS click is tried
                                        if not ("js" in pagination_type and not click_error): # Avoid double log if already JS and successful
                                             logger.info("  Attempting JS click for pagination element.")
                                        try:
                                            await pagination_element.evaluate("el => el.click()") # More direct JS click
                                            click_error = None # JS click succeeded
                                        except Exception as js_click_err:
                                            logger.error(f"  JS click also failed: {js_click_err}. Assuming end of pagination.")
                                            break # Failed to click by any means
                                    
                                    if click_error: # If still an error after trying both
                                        break


                                except Exception as e_click: # Catch other potential errors during click process
                                    logger.error(f"Error during pagination click process: {e_click}")
                                    break
                                
                                logger.info(f"Pagination element clicked. Waiting {pagination_wait_ms / 1000}s...")
                                await page.wait_for_timeout(pagination_wait_ms)
                                pagination_successful = True
                        
                        except PlaywrightTimeoutError: 
                            logger.info(f"Pagination element '{pag_selector_str}' not found or not interactable. Assuming end."); break
                        except PlaywrightError as pag_err: 
                            logger.error(f"Error with pagination element '{pag_selector_str}': {pag_err}"); logger.error(traceback.format_exc()); break
                        except Exception as e: 
                            logger.error(f"Unexpected pagination error: {e}"); logger.error(traceback.format_exc()); break
                    
                    else: 
                        logger.error(f"Error: Unknown pagination_type '{pagination_type}'. Stopping."); break

                    if not pagination_successful and pagination_type != "scroll":
                        logger.info(f"Pagination action failed for type '{pagination_type}'. Stopping."); break
                    current_page_num += 1
                
                if current_page_num > max_pages :
                     logger.info(f"Reached max pages limit ({max_pages}) for {site_name}.")


        except PlaywrightError as e:
            logger.error(f"A critical Playwright error occurred for {site_name}: {e}")
            logger.error(traceback.format_exc())
            return set() 
        except Exception as e:
            logger.error(f"An unexpected error occurred during scraping for {site_name}: {e}")
            logger.error(traceback.format_exc())
            return set() 
        finally: 
            if page and not page.is_closed():
                try: await page.close()
                except Exception as ex_close: logger.error(f"Error closing page for {site_name} in finally: {ex_close}")
            if context:
                try: await context.close()
                except Exception as ex_close: logger.error(f"Error closing context for {site_name} in finally: {ex_close}")
            if browser and browser.is_connected():
                try: await browser.close()
                except Exception as ex_close: logger.error(f"Error closing browser for {site_name} in finally: {ex_close}")

    duration = asyncio.get_event_loop().time() - start_time
    logger.info(f"--- Finished scrape for: {site_name} in {duration:.2f} seconds ---")
    logger.info(f"Found {len(all_job_links)} unique links.")
    return all_job_links

# --- Main Asynchronous Execution ---

async def amain():

    db = PostgresLoader()
    db.connect()
    config_file = "new_usa_site_configs/site-config-USA_jd_link_extractor_script6.CSV"
    sites = None  # if you wanna specific company
    output_file = f"{get_settings().LOCAL_SCRAPED_LINKS_DIR}/results_for_script6.csv"

    # Load configurations - this part can remain synchronous
    try:
        site_configs = load_configs_csv(config_file)
        if not site_configs:
            # Error messages are printed within load_configs_csv
            sys.exit(1) # Exit if config loading fails
    except Exception:
         sys.exit(1) # Exit on any exception during config loading

    if sites:
        # Filter sites to scrape based on command-line input
        sites_to_scrape = []
        invalid_sites = []
        for site_arg in sites:
            if site_arg in site_configs:
                sites_to_scrape.append(site_arg)
            else:
                invalid_sites.append(site_arg)

        if invalid_sites:
            logger.warning(f"The following specified sites were not found in the config file and will be skipped: {', '.join(invalid_sites)}")
        if not sites_to_scrape:
            logger.error("No valid sites specified to scrape were found in the configuration.")
            sys.exit(1)
        logger.info(f"Scraping specified sites: {', '.join(sites_to_scrape)}")
    else:
        # Scrape all sites defined in the config file
        sites_to_scrape = list(site_configs.keys())
        logger.info(f"Scraping all {len(sites_to_scrape)} sites defined in the configuration file.")

    all_results_for_csv = []
    summary_counts = {}
    total_links_saved_to_csv = 0 # Renamed for clarity, this is the count *in the CSV*

    logger.info("Ensuring Playwright browsers are installed (run 'playwright install' if you encounter issues)...")
    # Attempt a quick check using async playwright
    try:
        async with async_playwright() as p:
            # Use await for launch and close
            browser = await p.chromium.launch(headless=True)
            await browser.close()
        logger.info("Playwright chromium check successful.")
    except Exception as install_err:
        logger.error(f"Playwright check failed: {install_err}. Please run 'playwright install'.")
        # Optionally exit if check fails, or just warn
        # sys.exit(1)

    batch_size = 10
    batch_run = (len(sites_to_scrape) + batch_size-1) // batch_size
    batches = []
    for i in range(0,batch_run):
        batches.append(sites_to_scrape[i*batch_size:min(len(sites_to_scrape),(i+1)*batch_size)])

        results = []
    for batch in batches:
        tasks = [scrape_job_links_async(name, site_configs) for name in batch]
        results.extend(await asyncio.gather(*tasks))


    # Iterate through the results and process them
    for i, company_name in enumerate(sites_to_scrape):
        result = results[i] # Get the result for the current company
        config: dict = site_configs[company_name] # Get config for this company

        if isinstance(result, Exception):
            # Handle scraping errors for this specific site
            logger.error(f"Scraping task failed for {company_name}: {result}")
            scraped_links = set() # Treat as 0 links found for summary and CSV
        else:
            # Task succeeded, result is the set of links
            scraped_links = result

        # Capture the number of links found for this company for the summary
        summary_counts[company_name] = len(scraped_links)
        logger.info(f"Links found for {company_name}: {len(scraped_links)}")
        
        # check the company exists or create it 
        company_id, created = db.get_or_create_company(company_name, config.get("company_url",""), config)
        logger.info(f"Company ID: {company_id}, {"created" if created else 'already exists'}")

        # Apply your filter logic here
        links_to_process = set(scraped_links)
        # it should be unique links
        links_to_process = db.process_new_links(links_to_process,company_id)

        # Get config details for CSV output
        content_selectors_for_site = config.get('content_selectors')
        link_count_for_csv = 0
        first_iteration = True
        # Use links_to_process after applying your filter logic
        links_to_process = links_to_process if links_to_process else set()
        for link in sorted(list(links_to_process)):
            if first_iteration:
                # First row has full details
                all_results_for_csv.append(
                    ScrappedJDLinkModel(company_id=company_id,
                                        job_url=link,
                                        content_selectors=str(content_selectors_for_site) if content_selectors_for_site else None,
                                        ).model_dump()
                )
                first_iteration = False
            else:
                # Subsequent rows have empty company_name for cleaner CSV
                all_results_for_csv.append(
                    ScrappedJDLinkModel(company_id=company_id,
                        job_url=link).model_dump())
            link_count_for_csv += 1

        total_links_saved_to_csv += link_count_for_csv # Increment total for CSV count


    logger.info("==============================")
    logger.info(f"Total unique links saved to CSV across {len(sites_to_scrape)} site(s): {total_links_saved_to_csv}")
    logger.info("==============================")

    if all_results_for_csv:
        try:
            # Ensure the output directory exists if a path is specified
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True, mode=0o755)
                logger.info(f"Created output directory: {output_dir}")

            with open(output_file, "w", newline='', encoding='utf-8') as f:
                # Define fieldnames including the potentially empty content_selectors, company_url, company_linkedin
                fieldnames = ['company_id', 'job_url', 'content_selectors','source']
                writer = csv.DictWriter(f, fieldnames=fieldnames)

                writer.writeheader()
                writer.writerows(all_results_for_csv)
            logger.info(f"Results successfully saved to {output_file}")
        except IOError as e:
            logger.error(f"Could not write to output file '{output_file}'. Check permissions or path. Error: {e}")
            logger.debug("Stack trace:", exc_info=True)
        except Exception as e:
            logger.error(f"Error saving results to CSV file '{output_file}': {e}")
            logger.debug("Stack trace:", exc_info=True)
    else:
        logger.info("No links were scraped successfully. Output file will not be created.")

    logger.info("Script finished.")
    # You might want to return the output file path or something else useful
    # return output_file # Changed to return None as main doesn't necessarily need to return the path

async def main():
    await amain()

if __name__ == "__main__":
    asyncio.run(main())