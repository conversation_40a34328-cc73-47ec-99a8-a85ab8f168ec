import json
import re
import os
import csv
from urllib.parse import quote, urljoin, urlparse
from etl.extractor.models.jd_schema import ScrappedJDLinkModel
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError, <PERSON><PERSON><PERSON> as PlaywrightError
import sys
import traceback
import asyncio
import html # For progressivebyte
from config.core.settings import get_settings
from etl.loader.load_to_postgres import PostgresLoader
from config.core import logger


# --- Configuration Loading ---
def load_configs_csv(filename: str):
    """Loads site configurations from a CSV file."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    filepath = os.path.join(script_dir, filename)

    configs = {}
    logger.info(f"Attempting to load configurations from {filepath}")
    try:
        with open(filepath, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            if not reader.fieldnames:
                logger.error(f"Error: CSV file '{filepath}' is empty or has no header.")
                return None

            required_headers = ['company_name', 'start_url', 'job_link_locator_strategy', 'job_link_locator_value']
            missing_headers = [h for h in required_headers if h not in reader.fieldnames]
            if missing_headers:
                logger.error(f"CSV file '{filepath}' is missing essential header columns: {', '.join(missing_headers)}. Cannot proceed.")
                return None

            def get_safe_stripped_value(row_dict, key, default_if_missing_or_none=''):
                val = row_dict.get(key)
                if val is None:
                    return default_if_missing_or_none
                return val.strip()

            for i, row in enumerate(reader):
                company_name = get_safe_stripped_value(row, 'company_name')
                if not company_name:
                    logger.warning(f"Skipping row {i+2} in {filepath} due to missing or empty 'company_name'.")
                    continue

                config_entry = {}
                config_entry['start_url'] = get_safe_stripped_value(row, 'start_url')
                if not config_entry['start_url']:
                    logger.warning(f"Skipping site '{company_name}' (row {i+2}) due to missing or empty 'start_url'.")
                    continue

                jl_strategy = get_safe_stripped_value(row, 'job_link_locator_strategy')
                jl_value = get_safe_stripped_value(row, 'job_link_locator_value')
                if jl_strategy and jl_value:
                    config_entry['job_link_locator'] = [jl_strategy, jl_value]
                else:
                    logger.warning(f"Skipping site '{company_name}' (row {i+2}) due to missing 'job_link_locator_strategy' or 'job_link_locator_value'.")
                    continue

                for loc_key_base in ["initial_wait_locator", "navigation_click_locator", "pagination_locator", "iframe_locator"]:
                    strategy = get_safe_stripped_value(row, f"{loc_key_base}_strategy")
                    value = get_safe_stripped_value(row, f"{loc_key_base}_value")
                    if strategy and value:
                        config_entry[loc_key_base] = [strategy, value]
                
                for loc_key_base_extra in ["page_input_locator", "total_pages_locator"]:
                    strategy = get_safe_stripped_value(row, f"{loc_key_base_extra}_strategy")
                    value = get_safe_stripped_value(row, f"{loc_key_base_extra}_value")
                    if strategy and value:
                        config_entry[loc_key_base_extra] = [strategy, value]


                config_entry['pagination_type'] = get_safe_stripped_value(row, 'pagination_type', 'none') or 'none'
                config_entry['base_url'] = get_safe_stripped_value(row, 'base_url') or None
                config_entry['link_filter_keyword'] = get_safe_stripped_value(row, 'link_filter_keyword') or None
                config_entry['link_extraction_method'] = get_safe_stripped_value(row, 'link_extraction_method', 'href') or 'href'
                config_entry['link_attribute'] = get_safe_stripped_value(row, 'link_attribute') or None
                config_entry['onclick_regex'] = get_safe_stripped_value(row, 'onclick_regex') or None
                config_entry['pagination_locator_template'] = get_safe_stripped_value(row, 'pagination_locator_template') or None
                config_entry['content_selectors'] = get_safe_stripped_value(row, 'content_selectors') or None
                config_entry['company_url'] = get_safe_stripped_value(row, 'company_url') or None
                config_entry['company_linkedin'] = get_safe_stripped_value(row, 'company_linkedin') or None
                config_entry['items_per_page_for_url_iteration'] = get_safe_stripped_value(row, 'items_per_page_for_url_iteration') 

                for int_key, default_val in [
                    ('initial_wait_time', 5), ('initial_wait_timeout', 20),
                    ('navigation_wait_time', 5), ('page_load_timeout', 60),
                    ('pagination_wait_time', 5), ('max_pages', 50)
                ]:
                    val_str = get_safe_stripped_value(row, int_key)
                    try:
                        config_entry[int_key] = int(val_str) if val_str else default_val
                    except ValueError:
                        logger.warning(f"Invalid integer value '{val_str}' for '{int_key}' in site '{company_name}' (row {i+2}). Using default {default_val}.")
                        config_entry[int_key] = default_val
                
                items_per_page_str = config_entry.get('items_per_page_for_url_iteration')
                try:
                    config_entry['items_per_page_for_url_iteration'] = int(items_per_page_str) if items_per_page_str else 25 
                except ValueError:
                    logger.warning(f"Invalid integer value '{items_per_page_str}' for 'items_per_page_for_url_iteration' in site '{company_name}'. Using default 25.")
                    config_entry['items_per_page_for_url_iteration'] = 25


                configs[company_name] = config_entry

        if not configs:
            logger.warning(f"No valid configurations were loaded from {filepath}.")
            return None
        logger.info(f"Successfully loaded {len(configs)} configurations from {filepath}")
        return configs

    except FileNotFoundError:
        logger.error(f"Configuration file '{filepath}' not found.")
        raise
    except Exception as e:
        logger.error(f"Unexpected error while loading CSV configurations from {filepath}: {e}")
        logger.error(traceback.format_exc())
        raise RuntimeError(f"Error loading CSV configurations: {e}")

# --- Helper Functions ---
def get_playwright_selector(locator_tuple):
    if not isinstance(locator_tuple, (list, tuple)) or len(locator_tuple) != 2:
        logger.warning(f"Invalid locator_tuple format: {locator_tuple}. Expected [strategy, value]. Using value as is.")
        return str(locator_tuple)

    strategy, value = locator_tuple
    strategy_lower = strategy.lower().replace(" ", "_").replace("-", "_")

    if strategy_lower in ["css", "css_selector"]:
        return f"css={value}"
    elif strategy_lower == "xpath":
        if not (value.startswith('/') or value.startswith('(') or value.startswith('.')):
             logger.warning(f"XPath '{value}' might be invalid (doesn't start with /, (, or .). Using it anyway.")
        return f"xpath={value}"
    elif strategy_lower == "id":
        return f"id={value}" 
    elif strategy_lower == "name":
         return f"[name='{value}']" 
    elif strategy_lower in ["link_text", "text"]:
         return f"text='{value}'" 
    elif strategy_lower in ["partial_link_text", "text_contains"]:
         return f"text*='{value}'" 
    elif strategy_lower in ["tag_name", "tag"]:
         return f"css={value}" 
    elif strategy_lower in ["class", "class_name"]:
        return f"css=.{'.'.join(value.split())}"
    else:
        logger.warning(f"Unsupported locator strategy '{strategy}' for Playwright conversion. Using value directly as CSS selector: {value}")
        return f"css={value}"

def resolve_url(base_url, link_url):
    if not link_url:
        return None
    link_url = link_url.strip()
    if not link_url or link_url.lower().startswith('javascript:'):
        return None

    if link_url.startswith("//"):
        parsed_base = urlparse(base_url)
        scheme = parsed_base.scheme if parsed_base.scheme else 'https'
        return f"{scheme}:{link_url}"
    try:
        absolute_url = urljoin(base_url, link_url)
        if urlparse(absolute_url).scheme in ['http', 'https'] and urlparse(absolute_url).netloc:
            return absolute_url
        else:
            logger.warning(f"Resolved URL '{absolute_url}' from base '{base_url}' and link '{link_url}' seems invalid. Skipping.")
            return None
    except Exception as e:
        logger.error(f"Error resolving URL: base='{base_url}', link='{link_url}'. Error: {e}")
        return None

# --- Core Asynchronous Scraping Function ---
async def scrape_job_links_async(site_name, configs):
    if site_name not in configs:
        logger.error(f"Configuration for site '{site_name}' not found.")
        return set()

    config = configs[site_name]
    all_job_links = set() 
    start_time = asyncio.get_event_loop().time()

    logger.info(f"\n--- Starting scrape for: {site_name} ---")
    logger.info(f"Start URL: {config['start_url']}")

    p_instance = None
    browser = None
    context = None
    page = None
    current_scope = None 

    try:
        p_instance = await async_playwright().start()
        
        try:
            browser = await p_instance.chromium.launch(headless=True, args=["--no-sandbox", "--disable-dev-shm-usage"])
        except PlaywrightError as launch_err:
            logger.error(f"Error launching browser for {site_name}: {launch_err}")
            return all_job_links 

        try:
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36',
                accept_downloads=False,
            )
            await context.grant_permissions(['geolocation', 'notifications'], origin=config['start_url'])
            page = await context.new_page()
            current_scope = page 

            default_timeout_ms = config.get("page_load_timeout", 60) * 1000
            page.set_default_timeout(default_timeout_ms)
            page.set_default_navigation_timeout(default_timeout_ms)

            logger.info(f"Navigating to start URL...")
            try:
                response = await page.goto(config['start_url'], wait_until='domcontentloaded', timeout=default_timeout_ms)
                if response and not response.ok:
                    logger.warning(f"Received non-OK status code {response.status} for {config['start_url']}")
                logger.info("Navigation successful.")
            except PlaywrightTimeoutError:
                logger.error(f"Timeout navigating to {config['start_url']} for {site_name}.")
                return all_job_links 
            except PlaywrightError as e:
                logger.error(f"Playwright navigation error for {site_name} to {config['start_url']}: {e}")
                return all_job_links

            if config.get("iframe_locator"):
                iframe_selector = get_playwright_selector(config["iframe_locator"])
                logger.info(f"Attempting to switch to iframe: {iframe_selector}")
                try:
                    frame_locator = page.frame_locator(iframe_selector)
                    try:
                        await frame_locator.locator(':root').wait_for(state='attached', timeout=15000) 
                        current_scope = frame_locator 
                        logger.info("Successfully focused on iframe.")
                    except PlaywrightTimeoutError:
                        logger.error(f"Timeout waiting for iframe ({iframe_selector}) content to attach for {site_name}.")
                        return all_job_links
                except PlaywrightError as e: 
                    logger.error(f"Error locating iframe {iframe_selector} for {site_name}: {e}")
                    return all_job_links

            nav_clicked = False
            if config.get("navigation_click_locator") and config.get("pagination_type") == "click_to_navigate":
                nav_selector = get_playwright_selector(config["navigation_click_locator"])
                initial_wait_timeout_ms = config.get("initial_wait_timeout", 20) * 1000
                nav_wait_ms = config.get("navigation_wait_time", 5) * 1000
                try:
                    logger.info(f"Attempting initial navigation click: {nav_selector}")
                    nav_button = current_scope.locator(nav_selector)
                    await nav_button.wait_for(state="visible", timeout=initial_wait_timeout_ms)
                    await nav_button.click(timeout=initial_wait_timeout_ms // 2)
                    logger.info(f"Navigation element clicked. Waiting {nav_wait_ms / 1000}s...")
                    await page.wait_for_timeout(nav_wait_ms) 
                    nav_clicked = True
                except PlaywrightTimeoutError:
                    logger.warning(f"Timeout finding or clicking initial navigation element {nav_selector} for {site_name}.")
                except PlaywrightError as e:
                    logger.error(f"Error during initial navigation click for {site_name}: {e}")
                    return all_job_links 

            if config.get("initial_wait_locator") and not nav_clicked:
                wait_selector = get_playwright_selector(config["initial_wait_locator"])
                initial_wait_timeout_ms = config.get("initial_wait_timeout", 20) * 1000
                try:
                    logger.info(f"Waiting for initial element: {wait_selector} (timeout: {initial_wait_timeout_ms / 1000}s)")
                    await current_scope.locator(wait_selector).first.wait_for(state='attached', timeout=initial_wait_timeout_ms)
                    logger.info("Initial element found.")
                except PlaywrightTimeoutError:
                    logger.warning(f"Initial wait element {wait_selector} not found within timeout for {site_name}. Continuing...")
                except PlaywrightError as e:
                     logger.error(f"Error waiting for initial element {wait_selector}: {e}. Continuing...")
            elif not config.get("navigation_click_locator") and not config.get("initial_wait_locator"):
                 initial_wait_ms = config.get("initial_wait_time", 5) * 1000
                 if initial_wait_ms > 0:
                     logger.info(f"Performing initial wait of {initial_wait_ms / 1000} seconds...")
                     await page.wait_for_timeout(initial_wait_ms) 

            current_page_num = 1
            max_pages = config.get("max_pages", 50)
            consecutive_no_new_links = 0

            if config.get("pagination_type") == "url_iteration":
                logger.info(f"Using URL iteration for {site_name}")
                start_url_base = config['start_url']
                parsed_start_url_for_iter = urlparse(start_url_base)
                start_url_base_no_query = f"{parsed_start_url_for_iter.scheme}://{parsed_start_url_for_iter.netloc}{parsed_start_url_for_iter.path}"
                page_param_template = config.get('pagination_locator_template', "&startrow={startrow}")
                items_per_page = config.get('items_per_page_for_url_iteration', 25)
                actual_max_pages = max_pages

                if config.get("total_pages_locator"):
                    try:
                        total_pages_selector = get_playwright_selector(config["total_pages_locator"])
                        total_pages_text_element = current_scope.locator(total_pages_selector)
                        await total_pages_text_element.wait_for(state='visible', timeout=10000)
                        total_pages_text = await total_pages_text_element.inner_text()
                        numbers_in_text = re.findall(r'\d+', total_pages_text)
                        if numbers_in_text:
                            if "of " in total_pages_text.lower(): 
                                match = re.search(r'of\s*(\d+)', total_pages_text, re.IGNORECASE)
                                if match: actual_max_pages = int(match.group(1))
                            elif "/" in total_pages_text and ("Page" in total_pages_text or "page" in total_pages_text) : 
                                actual_max_pages = int(total_pages_text.split('/')[-1].strip())
                            else:
                                actual_max_pages = int(numbers_in_text[-1])
                            logger.info(f"Determined total pages: {actual_max_pages} for {site_name} from text: '{total_pages_text}'")
                            if actual_max_pages > max_pages: 
                                logger.info(f"Total pages found ({actual_max_pages}) exceeds configured max_pages ({max_pages}). Capping at {max_pages}.")
                                actual_max_pages = max_pages
                        else:
                            logger.warning(f"Could not extract total pages number from '{total_pages_text}' for {site_name}. Using configured max_pages: {max_pages}")
                    except Exception as e:
                        logger.warning(f"Could not determine total pages for {site_name} using locator {config.get('total_pages_locator')}: {e}. Using configured max_pages: {max_pages}")
                else: 
                    logger.info(f"No total_pages_locator for {site_name}. Using configured max_pages: {max_pages}")

                for page_iter in range(actual_max_pages): 
                    page_links_found_iter = set()
                    current_iter_url = ""
                    if "{startrow}" in page_param_template:
                        param_val = page_iter * items_per_page
                        current_iter_url = f"{start_url_base_no_query.rstrip('/')}{page_param_template.format(startrow=param_val)}"
                    elif "{page_num}" in page_param_template:
                        param_val = page_iter + 1 
                        if page_param_template.startswith("http"): 
                            current_iter_url = page_param_template.format(page_num=param_val)
                        elif page_param_template.startswith("/"): 
                            parsed_start_url = urlparse(start_url_base_no_query)
                            current_iter_url = f"{parsed_start_url.scheme}://{parsed_start_url.netloc}{page_param_template.format(page_num=param_val)}"
                        else: 
                             current_iter_url = f"{start_url_base_no_query.rstrip('/')}{page_param_template.format(page_num=param_val)}"
                    else:
                        logger.error(f"pagination_locator_template for url_iteration needs {{startrow}} or {{page_num}} for {site_name}. Stopping.")
                        break
                    
                    if page_iter > 0 or (page_iter == 0 and current_iter_url != config['start_url']):
                        logger.info(f"Navigating to URL iteration page {page_iter + 1}: {current_iter_url}")
                        try:
                            await page.goto(current_iter_url, wait_until='domcontentloaded', timeout=default_timeout_ms)
                            await page.wait_for_timeout(config.get("pagination_wait_time", 5) * 1000)
                        except PlaywrightTimeoutError:
                            logger.warning(f"Timeout navigating to {current_iter_url}. Assuming end of pages.")
                            break
                        except PlaywrightError as e:
                            logger.error(f"Error navigating to {current_iter_url}: {e}. Stopping.")
                            break
                    elif page_iter == 0 and current_iter_url == config['start_url']:
                        logger.info(f"URL iteration page 1 ({current_iter_url}) is same as start URL. Using initial load.")
                    
                    job_link_selector = get_playwright_selector(config["job_link_locator"])
                    try:
                        await current_scope.locator(job_link_selector).first.wait_for(state='attached', timeout=15000)
                        job_elements = await current_scope.locator(job_link_selector).all()
                        
                        if not job_elements and page_iter > 0 : 
                            logger.info("No job elements found on this URL iteration. Assuming end of pages.")
                            break
                        logger.info(f"Found {len(job_elements)} potential link elements on URL iteration {page_iter + 1}.")

                        for element in job_elements: 
                            link_extraction_attr_name = config.get("link_extraction_method", "href")
                            if link_extraction_attr_name == "attribute":
                                link_extraction_attr_name = config.get("link_attribute", "href")
                            
                            raw_link = await element.get_attribute(link_extraction_attr_name)
                            absolute_link = resolve_url(config.get("base_url") or current_iter_url, raw_link)
                            if absolute_link and (not config.get("link_filter_keyword") or config.get("link_filter_keyword") in absolute_link):
                                page_links_found_iter.add(absolute_link)
                    except PlaywrightTimeoutError:
                        logger.warning(f"No job links found on {current_iter_url} after waiting. Assuming end of pages or issue with selector.")
                        if page_iter > 0: break 
                    except PlaywrightError as e:
                        logger.error(f"Error locating job links on {current_iter_url}: {e}")
                        break 
                    
                    if not page_links_found_iter and page_iter > 0: 
                         logger.info(f"No links extracted from {current_iter_url}, assuming end of URL iteration.")
                         break
                    
                    newly_added_this_iter = len(page_links_found_iter - all_job_links)
                    all_job_links.update(page_links_found_iter)
                    logger.info(f"Found {len(page_links_found_iter)} unique links on this iteration, {newly_added_this_iter} are new. Total: {len(all_job_links)}")

                    if newly_added_this_iter == 0 and page_iter > 0:
                        consecutive_no_new_links +=1
                        if consecutive_no_new_links >=2:
                            logger.info(f"No new links for {consecutive_no_new_links} consecutive URL iterations. Stopping.")
                            break
                    else:
                        consecutive_no_new_links = 0
            
            else: # Other pagination types
                while current_page_num <= max_pages:
                    logger.info(f"Scraping page/view {current_page_num}...")
                    page_links_found = set()
                    await page.wait_for_timeout(1500) 

                    try:
                        job_link_selector = get_playwright_selector(config["job_link_locator"])
                        link_extraction_method = config.get("link_extraction_method", "href")
                        link_attribute = config.get("link_attribute")
                        onclick_regex_str = config.get("onclick_regex")
                        link_filter_keyword = config.get("link_filter_keyword")
                        base_url_config = config.get("base_url")

                        onclick_regex_compiled = None
                        if link_extraction_method == "onclick_regex" and onclick_regex_str:
                            try: onclick_regex_compiled = re.compile(onclick_regex_str)
                            except re.error as re_err:
                                logger.warning(f"Invalid onclick_regex '{onclick_regex_str}'. Falling back to 'href'. Error: {re_err}")
                                link_extraction_method = "href"
                        
                        job_elements = []
                        try:
                            await current_scope.locator(job_link_selector).first.wait_for(state='attached', timeout=15000)
                            job_elements = await current_scope.locator(job_link_selector).all()
                            logger.info(f"Found {len(job_elements)} potential link elements on page {current_page_num}.")
                        except PlaywrightTimeoutError:
                            logger.warning(f"Timeout waiting for job links ({job_link_selector}) on page {current_page_num}.")
                            if current_page_num == 1 and not all_job_links: logger.warning(f"No job links found on the first page for {site_name}. Check locators.")
                            if config.get("pagination_type") not in ['scroll', 'load_more_scroll']: break 
                        except PlaywrightError as e:
                             logger.error(f"Error locating job links ({job_link_selector}) on page {current_page_num}: {e}")
                             break 
                        
                        if not job_elements and config.get("pagination_type") not in ['scroll', 'load_more_scroll']:
                             if current_page_num > 1: logger.info("No more job link elements found. Ending pagination.")
                             elif current_page_num == 1 and not all_job_links: logger.info("No job links elements found on first page. Ending pagination.")
                             break
                        
                        for i, element in enumerate(job_elements):
                            raw_link = None
                            current_element_base_url = base_url_config or page.url 
                            try:
                                if link_extraction_method == "href":
                                    raw_link = await element.get_attribute("href")
                                elif link_extraction_method == "attribute" and link_attribute:
                                    raw_link = await element.get_attribute(link_attribute)
                                elif link_extraction_method == "onclick_regex" and onclick_regex_compiled:
                                    onclick_attr = await element.get_attribute("onclick")
                                    if onclick_attr:
                                        match = onclick_regex_compiled.search(onclick_attr)
                                        if match: raw_link = match.group(1)
                                elif link_extraction_method == "custom_progressivebyte_json_attr_constructor":
                                    data_attr = await element.get_attribute("data-eael-wrapper-link")
                                    if data_attr:
                                        try:
                                            decoded_data = html.unescape(data_attr)
                                            json_data = json.loads(decoded_data)
                                            raw_link = json_data.get("url")
                                        except Exception as json_e:
                                            logger.warning(f"Could not parse JSON from data-eael-wrapper-link for {site_name}: {json_e}")
                                
                                elif link_extraction_method not in ["href", "attribute", "onclick_regex", "custom_progressivebyte_json_attr_constructor"]: 
                                    logger.warning(f"Unknown link_extraction_method '{link_extraction_method}' for {site_name}. Defaulting to 'href'.")
                                    raw_link = await element.get_attribute("href")

                                absolute_link = resolve_url(current_element_base_url, raw_link)

                                if absolute_link:
                                    if link_filter_keyword:
                                        if link_filter_keyword.startswith("NOT:"):
                                            keyword_to_exclude = link_filter_keyword[4:]
                                            if keyword_to_exclude in absolute_link:
                                                continue 
                                        elif link_filter_keyword not in absolute_link:
                                            continue 
                                    page_links_found.add(absolute_link)
                            except PlaywrightError as el_err:
                                logger.error(f"Error processing element {i+1} on page {current_page_num} for {site_name}: {el_err}")
                            except Exception as e: 
                                logger.error(f"Unexpected error processing element {i+1} for {site_name}: {e}")
                    
                    except Exception as page_err: 
                        logger.error(f"Error during link extraction on page {current_page_num} for {site_name}: {page_err}")
                        logger.error(traceback.format_exc())
                        break 

                    newly_added_count = len(page_links_found - all_job_links)
                    logger.info(f"Found {len(page_links_found)} unique links on this page/view, {newly_added_count} are new.")
                    all_job_links.update(page_links_found)

                    pagination_type = config.get("pagination_type", "none")
                    pagination_wait_ms = config.get("pagination_wait_time", 5) * 1000

                    if pagination_type in ["load_more", "load_more_js", "scroll", "load_more_scroll", "next_button_js_scroll"]:
                        if newly_added_count == 0 and current_page_num > 1 : 
                             consecutive_no_new_links += 1
                             logger.info(f"No new links found on page/view {current_page_num}. Consecutive count: {consecutive_no_new_links}")
                             if consecutive_no_new_links >= 2: 
                                  logger.info(f"Stopping {pagination_type} pagination after {consecutive_no_new_links} attempts with no new links.")
                                  break
                        else:
                            consecutive_no_new_links = 0 
                    
                    pagination_successful = False
                    if pagination_type == "none" or (pagination_type == "click_to_navigate" and nav_clicked):
                        logger.info(f"Pagination type is '{pagination_type}'. Stopping pagination loop.")
                        break
                    elif pagination_type == "scroll":
                        logger.info("Scrolling down...")
                        scope_for_scroll = current_scope if hasattr(current_scope, 'evaluate') else page
                        last_height = await scope_for_scroll.evaluate("document.body.scrollHeight")
                        await scope_for_scroll.evaluate("window.scrollTo(0, document.body.scrollHeight);")
                        await page.wait_for_timeout(pagination_wait_ms + 1000) 
                        new_height = await scope_for_scroll.evaluate("document.body.scrollHeight")
                        if new_height == last_height and current_page_num > 1 : 
                            logger.info("Scroll height did not change. Assuming end of content.")
                            break
                        else:
                             logger.info(f"Scrolled from {last_height} to {new_height}. Proceeding to scrape new view.")
                             pagination_successful = True
                    elif pagination_type in ["next_button", "load_more", "next_button_js", "load_more_js", "next_button_url", "next_button_data_table", "load_more_scroll", "next_button_js_scroll", "custom_js_input_and_click"]: 
                        pag_selector_tuple = config.get("pagination_locator")
                        pag_selector_template = config.get("pagination_locator_template")
                        final_pag_selector = None

                        if pag_selector_tuple:
                            final_pag_selector = get_playwright_selector(pag_selector_tuple)
                        elif pagination_type == "next_button_data_table" and pag_selector_template:
                            try:
                                next_page_num_for_selector = current_page_num + 1
                                final_pag_selector = get_playwright_selector(['XPATH', pag_selector_template.format(page_num=next_page_num_for_selector)])
                                logger.info(f"Using pagination template for page {next_page_num_for_selector}: {final_pag_selector}")
                            except Exception as fmt_err:
                                 logger.error(f"Error formatting pagination template for page {next_page_num_for_selector}: {fmt_err}")
                                 break
                        
                        if not final_pag_selector and pagination_type != "custom_js_input_and_click": 
                            logger.error(f"Pagination type '{pagination_type}' requires 'pagination_locator' or a valid 'pagination_locator_template'. Stopping.")
                            break
                        try:
                            if pagination_type == "custom_js_input_and_click":
                                page_input_locator_cfg = config.get("page_input_locator")
                                go_button_locator_cfg = config.get("pagination_locator") 

                                if not page_input_locator_cfg or not go_button_locator_cfg:
                                    logger.error(f"custom_js_input_and_click pagination for {site_name} requires page_input_locator and pagination_locator (for Go button). Stopping.")
                                    break
                                
                                page_input_selector = get_playwright_selector(page_input_locator_cfg)
                                go_button_selector = get_playwright_selector(go_button_locator_cfg)
                                next_page_to_input = current_page_num + 1 

                                logger.info(f"Attempting custom pagination for {site_name}: input '{next_page_to_input}' into {page_input_selector}, click {go_button_selector}")
                                input_box = current_scope.locator(page_input_selector)
                                go_button = current_scope.locator(go_button_selector)

                                await input_box.wait_for(state='visible', timeout=10000)
                                await input_box.fill(str(next_page_to_input))
                                await page.wait_for_timeout(500) 
                                
                                await go_button.wait_for(state='visible', timeout=10000)
                                if not await go_button.is_enabled(timeout=5000):
                                    logger.info(f"Go button for custom pagination not enabled for page {next_page_to_input}. Assuming end.")
                                    break
                                await go_button.click(timeout=10000)
                                pagination_successful = True

                            else: 
                                logger.info(f"Looking for pagination element: {final_pag_selector}")
                                pagination_element = current_scope.locator(final_pag_selector)
                                await pagination_element.wait_for(state='attached', timeout=10000)

                                if not await pagination_element.is_visible(timeout=5000):
                                    logger.info("Pagination element found but not visible. Assuming end of pages.")
                                    break
                                
                                is_disabled_attr = await pagination_element.get_attribute("disabled")
                                aria_disabled_attr = await pagination_element.get_attribute("aria-disabled")
                                class_attr = await pagination_element.get_attribute("class") or ""
                                
                                if is_disabled_attr is not None or aria_disabled_attr == "true" or \
                                   ("disabled" in class_attr.lower() or "inactive" in class_attr.lower()):
                                    logger.info("Pagination element found but is disabled (by attribute or class). Assuming end of pages.")
                                    break
                                
                                if not await pagination_element.is_enabled(timeout=5000): 
                                    logger.info("Pagination element found but not enabled (by is_enabled check). Assuming end of pages.")
                                    break

                                if pagination_type == "next_button_url":
                                     next_url = await pagination_element.get_attribute('href')
                                     resolved_next_url = resolve_url(page.url, next_url) 
                                     if resolved_next_url and resolved_next_url != page.url and resolved_next_url.lower() != "javascript:void(0);":
                                         logger.info(f"Navigating to next page URL: {resolved_next_url}")
                                         await page.goto(resolved_next_url, wait_until='domcontentloaded', timeout=default_timeout_ms)
                                         pagination_successful = True
                                     else:
                                         logger.info(f"Next button found but no valid/different URL ('{resolved_next_url}'). Assuming end.")
                                         break
                                else: 
                                    logger.info(f"Clicking pagination element ({pagination_type})...")
                                    await pagination_element.scroll_into_view_if_needed(timeout=5000)
                                    await page.wait_for_timeout(200)

                                    if "js" in pagination_type: 
                                        scope_for_js_click = current_scope if hasattr(current_scope, 'evaluate') else page
                                        element_handle = await pagination_element.element_handle()
                                        if element_handle:
                                            await scope_for_js_click.evaluate("arguments[0].click();", element_handle)
                                            await element_handle.dispose() 
                                        else:
                                            logger.error(f"Could not get element handle for JS click: {final_pag_selector}")
                                            break
                                    else: 
                                        await pagination_element.click(timeout=10000)
                                    pagination_successful = True
                            
                            if pagination_successful:
                                logger.info(f"Pagination action performed. Waiting {pagination_wait_ms / 1000}s...")
                                await page.wait_for_timeout(pagination_wait_ms) 
                                if 'js' in pagination_type or 'scroll' in pagination_type or 'custom_js_input_and_click' in pagination_type:
                                   try:
                                       await page.wait_for_load_state('domcontentloaded', timeout=15000)
                                   except PlaywrightTimeoutError:
                                       logger.warning("Timeout waiting for domcontentloaded after JS/scroll pagination. Content might be stale.")
                                   await page.wait_for_timeout(1000) 

                        except PlaywrightTimeoutError:
                            logger.warning(f"Pagination element not found or not interactable within timeout for {site_name}. Assuming end. Selector: {final_pag_selector if final_pag_selector else 'N/A for custom_js_input'}")
                            break
                        except PlaywrightError as pag_err:
                            logger.error(f"Error interacting with pagination element for {site_name}. Selector: {final_pag_selector if final_pag_selector else 'N/A for custom_js_input'}: {pag_err}")
                            break
                        except Exception as e: 
                            logger.error(f"Unexpected error during pagination action for {site_name}: {e}")
                            break
                    else:
                        logger.error(f"Unknown pagination_type '{pagination_type}' for {site_name}. Stopping.")
                        break

                    if not pagination_successful and pagination_type != "scroll": 
                        logger.info(f"Pagination action failed or was not applicable for type '{pagination_type}' for {site_name}. Stopping.")
                        break
                    current_page_num += 1
            
            if current_page_num > max_pages and config.get("pagination_type") != "url_iteration":
                logger.info(f"Reached max pages limit ({max_pages}) for {site_name}.")

        except PlaywrightError as e_scrape:
            logger.error(f"A Playwright error occurred during active scraping for {site_name}: {e_scrape}")
            logger.error(traceback.format_exc())
        except Exception as e_generic_scrape:
            logger.error(f"An unexpected error occurred during active scraping for {site_name}: {e_generic_scrape}")
            logger.error(traceback.format_exc())

    except Exception as e_outer:
        logger.error(f"A top-level error occurred for {site_name} before or during Playwright setup: {e_outer}")
        logger.error(traceback.format_exc())
    finally:
        if page: 
            try: await page.close()
            except Exception as e_page_close: logger.debug(f"Error closing page for {site_name}: {e_page_close}")
        if context: 
            try: await context.close()
            except Exception as e_context_close: logger.debug(f"Error closing context for {site_name}: {e_context_close}")
        if browser: 
            try: await browser.close()
            except Exception as e_browser_close: logger.debug(f"Error closing browser for {site_name}: {e_browser_close}")
        if p_instance: 
            try: await p_instance.stop()
            except Exception as e_p_stop: logger.debug(f"Error stopping Playwright for {site_name}: {e_p_stop}")

    duration = asyncio.get_event_loop().time() - start_time
    logger.info(f"--- Finished scrape for: {site_name} in {duration:.2f} seconds ---")
    logger.info(f"Found {len(all_job_links)} unique links.")
    return all_job_links

# --- Main Asynchronous Execution ---
async def amain():

    db = PostgresLoader()
    db.connect()
    config_file = "new_usa_site_configs/site-config-USA_jd_link_extractor_script8.CSV"
    sites = None  # if you wanna specific company
    output_file = f"{get_settings().LOCAL_SCRAPED_LINKS_DIR}/results_for_script8.csv"

    # Load configurations - this part can remain synchronous
    try:
        site_configs = load_configs_csv(config_file)
        if not site_configs:
            # Error messages are printed within load_configs_csv
            sys.exit(1) # Exit if config loading fails
    except Exception:
         sys.exit(1) # Exit on any exception during config loading

    if sites:
        # Filter sites to scrape based on command-line input
        sites_to_scrape = []
        invalid_sites = []
        for site_arg in sites:
            if site_arg in site_configs:
                sites_to_scrape.append(site_arg)
            else:
                invalid_sites.append(site_arg)

        if invalid_sites:
            logger.warning(f"The following specified sites were not found in the config file and will be skipped: {', '.join(invalid_sites)}")
        if not sites_to_scrape:
            logger.error("No valid sites specified to scrape were found in the configuration.")
            sys.exit(1)
        logger.info(f"Scraping specified sites: {', '.join(sites_to_scrape)}")
    else:
        # Scrape all sites defined in the config file
        sites_to_scrape = list(site_configs.keys())
        logger.info(f"Scraping all {len(sites_to_scrape)} sites defined in the configuration file.")

    all_results_for_csv = []
    summary_counts = {}
    total_links_saved_to_csv = 0 # Renamed for clarity, this is the count *in the CSV*

    logger.info("Ensuring Playwright browsers are installed (run 'playwright install' if you encounter issues)...")
    # Attempt a quick check using async playwright
    try:
        async with async_playwright() as p:
            # Use await for launch and close
            browser = await p.chromium.launch(headless=True)
            await browser.close()
        logger.info("Playwright chromium check successful.")
    except Exception as install_err:
        logger.error(f"Playwright check failed: {install_err}. Please run 'playwright install'.")
        # Optionally exit if check fails, or just warn
        # sys.exit(1)

    batch_size = 10
    batch_run = (len(sites_to_scrape) + batch_size-1) // batch_size
    batches = []
    for i in range(0,batch_run):
        batches.append(sites_to_scrape[i*batch_size:min(len(sites_to_scrape),(i+1)*batch_size)])

        results = []
    for batch in batches:
        tasks = [scrape_job_links_async(name, site_configs) for name in batch]
        results.extend(await asyncio.gather(*tasks))


    # Iterate through the results and process them
    for i, company_name in enumerate(sites_to_scrape):
        result = results[i] # Get the result for the current company
        config: dict = site_configs[company_name] # Get config for this company

        if isinstance(result, Exception):
            # Handle scraping errors for this specific site
            logger.error(f"Scraping task failed for {company_name}: {result}")
            scraped_links = set() # Treat as 0 links found for summary and CSV
        else:
            # Task succeeded, result is the set of links
            scraped_links = result

        # Capture the number of links found for this company for the summary
        summary_counts[company_name] = len(scraped_links)
        logger.info(f"Links found for {company_name}: {len(scraped_links)}")
        
        # check the company exists or create it 
        company_id, created = db.get_or_create_company(company_name, config.get("company_url",""), config)
        logger.info(f"Company ID: {company_id}, {"created" if created else 'already exists'}")

        # Apply your filter logic here
        links_to_process = set(scraped_links)
        # it should be unique links
        links_to_process = db.process_new_links(links_to_process,company_id)

        # Get config details for CSV output
        content_selectors_for_site = config.get('content_selectors')
        link_count_for_csv = 0
        first_iteration = True
        # Use links_to_process after applying your filter logic
        links_to_process = links_to_process if links_to_process else set()
        for link in sorted(list(links_to_process)):
            if first_iteration:
                # First row has full details
                all_results_for_csv.append(
                    ScrappedJDLinkModel(company_id=company_id,
                                        job_url=link,
                                        content_selectors=str(content_selectors_for_site) if content_selectors_for_site else None,
                                        ).model_dump()
                )
                first_iteration = False
            else:
                # Subsequent rows have empty company_name for cleaner CSV
                all_results_for_csv.append(
                    ScrappedJDLinkModel(company_id=company_id,
                        job_url=link).model_dump())
            link_count_for_csv += 1

        total_links_saved_to_csv += link_count_for_csv # Increment total for CSV count


    logger.info("==============================")
    logger.info(f"Total unique links saved to CSV across {len(sites_to_scrape)} site(s): {total_links_saved_to_csv}")
    logger.info("==============================")

    if all_results_for_csv:
        try:
            # Ensure the output directory exists if a path is specified
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True, mode=0o755)
                logger.info(f"Created output directory: {output_dir}")

            with open(output_file, "w", newline='', encoding='utf-8') as f:
                # Define fieldnames including the potentially empty content_selectors, company_url, company_linkedin
                fieldnames = ['company_id', 'job_url', 'content_selectors','source']
                writer = csv.DictWriter(f, fieldnames=fieldnames)

                writer.writeheader()
                writer.writerows(all_results_for_csv)
            logger.info(f"Results successfully saved to {output_file}")
        except IOError as e:
            logger.error(f"Could not write to output file '{output_file}'. Check permissions or path. Error: {e}")
            logger.debug("Stack trace:", exc_info=True)
        except Exception as e:
            logger.error(f"Error saving results to CSV file '{output_file}': {e}")
            logger.debug("Stack trace:", exc_info=True)
    else:
        logger.info("No links were scraped successfully. Output file will not be created.")

    logger.info("Script finished.")
    # You might want to return the output file path or something else useful
    # return output_file # Changed to return None as main doesn't necessarily need to return the path

async def main():
    await amain()

if __name__ == "__main__":
    asyncio.run(main())