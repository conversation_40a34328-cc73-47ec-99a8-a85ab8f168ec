import asyncio
from .new_usa_jd_link_extractor_script import main as script1
from .new_usa_jd_link_extractor_script2 import main as script2
from .new_usa_jd_link_extractor_script3 import main as script3
from .new_usa_jd_link_extractor_script4 import main as script4
from .new_usa_jd_link_extractor_script5 import main as script5
from .new_usa_jd_link_extractor_script6 import main as script6
from .new_usa_jd_link_extractor_script7 import main as script7
from .new_usa_jd_link_extractor_script8 import main as script8
from .new_usa_jd_link_extractor_script9 import main as script9
from .new_usa_jd_link_extractor_script10 import main as script10


async def main():
    # Running all scripts concurrently for maximum speed
    tasks = [
        asyncio.create_task(script1()),
        asyncio.create_task(script2()),
        asyncio.create_task(script3()),
        asyncio.create_task(script4()),
        asyncio.create_task(script5()),
        asyncio.create_task(script6()),
        asyncio.create_task(script7()),
        asyncio.create_task(script8()),
        asyncio.create_task(script9()),
        asyncio.create_task(script10())
    ]

    # Wait for all scripts to complete
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Log results
    for i, result in enumerate(results, 1):
        if isinstance(result, Exception):
            print(f"Script {i} failed: {result}")
        else:
            print(f"Script {i} completed successfully")