import asyncio
from .new_usa_jd_link_extractor_script import main as script1
from .new_usa_jd_link_extractor_script2 import main as script2
from .new_usa_jd_link_extractor_script3 import main as script3
from .new_usa_jd_link_extractor_script4 import main as script4
from .new_usa_jd_link_extractor_script5 import main as script5
from .new_usa_jd_link_extractor_script6 import main as script6
from .new_usa_jd_link_extractor_script7 import main as script7
from .new_usa_jd_link_extractor_script8 import main as script8
from .new_usa_jd_link_extractor_script9 import main as script9
from .new_usa_jd_link_extractor_script10 import main as script10


<<<<<<< Updated upstream
async def main():
    # Running scripts in controlled batches to avoid system crash
    # Maximum 3 scripts at a time to prevent resource exhaustion

    # Batch 1: Run first 3 scripts concurrently
    print("Starting Batch 1: New USA Scripts 1-3")
    batch1_tasks = [
        asyncio.create_task(script1()),
        asyncio.create_task(script2()),
        asyncio.create_task(script3())
    ]
    batch1_results = await asyncio.gather(*batch1_tasks, return_exceptions=True)

    # Log batch 1 results
    for i, result in enumerate(batch1_results, 1):
        if isinstance(result, Exception):
            print(f"New USA Script {i} failed: {result}")
        else:
            print(f"New USA Script {i} completed successfully")

    # Batch 2: Run next 3 scripts concurrently
    print("Starting Batch 2: New USA Scripts 4-6")
    batch2_tasks = [
        asyncio.create_task(script4()),
        asyncio.create_task(script5()),
        asyncio.create_task(script6())
    ]
    batch2_results = await asyncio.gather(*batch2_tasks, return_exceptions=True)

    # Log batch 2 results
    for i, result in enumerate(batch2_results, 4):
        if isinstance(result, Exception):
            print(f"New USA Script {i} failed: {result}")
        else:
            print(f"New USA Script {i} completed successfully")

    # Batch 3: Run next 3 scripts concurrently
    print("Starting Batch 3: New USA Scripts 7-9")
    batch3_tasks = [
        asyncio.create_task(script7()),
        asyncio.create_task(script8()),
        asyncio.create_task(script9())
    ]
    batch3_results = await asyncio.gather(*batch3_tasks, return_exceptions=True)

    # Log batch 3 results
    for i, result in enumerate(batch3_results, 7):
        if isinstance(result, Exception):
            print(f"New USA Script {i} failed: {result}")
        else:
            print(f"New USA Script {i} completed successfully")

    # Batch 4: Run remaining 1 script
    print("Starting Batch 4: New USA Script 10")
    batch4_tasks = [
        asyncio.create_task(script10())
    ]
    batch4_results = await asyncio.gather(*batch4_tasks, return_exceptions=True)

    # Log batch 4 results
    for i, result in enumerate(batch4_results, 10):
        if isinstance(result, Exception):
            print(f"New USA Script {i} failed: {result}")
        else:
            print(f"New USA Script {i} completed successfully")
=======
def main():
    script1()
    script2()
    script3()
    script4()
    script5()
    script6()
    script7()
    script8()
    script9()
    #script10()
>>>>>>> Stashed changes
