import re
import os
import csv
from urllib.parse import urljoin, urlparse
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError, <PERSON><PERSON><PERSON> as PlaywrightError
import sys
import traceback
import logging
import asyncio
from config.core.settings import get_settings
from etl.extractor.models import ScrappedJDLinkModel
from etl.loader.load_to_postgres import PostgresLoader
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def load_configs_csv(filename: str):
    """Loads site configurations from a CSV file located in the same directory as this script."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    filepath = os.path.join(script_dir, filename)

    configs = {}
    logger.info(f"Attempting to load configurations from {filepath}")
    try:
        with open(filepath, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            if not reader.fieldnames:
                logger.error(f"Error: CSV file '{filepath}' is empty or has no header.")
                return None

            # Essential headers for basic operation
            required_headers = ['company_name', 'start_url', 'job_link_locator_strategy', 'job_link_locator_value']
            missing_headers = [h for h in required_headers if h not in reader.fieldnames]
            if missing_headers:
                logger.error(f"CSV file '{filepath}' is missing essential header columns: {', '.join(missing_headers)}. Cannot proceed.")
                return None

            # Helper to safely get and strip values, providing defaults
            def get_safe_stripped_value(row_dict: dict, key, default_if_missing_or_none=''):
                val = row_dict.get(key)
                if val is None:
                    return default_if_missing_or_none
                return val.strip()

            for i, row in enumerate(reader):
                company_name = get_safe_stripped_value(row, 'company_name')
                if not company_name:
                    logger.warning(f"Skipping row {i+2} in {filepath} due to missing or empty 'company_name'.")
                    continue

                config_entry = {}

                # --- Mandatory Fields ---
                config_entry['start_url'] = get_safe_stripped_value(row, 'start_url')
                if not config_entry['start_url']:
                    logger.warning(f"Skipping site '{company_name}' (row {i+2}) due to missing or empty 'start_url'.")
                    continue

                jl_strategy = get_safe_stripped_value(row, 'job_link_locator_strategy')
                jl_value = get_safe_stripped_value(row, 'job_link_locator_value')
                if jl_strategy and jl_value:
                    config_entry['job_link_locator'] = [jl_strategy, jl_value]
                else:
                    logger.warning(f"Skipping site '{company_name}' (row {i+2}) due to missing 'job_link_locator_strategy' or 'job_link_locator_value'.")
                    continue

                # --- Optional Locator Fields ---
                for loc_key_base in ["initial_wait_locator", "navigation_click_locator", "pagination_locator"]:
                    strategy = get_safe_stripped_value(row, f"{loc_key_base}_strategy")
                    value = get_safe_stripped_value(row, f"{loc_key_base}_value")
                    if strategy and value:
                        config_entry[loc_key_base] = [strategy, value]

                # --- String/Optional Fields with Defaults ---
                config_entry['pagination_type'] = get_safe_stripped_value(row, 'pagination_type', 'none') or 'none'
                config_entry['base_url'] = get_safe_stripped_value(row, 'base_url') or None
                config_entry['link_filter_keyword'] = get_safe_stripped_value(row, 'link_filter_keyword') or None
                config_entry['link_extraction_method'] = get_safe_stripped_value(row, 'link_extraction_method', 'href') or 'href'
                config_entry['link_attribute'] = get_safe_stripped_value(row, 'link_attribute') or None
                config_entry['onclick_regex'] = get_safe_stripped_value(row, 'onclick_regex') or None
                config_entry['pagination_locator_template'] = get_safe_stripped_value(row, 'pagination_locator_template') or None
                config_entry['content_selectors'] = get_safe_stripped_value(row, 'content_selectors') or None # Keep for output
                config_entry['company_url'] = get_safe_stripped_value(row, 'company_url') or None  # Add company_url
                config_entry['company_linkedin'] = get_safe_stripped_value(row, 'company_linkedin') or None  # Add company_linkedin


                # --- Integer Fields with Defaults ---
                for int_key, default_val in [
                    ('initial_wait_time', 5), ('initial_wait_timeout', 20),
                    ('navigation_wait_time', 5), ('page_load_timeout', 60),
                    ('pagination_wait_time', 5), ('max_pages', 50) # Added max pages
                ]:
                    val_str = get_safe_stripped_value(row, int_key)
                    try:
                        config_entry[int_key] = int(val_str) if val_str else default_val
                    except ValueError:
                        logger.warning(f"Invalid integer value '{val_str}' for '{int_key}' in site '{company_name}' (row {i+2}). Using default {default_val}.")
                        config_entry[int_key] = default_val

                configs[company_name] = config_entry

        if not configs:
            logger.warning(f"No valid configurations were loaded from {filepath}.")
            return None
        logger.info(f"Successfully loaded {len(configs)} configurations from {filepath}")
        return configs

    except FileNotFoundError:
        logger.error(f"Configuration file '{filepath}' not found.")
        # Use raise instead of sys.exit(1) inside functions called by async main
        raise FileNotFoundError(f"Configuration file '{filepath}' not found.")
    except Exception as e:
        logger.error(f"Unexpected error while loading CSV configurations from {filepath}: {e}")
        logger.error(traceback.format_exc())
        # Use raise instead of sys.exit(1)
        raise RuntimeError(f"Error loading CSV configurations: {e}")

# --- Helper Functions (Remain Synchronous) ---

def get_playwright_selector(locator_tuple):
    """Converts [strategy, value] list to Playwright selector string."""
    if not isinstance(locator_tuple, (list, tuple)) or len(locator_tuple) != 2:
        logger.warning(f"Invalid locator_tuple format: {locator_tuple}. Expected [strategy, value]. Using value as is.")
        return str(locator_tuple) # Return as string to avoid downstream errors

    strategy, value = locator_tuple
    strategy_lower = strategy.lower().replace(" ", "_").replace("-", "_")

    if strategy_lower in ["css", "css_selector"]:
        return f"css={value}"
    elif strategy_lower == "xpath":
        # Basic check if it looks like a valid XPath starting
        if not (value.startswith('/') or value.startswith('(') or value.startswith('.')):
             logger.warning(f"XPath '{value}' might be invalid (doesn't start with /, (, or .). Using it anyway.")
        return f"xpath={value}"
    elif strategy_lower == "id":
        return f"#{value}" # Equivalent to css=#value
    elif strategy_lower == "name":
         return f"[name='{value}']" # Equivalent to css=[name='value']
    elif strategy_lower in ["link_text", "text"]:
         return f"text={value}"
    elif strategy_lower in ["partial_link_text", "text_contains"]:
         return f"text*={value}"
    elif strategy_lower in ["tag_name", "tag"]:
         return f"{value}"
    elif strategy_lower in ["class", "class_name"]:
        return f".{value.replace(' ', '.')}" # Handle multiple classes potentially
    else:
        logger.warning(f"Unsupported locator strategy '{strategy}' for Playwright conversion. Using value directly as CSS selector: {value}")
        return f"css={value}" # Default guess to CSS


def resolve_url(base_url, link_url:str|None):
    """Resolves a potentially relative link URL against a base URL."""
    if not link_url:
        return None
    link_url = link_url.strip()
    if not link_url or link_url.lower().startswith('javascript:'):
        return None

    if link_url.startswith("//"):
        parsed_base = urlparse(base_url)
        scheme = parsed_base.scheme if parsed_base.scheme else 'https' # Default to https
        return f"{scheme}:{link_url}"

    try:
        absolute_url = urljoin(base_url, link_url)
        if urlparse(absolute_url).scheme in ['http', 'https'] and urlparse(absolute_url).netloc:
            return absolute_url
        else:
            logger.warning(f"Resolved URL '{absolute_url}' from base '{base_url}' and link '{link_url}' seems invalid. Skipping.")
            return None
    except Exception as e:
        logger.error(f"Error resolving URL: base='{base_url}', link='{link_url}'. Error: {e}")
        return None

# --- Core Asynchronous Scraping Function ---

async def scrape_job_links_async(site_name, configs):
    """Scrapes job links for a specific site using Playwright async API based on loaded configs."""
    if site_name not in configs:
        logger.error(f"Configuration for site '{site_name}' not found.")
        return set() # Return empty set on config error

    config = configs[site_name]
    all_job_links = set()
    start_time = asyncio.get_event_loop().time()

    logger.info(f"\n--- Starting scrape for: {site_name} ---")
    logger.info(f"Start URL: {config['start_url']}")

    browser = None
    context = None
    page = None

    # Use async with for playwright
    async with async_playwright() as p:
        try:
            # Use await for launch
            browser = await p.chromium.launch(headless=True, args=["--no-sandbox", "--disable-dev-shm-usage"])
        except PlaywrightError as launch_err:
            logger.error(f"Error launching browser for {site_name}: {launch_err}")
            logger.error("Ensure browser binaries are installed (run 'playwright install')")
            # Do not raise, return empty set
            return set()

        try:
            # Use await for new_context and new_page
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36',
                accept_downloads=False, # Don't automatically download files
                # viewport={'width': 1280, 'height': 1024} # Define viewport if needed
            )
            # --- Handle Permissions (Basic Example) ---
            # This is a very basic example. Real permission dialogs are complex.
            # You might need more specific handlers based on the site.
            await context.grant_permissions(['geolocation', 'notifications'], origin=config['start_url'])
            # ---

            page = await context.new_page()

            default_timeout_ms = config.get("page_load_timeout", 60) * 1000
            # Use await for set_default_timeout and set_default_navigation_timeout
            page.set_default_timeout(default_timeout_ms)
            page.set_default_navigation_timeout(default_timeout_ms)


            logger.info("Navigating to start URL...")
            try:
                # Use await for goto
                response = await page.goto(config['start_url'], wait_until='domcontentloaded', timeout=default_timeout_ms)
                if response and not response.ok:
                    logger.warning(f"Received non-OK status code {response.status} for {config['start_url']}")
                logger.info("Navigation successful.")
            except PlaywrightTimeoutError:
                logger.error(f"Timeout navigating to {config['start_url']} for {site_name}.")
                return set() # Return empty set on navigation timeout
            except PlaywrightError as e:
                logger.error(f"Playwright navigation error for {site_name} to {config['start_url']}: {e}")
                return set() # Return empty set on navigation error

            # --- Initial Action/Wait ---
            nav_clicked = False
            if config.get("navigation_click_locator") and config.get("pagination_type") == "click_to_navigate":
                nav_selector = get_playwright_selector(config["navigation_click_locator"])
                initial_wait_timeout_ms = config.get("initial_wait_timeout", 20) * 1000
                nav_wait_ms = config.get("navigation_wait_time", 5) * 1000
                try:
                    logger.info(f"Attempting initial navigation click: {nav_selector}")
                    nav_button = page.locator(nav_selector)
                    # Use await for wait_for and click
                    await nav_button.wait_for(state="visible", timeout=initial_wait_timeout_ms)
                    await nav_button.click(timeout=initial_wait_timeout_ms // 2)
                    logger.info(f"Navigation element clicked. Waiting {nav_wait_ms / 1000}s...")
                    # Use await for wait_for_timeout
                    await page.wait_for_timeout(nav_wait_ms)
                    nav_clicked = True
                except PlaywrightTimeoutError:
                    logger.warning(f"Timeout finding or clicking initial navigation element {nav_selector} for {site_name}.")
                    # Decide whether to continue or fail based on the site's behavior
                    # return set() # Uncomment to fail if initial nav fails
                except PlaywrightError as e:
                    logger.error(f"Error during initial navigation click for {site_name}: {e}")
                    return set() # Usually safer to fail here

            if config.get("initial_wait_locator") and not nav_clicked:
                wait_selector = get_playwright_selector(config["initial_wait_locator"])
                initial_wait_timeout_ms = config.get("initial_wait_timeout", 20) * 1000
                try:
                    logger.info(f"Waiting for initial element: {wait_selector} (timeout: {initial_wait_timeout_ms / 1000}s)")
                    # Use await for wait_for
                    await page.locator(wait_selector).first.wait_for(state='attached', timeout=initial_wait_timeout_ms)
                    logger.info("Initial element found.")
                except PlaywrightTimeoutError:
                    logger.warning(f"Initial wait element {wait_selector} not found within timeout for {site_name}. Continuing...")
                except PlaywrightError as e:
                     logger.error(f"Error waiting for initial element {wait_selector}: {e}. Continuing...")

            elif not config.get("navigation_click_locator") and not config.get("initial_wait_locator"):
                 initial_wait_ms = config.get("initial_wait_time", 5) * 1000
                 if initial_wait_ms > 0:
                     logger.info(f"Performing initial wait of {initial_wait_ms / 1000} seconds...")
                     # Use await for wait_for_timeout
                     await page.wait_for_timeout(initial_wait_ms)

            # --- Scraping and Pagination Loop ---
            current_page_num = 1
            max_pages = config.get("max_pages", 50)
            consecutive_no_new_links = 0 # Counter for scroll/load more loops

            while current_page_num <= max_pages:
                logger.info(f"Scraping page/view {current_page_num}...")
                page_links_found = set()
                # Use await for wait_for_timeout
                await page.wait_for_timeout(1500) # Small delay for dynamic content settling

                try:
                    job_link_selector = get_playwright_selector(config["job_link_locator"])
                    link_extraction_method = config.get("link_extraction_method", "href")
                    link_attribute = config.get("link_attribute")
                    onclick_regex_str = config.get("onclick_regex")
                    link_filter_keyword = config.get("link_filter_keyword")
                    base_url = config.get("base_url") or page.url # Use page URL as fallback base

                    # Ensure base_url ends with '/' for urljoin
                    if base_url and not base_url.endswith('/'):
                         parsed_uri = urlparse(base_url)
                         # Check if it looks like a full URL, not just a path component
                         if parsed_uri.scheme and parsed_uri.netloc:
                             base_url += '/'
                         else: # If it's just a path, resolve against page URL first
                             # Use await for page.url in case it's async
                             base_url = urljoin(await page.url, base_url + '/') # Note: page.url is sync, but being cautious


                    onclick_regex_compiled = None
                    if link_extraction_method == "onclick_regex" and onclick_regex_str:
                        try:
                            onclick_regex_compiled = re.compile(onclick_regex_str)
                        except re.error as re_err:
                            logger.warning(f"Invalid onclick_regex '{onclick_regex_str}' for {site_name}: {re_err}. Will not use.")
                            link_extraction_method = "href" # Fallback

                    job_elements = []
                    try:
                        # Wait briefly for the first element to confirm they exist on the page/view
                        # Use await for wait_for
                        await page.locator(job_link_selector).first.wait_for(state='attached', timeout=15000)
                        # Use await for .all()
                        job_elements = await page.locator(job_link_selector).all()
                        logger.info(f"Found {len(job_elements)} potential link elements on page {current_page_num}.")
                    except PlaywrightTimeoutError:
                        logger.warning(f"Timeout waiting for job links ({job_link_selector}) on page {current_page_num}. Assuming no more links on this page/view.")
                        # If it's the first page and nothing found, might be a config issue
                        if current_page_num == 1 and not all_job_links:
                            logger.warning(f"No job links found on the first page for {site_name}. Check locators.")
                        # For scrolling, this might just mean the end
                        if config.get("pagination_type") != 'scroll':
                             break # Exit loop if not scrolling and no links found
                    except PlaywrightError as e:
                         logger.error(f"Error locating job links ({job_link_selector}) on page {current_page_num}: {e}")
                         break # Exit loop on error


                    if not job_elements and config.get("pagination_type") != 'scroll':
                         if current_page_num > 1:
                              logger.info("No more job link elements found. Ending pagination.")
                         break # Exit loop if not scrolling and no elements found

                    for i, element in enumerate(job_elements):
                        raw_link = None
                        try:
                            # Use await for get_attribute
                            if link_extraction_method == "href":
                                raw_link = await element.get_attribute("href")
                            elif link_extraction_method == "attribute" and link_attribute:
                                raw_link = await element.get_attribute(link_attribute)
                            elif link_extraction_method == "onclick_regex" and onclick_regex_compiled:
                                onclick_attr = await element.get_attribute("onclick")
                                if onclick_attr:
                                    match = onclick_regex_compiled.search(onclick_attr)
                                    if match:
                                        # Assuming regex captures the URL part in group 1
                                        raw_link = match.group(1)
                            else: # Fallback or unknown method
                                raw_link = await element.get_attribute("href") # Default to href
                                if link_extraction_method not in ["href", "attribute", "onclick_regex"]:
                                     logger.warning(f"Unknown link_extraction_method '{link_extraction_method}'. Defaulting to 'href'.")

                            # Use await for page.url in resolve_url if base_url is None
                            absolute_link = resolve_url(base_url, raw_link)

                            if absolute_link:
                                # Apply filter keyword if specified
                                if link_filter_keyword and link_filter_keyword not in absolute_link:
                                    continue
                                # Add to page-specific set to track new links *on this page*
                                page_links_found.add(absolute_link)

                        except PlaywrightError as el_err:
                             logger.error(f"Error processing element {i+1} on page {current_page_num}: {el_err}")
                        except Exception as e:
                            logger.error(f"Unexpected error processing element {i+1}: {e}")
                            # traceback.print_exc() # Uncomment for more detail if needed

                except Exception as page_err:
                    logger.error(f"Error during link extraction on page {current_page_num} for {site_name}: {page_err}")
                    logger.error(traceback.format_exc())
                    break # Stop processing this site on major error

                newly_added_count = len(page_links_found - all_job_links)
                logger.info(f"Found {len(page_links_found)} unique links on this page, {newly_added_count} are new.")
                all_job_links.update(page_links_found)

                # --- Pagination Handling ---
                pagination_type = config.get("pagination_type", "none")
                pagination_wait_ms = config.get("pagination_wait_time", 5) * 1000

                # Exit conditions for load_more/scroll types
                if pagination_type in ["load_more", "load_more_js", "scroll", "load_more_scroll"]:
                    if newly_added_count == 0 and current_page_num > 1 :
                         consecutive_no_new_links += 1
                         logger.info(f"No new links found on page {current_page_num}. Consecutive count: {consecutive_no_new_links}")
                         if consecutive_no_new_links >= 2: # Stop after 2 consecutive scrolls/loads with no new links
                              logger.info(f"Stopping {pagination_type} pagination for {site_name} after {consecutive_no_new_links} attempts with no new links.")
                              break
                    else:
                        consecutive_no_new_links = 0 # Reset if new links were found

                pagination_successful = False
                if pagination_type == "none" or (pagination_type == "click_to_navigate" and nav_clicked): # Already navigated
                    logger.info(f"Pagination type is '{pagination_type}'. Stopping pagination loop.")
                    break

                elif pagination_type == "scroll":
                    logger.info("Scrolling down...")
                    # Use await for evaluate
                    last_height = await page.evaluate("document.body.scrollHeight")
                    await page.evaluate("window.scrollTo(0, document.body.scrollHeight);")
                    # Use await for wait_for_timeout
                    await page.wait_for_timeout(pagination_wait_ms + 1000) # Extra wait after scroll
                    # Optional: Wait for network idle or a specific element to appear after scroll
                    # await page.wait_for_load_state('networkidle', timeout=5000)
                    new_height = await page.evaluate("document.body.scrollHeight")
                    if new_height == last_height:
                        logger.info("Scroll height did not change. Assuming end of content.")
                        break # Exit if scroll height doesn't change
                    else:
                         logger.info(f"Scrolled from {last_height} to {new_height}. Proceeding to scrape new view.")
                         pagination_successful = True # Indicate scroll happened

                elif pagination_type in ["next_button", "load_more", "next_button_js", "load_more_js", "next_button_url", "next_button_data_table", "load_more_scroll", "next_button_js_scroll"]:
                    pag_selector = None
                    if config.get("pagination_locator"):
                        pag_selector = get_playwright_selector(config["pagination_locator"])
                    elif pagination_type == "next_button_data_table" and config.get("pagination_locator_template"):
                        try:
                            next_page_num_for_selector = current_page_num + 1
                            template = config["pagination_locator_template"]
                            pag_selector = get_playwright_selector(['XPATH', template.format(next_page_num_for_selector)]) # Assume XPath for templates usually
                            logger.info(f"Using pagination template for page {next_page_num_for_selector}: {pag_selector}")
                        except Exception as fmt_err:
                             logger.error(f"Error formatting pagination template for page {next_page_num_for_selector}: {fmt_err}")
                             break

                    if not pag_selector:
                        logger.error(f"Pagination type '{pagination_type}' requires 'pagination_locator' or a valid 'pagination_locator_template'. Stopping.")
                        break

                    try:
                        logger.info(f"Looking for pagination element: {pag_selector}")
                        pagination_element = page.locator(pag_selector)
                        # Use await for wait_for
                        await pagination_element.wait_for(state='attached', timeout=10000) # Wait longer for pagination element

                        # Use await for is_visible
                        if not await pagination_element.is_visible(timeout=5000):
                            logger.info("Pagination element found but not visible. Assuming end of pages.")
                            break

                        # Handle different click/navigation types
                        if pagination_type == "next_button_url":
                             # Use await for get_attribute
                             next_url = await pagination_element.get_attribute('href')
                             # Use await for page.url in resolve_url
                             resolved_next_url = resolve_url(await page.url, next_url)
                             # Check if resolved_next_url is different from current page url
                             if resolved_next_url and resolved_next_url != await page.url and resolved_next_url != "javascript:void(0)":
                                 logger.info(f"Navigating to next page URL: {resolved_next_url}")
                                 # Use await for goto
                                 await page.goto(resolved_next_url, wait_until='domcontentloaded', timeout=default_timeout_ms)
                                 pagination_successful = True
                             else:
                                 logger.info("Next button found but no valid/different URL. Assuming end.")
                                 break
                        else: # All other click-based types
                            logger.info(f"Clicking pagination element ({pagination_type})...")
                            # Use await for scroll_into_view_if_needed and click
                            await pagination_element.scroll_into_view_if_needed(timeout=5000)
                            await pagination_element.click(timeout=10000)
                            logger.info(f"Pagination element clicked. Waiting {pagination_wait_ms / 1000}s...")
                            # Use await for wait_for_timeout
                            await page.wait_for_timeout(pagination_wait_ms)
                            # Optional: Add a wait for load state if needed after JS clicks
                            # if 'js' in pagination_type:
                            #    await page.wait_for_load_state('networkidle', timeout=10000)
                            pagination_successful = True

                    except PlaywrightTimeoutError:
                        logger.warning(f"Pagination element '{pag_selector}' not found or not interactable within timeout. Assuming end.")
                        break
                    except PlaywrightError as pag_err:
                        logger.error(f"Error interacting with pagination element '{pag_selector}': {pag_err}")
                        logger.error(traceback.format_exc())
                        break
                    except Exception as e:
                        logger.error(f"Unexpected error during pagination action: {e}")
                        logger.error(traceback.format_exc())
                        break

                else:
                    logger.error(f"Unknown pagination_type '{pagination_type}'. Stopping.")
                    break

                # Break if pagination action failed (unless it was scroll type where height check handles it)
                if not pagination_successful and pagination_type != "scroll":
                    logger.info(f"Pagination action failed for type '{pagination_type}'. Stopping.")
                    break

                current_page_num += 1

            # End of while loop (max pages or break condition met)
            if current_page_num > max_pages:
                logger.info(f"Reached max pages limit ({max_pages}) for {site_name}.")

        except PlaywrightError as e:
            logger.error(f"A critical Playwright error occurred for {site_name}: {e}")
            logger.error(traceback.format_exc())
            # Ensure browser/context/page are closed even on critical error
            if page and not page.is_closed():
                 try: await page.close()
                 except Exception as ex_close: logger.error(f"Error closing page after critical error for {site_name}: {ex_close}")
            if context:
                 try: await context.close()
                 except Exception as ex_close: logger.error(f"Error closing context after critical error for {site_name}: {ex_close}")
            if browser and browser.is_connected(): # is_connected might be needed for async
                 try: await browser.close()
                 except Exception as ex_close: logger.error(f"Error closing browser after critical error for {site_name}: {ex_close}")
            # Return empty set on critical error
            return set()

        except Exception as e:
            logger.error(f"An unexpected error occurred during scraping for {site_name}: {e}")
            logger.error(traceback.format_exc())
            # Ensure browser/context/page are closed even on unexpected error
            if page and not page.is_closed():
                 try: await page.close()
                 except Exception as ex_close: logger.error(f"Error closing page after unexpected error for {site_name}: {ex_close}")
            if context:
                 try: await context.close()
                 except Exception as ex_close: logger.error(f"Error closing context after unexpected error for {site_name}: {ex_close}")
            if browser and browser.is_connected():
                 try: await browser.close()
                 except Exception as ex_close: logger.error(f"Error closing browser after unexpected error for {site_name}: {ex_close}")
            # Return empty set on unexpected error
            return set()

        # The `finally` block inside async with is still useful for ensuring close,
        # but the return statements above need their own closing logic in case the error
        # happens before finally is reached (e.g. in browser launch) or if `asyncio.gather`
        # cancels the task. For robustness, closing is added in except blocks above.
        # The async with handles closing on normal completion or exceptions raised within the block.
        # The errors like "Event loop is closed!" likely indicate the async context was already torn down.


    duration = asyncio.get_event_loop().time() - start_time
    logger.info(f"--- Finished scrape for: {site_name} in {duration:.2f} seconds ---")
    logger.info(f"Found {len(all_job_links)} unique links.")
    return all_job_links # Return the set of links

# --- Main Asynchronous Execution ---

# Make the main function async
async def amain():

    db = PostgresLoader()
    db.connect()
    config_file = "uk_site_configs.csv"
    sites = None  # if you wanna specific company
    output_file = f"{get_settings().LOCAL_SCRAPED_LINKS_DIR}/uk_scrapped_jd_links.csv"

    # Load configurations - this part can remain synchronous
    try:
        site_configs = load_configs_csv(config_file)
        if not site_configs:
            # Error messages are printed within load_configs_csv
            sys.exit(1) # Exit if config loading fails
    except Exception:
         sys.exit(1) # Exit on any exception during config loading

    if sites:
        # Filter sites to scrape based on command-line input
        sites_to_scrape = []
        invalid_sites = []
        for site_arg in sites:
            if site_arg in site_configs:
                sites_to_scrape.append(site_arg)
            else:
                invalid_sites.append(site_arg)

        if invalid_sites:
            logger.warning(f"The following specified sites were not found in the config file and will be skipped: {', '.join(invalid_sites)}")
        if not sites_to_scrape:
            logger.error("No valid sites specified to scrape were found in the configuration.")
            sys.exit(1)
        logger.info(f"Scraping specified sites: {', '.join(sites_to_scrape)}")
    else:
        # Scrape all sites defined in the config file
        sites_to_scrape = list(site_configs.keys())
        logger.info(f"Scraping all {len(sites_to_scrape)} sites defined in the configuration file.")

    all_results_for_csv = []
    summary_counts = {}
    total_links_saved_to_csv = 0 # Renamed for clarity, this is the count *in the CSV*

    logger.info("Ensuring Playwright browsers are installed (run 'playwright install' if you encounter issues)...")
    # Attempt a quick check using async playwright
    try:
        async with async_playwright() as p:
            # Use await for launch and close
            browser = await p.chromium.launch(headless=True)
            await browser.close()
        logger.info("Playwright chromium check successful.")
    except Exception as install_err:
        logger.error(f"Playwright check failed: {install_err}. Please run 'playwright install'.")
        # Optionally exit if check fails, or just warn
        # sys.exit(1)

    batch_size = 10
    batch_run = (len(sites_to_scrape) + batch_size-1) // batch_size
    batches = []
    for i in range(0,batch_run):
        batches.append(sites_to_scrape[i*batch_size:min(len(sites_to_scrape),(i+1)*batch_size)])

        results = []
    for batch in batches:
        tasks = [scrape_job_links_async(name, site_configs) for name in batch]
        results.extend(await asyncio.gather(*tasks))


    # Iterate through the results and process them
    for i, company_name in enumerate(sites_to_scrape):
        result = results[i] # Get the result for the current company
        config: dict = site_configs[company_name] # Get config for this company

        if isinstance(result, Exception):
            # Handle scraping errors for this specific site
            logger.error(f"Scraping task failed for {company_name}: {result}")
            scraped_links = set() # Treat as 0 links found for summary and CSV
        else:
            # Task succeeded, result is the set of links
            scraped_links = result

        # Capture the number of links found for this company for the summary
        summary_counts[company_name] = len(scraped_links)
        logger.info(f"Links found for {company_name}: {len(scraped_links)}")
        
        # check the company exists or create it 
        company_id, created = db.get_or_create_company(company_name, config.get("company_url",""), config)
        logger.info(f"Company ID: {company_id}, {"created" if created else 'already exists'}")

        # Apply your filter logic here
        links_to_process = set(scraped_links)
        # it should be unique links
        links_to_process = db.process_new_links(links_to_process,company_id)

        # Get config details for CSV output
        content_selectors_for_site = config.get('content_selectors')
        link_count_for_csv = 0
        first_iteration = True
        # Use links_to_process after applying your filter logic
        links_to_process = links_to_process if links_to_process else set()
        for link in sorted(list(links_to_process)):
            if first_iteration:
                # First row has full details
                all_results_for_csv.append(
                    ScrappedJDLinkModel(company_id=company_id,
                                        job_url=link,
                                        content_selectors=str(content_selectors_for_site) if content_selectors_for_site else None,
                                        ).model_dump()
                )
                first_iteration = False
            else:
                # Subsequent rows have empty company_name for cleaner CSV
                all_results_for_csv.append(
                    ScrappedJDLinkModel(company_id=company_id,
                        job_url=link).model_dump())
            link_count_for_csv += 1

        total_links_saved_to_csv += link_count_for_csv # Increment total for CSV count


    # # --- Summary and Output ---
    # logger.info("\n\n--- Scraping Summary ---")
    # # Iterate through the summary_counts dictionary populated during result processing
    # if summary_counts:
    #     # Sort by company name for consistent output order
    #     for company_name in sorted(summary_counts.keys()):
    #          num_jobs = summary_counts[company_name]
    #          logger.info(f"- {company_name}: {num_jobs} links")
    # else:
    #     logger.info("No sites were attempted for scraping or configuration loading failed.")


    logger.info("==============================")
    logger.info(f"Total unique links saved to CSV across {len(sites_to_scrape)} site(s): {total_links_saved_to_csv}")
    logger.info("==============================")

    if all_results_for_csv:
        try:
            # Ensure the output directory exists if a path is specified
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True, mode=0o755)
                logger.info(f"Created output directory: {output_dir}")

            with open(output_file, "w", newline='', encoding='utf-8') as f:
                # Define fieldnames including the potentially empty content_selectors, company_url, company_linkedin
                fieldnames = ['company_id', 'job_url', 'content_selectors','source']
                writer = csv.DictWriter(f, fieldnames=fieldnames)

                writer.writeheader()
                writer.writerows(all_results_for_csv)
            logger.info(f"Results successfully saved to {output_file}")
        except IOError as e:
            logger.error(f"Could not write to output file '{output_file}'. Check permissions or path. Error: {e}")
            logger.debug("Stack trace:", exc_info=True)
        except Exception as e:
            logger.error(f"Error saving results to CSV file '{output_file}': {e}")
            logger.debug("Stack trace:", exc_info=True)
    else:
        logger.info("No links were scraped successfully. Output file will not be created.")

    logger.info("Script finished.")
    # You might want to return the output file path or something else useful
    # return output_file # Changed to return None as main doesn't necessarily need to return the path

async def main():
    await amain()

if __name__ == "__main__":
    asyncio.run(main())