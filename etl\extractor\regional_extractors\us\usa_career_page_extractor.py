import asyncio
from .usa_jd_link_extractor_script1 import main as usa_jd_link_extractor1
from .usa_jd_link_extractor_script2 import main as usa_jd_link_extractor2
from .usa_jd_link_extractor_script3 import main as usa_jd_link_extractor3
from .usa_jd_link_extractor_script4 import main as usa_jd_link_extractor4
from .usa_jd_link_extractor_script5 import main as usa_jd_link_extractor5
from .usa_jd_link_extractor_script6 import main as usa_jd_link_extractor6

async def main():
    # Running scripts in controlled batches to avoid system crash
    # Maximum 3 scripts at a time to prevent resource exhaustion

    # Batch 1: Run first 3 scripts concurrently
    print("Starting Batch 1: USA Scripts 1-3")
    batch1_tasks = [
        asyncio.create_task(usa_jd_link_extractor1()),
        asyncio.create_task(usa_jd_link_extractor2()),
        asyncio.create_task(usa_jd_link_extractor3())
    ]
    batch1_results = await asyncio.gather(*batch1_tasks, return_exceptions=True)

    # Log batch 1 results
    for i, result in enumerate(batch1_results, 1):
        if isinstance(result, Exception):
            print(f"USA Script {i} failed: {result}")
        else:
            print(f"USA Script {i} completed successfully")

    # Batch 2: Run remaining 3 scripts concurrently
    print("Starting Batch 2: USA Scripts 4-6")
    batch2_tasks = [
        asyncio.create_task(usa_jd_link_extractor4()),
        asyncio.create_task(usa_jd_link_extractor5()),
        asyncio.create_task(usa_jd_link_extractor6())
    ]
    batch2_results = await asyncio.gather(*batch2_tasks, return_exceptions=True)

    # Log batch 2 results
    for i, result in enumerate(batch2_results, 4):
        if isinstance(result, Exception):
            print(f"USA Script {i} failed: {result}")
        else:
            print(f"USA Script {i} completed successfully")

if __name__ == "__main__":
    main()