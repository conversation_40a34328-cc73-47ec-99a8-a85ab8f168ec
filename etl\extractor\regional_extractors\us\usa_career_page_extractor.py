import asyncio
from .usa_jd_link_extractor_script1 import main as usa_jd_link_extractor1
from .usa_jd_link_extractor_script2 import main as usa_jd_link_extractor2
from .usa_jd_link_extractor_script3 import main as usa_jd_link_extractor3
from .usa_jd_link_extractor_script4 import main as usa_jd_link_extractor4
from .usa_jd_link_extractor_script5 import main as usa_jd_link_extractor5
from .usa_jd_link_extractor_script6 import main as usa_jd_link_extractor6

async def main():
    # Running all scripts concurrently for maximum speed
    tasks = [
        asyncio.create_task(usa_jd_link_extractor1()),
        asyncio.create_task(usa_jd_link_extractor2()),
        asyncio.create_task(usa_jd_link_extractor3()),
        asyncio.create_task(usa_jd_link_extractor4()),
        asyncio.create_task(usa_jd_link_extractor5()),
        asyncio.create_task(usa_jd_link_extractor6())
    ]

    # Wait for all scripts to complete
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Log results
    for i, result in enumerate(results, 1):
        if isinstance(result, Exception):
            print(f"USA Script {i} failed: {result}")
        else:
            print(f"USA Script {i} completed successfully")

if __name__ == "__main__":
    main()