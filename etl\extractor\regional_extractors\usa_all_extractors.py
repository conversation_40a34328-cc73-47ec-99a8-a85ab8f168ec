import asyncio
from .us.usa_career_page_extractor import main as usa_extractor
from .new_usa.usa_career_page_extractor import main as new_usa_extractor

async def main():
    """
    Run USA extractors sequentially to prevent system crashes
    Maximum 3 scripts at a time per extractor
    """
    print("Starting USA extractors sequentially to prevent system crashes...")
    
    # Step 1: Run USA Extractor (us) first - 6 scripts in batches of 3
    print("\n" + "="*60)
    print("STEP 1: Starting USA Extractor (us) - 6 scripts")
    print("="*60)
    try:
        await usa_extractor()
        print("✅ USA Extractor (us) completed successfully")
    except Exception as e:
        print(f"❌ USA Extractor (us) failed: {e}")
    
    # Step 2: Run New USA Extractor (new_usa) second - 10 scripts in batches of 3
    print("\n" + "="*60)
    print("STEP 2: Starting New USA Extractor (new_usa) - 10 scripts")
    print("="*60)
    try:
        await new_usa_extractor()
        print("✅ New USA Extractor (new_usa) completed successfully")
    except Exception as e:
        print(f"❌ New USA Extractor (new_usa) failed: {e}")
    
    print("\n" + "="*60)
    print("🎯 ALL USA EXTRACTORS COMPLETED!")
    print("Total: 16 scripts executed safely in sequential batches")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
