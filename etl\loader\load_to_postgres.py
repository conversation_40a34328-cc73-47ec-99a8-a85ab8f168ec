import os
import hashlib
from urllib.parse import quote_plus
import psycopg2
from psycopg2.extras import execute_values
from datetime import datetime
from typing import Any
from enum import IntEnum
import logging
import requests
import json
import psycopg2
import logging
from psycopg2.extras import Json

from config.core.settings import get_settings

logger = logging.getLogger(__name__)

class Source(IntEnum):
    """Integer Enum for Job Source."""
    LINKEDIN = 1
    INDEED = 2
    COMPANY_WEBSITE = 3
    OTHER = 4

    @classmethod
    def from_string(cls, source_str: str|None) -> int:
        """Converts a source string to its integer enum value."""
        if not source_str:
            return cls.OTHER.value
        if source_str.isnumeric():
            return int(source_str)
        try:
            return cls[source_str.upper()].value
        except KeyError:
            logger.warning(f"Unknown source string '{source_str}', defaulting to OTHER")
            return cls.OTHER.value

class LocationType(IntEnum):
    """Integer Enum for Job Location Type."""
    REMOTE = 1
    HYBRID = 2
    ONSITE = 3
    FLEXIBLE = 4
    NOT_SPECIFIED = 5

    @classmethod
    def from_string(cls, location_type_str: None|str) -> None | int:
        """Converts a location type string to its integer enum value."""
        if not location_type_str:
            return None
        try:
            return cls[location_type_str.upper()].value
        except KeyError:
            if location_type_str.isnumeric():
                return int(location_type_str)
            for location_type in cls:
                if location_type_str.lower() in location_type.name.lower():
                    return location_type.value
            logger.warning(f"Unknown location type string '{location_type_str}', using NULL")
            return cls.NOT_SPECIFIED.value

class JobType(IntEnum):
    """Integer Enum for Job Type."""
    FULL_TIME = 1
    PART_TIME = 2
    CONTRACT = 3
    INTERNSHIP = 4
    FREELANCE = 5
    TEMPORARY = 6
    NOT_SPECIFIED = 7

    @classmethod
    def from_string(cls, job_type_str: None|str) -> None | int:
        """Converts a job type string to its integer enum value."""
        if not job_type_str:
            return None
        try:
            return cls[job_type_str.upper().replace(' ', '_')].value
        except KeyError:
            if job_type_str.isnumeric():
                return int(job_type_str)
            for job_type in cls:
                if job_type_str.lower() in job_type.name.lower():
                    return job_type.value
            logger.warning(f"Unknown job type string '{job_type_str}', using NULL")
            return cls.NOT_SPECIFIED.value

class PostgresLoader:
    class  LinkedInRecruiterProfiles:
        @staticmethod
        def add_recruiter_linkedin_profiles(
            company_name: str, location: str
        ) -> str:
            logger.info(f"add_recruiter_linkedin_profiles called with {company_name} and {location}")

            # Define top job roles
            top_roles = [
                "CEO",
                "Founder",
                "Co-Founder",
                "Managing Director",
                "Director",
                "CTO",
                "CFO",
                "COO",
                "HR Manager",
                "Talent Acquisition",
                "Recruiter",
                "VP",
                "President",
                "Chief Human Resources Officer",
            ]

            # Create the search query
            role_query = " OR ".join([f'"{role}"' for role in top_roles])
            query = f'site:linkedin.com/in "{company_name}" "{location}" ({role_query})'

            api_key = os.getenv("SEARCH_ENGINE_API_kEY")
            search_engine_id = os.environ["SEARCH_ENGINE_ID"]

            # API request
            api_url = f"https://www.googleapis.com/customsearch/v1?q={quote_plus(query)}&key={api_key}&cx={search_engine_id}"
            response = requests.get(api_url, timeout=60)
            data = response.json()

            # Extract results
            results = []
            for item in data.get("items", []):
                title = item.get("title", "")
                link = item.get("link", "")
                if " - " in title:
                    name, designation = title.split(" - ", 1)
                else:
                    name = title
                    designation = ""

                results.append(
                    {
                        "recruiter_name": name.strip(),
                        "recruiter_url": link.strip(),
                        "designation": designation.strip(),
                    }
                )

            return results
    

    # Define the schema name and table names used by this loader
    SCHEMA_NAME = "job_marketing"
    COMPANY_TABLE = f"{SCHEMA_NAME}.t_company"
    JOB_TABLE = f"{SCHEMA_NAME}.t_jd"
    RECRUITER_PROFILE_TABLE = f"{SCHEMA_NAME}.t_recruiter_profile"


    def __init__(self):
        """
        Initialize PostgresLoader with database configuration from environment variables.
        """
        setting = get_settings()
        self.db_config = {
            "host":     setting.POSTGRES_HOST,
            "port":     setting.POSTGRES_PORT,
            "database": setting.POSTGRES_DB,
            "user":     setting.POSTGRES_USER,
            "password": setting.POSTGRES_PASSWORD
        }

        if not all(self.db_config.values()):
            required_keys = ["host", "database", "user", "password"]
            missing = [key for key in required_keys if not self.db_config.get(key)]
            if missing:
                 raise ValueError(f"Missing required PostgreSQL environment variables: {', '.join(missing)}")


        self.conn:psycopg2.extensions.connection | None = None
        self.cursor:psycopg2.extensions.cursor | None = None
        
    def __enter__(self):
        """Context manager entry: connect to the database."""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit: close the database connection, handle exceptions."""
        if exc_type:
            if self.conn:
                try:
                    self.conn.rollback()
                    logger.error(f"Transaction rolled back due to exception: {exc_val}")
                except Exception as e:
                    logger.error(f"Error during rollback: {e}")
        self.close()

    def process_new_links(self, new_links:set,company_id:int)->set|None:
        # must check the unique links
        self.cursor.execute(
                                             f"SELECT jd_links FROM {self.COMPANY_TABLE} WHERE id = %s",
                                             (company_id,)
                                            )
        existing_links = self.cursor.fetchone()
        
        if existing_links and existing_links[0]:
            logger.info(f"Existing links: {existing_links[0]}")
            unique_links = set(existing_links[0])
            new_links = new_links.difference(unique_links)
            logger.info(f"{new_links} New links ready to store.")
            logger.debug(f"Unique links: {new_links}")

        if not new_links:
            return None
        # Update the database to add new links
        # Combine new_links and existing_links[0] safely
        updated_links = set(new_links)
        if existing_links and existing_links[0]:
            updated_links = updated_links.union(set(existing_links[0]))
        self.cursor.execute(
            f"UPDATE {self.COMPANY_TABLE} SET jd_links = %s WHERE id = %s",
            (Json(list(updated_links)), company_id)
        )

        self.conn.commit()
        return new_links
        

        
    def connect(self):
        """Establish database connection"""
        if self.conn is None or self.conn.closed != 0:
            try:
                self.conn = psycopg2.connect(**self.db_config)
                self.cursor = self.conn.cursor()
                logger.info("Successfully connected to PostgreSQL database")

            except Exception as e:
                logger.error(f"Error connecting to PostgreSQL: {e}")
                raise

    def close(self):
        """Close database connection"""
        if self.cursor:
            try:
                self.cursor.close()
            except Exception as e:
                 logger.warning(f"Error closing cursor: {e}")
            self.cursor = None
        if self.conn:
            try:
                if self.conn.status == psycopg2.extensions.STATUS_IN_TRANSACTION:
                     self.conn.rollback()
                     logger.warning("Rolled back pending transaction on connection close.")
                self.conn.close()
            except Exception as e:
                 logger.warning(f"Error closing connection: {e}")
            self.conn = None
            logger.info("Database connection closed")

    def get_or_create_company(self, company_name: str, company_url:str, meta: None | dict[str, Any] = None) -> tuple[int,bool]:
        """
        Get the ID of an existing company or create a new one in t_company table.
        if new company create then get the LinkedIn recruiter profile

        Args:
            company_name: The name of the company.
            company_url: The URL of the company .
            meta: Additional company metadata (optional, will be stored as JSONB).

        Returns:
            The integer primary key ID of the company from t_company , if newly created

        Raises:
            Exception: If a database error occurs.
        """
        if not self.conn or self.cursor is None:
            logger.warning("Database connection is not established.")
            self.connect()

        try:
            identifier_string = f"{company_name.strip().lower()}##{company_url.strip().lower() if company_url else ''}"
            unique_id = hashlib.md5(identifier_string.encode(), usedforsecurity=False).hexdigest()

            # Check if company exists by unique_id in the specified table
            self.cursor.execute(
                f"""
                SELECT id FROM {self.COMPANY_TABLE}
                WHERE unique_id = %s
                """,
                (unique_id,)
            )
            result = self.cursor.fetchone()

            if result:
                company_id = result[0]
                logger.debug(f"Found existing company: {company_name} (ID: {company_id}) in {self.COMPANY_TABLE}")
                return company_id, False
            else:
                # Company not found, create a new one in the specified table
                now_utc = datetime.now()
                self.cursor.execute(
                    f"""
                    INSERT INTO {self.COMPANY_TABLE}
                    (company_name, company_url, meta, unique_id, created_at, modified_at, status)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                    """,
                    (
                        company_name,
                        company_url,
                        json.dumps(meta) if meta is not None else None,
                        unique_id,
                        now_utc,
                        now_utc,
                        True
                    )
                )
                company_id = self.cursor.fetchone()[0]
                self.conn.commit()
                logger.info(f"Created new company: {company_name} (ID: {company_id}) in {self.COMPANY_TABLE}")
                # get linkedin recruiter profile
                self.add_recruiter_profiles(company_id, company_name, company_url)
                return company_id, True

        except Exception as e:
            self.conn.rollback()
            logger.error(f"Error in get_or_create_company for '{company_name}' in {self.COMPANY_TABLE}: {e}")
            raise


    async def bulk_insert_jobs(self, jobs: list[dict[str, Any]]):
        """
        Bulk insert or update job records into the 'job_marketing.t_jd' table.

        This method expects the input job data to be pre-processed.
        """
        if not self.conn or self.cursor is None:
            raise ConnectionError("Database connection is not established.")

        if not jobs:
            logger.info("No jobs provided for bulk insert.")
            return
        
        job_data_tuples = []
        for job in jobs:
            if not job.get("apply_link"):
                logger.warning(f"Skipping job with missing apply_link: {job}")
                continue
            try:
                id = job['id']
                #FIXME: CHANGE UNIQUE IDENTIFIER
                unique_identifier = job.get('unique_identifier', str(id))
                source = Source.from_string(job['source'])
                title = job['title']
                description = job['description']
                location = job.get('location')
                location_type = LocationType.from_string(job.get('location_type'))
                job_type = JobType.from_string(job.get('job_type'))
                min_experience = int(job.get('min_experience', 0) or 0)
                max_experience = int(job.get('max_experience', 0) or 0)
                ctc = str(job.get('ctc', '')) if job.get('ctc') is not None else None
                currency_code = job.get('currency_code', 'USD')
                skills = job.get('skills') if isinstance(job.get('skills'), str) else None
                apply_link = job['apply_link']
                company_id = job['company_id']
                # company_name = job.get('company_name')
                # company_website = job.get('company_website')
                meta_json = json.dumps(job['meta']) if job.get('meta') is not None else None
                embedding = job.get('embedding')
                created_at = job.get('created_at', datetime.now().isoformat())
                modified_at = job.get('modified_at', datetime.now().isoformat())
                posted_at = job.get('posted_at')

                job_data_tuples.append((
                    id,
                    unique_identifier,
                    source,
                    title,
                    description,
                    location,
                    location_type,
                    job_type,
                    min_experience,
                    max_experience,
                    ctc,
                    currency_code,
                    skills,
                    apply_link,
                    company_id,
                    # company_name,
                    # company_website,
                    meta_json,
                    embedding,
                    created_at,
                    modified_at,
                    posted_at
                ))
            except KeyError as e:
                logger.error(f"Missing required key in job data: {e}. Skipping job: {job.get('unique_identifier', 'N/A')}")
                continue
            except Exception as e:
                 logger.error(f"Error processing job data for {job.get('unique_identifier', 'N/A')}: {e}. Skipping job.")
                 continue


        if not job_data_tuples:
            logger.warning("No valid job data tuples prepared for insertion.")
            return

        insert_columns = [
            'id',
            'unique_identifier', 'source', 'title', 'description', 'location',
            'location_type', 'job_type', 'min_experience', 'max_experience', 'ctc',
            'currency_code', 'skills', 'apply_link', 'company_id', # 'company_name', 'company_website',
            'meta', 'embedding', 'created_at', 'modified_at', 'posted_at'
        ]
        insert_column_names = ", ".join(insert_columns)

        update_set_clause = ", ".join([f"{col} = EXCLUDED.{col}" for col in insert_columns if col not in ('unique_identifier', 'created_at')])
        if 'modified_at' not in update_set_clause:
             update_set_clause += ", modified_at = EXCLUDED.modified_at"

        # Use the schema-qualified table name for INSERT and ON CONFLICT
        sql_query = f"""
            INSERT INTO {self.JOB_TABLE} ({insert_column_names})
            VALUES %s
            ON CONFLICT (unique_identifier) DO UPDATE SET
                {update_set_clause}
        """

        try:
            execute_values(
                self.cursor,
                sql_query,
                job_data_tuples
            )

            self.conn.commit()
            logger.info(f"Successfully inserted/updated {len(job_data_tuples)} jobs into {self.JOB_TABLE}.")
        except Exception as e:
            self.conn.rollback()
            logger.error(f"Error in bulk_insert_jobs for {self.JOB_TABLE}: {e}")
            raise
    
    def bulk_insert_recruiter_profiles(self,company_id:int,profiles: list[dict]):
        try:
            for profile in profiles:
                self.cursor.execute(f"""
                    INSERT INTO {self.RECRUITER_PROFILE_TABLE} (company_id, recruiter_name, recruiter_url, designation)
                    VALUES (%s, %s, %s, %s)
                """, (
                    company_id,
                    profile["recruiter_name"],
                    profile["recruiter_url"],
                    profile["designation"]
                ))
            self.conn.commit()
        except Exception as e:
            self.conn.rollback()
            logger.error(f"Failed to insert recruiter profiles: {e}")
            raise

    def add_recruiter_profiles(self, company_id :int, company_name: str, location: str):
        linkedin_profiles = self.LinkedInRecruiterProfiles.add_recruiter_linkedin_profiles(company_name, location)
        self.bulk_insert_recruiter_profiles(company_id,linkedin_profiles)
        
        return linkedin_profiles
    


if __name__ == "__main__":
    loader = PostgresLoader()
    loader.connect()
    loader.add_recruiter_profiles(1, "Company A", "New York")
