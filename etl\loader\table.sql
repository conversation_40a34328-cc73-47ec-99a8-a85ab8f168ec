--"""
--This script now includes CREATE SCHEMA and uses schema-qualified (schema.table) and prefixed (t_tablename) names throughout.

-- ======================================================================
-- Initial Database Schema Setup Script (Manual Management) - Updated for job_marketing schema and t_ prefix
-- ======================================================================
-- This script creates the initial tables, indexes, and constraints
-- within the 'job_marketing' schema, using a 't_' prefix for tables.
--
-- IMPORTANT:
-- 1. This script is intended for INITIAL DATABASE SETUP.
-- 2. MANAGING FUTURE SCHEMA CHANGES MANUALLY requires writing
--    separate ALTER TABLE, DROP COLUMN, etc., scripts and
--    carefully tracking which scripts have been applied to which
--    database instances. This is prone to errors and strongly
--    discouraged for production systems or teams.
-- 3. Database migration tools (like Alembic for Python) automate
--    this process safely and reliably.
-- ======================================================================
--"""
-- 1. Create the schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS job_marketing;

-- 2. Enable necessary extensions
--    If you are using the `vector` type for embeddings, you need the pgvector extension.
--    You may need database superuser privileges to run CREATE EXTENSION.
--    Run this command only once per database.
--    Note: Extensions are database-level, not schema-level.
CREATE EXTENSION IF NOT EXISTS vector;


-- 3. Drop tables if they exist and you want a clean start (Optional, for development)
--    Specify the schema and table name. Drop tables with foreign keys first.
-- !!! Use with CAUTION on environments with data you want to keep !!!
-- DROP TABLE IF EXISTS job_marketing.t_jd CASCADE; -- *** Changed table name and schema ***
-- DROP TABLE IF EXISTS job_marketing.t_recruiter_profile CASCADE; -- *** Changed table name and schema ***
-- DROP TABLE IF EXISTS job_marketing.t_company CASCADE; -- *** Changed table name and schema ***


-- 4. Create 'company' table (as t_company inside job_marketing schema)
CREATE TABLE IF NOT EXISTS job_marketing.t_company ( -- *** Changed table name and schema ***
    id SERIAL PRIMARY KEY, -- Auto-incrementing integer primary key
    company_name VARCHAR(255) NOT NULL, -- Company name
    company_url TEXT, -- Company website URL (can be NULL)
    jd_links JSONB, -- JSON array of job description links
    meta JSONB, -- Additional metadata (stored as JSON)
    unique_id VARCHAR(32) UNIQUE NOT NULL, -- MD5 hash for stable identification (e.g., hash of name+url)
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(), -- Record creation timestamp
    modified_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(), -- Record modification timestamp
    status BOOLEAN DEFAULT TRUE -- Active status flag
);

-- 5. Create Indexes for 't_company' table
--    Index names are also schema-qualified for clarity.
CREATE INDEX IF NOT EXISTS idx_t_company_unique_id ON job_marketing.t_company(unique_id); -- *** Changed index name and schema/table ***
CREATE INDEX IF NOT EXISTS idx_t_company_name ON job_marketing.t_company(company_name); -- *** Changed index name and schema/table ***
-- Optional: Index on company_url if frequently queried or used in joins/lookups
-- CREATE INDEX IF NOT EXISTS idx_t_company_url ON job_marketing.t_company(company_url); -- *** Changed index name and schema/table ***


-- 6. Create 'job_marketing' table (as t_jd inside job_marketing schema)
CREATE TABLE IF NOT EXISTS job_marketing.t_jd ( -- *** Changed table name and schema ***
    id BIGINT PRIMARY KEY,
    unique_identifier VARCHAR(32) UNIQUE NOT NULL, -- MD5 hash for stable job identification
    source SMALLINT NOT NULL, -- Source of the job posting (Integer Enum ID, see Python IntEnum mapping below)
    title VARCHAR(255) NOT NULL, -- Job title
    description TEXT NOT NULL, -- Full job description
    location VARCHAR(1000), -- Job location string
    location_type SMALLINT, -- Type of location (Integer Enum ID, see Python IntEnum mapping below, can be NULL)
    job_type SMALLINT, -- Type of job (Integer Enum ID, see Python IntEnum mapping below, can be NULL)
    min_experience INTEGER DEFAULT 0, -- Minimum required experience
    max_experience INTEGER DEFAULT 0, -- Maximum required experience
    ctc VARCHAR(255), -- Compensation details (stored as string)
    currency_code VARCHAR(3) DEFAULT 'USD', -- Currency code for CTC
    skills TEXT, -- Required skills (e.g., comma-separated string)
    apply_link TEXT, -- Link to apply for the job
    company_id INTEGER NOT NULL, -- Foreign key linking to the company table (refers to job_marketing.t_company)
    -- Denormalized company info for convenience (optional, could join instead)
    company_name VARCHAR(255),
    company_website VARCHAR(500),
    meta JSONB, -- Additional job-specific metadata
    embedding vector(768), -- Vector embedding (requires pgvector extension enabled)
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(), -- Record creation timestamp
    modified_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(), -- Record modification timestamp

    -- Define the foreign key constraint - now explicitly referencing the schema and table
    CONSTRAINT fk_t_jd_company -- *** Changed constraint name and definition ***
        FOREIGN KEY (company_id)
        REFERENCES job_marketing.t_company(id) -- *** Referencing schema.table ***
        ON DELETE CASCADE -- Delete job_marketing records if the associated company is deleted
);

-- 7. Create Indexes for 't_jd' table
--    Index names are also schema-qualified for clarity.
CREATE INDEX IF NOT EXISTS idx_t_jd_company_id ON job_marketing.t_jd(company_id); -- *** Changed index name and schema/table ***
CREATE INDEX IF NOT EXISTS idx_t_jd_unique_identifier ON job_marketing.t_jd(unique_identifier); -- *** Changed index name and schema/table ***
CREATE INDEX IF NOT EXISTS idx_t_jd_title ON job_marketing.t_jd(title); -- *** Changed index name and schema/table ***
CREATE INDEX IF NOT EXISTS idx_t_jd_location ON job_marketing.t_jd(location); -- *** Changed index name and schema/table ***
CREATE INDEX IF NOT EXISTS idx_t_jd_location_type ON job_marketing.t_jd(location_type); -- *** Changed index name and schema/table ***
CREATE INDEX IF NOT EXISTS idx_t_jd_job_type ON job_marketing.t_jd(job_type); -- *** Changed index name and schema/table ***
-- Optional: GIN index for full-text search on skills if needed
-- CREATE INDEX IF NOT EXISTS idx_t_jd_skills ON job_marketing.t_jd USING GIN (to_tsvector('english', skills)); -- *** Changed index name and schema/table ***


-- 8. Create 'recruiter_profile' table (as t_recruiter_profile inside job_marketing schema)
CREATE TABLE IF NOT EXISTS job_marketing.t_recruiter_profile ( -- *** Changed table name and schema ***
    id SERIAL PRIMARY KEY,
    company_id INTEGER NOT NULL, -- Foreign key linking to the company table (refers to job_marketing.t_company)
    recruiter_name TEXT NOT NULL,
    recruiter_url TEXT, -- URL of the recruiter's profile
    designation TEXT,
    meta JSONB, -- Optional metadata for the recruiter
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    modified_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),

    -- Define the foreign key constraint - now explicitly referencing the schema and table
    CONSTRAINT fk_t_recruiter_profile_company -- *** Changed constraint name and definition ***
        FOREIGN KEY (company_id)
        REFERENCES job_marketing.t_company(id) -- *** Referencing schema.table ***
        ON DELETE CASCADE -- Delete profiles if the company is deleted
);

-- 9. Create Index for 't_recruiter_profile' table
--    Index names are also schema-qualified for clarity.
CREATE INDEX IF NOT EXISTS idx_t_recruiter_profile_company_id ON job_marketing.t_recruiter_profile(company_id); -- *** Changed index name and schema/table ***
-- Optional: Index on recruiter_url if used for lookups
-- CREATE INDEX IF NOT EXISTS idx_t_recruiter_profile_recruiter_url ON job_marketing.t_recruiter_profile(recruiter_url); -- *** Changed index name and schema/table ***


-- ======================================================================
-- Python Integer Enum Mapping Reference (for your application code)
-- ======================================================================
-- These mappings define the integer values stored in the SMALLINT columns.
-- Your Python application logic must convert between string names and these
-- integer values when inserting or querying data.

-- Source (used in job_marketing.t_jd.source):
-- 1 = LINKEDIN
-- 2 = INDEED
-- 3 = COMPANY_WEBSITE
-- 4 = OTHER

-- LocationType (used in job_marketing.t_jd.location_type):
-- 1 = REMOTE
-- 2 = HYBRID
-- 3 = ONSITE
-- 4 = FLEXIBLE
-- (NULL = Unknown/Not Specified)

-- JobType (used in job_marketing.t_jd.job_type):
-- 1 = FULL_TIME
-- 2 = PART_TIME
-- 3 = CONTRACT
-- 4 = INTERNSHIP
-- 5 = FREELANCE
-- 6 = TEMPORARY
-- (NULL = Unknown/Not Specified)

-- ======================================================================

-- To run this script 
-- psql -U username -d database_name -f etl/loader/table.sql