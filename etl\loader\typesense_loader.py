from http import client
from typing import List
import typesense
import json

import typesense.exceptions
from dotenv import load_dotenv
import os

load_dotenv()

class TypesenseHelper:
    def __init__(self):
        self.client = typesense.Client({
            "api_key": os.getenv("TYPESENSE_API_KEY"),
            "nodes": [{
                "host": os.getenv("TYPESENSE_HOST"),
                "port": os.getenv("TYPESENSE_PORT"),
                "protocol": "https"
            }],
            "connection_timeout_seconds": 5
        })

        self.schemas = {
            "jobs": {
                "name": "jobs",
                "fields": [
                    {"name": "id", "type": "string"},
                    {"name": "unique_identifier", "type": "string"},
                    {"name": "source", "type": "string", "facet": True},
                    {"name": "title", "type": "string", "searchable": True},
                    {"name": "description", "type": "string", "searchable": True},
                    {"name": "location", "type": "string", "optional": True},
                    {"name": "location_type", "type": "string", "facet": True, "optional": True},
                    {"name": "job_type", "type": "string", "facet": True, "optional": True},
                    {"name": "min_experience", "type": "int32", "optional": True},
                    {"name": "max_experience", "type": "int32", "optional": True},
                    {"name": "ctc", "type": "string", "optional": True},
                    {"name": "currency_code", "type": "string", "optional": True},
                    {"name": "skills", "type": "string", "optional": True, "searchable": True},
                    {"name": "apply_link", "type": "string", "optional": True},
                    {"name": "company_id", "type": "int32"},
                    {"name": "meta", "type": "string", "optional": True},
                    {"name": "embedding", "type": "float[]"},
                    {"name": "created_at", "type": "int64"},
                    {"name": "modified_at", "type": "int64"}
                ],
                "default_sorting_field": "created_at"
            },
            "companies": {
                "name": "companies",
                "fields": [
                    {"name": "id", "type": "int32"},
                    {"name": "company_name", "type": "string", "facet": True},
                    {"name": "description", "type": "string", "optional": True},
                    {"name": "company_url", "type": "string", "optional": True},
                    {"name": "meta", "type": "string", "optional": True},
                    {"name": "unique_id", "type": "string"},
                    {"name": "created_at", "type": "int64"},
                    {"name": "modified_at", "type": "int64"},
                    {"name": "status", "type": "bool", "optional": True}
                ],
                "default_sorting_field": "created_at"
            },
            "company_contacts": {
                "name": "company_contacts",
                "fields": [
                    {"name": "id", "type": "int32"},
                    {"name": "name", "type": "string"},
                    {"name": "image_url", "type": "string", "optional": True},
                    {"name": "profile_url", "type": "string"},
                    {"name": "designation", "type": "string"},
                    {"name": "company_id", "type": "int32"},
                    {"name": "meta", "type": "string", "optional": True},
                    {"name": "created_at", "type": "int64"},
                    {"name": "modified_at", "type": "int64"}
                ],
                "default_sorting_field": "created_at"
            },
            "contacts": {
                "name": "contacts",
                "fields": [
                    {"name": "id", "type": "int32"},
                    {"name": "profile_id", "type": "int32"},
                    {"name": "alpha_code", "type": "string", "optional": True},
                    {"name": "country_code", "type": "string", "optional": True},
                    {"name": "phone_number", "type": "string", "optional": True},
                    {"name": "email", "type": "string", "optional": True},
                    {"name": "is_valid", "type": "bool", "optional": True},
                    {"name": "created_at", "type": "int64"},
                    {"name": "modified_at", "type": "int64"}
                ],
                "default_sorting_field": "created_at"
            }
        }

    def create_collection(self, name: str):
        try:
            self.client.collections[name].retrieve()
        except Exception:
            self.client.collections.create(self.schemas[name])
            print(f"Created new '{name}' collection.")
        
        

    def bulk_index(self, collection_name: str, data_list: List[dict], model_class):
        if not data_list:
            print(f"No data to index into '{collection_name}'.")
            return

        valid_docs = []
        for item in data_list:
            try:
                model = model_class(**item)
                valid_docs.append(model.to_dict())
            except Exception as e:
                print(f"Skipping invalid item: {e}")

        results = self.client.collections[collection_name].documents.import_(
            valid_docs, {'action': 'upsert'}
        )
        failed = [r for r in results if not r.get('success')]
        print(f"Indexed {len(valid_docs)} documents to '{collection_name}'. Failed: {len(failed)}")
        for fail in failed:
            print(json.dumps(fail, indent=2))

    async def bulk_index_jobs_to_typesense(self, jobs: List[dict], attempt: int = 0):
        if not jobs:
            print("No jobs to index.")
            return

        typesense_docs = []
        for job in jobs:
            try:
                typesense_docs.append({
                    "id": job["id"],
                    "unique_identifier": job["unique_identifier"],
                    "source": job["source"],
                    "title": job["title"],
                    "description": job["description"],
                    "location": job.get("location"),
                    "location_type": job.get("location_type", "unspecified"),
                    "job_type": job.get("job_type", "unspecified"),
                    "min_experience": int(job.get("min_experience") or 0),
                    "max_experience": int(job.get("max_experience") or 0),
                    "ctc": job.get("ctc"),
                    "currency_code": job.get("currency_code"),
                    "skills": job.get("skills"),
                    "apply_link": job.get("apply_link"),
                    "company_id": job["company_id"],
                    "meta": job.get("meta", "{}"),
                    "embedding": job.get("embedding"),
                    "created_at": job["created_at"],
                    "modified_at": job["modified_at"]
                })
            except Exception as e:
                print(f"Skipping job id {job.get('id')} due to error: {e}")

        try:
            results = self.client.collections["jobs"].documents.import_(typesense_docs, {"action": "upsert"})
            failed = [r for r in results if not r.get("success")]
            print(f"Indexed {len(typesense_docs)} jobs. Failed: {len(failed)}")
            for fail in failed:
                print(f"Failed doc: {json.dumps(fail, indent=2)}")

        except typesense.exceptions.ObjectNotFound:
            print(f"Collection 'jobs' not found. Creating collection and retrying...")
            self.create_collection("jobs")
            if attempt < 1:
                return self.bulk_index_jobs_to_typesense(jobs, attempt=attempt + 1)
            else:
                print("Retry failed after creating collection.")
        except typesense.exceptions.ObjectAlreadyExists as e:
            print(f"Some job(s) already exist in Typesense. Consider updating them if needed. Error: {e}")
        except Exception as e:
            print(f"Unexpected error during indexing jobs: {e}")

            
if __name__ == "__main__":
    helper = TypesenseHelper()

    
    helper.create_collection("jobs")
    helper.create_collection("companies")
    helper.create_collection("company_contacts")
    helper.create_collection("contacts")
