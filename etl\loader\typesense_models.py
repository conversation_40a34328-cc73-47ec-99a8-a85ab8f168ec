from pydantic import BaseModel
from typing import List


class JobModel(BaseModel):
    id: str
    unique_identifier: str
    source: str
    title: str
    description: str
    location: str | None = None
    location_type: str | None = None
    job_type: str | None = None
    min_experience: int | None = 0
    max_experience: int | None = 0
    ctc: str | None = None
    currency_code: str | None = None
    skills: str | None = None
    apply_link: str | None = None
    company_id: int
    meta: str | None = "{}"
    embedding: List[float]
    created_at: int
    modified_at: int

    def to_dict(self):
        return self.dict()


class CompanyModel(BaseModel):
    id: str
    company_name: str
    description: str | None = None
    company_url: str | None = None
    meta: str | None = None
    unique_id: str
    created_at: int
    modified_at: int
    status: bool | None = True

    def to_dict(self):
        return self.dict()


class CompanyContactModel(BaseModel):
    id: str
    name: str
    image_url: str | None = None
    profile_url: str
    designation: str
    company_id: int
    meta: str | None = None
    created_at: int
    modified_at: int

    def to_dict(self):
        return self.dict()


class ContactModel(BaseModel):
    id: str
    profile_id: int
    alpha_code: str | None = None
    country_code: str | None = None
    phone_number: str | None = None
    email: str | None = None
    is_valid: bool | None = True
    created_at: int
    modified_at: int

    def to_dict(self):
        return self.dict()

