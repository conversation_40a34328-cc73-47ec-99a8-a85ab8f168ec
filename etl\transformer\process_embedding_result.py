
import shutil
from config.core.settings import get_settings
from etl.ai.gcs_client import GCSClient
from etl.loader.load_to_postgres import PostgresLoader
import logging
import os
from etl.loader.typesense_loader import <PERSON>senseHelper
from typing import Iterator

from pyspark.sql import SparkSession 
from pyspark.sql.functions import col, element_at
from snowflakekit import SnowflakeGenerator,SnowflakeConfig
import asyncio
logger = logging.getLogger(__name__)



class EmbeddingResultProcessor:
    def __init__(self, spark: SparkSession, input_path: str):
        self.spark = spark
        self.input_path = input_path # This should be the local directory path

    def process_and_yield_batches(self, batch_size: int = 1000) -> Iterator[list[dict]]:
        """
        Reads processed JSONL files from a local path, selects meta and embeddings,
        and yields the results in batches as Python dictionaries.

        Args:
            batch_size (int): The number of records per batch.

        Yields:
            List[Dict]: A list containing dictionaries for each record in the batch.
        """
        logger.info(f"Reading embedding result files from local path: {self.input_path}")
        try:
            # Spark reads from the local filesystem just like HDFS if running locally
            # If running on a cluster, ensure the local path is accessible or use hdfs:/
            # Assuming local path is correct for the execution environment.
            df = self.spark.read.format("json").option("recursiveFileLookup", "true").load(self.input_path)

            if df.isEmpty():
                logger.warning("Input DataFrame is empty. No data to process.")
                yield from [] # Yield nothing for an empty iterator
                return

            # Select all fields from 'instance.meta'
            # Select the 'embeddings' struct from the first element of 'predictions'
            result_df = df.select(
                col("instance.meta.*"),
                element_at(col("predictions"), 1).getItem("embeddings").getItem("values").alias("embedding")
            )

            logger.info(f"Starting collection and batching of processed data (batch size: {batch_size})")

            current_batch = []
            # Use toLocalIterator to stream data from Spark partitions to the driver
            for row in result_df.toLocalIterator():
                processed_dict = row.asDict()
                print(processed_dict)
                current_batch.append(processed_dict)

                # If the current batch size reaches the limit, yield it
                if len(current_batch) >= batch_size:
                    logger.debug(f"Yielding a batch of {len(current_batch)} records.")
                    yield current_batch
                    current_batch = []

            # Yield any remaining records in the last batch
            if current_batch:
                logger.debug(f"Yielding the final batch of {len(current_batch)} records.")
                yield current_batch

            logger.info("Finished processing and yielding all batches.")

        except Exception as e:
            logger.error(f"Failed during processing and batching: {e}")
            raise # Re-raise the exception after logging

async def main_embedding_result_processing():
    #helper = TypesenseHelper()
    settings = get_settings()
    postgres_loader = PostgresLoader()
    postgres_loader.connect()
    spark = SparkSession.builder \
        .appName("EmbeddingResultProcessing") \
        .getOrCreate()
    logger.info("Starting the mock processing for embedding result data...")

    # Define paths using environment variables with defaults for safety
    local_download_path = settings.EMBEDDING_RESULTS_DIR
    gcp_config_path = settings.GCP_CONFIG_PATH

    if not gcp_config_path:
        logger.error("GCP_CONFIG_PATH environment variable not set. Cannot initialize GCSClient.")
        return  # Skip further processing if GCP config path is not set

    total_records_processed = 0
    total_batches_processed = 0

    try:
        
        logger.info("Simulating file download...")

        # 1. Initialize GCSClient and download files
        gcs_client = GCSClient(gcp_config_path)
        gcs_client.download_files_from_gcs(settings.GCS_OUTPUT_EMBEDDING_PREFIX, local_download_path)
        # 2. Initialize Processor and process in batches
        # Point the processor to the local directory where files were downloaded
        processor = EmbeddingResultProcessor(spark, local_download_path)

        batch_size = int(os.environ.get("DB_BATCH_SIZE", 1000))
        logger.info(f"Starting batch processing with batch size: {batch_size}")

        # process_and_yield_batches returns a generator
        processed_batches_generator = processor.process_and_yield_batches(batch_size=batch_size)
        config = SnowflakeConfig(
            epoch=1609459200000,
            node_id=1,
            worker_id=1,
        )
        generator = SnowflakeGenerator(config=config)
        for batch in processed_batches_generator:
            total_batches_processed += 1
            ids = await asyncio.gather(*[generator.generate() for _ in batch])
            for record, id_ in zip(batch, ids):
                record["id"] = id_
            total_records_processed += len(batch)
            logger.info(f"Received Batch {total_batches_processed} with {len(batch)} records.") 
            try:
                await postgres_loader.bulk_insert_jobs(batch)
                # await asyncio.gather(
                #     ,
                #     helper.bulk_index_jobs_to_typesense(batch)
                # )
    
                
            except Exception as e:
                logger.error(f"Failed to insert batch {total_batches_processed}: {e}")

        logger.info(f"Total batches processed: {total_batches_processed}")
        logger.info(f"Total records processed: {total_records_processed}")

    except Exception as e:
        logger.error(f"An error occurred during the main processing flow: {e}")

    finally:
        # Optional: Clean up the local downloaded files (mocked clean-up)
        if os.path.exists(local_download_path):
            try:
                shutil.rmtree(local_download_path)
                logger.info(f"Cleaned up local directory: {local_download_path}")
            except Exception as cleanup_error:
                logger.warning(f"Failed to clean up local directory {local_download_path}: {cleanup_error}")

def main():
    asyncio.run(main_embedding_result_processing())

if __name__ == "__main__":
    # Initialize Spark session
    main()
    
