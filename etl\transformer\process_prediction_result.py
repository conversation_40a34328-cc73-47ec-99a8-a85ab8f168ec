import asyncio
import math
import os
import re
import json
import ast
import shutil
from typing import Optional, List, Dict, Any
from pathlib import Path
from pyspark.sql import SparkSession, Row
from pyspark.sql import functions as F
from pyspark.sql.types import (
    StructType, StructField, StringType, IntegerType, ArrayType
)
from config.core.settings import get_settings
from etl.ai.embedding_batch_processing import GenAIBatchEmbedding
from etl.ai.gcs_client import GCS<PERSON>lient
from etl.transformer import logger

# --- Schemas ---
# Schema for the input prediction JSONL file
PREDICTION_INPUT_SCHEMA = StructType([
    StructField("request", StructType([
        StructField("contents", ArrayType(StructType([
            StructField("parts", ArrayType(StructType([
                StructField("text", StringType(), True)
            ])), True)
        ])), True)
    ]), True),
    StructField("response", StructType([
        Struct<PERSON>ield("candidates", ArrayType(StructType([
            StructField("content", StructType([
                StructField("parts", ArrayType(StructType([
                    StructField("text", StringType(), True)
                ])), True)
            ]), True)
        ])), True)
    ]), True),
    StructField("processed_time", StringType(), True)
])

# Schema for direct JD JSON files
DIRECT_JD_SCHEMA = StructType([
    StructField("jd_link", StringType(), True),
    StructField("company_id", IntegerType(), True),
    StructField("source", IntegerType(), True),
    StructField("title", StringType(), True),
    StructField("location", StringType(), True),
    StructField("location_type", StringType(), True),
    StructField("job_type", StringType(), True),
    StructField("skills", StringType(), True),
    StructField("min_experience", IntegerType(), True),
    StructField("max_experience", IntegerType(), True),
    StructField("apply_link", StringType(), True),
    StructField("description", StringType(), True),
    StructField("ctc", StringType(), True),
    StructField("currency", StringType(), True),
    StructField("meta", StructType([]), True)  # For any additional metadata
])

# Schema for the output jd_data (from the input prompt)
JD_DATA_SCHEMA = StructType([
    StructField("jd_link", StringType(), True),
    StructField("company_name", StringType(), True),
    StructField("source", IntegerType(), True),
    StructField("title", StringType(), True),
    StructField("min_experience", IntegerType(), True),
    StructField("max_experience", IntegerType(), True),
    StructField("apply_link", StringType(), True),
    StructField("location_type", StringType(), True),
    StructField("job_type", StringType(), True),
    StructField("company_id", IntegerType(), True),
    StructField("ctc", StringType(), True),
    StructField("currency", StringType(), True)
])

# Schema for the AI response payload
AI_RESPONSE_SCHEMA = StructType([
    StructField("skills", StringType(), True),
    StructField("location", StringType(), True)
])

# --- UDFs for Prediction Processing ---
def extract_text_from_tag(html_content: Optional[str], tag_name: str) -> Optional[str]:
    if not html_content:
        return None
    pattern = rf'<{tag_name}>(.*?)</{tag_name}>'
    match = re.search(pattern, html_content, re.DOTALL | re.IGNORECASE)
    if match:
        return match.group(1).strip()
    return None

@F.udf(StringType())
def udf_extract_description_from_prompt(prompt_text: Optional[str]) -> Optional[str]:
    return extract_text_from_tag(prompt_text, "description")

@F.udf(JD_DATA_SCHEMA)
def udf_extract_jd_data_from_prompt(prompt_text: Optional[str]) -> Optional[Row]:
    if not prompt_text:
        return None
    jd_data_str = extract_text_from_tag(prompt_text, "jd_data")
    if jd_data_str:
        try:
            data = ast.literal_eval(jd_data_str)
            return Row(
                jd_link=data.get("jd_link"),
                company_name=data.get("company_name"),
                source=data.get("source"),
                title=data.get("title"),
                min_experience=data.get("min_experience"),
                max_experience=data.get("max_experience"),
                apply_link=data.get("apply_link"),
                location_type=data.get("location_type"),
                job_type=data.get("job_type"),
                company_id=data.get("company_id"),
                ctc=data.get("ctc"),
                currency=data.get("currency")
            )
        except (SyntaxError, ValueError, TypeError) as e:
            logger.warning(f"Could not parse jd_data string: {jd_data_str}. Error: {e}")
            return None
    return None

@F.udf(AI_RESPONSE_SCHEMA)
def udf_parse_ai_response_payload(ai_response_text: Optional[str]) -> Optional[Row]:
    if not ai_response_text:
        return None
    try:
        clean_text = ai_response_text.strip()
        if clean_text.startswith("```json"):
            clean_text = clean_text[len("```json"):].strip()
        if clean_text.endswith("```"):
            clean_text = clean_text[:-len("```")].strip()

        data = json.loads(clean_text)
        return Row(
            skills=data.get("skills"),
            location=data.get("location")
        )
    except json.JSONDecodeError as e:
        logger.warning(f"Could not parse AI response JSON: {ai_response_text}. Error: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error parsing AI response: {ai_response_text}. Error: {e}")
        return None


class UnifiedJDProcessor:
    def __init__(self, spark: SparkSession):
        self.spark = spark

    def process_prediction_results(self, prediction_path: str) -> List[Dict[str, Any]]:
        """
        Process prediction JSONL files and extract JD data for embedding.
        
        Args:
            prediction_path (str): Path to prediction JSONL files
            
        Returns:
            List[Dict[str, Any]]: List of processed JD records
        """
        logger.info(f"Processing prediction results from {prediction_path}")
        
        try:
            raw_df = self.spark.read \
                .format("json") \
                .option("recursiveFileLookup", "true") \
                .schema(PREDICTION_INPUT_SCHEMA) \
                .load(prediction_path)
        except Exception as e:
            logger.error(f"Failed to read prediction files from {prediction_path}: {e}")
            return []

        logger.info(f"Initial prediction records count: {raw_df.count()}")

        # Extract relevant text fields
        processed_df = raw_df.withColumn(
            "input_prompt_text", F.expr("request.contents[0].parts[0].text")
        ).withColumn(
            "ai_response_text", F.expr("response.candidates[0].content.parts[0].text")
        )

        # Apply UDFs to extract structured data
        processed_df = processed_df.withColumn(
            "extracted_description", udf_extract_description_from_prompt(F.col("input_prompt_text"))
        ).withColumn(
            "extracted_jd_data", udf_extract_jd_data_from_prompt(F.col("input_prompt_text"))
        ).withColumn(
            "parsed_ai_response", udf_parse_ai_response_payload(F.col("ai_response_text"))
        )

        # Filter valid records
        valid_jds_df = processed_df.filter(
            F.col("parsed_ai_response").isNotNull() &
            F.col("parsed_ai_response.skills").isNotNull() &
            (F.trim(F.col("parsed_ai_response.skills")) != "") &
            F.col("extracted_description").isNotNull() &
            (F.trim(F.col("extracted_description")) != "") &
            F.col("extracted_jd_data.title").isNotNull() &
            (F.trim(F.col("extracted_jd_data.title")) != "")
        )

        logger.info(f"Valid prediction JDs count: {valid_jds_df.count()}")

        if valid_jds_df.count() == 0:
            logger.warning("No valid JDs found from prediction results.")
            return []

        # Prepare embedding format
        embedding_df = self._prepare_embedding_format_from_prediction(valid_jds_df)
        
        # Convert to list of dictionaries
        return [row.asDict(recursive=True) for row in embedding_df.collect()]

    def process_direct_jds(self, direct_jd_path: str) -> List[Dict[str, Any]]:
        """
        Process direct JD JSON files that don't need prediction processing.
        
        Args:
            direct_jd_path (str): Path to directory containing JD JSON files
            
        Returns:
            List[Dict[str, Any]]: List of processed JD records
        """
        logger.info(f"Processing direct JDs from {direct_jd_path}")
        
        try:
            # Read JSON files from directory
            direct_df = self.spark.read \
                .format("json") \
                .option("recursiveFileLookup", "true") \
                .option("multiline", "true") \
                .schema(DIRECT_JD_SCHEMA) \
                .load(direct_jd_path)
        except Exception as e:
            logger.error(f"Failed to read direct JD files from {direct_jd_path}: {e}")
            return []

        logger.info(f"Initial direct JD records count: {direct_df.count()}")

        # Filter valid records - ensure essential fields are present
        valid_direct_jds_df = direct_df.filter(
            F.col("title").isNotNull() &
            (F.trim(F.col("title")) != "") &
            F.col("description").isNotNull() &
            (F.trim(F.col("description")) != "") &
            F.col("skills").isNotNull() &
            (F.trim(F.col("skills")) != "")
        )

        logger.info(f"Valid direct JDs count: {valid_direct_jds_df.count()}")

        if not valid_direct_jds_df.count():
            logger.warning("No valid direct JDs found.")
            return []

        # Prepare embedding format
        embedding_df = self._prepare_embedding_format_from_direct(valid_direct_jds_df)
        
        # Convert to list of dictionaries
        return [row.asDict(recursive=True) for row in embedding_df.collect()]

    def _prepare_embedding_format_from_prediction(self, df):
        """Prepare embedding format from prediction results"""
        # Construct the 'content' field for embedding (location is optional)
        embedding_df = df.withColumn(
            "embedding_content_parts",
            F.array(
                F.concat(F.lit("Designation:"), F.col("extracted_jd_data.title")),
                F.concat(F.lit("skills : "), F.col("parsed_ai_response.skills")),
                # Location is optional - only include if not null/empty
                F.when(
                    F.col("parsed_ai_response.location").isNotNull() & 
                    (F.trim(F.col("parsed_ai_response.location")) != "") &
                    (F.col("parsed_ai_response.location") != "null"),
                    F.concat(F.lit("location : "), F.col("parsed_ai_response.location"))
                ).otherwise(F.lit(None))
            )
        ).withColumn(
            "embedding_content_parts_filtered",
            F.expr("filter(embedding_content_parts, x -> x is not null)")
        ).withColumn(
            "content",
            F.concat_ws(", ", F.col("embedding_content_parts_filtered"))
        )

        # Construct the 'meta' field
        embedding_df = embedding_df.withColumn(
            "meta",
            F.struct(
                F.col("extracted_jd_data.jd_link").alias("jd_link"),
                F.col("extracted_jd_data.company_name").alias("company_name"),
                F.col("extracted_description").alias("description"),
                F.col("parsed_ai_response.skills").alias("skills"),
                F.col("parsed_ai_response.location").alias("location"),
                F.coalesce(F.col("extracted_jd_data.company_id"), F.lit(0).cast(IntegerType())).alias("company_id"),
                F.coalesce(F.col("extracted_jd_data.source"), F.lit(3).cast(IntegerType())).alias("source"),
                F.col("extracted_jd_data.title").alias("title"),
                F.col("extracted_jd_data.location_type").alias("location_type"),
                F.col("extracted_jd_data.job_type").alias("job_type"),
                F.col("extracted_jd_data.min_experience").alias("min_experience"),
                F.col("extracted_jd_data.max_experience").alias("max_experience"),
                F.col("extracted_jd_data.apply_link").alias("apply_link"),
                F.col("extracted_jd_data.ctc").alias("ctc"),
                F.col("extracted_jd_data.currency").alias("currency")
            )
        )

        return embedding_df.select("content", "meta")

    def _prepare_embedding_format_from_direct(self, df):
        """Prepare embedding format from direct JD files"""
        # Construct the 'content' field for embedding (location is optional)
        embedding_df = df.withColumn(
            "embedding_content_parts",
            F.array(
                F.concat(F.lit("Designation:"), F.col("title")),
                F.concat(F.lit("skills : "), F.col("skills")),
                # Location is optional - only include if not null/empty
                F.when(
                    F.col("location").isNotNull() & 
                    (F.trim(F.col("location")) != "") &
                    (F.col("location") != "null"),
                    F.concat(F.lit("location : "), F.col("location"))
                ).otherwise(F.lit(None))
            )
        ).withColumn(
            "embedding_content_parts_filtered",
            F.expr("filter(embedding_content_parts, x -> x is not null)")
        ).withColumn(
            "content",
            F.concat_ws(", ", F.col("embedding_content_parts_filtered"))
        )

        # Construct the 'meta' field
        embedding_df = embedding_df.withColumn(
            "meta",
            F.struct(
                F.col("jd_link").alias("jd_link"),
                F.lit(None).cast(StringType()).alias("company_name"),  # Not available in direct JDs
                F.col("description").alias("description"),
                F.col("skills").alias("skills"),
                F.col("location").alias("location"),
                F.coalesce(F.col("company_id"), F.lit(0)).alias("company_id"),
                F.coalesce(F.col("source"), F.lit(3)).alias("source"),
                F.col("title").alias("title"),
                F.col("location_type").alias("location_type"),
                F.col("job_type").alias("job_type"),
                F.col("min_experience").alias("min_experience"),
                F.col("max_experience").alias("max_experience"),
                F.col("apply_link").alias("apply_link"),
                F.col("ctc").alias("ctc"),
                F.col("currency").alias("currency")
            )
        )

        return embedding_df.select("content", "meta")

    def process_unified_pipeline(self, prediction_path: str = None, direct_jd_path: str = None) -> List[List[Dict[str, Any]]]:
        """
        Unified processing pipeline that handles both prediction results and direct JDs.
        
        Args:
            prediction_path (str, optional): Path to prediction JSONL files
            direct_jd_path (str, optional): Path to direct JD JSON files
            
        Returns:
            List[List[Dict[str, Any]]]: Batched records for embedding
        """
        all_records = []
        
        # Process prediction results if path provided
        if prediction_path and os.path.exists(prediction_path):
            logger.info("Processing prediction results...")
            prediction_records = self.process_prediction_results(prediction_path)
            all_records.extend(prediction_records)
            logger.info(f"Added {len(prediction_records)} records from prediction results")
        
        # Process direct JDs if path provided
        if direct_jd_path and os.path.exists(direct_jd_path):
            logger.info("Processing direct JDs...")
            direct_records = self.process_direct_jds(direct_jd_path)
            all_records.extend(direct_records)
            logger.info(f"Added {len(direct_records)} records from direct JDs")
        
        if not all_records:
            logger.warning("No records found to process for embedding.")
            return []
        
        logger.info(f"Total records for embedding: {len(all_records)}")
        
        # Create batches
        batch_size = int(os.environ.get("EMBEDDING_BATCH_SIZE", 50))
        batched_records = []
        no_of_batches = math.ceil(len(all_records) / batch_size)
        
        for i in range(no_of_batches):
            start_index = i * batch_size
            end_index = min((i + 1) * batch_size, len(all_records))
            batched_records.append(all_records[start_index:end_index])
        
        logger.info(f"Created {len(batched_records)} batches of (up to) {batch_size} records each")
        
        # Clean up prediction files if they exist
        if( prediction_path and os.path.exists(prediction_path) and os.path.isdir(prediction_path)) or (
            direct_jd_path and os.path.exists(direct_jd_path) and os.path.isdir(direct_jd_path)):
            shutil.rmtree(prediction_path) if prediction_path else shutil.rmtree(direct_jd_path)
            logger.info(f"Cleaned up prediction files from {prediction_path if prediction_path else direct_jd_path}")
        
        return batched_records


async def process_embedding_batches(embedding_batches: List[List[Dict]], gcs_client: GCSClient, output_path: str) -> List[str]:
    """Process each batch and upload to GCS"""
    settings = get_settings()
    logger.info(f"Processing {len(embedding_batches)} batches for embedding.")
    embedding_inputs = []
    
    for i, batch in enumerate(embedding_batches):
        local_output_path = f"{output_path}/embedding_input_{i}.jsonl"
        os.makedirs(os.path.dirname(local_output_path), exist_ok=True, mode=0o755)
        
        with open(local_output_path, 'w', encoding='utf-8') as f:
            for record in batch:
                f.write(json.dumps(record) + '\n')
        
        logger.info(f"Written {len(batch)} records to {local_output_path}")
        
        blob_name = f"{settings.GCS_INPUT_PREFIX}/embedding/embedding_input_{i}.jsonl"
        gs_uri = gcs_client.upload_file(local_output_path, blob_name)
        logger.info(f"Uploaded {local_output_path} to GCS: {gs_uri}")
        
        embedding_inputs.append(gs_uri)
        os.remove(local_output_path)
    
    # Submit for batch embedding processing
    await GenAIBatchEmbedding().run_batch_embedding_async(embedding_inputs)
    logger.info(f"Submitted {len(embedding_inputs)} batches for embedding.")
    return embedding_inputs


async def amain_unified_embedding_prep():
    """Main async function for unified embedding preparation"""
    settings = get_settings()
    logger.info("Starting unified JD processing pipeline for embedding preparation...")
    
    # Initialize Spark
    spark = SparkSession.builder \
        .appName("Unified JD Processor for Embedding") \
        .config("spark.sql.legacy.json.allowEmptyString.enabled", "true") \
        .getOrCreate()
    spark.sparkContext.setLogLevel("WARN")
    
    try:
        # Initialize paths
        output_prediction_path = settings.GCS_OUTPUT_PREDICTION_PREFIX
        local_prediction_result_path = settings.VERTEX_PREDICTION_RESULTS_DIR
        direct_jd_path = settings.LOCAL_JD_SKIP_PREDICTION_DIR
        
        # Initialize GCS client
        gcs_client = GCSClient()
        
        # Download prediction results if they exist
        prediction_path = None
        if hasattr(settings, 'GCS_OUTPUT_PREDICTION_PREFIX') and settings.GCS_OUTPUT_PREDICTION_PREFIX:
            try:
                gcs_client.download_files_from_gcs(output_prediction_path, local_prediction_result_path)
                if os.path.exists(local_prediction_result_path) and os.listdir(local_prediction_result_path):
                    prediction_path = local_prediction_result_path
                    logger.info(f"Downloaded prediction results to {prediction_path}")
                else:
                    logger.info("No prediction results found to download")
            except Exception as e:
                logger.warning(f"Failed to download prediction results: {e}")
        
        # Initialize processor
        processor = UnifiedJDProcessor(spark)
        
        # Process using unified pipeline
        embedding_batches = processor.process_unified_pipeline(
            prediction_path=prediction_path,
            direct_jd_path=direct_jd_path
        )
        
        if not embedding_batches:
            logger.info("No records to process for embedding. Exiting.")
            return
        
        # Process embeddings
        embedding_input_local_path = settings.EMBEDDING_INPUT_BATCH_DIR
        await process_embedding_batches(embedding_batches, gcs_client, embedding_input_local_path)
        
        logger.info("Unified embedding preparation pipeline completed successfully.")
        
    except Exception as e:
        logger.error(f"Error in unified embedding preparation pipeline: {e}")
        raise
    finally:
        spark.stop()
        logger.info("Spark session stopped.")


def main_unified_embedding_prep():
    """Main function for unified embedding preparation"""
    asyncio.run(amain_unified_embedding_prep())


if __name__ == "__main__":
    main_unified_embedding_prep()