from contextlib import contextmanager
from pathlib import Path
import os
from config.core import logger

class FileManager:
    """Centralized file management utility"""
    
    @staticmethod
    def ensure_directory_exists(path: str, mode: int = 0o755) -> None:
        """Create directory if it doesn't exist"""
        try:
            os.makedirs(path, mode=mode, exist_ok=True)
            logger.debug(f"Directory ensured: {path}")
        except OSError as e:
            logger.error(f"Failed to create directory {path}: {e}")
            raise
    
    @staticmethod
    def safe_remove_file(file_path: str) -> bool:
        """Safely remove file if it exists"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Removed file: {file_path}")
                return True
            else:
                logger.debug(f"File not found for removal: {file_path}")
                return False
        except OSError as e:
            logger.error(f"Failed to remove file {file_path}: {e}")
            return False
    
    @staticmethod
    def safe_remove_directory(dir_path: str, remove_empty_parents: bool = False) -> bool:
        """Safely remove directory and optionally empty parent directories"""
        try:
            if os.path.exists(dir_path) and os.path.isdir(dir_path):
                # Remove all files in directory first
                for file_path in Path(dir_path).rglob("*"):
                    if file_path.is_file():
                        FileManager.safe_remove_file(str(file_path))
                
                # Remove directory
                os.rmdir(dir_path)
                logger.info(f"Removed directory: {dir_path}")
                
                # Remove empty parent directories if requested
                if remove_empty_parents:
                    FileManager._remove_empty_parents(dir_path)
                
                return True
            else:
                logger.debug(f"Directory not found for removal: {dir_path}")
                return False
        except OSError as e:
            logger.error(f"Failed to remove directory {dir_path}: {e}")
            return False
    
    @staticmethod
    def _remove_empty_parents(path: str) -> None:
        """Remove empty parent directories recursively"""
        parent = os.path.dirname(path)
        if parent and parent != path:  # Avoid infinite recursion
            try:
                if os.path.exists(parent) and not os.listdir(parent):
                    os.rmdir(parent)
                    logger.debug(f"Removed empty parent directory: {parent}")
                    FileManager._remove_empty_parents(parent)
            except OSError:
                pass  # Parent not empty or other error, stop recursion
    
    @staticmethod
    def get_files_with_extension(directory: str, extension: str) -> list[str]:
        """Get all files with specific extension in directory"""
        try:
            path = Path(directory)
            if not path.exists():
                return []
            
            files = [str(f) for f in path.rglob(f"*{extension}") if f.is_file()]
            logger.debug(f"Found {len(files)} files with extension {extension} in {directory}")
            return files
        except Exception as e:
            logger.error(f"Failed to list files in {directory}: {e}")
            return []
    
    @contextmanager
    def temporary_file(self, file_path: str):
        """Context manager for temporary files that auto-cleanup"""
        try:
            # Ensure directory exists
            self.ensure_directory_exists(os.path.dirname(file_path))
            yield file_path
        finally:
            # Cleanup
            self.safe_remove_file(file_path)