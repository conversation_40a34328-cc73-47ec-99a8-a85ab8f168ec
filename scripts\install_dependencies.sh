#!/bin/bash

# Install dependencies in Airflow containers
echo "Installing dependencies in Airflow containers..."

# Install playwright in the webserver
docker compose exec airflow-webserver pip install playwright

# Install playwright in the scheduler
docker compose exec airflow-scheduler pip install playwright

# Install playwright in the workers
docker compose exec airflow-worker-1 pip install playwright
docker compose exec airflow-worker-2 pip install playwright

echo "Dependencies installed successfully!"
