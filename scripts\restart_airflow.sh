#!/bin/bash

# <PERSON>ript to restart Airflow with the updated configuration

echo "Stopping all Docker containers..."
docker compose down

echo "Creating necessary directories..."
mkdir -p ./logs
mkdir -p ./dags
mkdir -p ./scripts
mkdir -p ./config
mkdir -p ./data

echo "Setting permissions..."
sudo chmod -R 777 ./logs
sudo chmod -R 777 ./dags
sudo chmod -R 777 ./scripts
sudo chmod -R 777 ./config
sudo chmod -R 777 ./data

echo "Creating a dummy gcp_config.json if it doesn't exist..."
if [ ! -f ./gcp_config.json ]; then
  echo '****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' > ./gcp_config.json
fi

echo "Creating .env file if it doesn't exist..."
if [ ! -f ./.env ]; then
  echo '# Airflow settings
AIRFLOW__CORE__EXECUTOR=CeleryExecutor
AIRFLOW__CORE__FERNET_KEY=46BKJoQYlPPOexq0OhDZnIlNepKFf87WFwLbfzqDDho=
AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION=False
AIRFLOW__CORE__LOAD_EXAMPLES=False
AIRFLOW__API__AUTH_BACKEND=airflow.api.auth.backend.basic_auth

# Database settings
AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@postgres/airflow
AIRFLOW__CELERY__RESULT_BACKEND=db+*********************************************
AIRFLOW__CELERY__BROKER_URL=redis://redis:6380/0

# User settings
AIRFLOW_UID=0
AIRFLOW_GID=0

# GCP settings
GCP_PROJECT_ID=your-project-id
GCP_CONFIG_PATH=/opt/airflow/gcp_config.json
GCS_BUCKET=your-gcs-bucket
GCS_OUTPUT_PREFIX=output

# Spark settings
SPARK_MASTER=local[*]' > ./.env
fi

# No longer need to create requirements-airflow.txt as we're using _PIP_ADDITIONAL_REQUIREMENTS

echo "Starting Docker containers..."
docker compose up -d

echo "Waiting for services to start..."
sleep 30

echo "Checking Airflow status..."
docker compose ps

echo "Checking DAG list..."
docker compose exec airflow-webserver airflow dags list

echo "Done! You can access the Airflow UI at http://localhost:8081"
echo "Default credentials: admin / admin"
