[{"jd_link": "https://careers.softwaremind.com/jobs/8ad-lead-quality-engineer/", "company_id": 3332, "source": 3, "skills": "", "title": "(8AD) Lead Quality Engineer", "location": "San Jose/Remote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/8ad-lead-quality-engineer/", "description": "Project - the aim you'll have Overview Software Mind is seeking qualified candidates located in Latam to fill the role of Lead Quality Engineer. In addition to a competitive salary rate and a positive work environment committed to delivering high-quality technology solutions, we also offer: Flexible schedules An authentic work-life balance Payment in US Dollars About the role: We are looking for a Lead QA Engineer who will manage the day-to-day, sprint-to-sprint activities they are assigned to. The QA Engineer needs to assess current testing needs and schedules. They also need to work with other team members to ensure that they adhere to specifications. Be able to report on any issues with their project. Create and deliver daily QA Metric reports to QA Lead and/or Stakeholders. #LI-DNI Expectations - the experience you need Some of the main responsibilities for the role include: Process enhancement – An effective QA Engineer should always seek out ways to improve processes, so familiarity with processes and efficiency enhancement is helpful in this role. Saying this is “good enough” is not the right answer. Organization skills – This role requires a degree of organization to successfully schedule and direct QA procedures and coordinate development and testing processes. Communication skills – Effective verbal and written communication is also important in this role as the QA Engineer provides reports to developers, engineers, and leadership to improve software and systems. Job Skills/Requirements – +90% English written and oral (at least B2 level) with excellent communication skills – 7+ years of QA experience as a QA Engineer – 5+ years working in an Agile Development Environment – Proficient with Azure DevOps, Azure Test Plans, AWS, SQL, Java, Selenium, JMeter, Cucumber, BDD, Gherkin, MS Word, Excel, Copilot, ChatGPT and PowerPoint – Effective leadership, scheduling, and management skills are also vital in this role because the QA Engineer coordinates the activities of their QA resources on their project – Ability to take business requests and break them down into manageable steps to achieve a goal Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Quality Assurance & Testing Automation Selenium Test Automation Engineer Kraków/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/8ai-product-designer/", "company_id": 3332, "source": 3, "skills": "", "title": "(8AI) Product Designer", "location": "San Jose/Remote", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/8ai-product-designer/", "description": "Project - the aim you'll have Overview Software Mind is seeking qualified candidates to fill the role of Product Designer. In addition to a competitive salary rate and a positive work environment, we are committed to delivering high-quality technology solutions, we also offer: Flexible schedules An authentic work-life balance Payment in US Dollars About the role: As a Product Designer, you will play a crucial role in shaping the user experience and visual identity of our AI-powered products. You will collaborate closely with cross-functional teams, including engineers, data scientists, product managers, and other designers, to create intuitive and engaging designs that meet user needs and business objectives. Your work will span the entire design process, from user research and concept development to detailed UI design and usability testing. #LI-DNI Expectations - the experience you need Some of the main responsibilities for the role include: Create high-fidelity mockups and prototypes to explore and communicate design ideas. Design intuitive, user-friendly interfaces for web and mobile applications Ensure consistency and adherence to design system guidelines across all aspects of the UX Work closely with product managers to define design requirements and priorities Collaborate with engineers to ensure feasible and scalable design solutions Present design ideas and concepts to stakeholders and incorporate feedback Plan and conduct usability tests to validate design decisions and gather user feedback Job Skills/Requirements – +90% English written and oral (at least B2 level) with excellent communication skills – 3+ years of experience as a Product Designer – Strong portfolio showcasing your design process and innovative solutions – Proficient in Figma and other design and prototyping tools – Solid understanding of user-centered design principles and best practices, including In-depth knowledge of Apple’s Human Interface Guidelines – Experience designing with the end user in mind. We are looking for someone who: Prioritizes user needs and behaviors in the design process. Has experience conducting user research, usability testing, or gathering user feedback. Can translate insights into intuitive, accessible, and delightful experiences. Understands human-centered design principles and applies them consistently. – Experience designing for end users (as opposed to internal tools). We are looking for someone who has: Designed customer-facing products (e. g. , mobile apps, websites, e-commerce platforms) rather than internal dashboards or tools used only by staff. Real-world experience creating experiences used by the general public, not just internal teams or B2B clients. – Ability to work in a fast-paced, dynamic environment and manage multiple projects simultaneously – Ability to take business requests and break them down into manageable steps to achieve a goal Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development Other (8SH) Power BI & Power Automate Developer San Jose/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/8nw-full-stack-react-typescript-node-js/", "company_id": 3332, "source": 3, "skills": "", "title": "(8NW) Full-Stack (React/TypeScript/Node.js)", "location": "San Jose/Remote", "location_type": "remote", "job_type": null, "min_experience": 2, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/8nw-full-stack-react-typescript-node-js/", "description": "Project - the aim you'll have Overview Software Mind is seeking qualified candidates to fill the role of Full-Stack Engineer (React/TypeScript/Node. js). In addition to a competitive salary rate and a positive work environment, we are committed to delivering high-quality technology solutions. We also offer: Flexible schedules and authentic work-life balance Opportunities for continuing education Birthday celebration Payment in US Dollars About the role: We are looking for a Full-Stack Developer to work with a team that handles data to work on credit card and bank-related products and services. Your work will include iterating and updating client-facing web pages and flows that guide users toward the right company-related products to meet their needs. You will also perform data integrations within the decision process to improve customer experience. The focus of the work is front-end development, but it also includes some back-end development tasks to access databases. Some of the main responsibilities for the role include: Write great code and be a team player who is willing and able to do code reviews and share your expertise and knowledge to support the team as a whole. Play a key role in supporting the ongoing efforts to make the client’s end-to-end SDLC fast and efficient, while maintaining industry-standard best practices. Work throughout the full tech stack using React. js, TypeScript, JavaScript, GraphQL, SQL, Material UI, CSS, and more. Contribute to the design and development of new applications, features, and flows to make the customer experience more intuitive and user-friendly. Support the adoption and migration of Next. js, to increase speed and responsiveness Develop new logic to address increased traffic scenarios across various pages and applications #LI-DNI Expectations - the experience you need Job Skills/Requirements – +90% English written and oral (at least B2-C1 level) with excellent communication skills – 5+ years in a professional software engineer position – 2+ years working on a public-facing website (Should understand: SEO, ADA, responsive, etc) – Advanced experience in CSS – Proficient experience in React, Node. js, Next. js, TypeScript, Context API, Datadog (or similar), and GraphQL – Proficient experience in SQL – Ability to take business requests and break them down into manageable steps to achieve a goal – Capable of designing a moderately sized software project, putting together a project plan to accomplish it, tracking progress, and executing it with a team – Able to see the big picture of how systems interact with one another Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development Java Node. js Software Engineer (Java & NodeJS/TypeScript) Kraków/Remote Software Development JavaScript Node. js React. js Senior Full-stack Software Engineer (Nest. js + React) Kraków/Remote Software Development Node. js React. js Team Leader/Tech Leader/Architect (Node. js + React) Kraków/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/8ai-frontend-engineer/", "company_id": 3332, "source": 3, "skills": "", "title": "(8AI) Frontend Engineer", "location": "San Jose/Remote", "location_type": "remote", "job_type": null, "min_experience": 7, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/8ai-frontend-engineer/", "description": "Project - the aim you'll have Overview Software Mind is seeking qualified candidates to fill the role of Frontend Engineer. In addition to a competitive salary rate and a positive work environment, we are committed to delivering high-quality technology solutions, we also offer: Flexible schedules and authentic work-life balance Opportunities for continuing education Social activities per country sponsored by the company Birthday celebration Payment in US Dollars About the role: As a Front-End Engineer, you’ll be responsible for designing and developing the user-facing components of our web applications. The ideal candidate will have extensive experience with modern front-end technologies, particularly React, and will be skilled in creating responsive, user-friendly interfaces. You will work closely with back-end engineers to ensure seamless integration of the front-end components and maintain high standards of performance and accessibility. #LI-DNI Expectations - the experience you need Some of the main responsibilities for the role include: Collaborate on the design and development of responsive and user-friendly web applications Utilize front-end technologies, particularly React, to create engaging and efficient user interfaces Implement features and interfaces using JavaScript and TypeScript Integrate with back-end services and APIs to ensure smooth data flow and application functionality Optimize front-end performance and ensure cross-browser compatibility Ensure the security and privacy of user data through the effective implementation of best practices Conduct testing, debugging, and troubleshooting to identify and address front-end issues Collaborate with cross-functional teams to implement continuous improvements and updates Job Skills/Requirements – +90% English written and oral (at least B2 level) with excellent communication skills – 7+ years of industry experience as a Front-End Engineer with a focus on web applications – Strong proficiency in React, JavaScript, and TypeScript – Experience with front-end technologies such as HTML5, CSS, and responsive design frameworks – Experience integrating with REST APIs and handling data from various sources – Familiarity with modern front-end build tools and workflows (e. g. , Webpack, Babel) – Knowledge of front-end performance optimization techniques and best practices – Familiarity with version control systems like Git – Exceptional creativity, along with strong collaboration and communication skills – Experience working with secure data, such as HIPAA or other sensitive information, is a plus – Ability to take business requests and break them down into manageable steps to achieve a goal – Capable of designing a moderately sized software project, putting together a project plan to accomplish it, tracking progress, and executing it with a team Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development JavaScript Node. js React. js Senior Full-stack Software Engineer (Nest. js + React) Kraków/Remote Software Development Node. js React. js (8NW) Full-Stack (React/TypeScript/Node. js) San Jose/Remote Software Development Node. js React. js Team Leader/Tech Leader/Architect (Node. js + React) Kraków/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/8pp-devops-engineer-aws-focus/", "company_id": 3332, "source": 3, "skills": "", "title": "(8PP) DevOps Engineer (AWS focus)", "location": "San Jose/Remote", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": 5, "apply_link": "https://careers.softwaremind.com/jobs/8pp-devops-engineer-aws-focus/", "description": "Project - the aim you'll have Overview Software Mind is seeking qualified candidates to fill the role of <PERSON><PERSON><PERSON> Engineer (AWS focus). In addition to a competitive salary rate and a positive work environment, we are committed to delivering high-quality technology solutions. We also offer: Flexible schedules and authentic work-life balance Opportunities for continuing education Birthday celebration Payment in US Dollars About the role: The AWS DevOps Engineer is a highly hands-on and technical role, responsible for developing, deploying, monitoring, and securing the client’s suite of SaaS products in a multi-cloud landscape. This position interacts with cross-functional teams that include developers, security engineers, SREs, corporate IT, and product management groups. The role focuses on supporting both internal development teams and external customers, resolving complex technical issues, and driving continuous improvement through infrastructure as code, cost optimization, and adherence to best practices in security and reliability. Success in this position requires effective collaboration across engineering and product teams, a customer-first mindset, and availability for Eastern US working hours. #LI-DNI Expectations - the experience you need Some of the main responsibilities for the role include: Infrastructure Management • Networking – Building out VPCs/VNets • Configuring security groups, subnets, and hosted zones • Infrastructure as Code – Provision, manage, and deploy resources using IaC tooling • Manage integrations between products and environments • Keeping services on the latest supported version Access Management • Configuring authentication via SAML/OIDC integrations • Managing Windows Active Directory and/or Entra ID domains Environment Support • Provide internal developer support and external production customer support • Work as part of a fast-paced operations environment with requests coming from ticketing systems • Troubleshoot complex issues to determine the root cause • Troubleshoot and resolve performance issues CI/CD • Work with Product Management and Software Development staff to deploy software to production • Leverage source control best practices for branching strategies, code reviews, and release management • Utilize CD pipelines to promote developer changes to ephemeral and long-standing testing environments Observability • Collect, visualize, and analyze logs/metrics to monitor the health • Implement alerting to identify environmental issues Security • Securely publish cloud apps to the internet via best practices in information security, privacy & zero trust principles • Comply with cloud security frameworks Reliability • Resolve issues in accordance with cloud services standards to ensure cases are resolved within SLAs • Design and implement solutions with reliability and resilience in mind Cost Management • Build cost-efficient solutions • Optimize resource utilization Job Skills/Requirements – 90+% English written and oral (at least B2 level) with excellent communication skills – 3-5 Years CloudOps and/or DevOps experience supporting PaaS/SaaS environments. – Proficient in AWS cloud services: AWS Lambdas, AWS Glue, EKS, Key Management, App Services (Elastic Beanstalk, App Runner), AWS Storage Accounts (S3, EFS, SQS, DynamoDB). – Understanding system and network architecture, system platforms, system access, and network protocols. – Experience in automating legacy processes and technologies in an AWS environment. – Experience using Terraform for infrastructure as code and automation. – Experience using various tools for application and infrastructure monitoring and troubleshooting. – Demonstrated ability to solve complex problems and possess a high level of technical skills, analytical, problem-solving, verbal, written communication, and presentation skills. – Experience with Agile methodology and running or participating in Scrum/Kanban procedures. – Experience with Python and Ansible. – Skilled in PowerShell for scripting and task automation. – Hands-on experience with Kubernetes for container orchestration and support. – Expertise in YAML and Helm for configuration management and deployment automation. – Strong documentation skills for maintaining clear and comprehensive technical records. – Proficiency in Git for version control and collaboration. – Experience with AWS DevOps for CI/CD pipelines and project management. – Knowledge of Docker for containerization and application deployment. – Availability to work in the Eastern US working hours. – Availability to support rotating on-call shifts. Additional Requirements: – Experience with API management for seamless integration and performance optimization. – Expertise with configuring identity federation (SAML, OIDC, Okta). – Understanding Firewall, Encryption, IDS/IPS, AV (anti-virus) tools. – Hands-on experience with Configuration Management (Puppet, Chef, & Ansible). – Experience with Infrastructure Monitoring, APM, and Log aggregation tools. – Experience with MLOps – model governance, deployment, security, monitoring. – Expertise in SQL for database management and data warehouse solutions (Databricks, Snowflake) Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers DevOps, Security & Cloud GCP Kubernetes Cloud Engineer Kraków/Remote DevOps, Security & Cloud Kubernetes Senior Kubernetes Engineer Warsaw/Remote DevOps, Security & Cloud Azure DevOps Kubernetes (8PP) DevOps Engineer (Azure focus) San Jose/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/8pp-devops-engineer/", "company_id": 3332, "source": 3, "skills": "", "title": "(8PP) DevOps Engineer (Azure focus)", "location": "San Jose/Remote", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": 5, "apply_link": "https://careers.softwaremind.com/jobs/8pp-devops-engineer/", "description": "Project - the aim you'll have Overview Software Mind is seeking qualified candidates to fill the role of <PERSON><PERSON>ps Engineer (Azure Focus). In addition to a competitive salary rate and a positive work environment, we are committed to delivering high-quality technology solutions. We also offer: Flexible schedules and authentic work-life balance Opportunities for continuing education Birthday celebration Payment in US Dollars About the role: The DevOps Engineer is a highly hands-on and technical role, responsible for developing, deploying, monitoring, and securing the client’s suite of SaaS products in a multi-cloud landscape. This position interacts with cross-functional teams that include developers, security engineers, SREs, corporate IT, and product management groups. The role focuses on supporting both internal development teams and external customers, resolving complex technical issues, and driving continuous improvement through infrastructure as code, cost optimization, and adherence to best practices in security and reliability. Success in this position requires effective collaboration across engineering and product teams, a customer-first mindset, and availability for Eastern US working hours and rotating on-call support. #LI-DNI Expectations - the experience you need Some of the main responsibilities for the role include: Infrastructure Management • Networking – Building out VPCs/VNets • Configuring security groups, subnets, and hosted zones • Infrastructure as Code – Provision, manage, and deploy resources using IaC tooling • Manage integrations between products and environments • Keeping services on the latest supported version Access Management • Configuring authentication via SAML/OIDC integrations • Managing Windows Active Directory and/or Entra ID domains Environment Support • Provide internal developer support and external production customer support • Work as part of a fast-paced operations environment with requests coming from ticketing systems • Troubleshoot complex issues to determine the root cause • Troubleshoot and resolve performance issues CI/CD • Work with Product Management and Software Development staff to deploy software to production • Leverage source control best practices for branching strategies, code reviews, and release management • Utilize CD pipelines to promote developer changes to ephemeral and long-standing testing environments Observability • Collect, visualize, and analyze logs/metrics to monitor the health • Implement alerting to identify environmental issues Security • Securely publish cloud apps to the internet via best practices in information security, privacy & zero trust principles • Comply with cloud security frameworks Reliability • Resolve issues in accordance with cloud services standards to ensure cases are resolved within SLAs • Design and implement solutions with reliability and resilience in mind Cost Management • Build cost-efficient solutions • Optimize resource utilization Job Skills/Requirements – +90% English written and oral (at least B2 level) with excellent communication skills – 3-5 Years CloudOps and/or DevOps experience supporting PaaS/SaaS environments. – AZ-900: Azure Fundamentals Certification is essential – AZ-104: Azure Administrator Associate or AZ-400: Azure DevOps certifications are preferred – Understanding system and network architecture, system platforms, system access, and network protocols. – Experience using various tools for application and infrastructure monitoring and troubleshooting. – Demonstrated ability to solve complex problems and possess a high level of technical skills, analytical, problem-solving, verbal, written communication, and presentation skills. – Experience with Agile methodology and running or participating in Scrum/Kanban procedures. – Proficient in Azure Cloud services – Data Factory, AKS, Key Vault, App Services, Storage Accounts – Experience in using Terraform for infrastructure, such as code and automation. – Skilled in PowerShell for scripting and task automation. – Hands-on experience with Kubernetes for container orchestration and support. – Expertise in YAML and Helm for configuration management and deployment automation. – Strong documentation skills for maintaining clear and comprehensive technical records. – Proficiency in Git for version control and collaboration. – Experience with Azure DevOps for CI/CD pipelines and project management. – Knowledge of Docker for containerization and application deployment. – Availability to work in the Eastern US working hours. – Availability to support rotating on-call shifts. Additional Requirements: – Experience with API management for seamless integration and performance optimization. – Expertise with configuring identity federation (SAML, OIDC, Okta). – Understanding Firewall, Encryption, IDS/IPS, AV (anti-virus) tools. – Hands-on experience with Configuration Management (Puppet, Chef, & Ansible). – Experience with Infrastructure Monitoring, APM, and Log aggregation tools. – Experience with MLOps – model governance, deployment, security, monitoring. – Expertise in SQL for database management and data warehouse solutions (Databricks, Snowflake) Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers DevOps, Security & Cloud Azure Senior DevOps Cloud Engineer (Azure) Kraków/Remote DevOps, Security & Cloud Azure DevOps Cloud Engineer Kraków/Remote DevOps, Security & Cloud AWS DevOps Kubernetes (8PP) DevOps Engineer (AWS focus) San Jose/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/8sh-power-bi-power-automate-developer/", "company_id": 3332, "source": 3, "skills": "", "title": "(8SH) Power BI & Power Automate Developer", "location": "San Jose/Remote", "location_type": "remote", "job_type": "full_time", "min_experience": 2, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/8sh-power-bi-power-automate-developer/", "description": "Project - the aim you'll have Overview Software Mind is seeking qualified candidates to fill the role of Power BI and Power Automate Developer. In addition to a competitive salary rate and a positive work environment, we are committed to delivering high-quality technology solutions, we also offer: Flexible schedules An authentic work-life balance Payment in US Dollars About the role: We are seeking a Power BI & Power Automate Developer to support our data reporting and business process automation needs. This role will play a key part in delivering clean, actionable insights through dashboards and reports, while also streamlining workflows using Power Automate. The ideal candidate has strong technical skills, a proactive mindset, and the ability to work independently in a fast-paced environment. This is a hands-on role requiring direct collaboration with business and technical teams to deliver high-quality, scalable solutions. #LI-DNI Expectations - the experience you need Some of the main responsibilities for the role include: Create and manage Power Automate flows to automate business processes and system interactions. Work directly with stakeholders to gather and refine technical and functional requirements. Perform data modeling, cleansing, and transformation using Power Query and SQL. Optimize report performance and ensure visualizations are user-friendly and actionable. Integrate data from multiple sources, including databases, Excel files, APIs, and SharePoint. Troubleshoot and resolve issues related to data accuracy, flow failures, or performance bottlenecks. Document all developed solutions, providing knowledge transfer to internal team members. Maintain data security and compliance with company policies throughout development and deployment. Participate in regular status meetings or check-ins as required by the project team. Collaborate proactively with IT, business analysts, and end users to ensure smooth integration of reports and automated workflows into existing systems and processes. Contribute to best practices and standards for Power BI report design, DAX usage, and Power Automate flow development. Provide clear documentation and in-line comments for Power BI measures, queries, and automation logic to support knowledge transfer and future maintenance Job Skills/Requirements – +90% English written and oral (at least B2 level) with excellent communication skills – Proven experience (3+ years) in Power BI development, including DAX, Power Query, and data modeling. -Strong experience (2+ years) in Power Automate, including scheduled flows, approval workflows, and system integrations. -Proficiency in SQL and working with various data sources (e. g. , SQL Server, Excel, APIs). -Ability to work independently and deliver solutions on a deadline. -Strong communication skills and ability to collaborate with technical and non-technical stakeholders. Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development Other (8AI) Product Designer San Jose/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/administration-specialist/", "company_id": 3332, "source": 3, "skills": "", "title": "Administration Specialist (Rzeszow)", "location": "Rzeszow", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/administration-specialist/", "description": "Position - how you'll contribute Providing comprehensive reception desk service, including ordering repairs, ensuring efficient office space organization, and maintaining contact with the building administrator Organization of domestic business trips Ensuring proper document flow Managing meetings and corporate events Organizing purchases (necessary office and food supplies) Collaborating with other internal departments on assigned tasks Supporting other administrative activities Expectations - the experience you need Experience in a similar position English proficiency at a communicative level (at least B1) Knowledge of MS Office High personal culture and interpersonal skills Strong organizational skills, accuracy, and responsibility Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/cloud-engineer/", "company_id": 3332, "source": 3, "skills": "", "title": "Cloud Engineer", "location": "Kraków/Remote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/cloud-engineer/", "description": "Project - the aim you'll have Our client is a gaming company established to reform the national gaming system. Operating under the principle of exclusivity, its mission is to provide games responsibly, mitigate gambling-related risks, and ensure player protection while preventing fraud. The company offers a wide range of games divided into three main categories: Lucky Games, Casino Games, and Betting Games. It emphasizes operating responsibly and contributing to a safe and secure gaming environment. Position - how you'll contribute Monitoring, alerting, automating and scaling cloud solutions. Creating coherence between development, operations and security in accordance with DevOps and Infrastructure as Code (IaC) principles. Performing Infrastructure as Code (IaC) tasks e. g. , in Terraform. Automating and standardizing cloud infrastructure processes. Implementing cloud governance policies. Expectations - the experience you need Hands-on experience with the public cloud service provider GCP and GKE Kubernetes High Availability services in Kubernetes environment Multitenant cluster – Kubernetes cluster services RBAC, Terraform, Grafana Cloud, Prometheus, Flux, Helm chart Practical work experience as a cloud engineer (5+ years) Must have experience in implementing, managing and operating systems/applications in a cloud computing environment Knowledge of tools for monitoring services and implementations in public clouds Experience in creating technical documents, diagrams, how-to guides and other deliverables Ability to learn and apply new concepts quickly Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers DevOps, Security & Cloud AWS DevOps Kubernetes (8PP) DevOps Engineer (AWS focus) San Jose/Remote DevOps, Security & Cloud Kubernetes Senior Kubernetes Engineer Warsaw/Remote DevOps, Security & Cloud Azure DevOps Kubernetes (8PP) DevOps Engineer (Azure focus) San Jose/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/devops-cloud-engineer/", "company_id": 3332, "source": 3, "skills": "", "title": "DevOps Cloud Engineer", "location": "Kraków/Remote", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": 3, "apply_link": "https://careers.softwaremind.com/jobs/devops-cloud-engineer/", "description": "Project - the aim you'll have We are looking for a talented DevOps Cloud Engineer to join our team and work on the one of exciting projects led by the Cloud Mind Service Unit. Our mission revolves around supporting entrepreneurs in uncovering the potential that the public cloud holds for their businesses. This is achieved through comprehensive analyses, valuable training and full engagement in the implementation processes, including leveraging advanced artificial intelligence systems. Position - how you'll contribute Develop and implement Infrastructure as Code (IaC) practices for automation and scalability. Design and implement Azure cloud solutions. Manage and optimize cloud infrastructure. Automate CI/CD processes using Azure DevOps. Monitor and ensure security of cloud environments. Create technical documentation and operational procedures. Provide technical support and troubleshoot infrastructure issues. Expectations - the experience you need Minimum 3 years of experience working with Microsoft Azure. Knowledge of Azure services: App Services, Virtual Machines, Storage, Networking. Experience with Infrastructure as Code (ARM Templates, Terraform). Familiarity with containerization (Docker, Kubernetes/AKS). Experience with Azure DevOps or similar CI/CD tools. Experience in developing Landing Zone. Additional skills - the edge you have One or more Azure cloud certifications. Ability to speak and write in English. Good communication with the team and clients (soft skills). Ability to create technical documentation. Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers DevOps, Security & Cloud Azure Senior DevOps Cloud Engineer (Azure) Kraków/Remote DevOps, Security & Cloud Azure DevOps Kubernetes (8PP) DevOps Engineer (Azure focus) San Jose/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/data-engineer-analyst/", "company_id": 3332, "source": 3, "skills": "", "title": "Data Engineer/Analyst", "location": "Kraków/Remote", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/data-engineer-analyst/", "description": "Project - the aim you'll have We are a dynamic and innovative team working in the media & advertising industry, leveraging cutting-edge technologies to drive data-driven insights. Our team is composed of experienced professionals who value creativity, collaboration, and continuous learning. We are looking for a proactive and analytical Data Analyst or Data Engineer who is eager to take on new challenges and think outside the box. The ideal candidate will have a strong foundation in SQL and data analytics, with additional experience in Python being a plus. Prior experience in the media or advertising industry is not required; we value curiosity and a willingness to learn. Position - how you'll contribute Analyze, process, and interpret large datasets to generate actionable insights. Develop and optimize SQL queries for efficient data retrieval and manipulation. Work with Snowflake and other modern data platforms to manage and analyze data. Utilize data visualization and BI tools to present insights effectively. Collaborate with cross-functional teams to support business intelligence and data-driven decision-making. Identify opportunities for automation and process improvement. Engage in innovative problem-solving and contribute to strategic initiatives. Expectations - the experience you need Strong proficiency in SQL. Experience working with databases and data warehouses (Snowflake experience is a plus). Familiarity with Python for data analysis and automation is an advantage. Experience with data visualization and BI tools (such as Tableau, Power BI, Looker, or similar) is a plus. Excellent analytical and problem-solving skills. A proactive, open-minded approach to challenges and learning new technologies. Strong communication and collaboration skills. Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/full-stack-software-engineer-typescript/", "company_id": 3332, "source": 3, "skills": "", "title": "Full-Stack Software Engineer (TypeScript)", "location": "Buenos Aires/Remote", "location_type": "remote", "job_type": null, "min_experience": 4, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/full-stack-software-engineer-typescript/", "description": "Project - the aim you'll have We are looking for a Mid-Level Full Stack Software Engineer to become a member of our dynamic team and work closely with one of our inspiring US clients who is dedicated to promoting economic mobility through education building high-quality automated tests and enhancing existing ones through optimization. If you are energetic, self-motivated, team-oriented, and enjoy being a key contributor in an entrepreneurial environment, this role is for you! Expectations - the experience you need 4+ years of experience in web application design, development, and implementation. 4+ experience with Typescript (Mandatory), NodeJS & React. Experience with RESTful Web Service development and consumption. Experience working with SaaS-based platforms and technologies. Experience with PostgreSQL. Experience with AWS/GCP. Nice to have requirements: Experience working on cloud-based infrastructure (ideally GCP or AWS). Experience with Event-driven and/or Service Oriented Architecture. Salesforce integration. Experience working in an Agile (Scrum) environment. Experience with tools such as Git, Jira, Cypress, VI test, Slack, MS Office, Docker, GraphQL. Our offer – professional development, personal growth Educational resources Flexible schedule and work from anywhere Referral Program Supportive and chill atmosphere We are accepting applications from LATAM countries Position at: Software Mind Latam #LI-DNI Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers DevOps, Security & Cloud Azure Senior DevOps Cloud Engineer (Azure) Kraków/Remote Software Development Golang Java Senior Back-end Software Engineer (Java + willing to learn Go) Bucharest/Remote Software Development Java Node. js Software Engineer (Java & NodeJS/TypeScript) Kraków/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/front-end-software-engineer-with-german-reactangular/", "company_id": 3332, "source": 3, "skills": "", "title": "Front-end Software Engineer with German (React&Angular)", "location": "Kraków/Remote", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/front-end-software-engineer-with-german-reactangular/", "description": "Project - the aim you'll have Project – the aim you’ll have: We are looking for a capable and motivated Intermediate Frontend Engineer to support one of our cross-functional product Teams. The role is ideal for someone with solid experience in frontend development who enjoys collaborative work, takes initiative, and is keen to grow further by contributing to complex projects in an agile setup. Position – how you’ll contribute: The Intermediate Frontend Engineer will: Support the development and maintenance of frontend projects, including migrating legacy solutions and assisting with framework version updates. Collaborate closely with our internal client teams and maintain clear communication with stakeholders on both the client and service side to ensure smooth project execution. Actively contribute to the team by listening, asking questions, sharing ideas, and being open to new concepts and perspectives. Help ensure high-quality standards in our digital products, making decisions based on data and user-centric thinking. Share knowledge with team members and support others with specific technical questions. Occasionally, you may organize internal sessions to foster learning and knowledge exchange. Identify challenges early Expectations - the experience you need We expect the nominated engineer to bring the following qualifications: Solid Frontend Knowledge: Proficiency in HTML5, CSS3, ES5/6, and TypeScript. Experience with at least one modern JavaScript framework (e. g. Vue. js/Nuxt, React/Gatsby, or Angular). Code Quality & Testing: Understanding of clean code practices and familiarity with writing meaningful test cases using tools like Jest or TestCafé. Toolchain Experience: Confident use of Git, build tools (e. g. NPM), and experience in setting up or working within existing environments. Team Collaboration: Comfortable working in agile setups (e. g. Scrum, Kanban), with a proactive and communicative approach. Language Skills: Good communication skills in both German and English, written and spoken. Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development JavaScript Node. js React. js Senior Full-stack Software Engineer (Nest. js + React) Kraków/Remote Software Development Node. js React. js (8NW) Full-Stack (React/TypeScript/Node. js) San Jose/Remote Software Development Node. js React. js Team Leader/Tech Leader/Architect (Node. js + React) Kraków/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-advertising-data-specialist/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior Advertising Data Specialist", "location": "Warsaw/Remote", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/senior-advertising-data-specialist/", "description": "Project - the aim you'll have We are a dynamic and innovative team working in the media & advertising industry, leveraging cutting-edge technologies to drive data-driven insights. Our team is composed of experienced professionals who value creativity, collaboration, and continuous learning. We are looking for a proactive and analytical Advertising Data Specialist who is eager to take on new challenges and think outside the box. The ideal candidate will have a strong foundation in programmatic platforms and data analytics, with additional experience in broadcast and ad serving systems being a plus *Hybrid working model Position - how you'll contribute analysis of data on the effectiveness of advertising formats (display, video, native, programmatic), analysis of the effectiveness of advertising placement strategy, support in planning, implementation, analysis and communication of A/B test results in the advertising area, cooperation with the advertising product team, UX/UI and editorial team in work on the roadmap and the advertising layout development strategy, identification of user behaviors and their reactions to ads, translation of business needs into analytical and reporting requirements, preparation of ad hoc analyses for team needs, preparation of reporting, dashboards and presentations Expectations - the experience you need knowledge of programmatic platforms (SSP) in the Polish market, proficiency in analytical tools (Google Analytics, Gemius Prism), ability to interpret data and draw business conclusions Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-back-end-engineer-python/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior Back-end Engineer (Python)", "location": "Buenos Aires/Remote", "location_type": "flexible", "job_type": null, "min_experience": 5, "max_experience": 5, "apply_link": "https://careers.softwaremind.com/jobs/senior-back-end-engineer-python/", "description": "Project - the aim you'll have We’re looking for a skilled Senior Backend Engineer (Python) to join our dynamic team building an AI-powered “co-intelligence” for industrial manufacturing, combining data analytics, generative AI, and interactive guidance to help reduce wasted energy and carbon footprint from large manufacturers. You will play a key role in designing and implementing the core business logic behind our user-facing applications. These applications are the daily workflow tools for plant engineers and operators, proactively assembling insights, guiding decisions, and managing complex industrial processes. You will collaborate closely with data infrastructure engineers, AI engineers, and front- end developers to deliver complete solutions – from early prototypes to production- grade systems. As part of a small, fast-moving team, you’ll have broad ownership and the opportunity to extend your contributions across the stack, including infrastructure, data pipelines, and front-end development, based on your skills and interests. If you enjoy working with cutting-edge technologies in a fast-paced environment this opportunity is for you! Expectations - the experience you need 10+ years of professional software development experience, with at least 5 years of Python development focused on backend systems. Proven experience building backend services with complex business logic – not just API wiring, but thoughtful state management, data modeling, and lifecycle orchestration. Strong working knowledge of SQL; experience with relational databases (e. g. Postgres), database schema design and evolution, and designing application- layer data workflows. Solid computer science fundamentals, including algorithmic thinking, data structures (graphs, queues, etc. ), and design patterns. Experience building RESTful APIs (and/or GraphQL) and integrating backend services with external systems. Strong unit and integration testing practices, and experience integrating with CI/CD pipelines. Comfortable working in a dynamic, early-stage startup environment: independent, adaptable, proactive, and able to drive work forward with minimal oversight. Excellent communication skills — ability to translate requirements into technical designs, iterate with teammates, and build consensus. Nice to have Demonstrated experience contributing to open-source projects. Experience building data-focused backend services to support data-driven visualizations, dashboards, and analytics applications. Familiarity with containerization (Docker) and cloud deployments (GCP preferred, AWS/Azure acceptable). Experience setting up scalable development processes for application backends (testing strategies, CI/CD, monitoring, observability). Familiarity with data lifecycle management, data lineage, and best practices for maintaining data integrity in complex systems. Experience working in Agile/Scrum development environments. Full-stack development experience with modern front-end frameworks (React, Next. js, Typescript). Experience with event-driven architectures or message queues (e. g. , Pub/Sub). What you will do Design, develop, and operate Python-based backend services and APIs to power data- driven web applications. Architect and implement complex business logic and state management for applications involving event lifecycles, workflow orchestration, and intelligent data processing. Integrate with our time-series data platform and AI reasoning engine to deliver proactive, interactive features to end users. Own the full application feature lifecycle: from rapid prototyping to scalable production deployment, with strong testing and CI/CD practices. Shape the overall architecture of application backends, collaborating with peers across data engineering, AI, infrastructure, and front-end teams. Support customer pilots and implementations by developing custom data processing and analytics modules as needed. Our Benefits Educational resources Flexible schedule and Work From Anywhere Referral Program Supportive and chill atmosphere We are accepting applications from LATAM countries Position at: Software Mind LATAM #LI-DNI Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development PHP Python Senior Back-end Software Developer (Python + PHP) Buenos Aires Software Development PHP Python Technical Lead Buenos Aires/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-back-end-software-developer-python-php/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior Back-end Software Developer (Python + PHP)", "location": "Buenos Aires", "location_type": "remote", "job_type": null, "min_experience": 4, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/senior-back-end-software-developer-python-php/", "description": "Project - the aim you'll have We are currently looking for a skilled Senior Back-end Software Engineer (Python + PHP) to become a member of our dynamic team and work closely with one of our inspiring US clients! Our client is modernizing the real estate world’s way of transferring information and funds to eliminate wire fraud and provide a secure, easy-to-use platform for title companies, law firms, and other financial services to protect themselves and their clients from wire fraud. Expectations – the experience you need 8+ years of experience with Python. 4+ years of experience in PHP web application development; experience with <PERSON><PERSON> is a plus. Bachelor’s or Master’s degree in Computer Science, Engineering, or a related field, or equivalent experience. Experience with custom application development. Strong experience in developing web applications using Flask, with openness to candidates with significant experience in other major Python web frameworks like Django or Pyramid. Knowledge of continuous integration/continuous deployment (CI/CD) pipelines Experience with software testing, TDD, and unit testing. Experience with relational databases, including schema design, query optimization, and integration with Python applications. Solid working experience building RESTful APIs. Familiarity with containerization technologies like Docker. Active experience integrating custom code with 3rd party web services. Hands-on experience with tools such as Git, Jira and Confluence. Experience working in the AWS (Amazon Web Services) ecosystem. Familiarity with application monitoring and reporting software such as Datadog and New Relic. Must be dedicated, passionate, hard-working, and excited to work in an energetic environment. Must be able to work with a team and collaborate effectively. We are looking for a creative and efficient problem solver. Position - how you'll contribute Actively participate in the design, development, and maintenance of web applications, ensuring high performance, responsiveness, and scalability. Contribute to the full software development lifecycle, from concept and design to testing and deployment. Engender a culture of documentation, collaboration, and best practices. Consult efforts regarding feature requests, feedback, and future product development for the business. Our offer – professional development, personal growth Educational resources Flexible schedule and work from anywhere Referral Program Supportive and chill atmosphere We are accepting applications from LATAM countries Position at: Software Mind Latam #LI-DNI Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development Python Senior Back-end Engineer (Python) Buenos Aires/Remote Software Development PHP SQL Technical Leader (PHP) Warsaw/Remote Software Development PHP Python Technical Lead Buenos Aires/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-back-end-software-engineer-go/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior Back-end Software Engineer (Java + Go)", "location": "Bucharest/Remote", "location_type": "remote", "job_type": null, "min_experience": 4, "max_experience": 4, "apply_link": "https://careers.softwaremind.com/jobs/senior-back-end-software-engineer-go/", "description": "Project - the aim you'll have Our client is a gaming company established to reform the national gaming system. Operating under the principle of exclusivity, its mission is to provide games responsibly, mitigate gambling-related risks, and ensure player protection while preventing fraud. The company offers a wide range of games divided into three main categories: Lucky Games, Casino Games, and Betting Games. It emphasizes operating responsibly and contributing to a safe and secure gaming environment. Position - how you'll contribute Collaborate with the team to design and implement scalable and efficient back-end applications and services. Build and maintain microservices architectures, ensuring high performance and reliability. Develop and manage containerized applications, ensuring smooth deployment and orchestration. Contribute to the setup and maintenance of CI/CD pipelines to streamline development workflows. Troubleshoot and resolve technical issues, ensuring system stability and optimal performance. Participate in code reviews and provide constructive feedback to ensure code quality. Work closely with other developers, product managers, and stakeholders to deliver high-quality software solutions. Expectations - the experience you need Minimum 4 years of software development experience, with a focus on GoLang. Proficiency with the GIN framework or similar frameworks. Hands-on experience with Docker and Kubernetes for containerization and orchestration. Strong command of SQL database technologies and concepts (experience with Postgres is a plus). Experience with cloud platforms, particularly GCP (AWS is also beneficial). Familiarity with CI/CD tools such as GitLab CI, Jenkins, or similar platforms. Strong understanding of software development best practices, version control systems (Git), and agile methodologies. Excellent problem-solving skills and a proactive attitude toward learning new technologies. Ability to efficiently work with tech stacks spanning multiple programming languages. Additional skills - the edge you have Experience with logging and monitoring tools like Prometheus, Grafana, or ELK Stack. Past experience with Java. Knowledge of testing frameworks and methodologies in GoLang. Understanding of security best practices for cloud and containerized environments. Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development Golang Java Senior Back-end Software Engineer (Java + willing to learn Go) Bucharest/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-back-end-software-engineer-java-willing-to-learn-go/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior Back-end Software Engineer (Java + willing to learn Go)", "location": "Bucharest/Remote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": 5, "apply_link": "https://careers.softwaremind.com/jobs/senior-back-end-software-engineer-java-willing-to-learn-go/", "description": "Project - the aim you'll have Our client is a gaming company established to reform the national gaming system. Operating under the principle of exclusivity, its mission is to provide games responsibly, mitigate gambling-related risks, and ensure player protection while preventing fraud. The company offers a wide range of games divided into three main categories: Lucky Games, Casino Games, and Betting Games. It emphasizes operating responsibly and contributing to a safe and secure gaming environment. Expectations - the experience you need Position – how you’ll contribute Collaborate with the team to design and implement scalable and efficient back-end applications and services. Build and maintain microservices architectures, ensuring high performance and reliability. Develop and manage containerized applications, ensuring smooth deployment and orchestration. Contribute to the setup and maintenance of CI/CD pipelines to streamline development workflows. Troubleshoot and resolve technical issues, ensuring system stability and optimal performance. Participate in code reviews and provide constructive feedback to ensure code quality. Work closely with other developers, product managers, and stakeholders to deliver high-quality software solutions. Minimum 5 years of software development experience, with a focus on Java. Basic understanding of GoLang and a strong willingness to learn and adapt. Your work will involve leveraging a cutting-edge technology stack that includes Java, RESTful APIs, Couchbase, Postgres, Docker, and more. Knowledge of enterprise message buses, such as Kafka or RabbitMQ. Experience creating HTTP APIs and a strong understanding of the HTTP stack. Excellent problem-solving skills and a proactive attitude toward learning new technologies. Ability to efficiently work with tech stacks spanning multiple programming languages. Excellent communication skills, including fluency in English, for effective client interactions. Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development Java Node. js Software Engineer (Java & NodeJS/TypeScript) Kraków/Remote Software Development Golang Senior Back-end Software Engineer (Java + Go) Bucharest/Remote Software Development Java Node. js Senior Software Engineer (Java & NodeJS/TypeScript) Kraków/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-back-end-software-engineer-java-jsp/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior Back-end Software Engineer (Java + JSP)", "location": "Chisinau/Remote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/senior-back-end-software-engineer-java-jsp/", "description": "Project - the aim you'll have We are seeking an experienced developer with strong JSP and CSS expertise to work on the modernization of our trade finance platform’s user interface. The ideal candidate will transform our legacy system into a vibrant, modern application while maintaining functionality and enhancing user experience. Our client is a Swiss-headquartered software development and IT services company, supporting a global customer base through offices in Geneva, London, Singapore, and Houston. It offers digital network and workflow solutions that overcome the shortcomings of today’s trade finance ecosystem, accelerating efficiency gains, providing a benchmark data reference for the industry, and delivering best-in-class value to end users through four product lines. Position - how you'll contribute Lead the modernization of a legacy JSP-based trade finance system, revamping it with modern UI/UX principles while maintaining robust backend functionality. Develop and maintain scalable, high-quality Java-based backend applications in a complex enterprise environment. Collaborate with business stakeholders to understand workflows and user needs, translating them into technical solutions. Work independently to implement features and improvements, even when specifications are incomplete. Ensure high standards of code quality, performance, and security throughout the development lifecycle. Identify opportunities for architectural or UI/UX improvements and proactively propose solutions. Expectations - the experience you need 5+ years of experience as a Java Developer, with a proven track record in building scalable, high-quality backend applications. Experience with the Spring Framework and Java REST APIs. Proficiency with SQL and relational databases. Previous experience with JSP and frontend technologies such as HTML, JavaScript, and CSS. Familiarity with SOLID and Domain-Driven Design (DDD) principles. Experience in UI/UX improvements, particularly in transitioning from legacy interfaces to modern designs. Excellent problem-solving skills and attention to detail. Strong English communication skills, both written and verbal. Additional skills - the edge you have Self-motivated with the ability to work with minimal supervision. Proactive approach to identifying and solving problems. Strong communication skills to articulate design recommendations. Adaptable to changing requirements and priorities. Collaborative team player who can also work autonomously. Experience in financial services or trade finance is a plus. Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development JavaScript Node. js React. js Senior Full-stack Software Engineer (Nest. js + React) Kraków/Remote Software Development JavaScript Vue. js Senior Front-end Engineer (Vue. js) Buenos Aires/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-devops-cloud-engineer-azure/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior DevOps Cloud Engineer (Azure)", "location": "Kraków/Remote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": 7, "apply_link": "https://careers.softwaremind.com/jobs/senior-devops-cloud-engineer-azure/", "description": "Project - the aim you'll have We are looking for a talented Senior DevOps Cloud Engineer to join our team and work on the one of exciting projects led by the Cloud Mind Service Unit. Our mission revolves around supporting entrepreneurs in uncovering the potential that the public cloud holds for their businesses. This is achieved through comprehensive analyses, valuable training and full engagement in the implementation processes, including leveraging advanced artificial intelligence systems. You will work for one of our Dutch clients which who provide highly secure and reliable IT environments for a variety of public and private customers on Dutch market. Position - how you'll contribute Design, implement, and maintain enterprise-grade Azure infrastructure in accordance with the Cloud Adoption Framework and industry best practices Build and manage Landing Zones using Terraform for automation Develop, extend, and maintain CI/CD pipelines in Azure DevOps, enabling environment provisioning Design and maintain secure private networks in Azure (Hub & Spoke, VPNs, firewalls, NSGs, private endpoints) Implement and maintain monitoring and alerting solutions (Azure Monitor, Log Analytics, Grafana) to ensure system reliability and rapid incident response Apply and enforce governance, security, and compliance using Azure Policy, Defender for Cloud, Key Vault, RBAC, and other controls Collaborate with DevOps engineers and architects on a daily basis Expectations - the experience you need Expectations – the experience you need: 5–7 years of experience in IT (DevOps, System Administration, Development), maintaining production systems and environments 5+ years of experience working with Microsoft Azure in complex enterprise environments Proven track record of designing and deploying Azure Landing Zones using Terraform Expertise in Azure DevOps: creating reusable templates, managing environments, release pipelines, and approvals Strong experience in monitoring and observability, including writing custom alert rules Hands-on experience with private networking in Azure and managing virtual machines (Windows and Linux) Deep understanding of cloud security, governance, and compliance principles, and the ability to apply them in practice Experience conducting technical audits and communicating findings to technical and non-technical stakeholders Additional skills - the edge you have Excellent communication skills in English (spoken and written) – ability to clearly explain technical topics to clients and team members Proactive mindset – able to identify, communicate, and implement improvements in existing systems and workflows Strong organizational skills – capable of prioritizing key tasks, documenting architecture, and maintaining knowledge-sharing practices Collaborative attitude – works well with other DevOps team members, takes ownership, and drives initiatives from idea to execution Holds one or more Azure cloud certifications Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers DevOps, Security & Cloud Azure DevOps Cloud Engineer Kraków/Remote DevOps, Security & Cloud Azure DevOps Kubernetes (8PP) DevOps Engineer (Azure focus) San Jose/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-devops-cloud-engineer/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior DevOps Cloud Engineer (Azure)", "location": "Kraków/Remote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": 7, "apply_link": "https://careers.softwaremind.com/jobs/senior-devops-cloud-engineer/", "description": "Project - the aim you'll have We are looking for a talented Senior DevOps Cloud Engineer to join our team and work on the one of exciting projects led by the Cloud Mind Service Unit. Our mission revolves around supporting entrepreneurs in uncovering the potential that the public cloud holds for their businesses. This is achieved through comprehensive analyses, valuable training and full engagement in the implementation processes, including leveraging advanced artificial intelligence systems. You will work for one of our Dutch clients which who provide highly secure and reliable IT environments for a variety of public and private customers on Dutch market. Position - how you'll contribute Design, implement, and maintain enterprise-grade Azure infrastructure in accordance with the Cloud Adoption Framework and industry best practices Build and manage Landing Zones using Terraform for automation Develop, extend, and maintain CI/CD pipelines in Azure DevOps, enabling environment provisioning Design and maintain secure private networks in Azure (Hub & Spoke, VPNs, firewalls, NSGs, private endpoints) Implement and maintain monitoring and alerting solutions (Azure Monitor, Log Analytics, Grafana) to ensure system reliability and rapid incident response Apply and enforce governance, security, and compliance using Azure Policy, Defender for Cloud, Key Vault, RBAC, and other controls Collaborate with DevOps engineers and architects on a daily basis Expectations - the experience you need Expectations – the experience you need: 5–7 years of experience in IT (DevOps, System Administration, Development), maintaining production systems and environments 5+ years of experience working with Microsoft Azure in complex enterprise environments Proven track record of designing and deploying Azure Landing Zones using Terraform Expertise in Azure DevOps: creating reusable templates, managing environments, release pipelines, and approvals Strong experience in monitoring and observability, including writing custom alert rules Hands-on experience with private networking in Azure and managing virtual machines (Windows and Linux) Deep understanding of cloud security, governance, and compliance principles, and the ability to apply them in practice Experience conducting technical audits and communicating findings to technical and non-technical stakeholders Additional skills - the edge you have Excellent communication skills in English (spoken and written) – ability to clearly explain technical topics to clients and team members Proactive mindset – able to identify, communicate, and implement improvements in existing systems and workflows Strong organizational skills – capable of prioritizing key tasks, documenting architecture, and maintaining knowledge-sharing practices Collaborative attitude – works well with other DevOps team members, takes ownership, and drives initiatives from idea to execution Holds one or more Azure cloud certifications Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers DevOps, Security & Cloud Azure DevOps Cloud Engineer Kraków/Remote DevOps, Security & Cloud Azure DevOps Kubernetes (8PP) DevOps Engineer (Azure focus) San Jose/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-front-end-engineer-vue-js/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior Front-end Engineer (Vue.js)", "location": "Buenos Aires/Remote", "location_type": "remote", "job_type": null, "min_experience": 4, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/senior-front-end-engineer-vue-js/", "description": "Project - the aim you'll have We are currently looking for a skilled Senior Front-end Engineer (Vue. js) to become a member of our dynamic team and work closely with one of our inspiring US clients! Our client is modernizing the real estate world’s way of transferring information and funds to eliminate wire fraud and provide a secure, easy-to-use platform for title companies, law firms, and other financial services to protect themselves and their clients from wire fraud. Expectations – the experience you need 6+ years of experience with Vue. js (preferably including Vue3 and modern Vue paradigms) 4+ years of experience with custom web application development Must be dedicated, passionate, curious, hard-working, and excited to work in an energetic environment A creative and efficient problem solver who flourishes when working on challenging issues Attention to detail and an understanding of what makes a great user experience Ability to work well with the Product team to iterate on designs and user experience flows Build reusable, testable code that will drive engineering efficiency Self-directed with a strong bias towards action and thrives in an ambiguous environment that is ripe for disruption Experience with Vue. js, JavaScript, HTML5, and CSS3 In-depth knowledge of front-end testing options Experience building consumer-facing front-end web apps Hands-on experience with tools such as git, Jira, and Confluence Strong skills with JavaScript and the corresponding toolchain Experience with PHP, Laravel, and j<PERSON>uer<PERSON> Demonstrated success in modernizing legacy applications with modern front-end best practices Position - how you'll contribute Own portions of the front-end development, including working with Product and Designers on UI concepts and implementing them. Work with other highly skilled front-end engineers to create a cohesive customer experience throughout the client’s ecosystem Engender a culture of documentation, collaboration, and best practices. Consult on efforts regarding feature requests, feedback, and future product development for the business. Work directly with the engineering leadership to continue establishing a maintainable, testable, and elegant code base. Our offer – professional development, personal growth Educational resources Flexible schedule and work from anywhere Referral Program Supportive and chill atmosphere We are accepting applications from LATAM countries Position at: Software Mind Latam #LI-DNI Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development JavaScript Senior Back-end Software Engineer (Java + JSP) Chisinau/Remote Software Development JavaScript Node. js React. js Senior Full-stack Software Engineer (Nest. js + React) Kraków/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-full-stack-engineer-c-net/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior Full-stack Engineer (C# + .NET)", "location": "San Jose/Remote", "location_type": "remote", "job_type": null, "min_experience": 6, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/senior-full-stack-engineer-c-net/", "description": "Project - the aim you'll have We’re looking for a Full Stack Engineer, who will play a critical role in designing and developing web applications and backend APIs. The ideal candidate will have a deep understanding of full-stack web development with a specialization in supporting mobile applications. Additionally, this candidate will be an expert on REST APIs and distributed systems that scale to millions of users. Main Responsibilities: Collaborate on the design and development of responsive and user-friendly healthcare web applications. Utilize front-end technologies (e. g. , React, Vue. js) to create engaging user interfaces. Develop server-side logic using back-end technologies like C# and . NET Core. Integrate third-party APIs and services to enhance application functionality. Ensure the security and privacy of healthcare data through effective implementation of best practices. Conduct testing, debugging, and troubleshooting to identify and address software issues. Collaborate with cross-functional teams to implement continuous improvements and updates. Expectations - the experience you need +90% English written and oral (at least B2 level) with excellent communication skills. 6+ years of industry experience as a Full Stack Engineer supporting enterprise B2B web applications. Strong understanding of back-end development using C# and Front-end development using React. In-depth knowledge of Azure services. Strong proficiency in front-end technologies (HTML5, CSS, JavaScript). Experience working in SaaS on Cloud-Native applications. Ability to take business requests and break them down into manageable steps to achieve a goal. Capable of designing a moderately sized software project, putting together a project plan to accomplish it, tracking progress, and executing it with a team. Able to see the big picture of how systems interact with one another. Nice to have: Experience with collecting large amounts of data from mobile and health devices. Experience designing and implementing scalable and resilient architectures. Familiarity with multi-tenancy considerations and solutions. Proficiency in scripting and automation using tools such as PowerShell or Azure CLI. Exceptional creativity, along with strong collaboration and communication skills. Experience working with secure data such as HIPAA or other sensitive information. Our Benefits Flexible schedules and authentic work-life balance. Opportunities for continuing education. Birthday celebration. Payment in US Dollars. We are accepting applications from LATAM countries #LI-DNI Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development JavaScript Node. js React. js Senior Full-stack Software Engineer (Nest. js + React) Kraków/Remote Software Development Node. js React. js (8NW) Full-Stack (React/TypeScript/Node. js) San Jose/Remote Software Development Node. js React. js Team Leader/Tech Leader/Architect (Node. js + React) Kraków/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-full-stack-software-engineer-nest-js-react/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior Full-stack Software Engineer (Nest.js + React)", "location": "Kraków/Remote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": 5, "apply_link": "https://careers.softwaremind.com/jobs/senior-full-stack-software-engineer-nest-js-react/", "description": "Project - the aim you'll have Working with a Scrum team of Engineers on a greenfield project Developing software applications and solutions based on documented business and system requirements Understanding how applications operate, they are structured, and how customers use them Taking ownership of the delivery schedule and quality of work Providing input and estimates about the effort required to build desired functionality Designing applications for stability, scalability, and performance Stack: JavaScript/TypeScript, Node. js, React, Azure Expectations - the experience you need Approximately 5 years of development experience using JavaScript/TypeScript. Expertise in building responsive and advanced UI components compliant with WCAG standards using React, ensuring applications are well-performant and optimized for mobile devices or weak internet connection. Deep knowledge of Redux. Proven knowledge in designing and implementing microservices with NestJS and PostgreSQL, applying clean code principles and design patterns. Ensuring applications are secure to the highest industry standards and resistant to cyber attacks, following OWASP guidelines. Strong experience with unit testing using Jest to meet high quality standards. This role requires candidates to be based in the European Union Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development Java Node. js Software Engineer (Java & NodeJS/TypeScript) Kraków/Remote Software Development JavaScript Senior Back-end Software Engineer (Java + JSP) Chisinau/Remote Software Development Node. js React. js (8NW) Full-Stack (React/TypeScript/Node. js) San Jose/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-kubernetes-engineer/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior Kubernetes Engineer", "location": "Warsaw/Remote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": 5, "apply_link": "https://careers.softwaremind.com/jobs/senior-kubernetes-engineer/", "description": "Project - the aim you'll have We are responsible for building a platform managing Kubernetes clusters for corporate clients and government institutions. Position - how you'll contribute You will be responsible for the development of the cluster management system – creating new clusters and methods of upgrading existing ones. Tshooting existing problems. Expectations - the experience you need Proficiency in Linux/Unix system Scripting skills and programming best practicies (Python) Containerization (Kubernetes at least 5 years of experience is must have) Monitoring (e. g. Prometheus + Grafana) Incident Management Tool automation Additional skills - the edge you have VMware vSphere knowledge Ability to work without documentation and with legacy codebase Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers DevOps, Security & Cloud AWS DevOps Kubernetes (8PP) DevOps Engineer (AWS focus) San Jose/Remote DevOps, Security & Cloud GCP Kubernetes Cloud Engineer Kraków/Remote DevOps, Security & Cloud Azure DevOps Kubernetes (8PP) DevOps Engineer (Azure focus) San Jose/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-mobile-engineer-react-native/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior Mobile Engineer (React Native)", "location": "Buenos Aires/Remote", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": 3, "apply_link": "https://careers.softwaremind.com/jobs/senior-mobile-engineer-react-native/", "description": "Project - the aim you'll have We are looking for a Senior Mobile Engineer (React Native) to join one of our USA Clients’ engineering teams which is building solutions to dramatically change the way people purchase their daily goods, providing the modern-day solution to meet customer’s immediate everyday needs with products ranging from snacks and ice cream to household goods and beer, at the click of a button. If you enjoy working with cutting-edge technologies in a fast-paced environment this opportunity is for you! Expectations - the experience you need 5+ years of experience in mobile app development, with at least 3 years in React Native. Expert knowledge of React Native, modern ES6+ JavaScript, TypeScript, and GraphQL. Deep experience with consuming and managing APIs, local storage, and navigation stacks. Proven ability to turn Figma designs into accurate, responsive UI components. Strong knowledge of Web and Native performance best practices and debugging tools. Fast learner with a track record of rapidly ramping up on unfamiliar codebases and projects. Excellent communication and collaboration skills with cross-functional partners and teams. Bachelor’s degree in computer science (or related field). Familiarity with iOS Human Interface Guidelines and Android Material Design principles. (Nice to Have) Experience working on apps with large user bases and/or real-time features. (Nice to Have) Experience with AI technologies, including integrating AI-driven features or working alongside AI/ML product teams. (Nice to Have) Contributions to open-source projects and/or a public GitHub portfolio. (Nice to Have) What you will do Architect, develop, and maintain complex, high-quality features in React Native, ES6, TypeScript, and GraphQL. Translate high-fidelity Figma designs into pixel-perfect interfaces with strong adherence to design systems and micro-interactions. Drive code quality and performance standards, focusing on cross-platform (Web, iOS, and Android) functionality. Own features end-to-end, including implementation, testing, deployment, and post-release optimization. Collaborate closely with product managers, designers, and stakeholders to deliver seamless user experiences. Our Benefits Educational resources. Flexible schedule and Work From Anywhere. Referral Program. Supportive and chill atmosphere. We are accepting applications from LATAM countries Position at: Software Mind LATAM #LI-DNI Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers DevOps, Security & Cloud Azure Senior DevOps Cloud Engineer (Azure) Kraków/Remote Software Development Golang Java Senior Back-end Software Engineer (Java + willing to learn Go) Bucharest/Remote Software Development Java Node. js Software Engineer (Java & NodeJS/TypeScript) Kraków/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-salesforce-administrator/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior Salesforce Administrator", "location": "Buenos Aires/Remote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": 5, "apply_link": "https://careers.softwaremind.com/jobs/senior-salesforce-administrator/", "description": "Project - the aim you'll have We’re looking for a skilled Senior Salesforce Administrator to join our team and work with one of our inspiring US clients, which specializes in software and solutions for shipping, mailing, and logistics management. Its mission is to help businesses optimize the entire shipping process, from label generation and carrier integration to returns management and international deliveries. If you enjoy working with cutting-edge technologies in a fast-paced environment, this opportunity is for you! Expectations - the experience you need Minimum of 5 years of experience in an enterprise-scale environment as a Salesforce Administrator. Certified Salesforce Administrator and additional Salesforce certifications preferred. Day-to-day BAU ticket handling. Proficiency in customizing and configuring Salesforce to meet specific business needs, including user management, security, and data models. Ability to identify and troubleshoot issues within the Salesforce platform and implement effective solutions. Skills in analyzing business processes, translating them into Salesforce solutions, and understanding how Salesforce can support business goals. Ability to manage the implementation of new features and updates, including user training and communication. Skills in planning, organizing, and executing projects related to Salesforce implementation and enhancements. Proficiency in managing user access, profiles, and permissions within Salesforce. Our offer – professional development, personal growth Educational resources Flexible schedule and Work From Anywhere Referral Program Supportive and chill atmosphere We are accepting applications from LATAM countries Position at: Software Mind AMERICAS #LI-DNI Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers DevOps, Security & Cloud Azure Senior DevOps Cloud Engineer (Azure) Kraków/Remote Software Development Golang Java Senior Back-end Software Engineer (Java + willing to learn Go) Bucharest/Remote Software Development Java Node. js Software Engineer (Java & NodeJS/TypeScript) Kraków/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-salesforce-business-analyst-6-8-month-contract/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior Salesforce Business Analyst (6-8 month contract)", "location": "Kraków/Remote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/senior-salesforce-business-analyst-6-8-month-contract/", "description": "Project - the aim you'll have We are looking for an experienced Salesforce Business Analyst to support our client. This role requires strong technical expertise in Salesforce and the ability to work independently in a fast-paced environment. Position - how you'll contribute Confidently configure Lightning, orchestrate Flows and fine-tune org settings without hand-holding. Liaise with stakeholders to translate needs into well-defined user stories and precise technical documentation. Deliver top-tier support and craft documentation that educates and engage. Explore Salesforce AI features and weave predictive insights into everyday operations. Navigate Gearset CI/CD, Jira, Zoom, the Distribution Engine—and beyond. Continuous Improvement & Best Practices: take an active role in team reviews, advocate for industry standards and strong internal protocols, and nurture a culture of innovation and high performance. Expectations - the experience you need Experience: 5+ years user-facing Salesforce work. Hands-on admin skills (Lightning, Flows, org setup). Equally fluent in Sales Cloud and Service Cloud. Certifications: Salesforce Admin, Advanced Admin and Business Analyst. Fluent Polish and English are required for this role. Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-salesforce-developer-6-8-month-contract/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior Salesforce Developer (6-8 month contract)", "location": "Kraków/Remote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/senior-salesforce-developer-6-8-month-contract/", "description": "Project - the aim you'll have Our client is a global technology company that provides innovative shipping and logistics solutions to e-commerce businesses of all sizes. With a strong presence in international markets, they help merchants streamline fulfillment operations and deliver exceptional customer experiences. The company values collaboration, efficiency, and continuous improvement, and is known for its supportive and agile work environment. We are looking for an experienced Salesforce Developer to support our client. This role requires strong technical expertise in Salesforce and the ability to work independently in a fast-paced environment. Position - how you'll contribute Salesforce Enhancements & System Integration: Design, implement, and maintain robust Salesforce solutions utilizing Apex, Visualforce, and Lightning Web Components, ensuring high performance and reliability across the platform. Database & BI Excellence: Apply advanced SQL and relational database expertise to build and optimize data models that support both Salesforce operations and broader BI strategies. End-to-End Development: Architect and implement scalable, reliable solutions that ensure data integrity and seamless integration with external systems. Collaboration: Translate complex technical concepts into clear, accessible language for diverse stakeholders, keeping everyone aligned. Documentation & Multitasking: Become the go-to expert in creating and utilising detailed documentation while effectively managing multiple projects in a fast-paced environment. Continuous Improvement & Best Practices: Engage in peer code reviews, advocate for best practices and process improvements, and mentor team members to cultivate a high-performing, forward-thinking development culture. Expectations - the experience you need Experience: 5+ years development (on any OO language) + good understanding of DB’s. Strong proficiency in object-oriented programming with hands-on experience in SQL and relational database management, crucial for BI tasks. Expertise in Apex and integration (REST/SOAP APIs). Strong documentation discipline. Cert: Platform Developer II (minimum). Proficient in English at a communicative level (B2+) Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers DevOps, Security & Cloud Azure Senior DevOps Cloud Engineer (Azure) Kraków/Remote Software Development Golang Java Senior Back-end Software Engineer (Java + willing to learn Go) Bucharest/Remote Software Development Java Node. js Software Engineer (Java & NodeJS/TypeScript) Kraków/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/senior-software-engineer-java-nodejs-typescript/", "company_id": 3332, "source": 3, "skills": "", "title": "Senior Software Engineer (Java & NodeJS/TypeScript)", "location": "Kraków/Remote", "location_type": "remote", "job_type": null, "min_experience": 5, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/senior-software-engineer-java-nodejs-typescript/", "description": "Project - the aim you'll have Our client is a gaming company established to reform the national gaming system. Operating under the principle of exclusivity, its mission is to provide games responsibly, mitigate gambling-related risks, and ensure player protection while preventing fraud. The company offers a wide range of games divided into three main categories: Lucky Games, Casino Games, and Betting Games. It emphasizes operating responsibly and contributing to a safe and secure gaming environment. Expectations - the experience you need 5+ years’ commercial development experience. Java and Spring Framework nodeJS with typescript Relational DBs (preferably PostgreSQL) Hands-on experience with the public cloud service provider GCP Nice to have : Docker, GCP GKE Kubernetes, Terraform, Redis, Grafana Cloud Strong analytical and communication skills, ability to work in a team. Understanding of CI/CD, SOLID, OOP principles Experience writing testable code Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development Java Node. js Software Engineer (Java & NodeJS/TypeScript) Kraków/Remote Software Development Golang Java Senior Back-end Software Engineer (Java + willing to learn Go) Bucharest/Remote Software Development JavaScript Node. js React. js Senior Full-stack Software Engineer (Nest. js + React) Kraków/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/software-engineer-angular/", "company_id": 3332, "source": 3, "skills": "", "title": "Software Engineer (Angular)", "location": "Kraków/Remote", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": 4, "apply_link": "https://careers.softwaremind.com/jobs/software-engineer-angular/", "description": "Project - the aim you'll have You will be part of an agile team that includes Polish and British developers and testers who deliver, rebuild and extend an existing IT ecosystem for a company in the travel industry. You will be engaged in adding new features, rebuilding existing solutions and proposing a new architecture. Your work will involve digital transformation initiatives, existing platform evolution, digital, web and mobile development and tech debt removal. Work will be divided between 4-5 teams, with each concentrating on a specific area. Position - how you'll contribute Developing new functionalities and maintaining existing ones Working with the team of the fronted developers Performing code reviews · Participating in creating product architecture Supporting the development team through exchanging knowledge and skills Collaborating closely with testers (QAs), business analysts, product owner, and scrum master Expectations - the experience you need At least 3-4 years of experience as a Software Engineer Very good knowledge of Angular (12+), Javascript, Typescript, CSS, GIT Fluency with web technology: HTML, CSS3 Practice creating front- end components libraries Experience with unit and integration tests (Je<PERSON>, Jasmine, Karma, Protractor) Good understanding of best development practices, code quality and, experience with code reviews Very good knowledge of design patterns Grasp of website availability Additional skills - the edge you have Experience using AzureDevOps tools Knowledge of state management systems such as NgRx, or other Redux pattern library Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development Angular React. js Front-end Software Engineer with German (React&Angular) Kraków/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/software-engineer-java-nodejs-typescript/", "company_id": 3332, "source": 3, "skills": "", "title": "Software Engineer (Java & NodeJS/TypeScript)", "location": "Kraków/Remote", "location_type": "remote", "job_type": null, "min_experience": 3, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/software-engineer-java-nodejs-typescript/", "description": "Project - the aim you'll have Our client is a gaming company established to reform the national gaming system. Operating under the principle of exclusivity, its mission is to provide games responsibly, mitigate gambling-related risks, and ensure player protection while preventing fraud. The company offers a wide range of games divided into three main categories: Lucky Games, Casino Games, and Betting Games. It emphasizes operating responsibly and contributing to a safe and secure gaming environment. Expectations - the experience you need 3+ years’ commercial development experience. Java and Spring Framework nodeJS with typescript Relational DBs (preferably PostgreSQL) Hands-on experience with the public cloud service provider GCP Nice to have : Docker, GCP GKE Kubernetes, Terraform, Redis, Grafana Cloud Strong analytical and communication skills, ability to work in a team. Understanding of CI/CD, SOLID, OOP principles Experience writing testable code Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development Golang Java Senior Back-end Software Engineer (Java + willing to learn Go) Bucharest/Remote Software Development JavaScript Node. js React. js Senior Full-stack Software Engineer (Nest. js + React) Kraków/Remote Software Development Node. js React. js (8NW) Full-Stack (React/TypeScript/Node. js) San Jose/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/team-leader-tech-leader-architect-node-js-react/", "company_id": 3332, "source": 3, "skills": "", "title": "Team Leader/Tech Leader/Architect (Node.js + React)", "location": "Kraków/Remote", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/team-leader-tech-leader-architect-node-js-react/", "description": "Project - the aim you'll have The candidate must be technically strong, self-going, proactive and take lead and ownership of the technical development processes. He or she will work very closely with the Product Manager, UX-designer and others in the product development process, roadmap and product direction. Needs to have a solid understanding of architecture, databases, data modelling and security Designing and building APIs, integrations and lay the foundation for larger features/concepts in the application Optimize and extend existing functionality, especially on the database side Take an active role in designing and implementing integrations and larger features Participate in customers meetings and lead technical discussions with customers Collaborate closely with the rest of the bigger organization Support and lead the development team Be the key decision maker in technical decisions Be responsible for operational concerns (monitoring, observability, deployments, database migrations etc. ) Be the final support channel for other customer facing functions (support, sales, consulting etc. ) Needs to follow the developer handbook Needs to engage with tech lead peers The most important aspect is to take pride and ownership in the product as a whole and lead the efforts in building and running a great product Expectations - the experience you need Expectations – the experience you need: We’re seeking a skilled developer with experience across our core technologies. You should be proficient in: General & DevOps AWS (Amazon Web Services): Experience with cloud infrastructure, specifically within AWS, is essential. CDK (Cloud Development Kit): You’ll be working with Infrastructure as Code (IaC) using AWS CDK. Firebase Auth: Familiarity with Firebase for authentication purposes. GitHub: Proficient in using GitHub for version control and collaboration. Frontend React: Strong expertise in building Single Page Applications (SPAs) with React. React Native: Experience developing and maintaining separate iOS and Android applications using React Native. State Management & Data Fetching: Hands-on experience with Redux and react-query. UI Libraries: Proficiency with MUI (Material-UI) for building user interfaces. Routing: Knowledge of React Router for navigation within SPAs. Tooling: Experience with Vite and TypeScript for frontend development. Backend Node. js & TypeScript: Strong backend development skills using Node. js and TypeScript. API Development: Experience with Express for REST APIs (internal) and Fastify for public-facing REST APIs. Database: Proficient in working with PostgreSQL databases. ORMs/Query Builders: Experience with Knex (for Express) and kysely (for Fastify) for database interactions. Containerization: Familiarity with Docker for containerizing applications. Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development Java Node. js Software Engineer (Java & NodeJS/TypeScript) Kraków/Remote Software Development JavaScript Node. js React. js Senior Full-stack Software Engineer (Nest. js + React) Kraków/Remote Software Development Node. js React. js (8NW) Full-Stack (React/TypeScript/Node. js) San Jose/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/software-engineer-with-german-java/", "company_id": 3332, "source": 3, "skills": "", "title": "Software Engineer with German (Java)", "location": "Kraków/Remote", "location_type": "remote", "job_type": null, "min_experience": 2, "max_experience": 5, "apply_link": "https://careers.softwaremind.com/jobs/software-engineer-with-german-java/", "description": "Project - the aim you'll have Project – the aim you’ll have: You will join a dynamic cross-functional development team tasked with building and scaling enterprise-grade applications for our client, using modern Java technologies and cloud-native solutions. The main goal is to deliver robust, maintainable, and high-performance systems that are aligned with microservices architecture principles. As part of this project, you will contribute to the design, implementation, and observability of mission-critical applications deployed in Kubernetes environments. Position – how you’ll contribute: As a Java Engineer, you will play a key role in the development lifecycle by designing and implementing backend services and APIs using Java 17 and Spring Boot 3. x. You will actively participate in system architecture decisions, write clean and testable code, and integrate observability tools such as OpenTelemetry. By collaborating with DevOps, QA, and product stakeholders in Agile ceremonies, you will ensure the solutions are scalable, secure, and production-ready. Your work will directly impact the stability and performance of client-facing systems. Expectations - the experience you need 3–5 years of professional experience in Java development 2+ years of hands-on experience with Spring Boot Proficiency in building RESTful APIs and using Spring Security, Spring Data JPA, and Hibernate Experience with SQL and NoSQL databases such as PostgreSQL, MySQL, MongoDB, or Redis Familiarity with cloud environments (AWS, Azure, or GCP) and container orchestration with Kubernetes Strong knowledge of CI/CD pipelines and tools like Jenkins or GitLab CI Experience with application monitoring, logging, and distributed tracing (e. g. , ELK stack, OpenTelemetry) Solid foundation in Git, Maven/Gradle, and testing frameworks like JUnit and Mockito Experience working in Agile development environments Language Skills: Good communication skills in both German and English, written and spoken. Additional skills - the edge you have Knowledge of event-driven architectures using Kafka or RabbitMQ Hands-on experience with Redis or Ehcache for caching strategies Familiarity with search engines like Elasticsearch or Apache Solr Basic frontend awareness (HTML, CSS, JavaScript) to support integration discussions A strong collaborative mindset and excellent communication skills Ability to contribute to architectural discussions and technical documentation A proactive attitude and the ability to work independently in a distributed team environment Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development Java Node. js Software Engineer (Java & NodeJS/TypeScript) Kraków/Remote Software Development Golang Java Senior Back-end Software Engineer (Java + willing to learn Go) Bucharest/Remote Software Development Java Node. js Senior Software Engineer (Java & NodeJS/TypeScript) Kraków/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/staff-software-engineer-react-typescript/", "company_id": 3332, "source": 3, "skills": "", "title": "Staff Software Engineer (React + TypeScript)", "location": "San Jose/Remote", "location_type": "remote", "job_type": null, "min_experience": 1, "max_experience": 10, "apply_link": "https://careers.softwaremind.com/jobs/staff-software-engineer-react-typescript/", "description": "Project - the aim you'll have Overview Software Mind is seeking qualified candidates to fill the role of Staff/Lead Software Engineer. In addition to a competitive salary rate and a positive work environment, committed to delivering high-quality technology solutions, and we also offer: Flexible schedules and authentic work-life balance Opportunities for continuing education Social activities per country sponsored by the company Birthday celebration Payment in US Dollars About the role: We are looking for a Staff (Lead) Software Engineer with expertise in React, TypeScript, Node. js, and SSR frameworks such as Next. js or Nuxt. js. The focus of the work is full-stack, with a slight emphasis on front-end. #LI-DNI Expectations - the experience you need The main responsibilities/tasks include: Your work will include both hands-on development and Team Lead duties: Hands-on development duties (60%) Write great code and be a team player who is willing to share your expertise and knowledge with others. Iterating and updating client-facing web pages and flows that guide users toward the right company-related products to meet their needs. Perform data integrations within the decision process to improve customer experience. Contribute to the design and development of new applications, features, and flows to make the customer experience more intuitive and user-friendly. Support ongoing efforts to optimize the client’s end-to-end SDLC, while maintaining best practices. Work throughout the full tech stack using React. js, TypeScript, Next. js, Node. js, GraphQL, SQL, REST API, Context API, and more. Own and drive various projects and support senior engineers in driving other projects Team Lead duties (40%): ​​​​Work collaborative and help “up-level” the team through mentorship, code reviews, knowledge sharing, and pair programming as needed on complex development tasks Be a true owner in every sense of the word as it relates to your work, responsibilities, and commitment to the team, and be an intrinsically motivated self-starter Provide thought leadership/expertise around the right level of engineering for a given solution to help ensure projects are not “over-engineered” Bring strong technical opinions to the table, but hold them loosely, being humble enough to let them go and move forward if the group consensus goes in another direction Attend and contribute to architecture and design meetings and discussions Partner with the Engineering Manager to: – Plan the roadmap & quarterly OKRS – Break down product requirements into actionable user stories – Bring a technical voice to planning & refinement meetings – Maintain a high bar for engineering excellence – Establish technical guardrails, conventions, and best practices – Suggest process improvements and ideas to make the team better Job Skills/Requirements – +90% English written and oral (at least B2 level) with excellent communication skills – 8–10 years of professional experience in software development. – 1–2 years in a technical leadership role (e. g. , Tech Lead, Team Lead, or Staff Engineer), demonstrating leadership and ownership of complex systems. – Expertise in React, advanced TypeScript, Node. js, and SSR frameworks such as Nest. js (v13) or Next. js – Experience with Astro. js, or similar modern web frameworks, with the ability to quickly ramp up on emerging technologies – Familiarity with islands architecture, partial hydration, or comparable strategies for optimizing front-end performance and interactivity – Familiarity with emerging web frameworks and concepts like Astro. js, partial hydration, and islands architecture. – Strong understanding of web performance optimization, including Core Web Vitals, caching strategies, static generation, and rendering techniques. – Proven ability to mentor engineers and uphold high standards in code quality and development best practices. – Experience building and maintaining scalable, production-ready applications. – Solid product mindset with the ability to align technical solutions with business objectives. – Effective collaboration with cross-functional teams, including engineering, product, and design. – Hands-on experience with observability and monitoring tools (e. g. , Datadog): – Experience setting up dashboards, configuring alerts, and tracing issues in prod environments Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development Java Node. js Software Engineer (Java & NodeJS/TypeScript) Kraków/Remote Software Development JavaScript Node. js React. js Senior Full-stack Software Engineer (Nest. js + React) Kraków/Remote Software Development Node. js React. js (8NW) Full-Stack (React/TypeScript/Node. js) San Jose/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/technical-lead/", "company_id": 3332, "source": 3, "skills": "", "title": "Technical Lead", "location": "Buenos Aires/Remote", "location_type": "remote", "job_type": null, "min_experience": 4, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/technical-lead/", "description": "Project - the aim you'll have We are currently looking for a skilled Technical Lead to become a member of our dynamic team and work closely with one of our inspiring US clients! Our client is modernizing the real estate world’s way of transferring information and funds to eliminate wire fraud and provide a secure, easy-to-use platform for title companies, law firms, and other financial services to protect themselves and their clients from wire fraud. Expectations – the experience you need 8+ years of software engineering experience 4+ years in a leadership role Strong experience with Python, Vue, PHP and AWS Experience working in a high-growth startup or scale-up environment Ability to balance speed and quality in a fast-moving environment Strong communication and project management skills Experience in the PropTech, FinTech, or marketplace domain (Nice to have) Position - how you'll contribute Lead, mentor, and manage a team of software engineers Drive engineering projects from concept to delivery, ensuring code quality, scalability, and performance Collaborate cross-functionally with Product, Design, and GTM to plan, prioritize, and execute on our roadmap Establish engineering best practices and promote a culture of technical excellence Balance hands-on technical contributions with people management Help shape our engineering processes and tools as we scale Our offer – professional development, personal growth Educational resources Flexible schedule and work from anywhere Referral Program Supportive and chill atmosphere We are accepting applications from LATAM countries Position at: Software Mind Latam #LI-DNI Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development Python Senior Back-end Engineer (Python) Buenos Aires/Remote Software Development PHP SQL Technical Leader (PHP) Warsaw/Remote Software Development PHP Python Senior Back-end Software Developer (Python + PHP) Buenos Aires SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/technical-leader-php/", "company_id": 3332, "source": 3, "skills": "", "title": "Technical Leader (PHP)", "location": "Warsaw/Remote", "location_type": "remote", "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://careers.softwaremind.com/jobs/technical-leader-php/", "description": "Project - the aim you'll have Project – the aim you’ll have: Our client is a Polish company specializing in software for call-center and CRM. A new team of 4+ people is being built and we are looking for a TechLead for it. The first tasks will include technological refactoring including migration from PHP 7. 4 to 8. X and the Zend framework to Laravel/Sympfony with containerization. The next tasks will include adding new functionalities based on AI. Position - how you'll contribute Designing and developing new features for the Customer multi-channel communication platform, a legacy PHP 7 system, and its surrounding services. Creating and maintaining services within the client’s ecosystem, focusing on reliability and seamless integration with the central monolith core. Actively participating in technical planning meetings, breaking down complex epics into developer-friendly tasks, and taking ownership of their execution. Collaborating closely with Product Owners and Business Analysts to refine requirements and ensure the technical feasibility of new functionalities. Ensuring high code quality through code reviews, applying best practices like TDD and SOLID principles, and utilizing static analysis tools. Driving the modernization of the client’s legacy system, including participation in the ongoing containerization project and improving system documentation. Mentoring and supporting other developers in the team, sharing knowledge, and fostering a collaborative environment. Expectations - the experience you need Proven commercial experience with PHP (versions 7. x to 8. x) and familiarity with frameworks like Zend, Laravel or Symfony. Hands-on experience in designing and building services in a microservices or service-oriented architecture, including understanding the challenges of monolith-service interaction. Experience working with and modernizing large-scale legacy systems, including strategies for refactoring and improving code quality. Practical knowledge of message queues (e. g. , RabbitMQ) and caching mechanisms (e. g. , Redis) for building scalable and resilient applications. Proficiency in using version control systems (Git) and experience with CI/CD pipelines (e. g. , GitLab, GitHub) to ensure smooth and frequent deployments. Proficiency in designing and consuming RESTful APIs, including experience integrating with third-party services and ensuring secure communication. Experience with containerization technologies (e. g. , Docker) and, ideally, infrastructure as code (e. g. , Ansible). Ability to work with raw SQL queries and understand the performance implications of different database interaction methods (ORM vs. raw queries). Strong problem-solving skills and the ability to propose solutions rather than just identifying issues, especially in a complex system with incomplete documentation. Fluency in Polish, allowing for effective communication with clients and team members, is essential. Additional skills - the edge you have Experience with Vue. js for frontend development. Familiarity with Python, as it is used in some of the client’s services connected to the telephony system. Experience with generative AI tools (e. g. , GitHub Copilot, Cursor) to enhance coding productivity. Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Software Development PHP Python Senior Back-end Software Developer (Python + PHP) Buenos Aires Software Development PHP Python Technical Lead Buenos Aires/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://careers.softwaremind.com/jobs/test-automation-engineer/", "company_id": 3332, "source": 3, "skills": "", "title": "Test Automation Engineer", "location": "Kraków/Remote", "location_type": "remote", "job_type": "full_time", "min_experience": 2, "max_experience": 3, "apply_link": "https://careers.softwaremind.com/jobs/test-automation-engineer/", "description": "Project - the aim you'll have Automate solutions that will drive the next generation of background screening. Own solutions and outcomes by automating complex cases and integrating QA automation at the CI/CD level. Collaborate within agile teams to develop scalable solutions. Implement and maintain performance and security testing protocols as part of the automation suite. Provide detailed reports and insights from automated testing to enhance product quality. Continuously evaluate and improve automation processes to increase efficiency and coverage. Expectations - the experience you need Bachelor’s Degree or equivalent. 5+ years of QA experience in high volume SaaS platforms. Experience with, TypeScript. Webdriver. io and Playwright frameworks, Browserstack and Github pipelines. Minimum 3 years working as a fulltime automation engineer for a customer facing high volume SaaS platform. Minimum 2 years working in an Agile development environment (Scrum or Kanban preferred). Experience with QA Automation platforms and technologies including CI/CD level automation integration. Proficiency with specific automation tools like Selenium, Webdriver. io and Playwright, Browserstack,Tosca, and CI/CD tools like Jenkins or Github pipelines. Knowledge of performance testing and security testing practices. Strong problem solving and analytical thinking skills. This role requires candidates to be based in the European Union. Our offer – professional development, personal growth Hover on each benefit to learn more Decide on the form and conditions of your employment *Applicable in: Poland & Latam & MD/RO Flexible employment and remote work *Applicable in: Poland & Latam & MD/RO Work with the latest technologies for industry leaders *Applicable in: Poland & Latam & MD/RO International projects with leading global clients *Applicable in: Poland & Latam & MD/RO Travel to clients’ and work in their environments *Applicable in: Poland & MD/RO International business trips *Applicable in: Poland & MD/RO Work comfortably in a relaxed and inspiring atmosphere that does not enforce a dress code *Applicable in: Poland & Latam & MD/RO Non-corporate atmosphere *Applicable in: Poland & Latam & MD/RO Develop your skills at work – language classes come to you *Applicable in: Poland & MD/RO Language classes *Applicable in: Poland & MD/RO Develop your competences and skills through skill centers and webinars *Applicable in: Poland & MD/RO Internal & external training *Applicable in: Poland & MD/RO Take care of the health of you and your loved ones *Applicable in: Poland & MD/RO Private healthcare and insurance *Applicable in: Poland & MD/RO Work out, swim and enjoy other fun activities *Applicable in: Poland & MD/RO - monthly sport budget Multisport card *Applicable in: Poland & MD/RO - monthly sport budget Take part in activities that support your physical and mental health *Applicable in: Poland & Latam & MD/RO Well-being initiatives *Applicable in: Poland & Latam & MD/RO Similar job offers Quality Assurance & Testing Automation Selenium (8AD) Lead Quality Engineer San Jose/Remote SHOW ALL JOBS", "ctc": null, "currency": null, "meta": {}}]